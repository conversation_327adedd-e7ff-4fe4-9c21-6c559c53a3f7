using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    public class LogsheetAllListEntity
    {
        public LogsheetAllListEntity()
        {
            
         
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:表单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SheetId { get; set; }
        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:表单明细总数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// Desc:未通过表单明细总数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int InPassCount { get; set; }
        /// <summary>
        /// Desc:未通过表单明细总数颜色
        /// Default:
        /// Nullable:False
        /// </summary>
        public string InPassCountColor { get; set; }
        /// <summary>
        /// Desc:通过表单明细总数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int PassCount { get; set; }
                /// <summary>
        /// Desc:通过表单明细总数颜色
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PassCountColor { get; set; }
        /// <summary>
        /// Desc:未填写表单明细总数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int EmptyCount { get; set; }
        /// <summary>
        /// Desc:未填写表单明细总数颜色
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EmptyCountColor { get; set; }
        /// <summary>
        /// Desc:执行ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExecutionId { get; set; }
        /// <summary>
        /// Desc:订单code
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoCode { get; set; }
        /// <summary>
        /// Desc:执行po状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExecutionStatus { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:组名
        /// Default:
        /// Nullable:False
        /// </summary>
        public string GroupName { get; set; }
        /// <summary>
        /// Desc:
        /// 组id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string GroupId { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// Desc:状态名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string StatusName { get; set; }
        /// <summary>
        /// Desc:状态颜色
        /// Default:
        /// Nullable:False
        /// </summary>
        public string StatusColor { get; set; }
        /// <summary>
        /// Desc:评论
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Comment { get; set; }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ShiftId { get; set; }
        /// <summary>
        /// Desc:班组名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ShiftName { get; set; }
        /// <summary>
        /// Desc:频率
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Frequency { get; set; }
        /// <summary>
        /// Desc:是否允许QA审核
        /// Default:
        /// Nullable:False
        /// </summary>
        public string QaAduit { get; set; }
        /// <summary>
        /// Desc:审批人
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Approveuserid { get; set; }
        /// <summary>
        /// Desc:审批时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? Approvedate { get; set; }
        /// <summary>
        /// Desc:表现
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Performance { get; set; }
        /// <summary>
        /// Desc:批次
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Batch { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SSCC { get; set; }
        /// <summary>
        /// Desc:物料
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Material { get; set; }

		public string CreateUserId { get; set; }
		public string ModifyUserId { get; set; }

		
		public DateTime CreateDate { get; set; }
		/// <summary>
		/// Desc:时间
		/// Default:
		/// Nullable:False
		/// </summary>
		public DateTime Date { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Formula")]
		public string Formula { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public int? Type { get; set; }
		/// <summary>
		/// Desc:计划开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PLAN_START_TIME")]
		public DateTime? PlanStartTime { get; set; }
		/// <summary>
		/// Desc:顺序号
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Sequence")]
		public long Sequence { get; set; }
		/// <summary>
		/// Desc:QAStatus
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QAStatus")]
		public string QAStatus { get; set; }
		/// <summary>
		/// Desc:FormulaNo
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "FormulaNo")]
		public string FormulaNo { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "Count")]
		public int Count { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TestingTime")]
		public DateTime? TestingTime { get; set; }
		public string TestingUser { get; set; }
		/// <summary>
		/// Desc:ProductionOrderId
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
	}
}