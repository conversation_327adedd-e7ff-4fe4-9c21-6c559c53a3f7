using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_PPM_LOGSHEET_ADN_DETAIL_VIEW")] 
    public class LogsheetAdnDetailViewEntity : EntityBase
    {
        public LogsheetAdnDetailViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_ID")]
        public string BatchId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PARAMETER_GROUP_ID")]
        public string ParameterGroupId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PO_EXECUTION_ID")]
        public string PoExecutionId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public int Status { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMMENT")]
        public string Comment { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SHIFT_ID")]
        public string ShiftId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FREQUENCY")]
        public string Frequency { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="APPROVEDATE")]
        public DateTime? Approvedate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="APPROVEUSERID")]
        public string Approveuserid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FREQUENCY_VALUE")]
        public decimal FrequencyValue { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QA_ADUIT")]
        public string QaAduit { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public int? Type { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName="DETAIL_ID")]
        public string DetailId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOGSHEET_ID")]
        public string LogsheetId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEMPLATE_ID")]
        public string TemplateId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VALUE")]
        public string Value { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_COMMENT")]
        public string DetailComment { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IMAGE")]
        public string Image { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_NULL")]
        public string IsNull { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_EDITABLE")]
        public string IsEditable { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ENTRY_TYPE")]
        public string EntryType { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EXTERNAL_URL")]
        public string ExternalUrl { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_PASS")]
        public string IsPass { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_CREATEDATE")]
        public DateTime? DetailCreatedate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_CREATEUSERID")]
        public string DetailCreateuserid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_MODIFYDATE")]
        public DateTime? DetailModifydate { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_MODIFYUSERID")]
        public string DetailModifyuserid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_UPDATETIMESTAMP")]
        public byte[] DetailUpdatetimestamp { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_PARAMETER_NAME")]
        public string DetailParameterName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DETAIL_PARAMTER_TYPE")]
        public string DetailParamterType { get; set; }
		/// <summary>
		/// Desc:ProductionOrderId
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
	}
}