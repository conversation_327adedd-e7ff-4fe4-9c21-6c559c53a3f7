using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    public class LogsheetQaCheckEntity
    {
        public LogsheetQaCheckEntity()
        {
            
         
        }
        /// <summary>
        /// Desc:返回状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public bool ReturnStatus { get; set; }
        /// <summary>
        /// Desc:返回代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ReturnCode { get; set; }
        /// <summary>
        /// Desc:返回信息
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ReturnMessage { get; set; }
        /// <summary>
        /// Desc:Qa状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string QaStatus { get; set; }
    }
}