using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models.PPM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("V_PPM_PHASE_SALESCONTAINER_VIEW")]
    public class PhaseSalescontainerViewEntity : EntityBase
    {
        public PhaseSalescontainerViewEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_ID")]
        public string SegmentId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SALESCONTAINER_ID")]
        public string SalescontainerId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SALES_CONTAINER")]
        public string SalesContainer { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
    }
}