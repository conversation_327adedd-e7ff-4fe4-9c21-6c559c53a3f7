using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///工单BOM表
    ///</summary>
    
    [SugarTable("PTM_I_ORDER_BOM")] 
    public class OrderBomEntity : EntityBase
    {
        public OrderBomEntity()
        {
        }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:订单类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_TYPE")]
        public string ProductionOrderType { get; set; }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FACTORY_CODE")]
        public string FactoryCode { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_DECS")]
        public string MaterialDecs { get; set; }
           /// <summary>
           /// Desc:计划生产量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal? Quantity { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT")]
        public string Unit { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FORMULA")]
        public string Formula { get; set; }
           /// <summary>
           /// Desc:配方编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FORMULA_CODE")]
        public string FormulaCode { get; set; }
           /// <summary>
           /// Desc:组件编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_ID")]
        public string ItemId { get; set; }
           /// <summary>
           /// Desc:组件描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_DECS")]
        public string ItemDecs { get; set; }
           /// <summary>
           /// Desc:组件用量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_QUANTITY_WITH_LOSS")]
        public decimal? ItemQuantityWithLoss { get; set; }
           /// <summary>
           /// Desc:组件用量（不含损耗）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_QUANTITY_WITHOUT_LOSS")]
        public decimal? ItemQuantityWithoutLoss { get; set; }
           /// <summary>
           /// Desc:组件单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ITEM_UNIT")]
        public string ItemUnit { get; set; }
           /// <summary>
           /// Desc:组件物料组
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_GROUP")]
        public string MaterialGroup { get; set; }
           /// <summary>
           /// Desc:发料仓库
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WAREHOUSE_ID")]
        public string WarehouseId { get; set; }

        /// <summary>
        /// Desc:传输号，用于解密
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TranNo")]
        public string TranNo { get; set; }

        /// <summary>
        /// Desc:长文本信息 ：LGTXT
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }


        [SugarColumn(IsIgnore =true)]
        public string MaterialCode { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string MaterialName { get; set; }

    }
}