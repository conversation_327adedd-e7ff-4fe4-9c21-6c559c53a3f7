using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_OrderRealtion")] 
    public class OrderrealtionEntity : EntityBase
    {
        public OrderrealtionEntity()
        {
        }
           /// <summary>
           /// Desc:煮制生产工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COOKORDER_ID")]
        public string CookorderId { get; set; }
           /// <summary>
           /// Desc:煮制生产工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COOKORDER_CODE")]
        public string CookorderCode { get; set; }
           /// <summary>
           /// Desc:灌装生产工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FILLORDER_ID")]
        public string FillorderId { get; set; }
           /// <summary>
           /// Desc:灌装生产工单代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FILLORDER_Code")]
        public string FillorderCode { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}