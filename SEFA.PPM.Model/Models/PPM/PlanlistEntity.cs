using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_PlanList")] 
    public class PlanlistEntity : EntityBase
    {
        public PlanlistEntity()
        {
        }
           /// <summary>
           /// Desc:生产线ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Line_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:生产线名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Line_Name")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Mat_ID")]
        public string MatId { get; set; }
           /// <summary>
           /// Desc:配方code
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Mat_Code")]
        public string MatCode { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="Mat_Name")]
        public string MatName { get; set; }
           /// <summary>
           /// Desc:计划表重量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PlanWeight")]
        public decimal Planweight { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}