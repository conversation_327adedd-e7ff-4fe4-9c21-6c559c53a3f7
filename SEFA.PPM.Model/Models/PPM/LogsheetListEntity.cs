using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    public class LogsheetListEntity
    {
        public LogsheetListEntity()
        {
            
         
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SheetName { get; set; }
        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// Desc:参数组ID（日志表名）
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ParameterGroupId { get; set; }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoId { get; set; }
		/// <summary>
		/// Desc:工单
		/// Default:
		/// Nullable:False
		/// </summary>
		public string PO{ get; set; }
		/// <summary>
		/// Desc:工单状态
		/// Default:
		/// Nullable:False
		/// </summary>
		public string PoStatus { get; set; }
        /// <summary>
        /// Desc:执行ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExecutionId { get; set; }
        /// <summary>
        /// Desc:执行po状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoExecutionStatus { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// Desc:评论
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Comment { get; set; }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ShiftId { get; set; }
        /// <summary>
        /// Desc:频率
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Frequency { get; set; }
        /// <summary>
        /// Desc:频率名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string FrequencyName { get; set; }
        /// <summary>
        /// Desc:频率设定值
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal? FrequencyValue { get; set; }
        /// <summary>
        /// Desc:审批时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime Approvedate { get; set; }
        /// <summary>
        /// Desc:审批人
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Approveuserid { get; set; }
        /// <summary>
        /// Desc:是否QA审批
        /// Default:
        /// Nullable:False
        /// </summary>
        public string QaAduit { get; set; }
        /// <summary>
        /// Desc:最新的entity插入时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? LastTimeEntity { get; set; }
        /// <summary>
        /// Desc:开始时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// Desc:搜索条件
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Search { get; set; }
        /// <summary>
        /// Desc:是否允许使用newEntry
        /// Default:
        /// Nullable:False
        /// </summary>
        public string IsNewEntry { get; set; }

		public string CheckResult { get; set; }

		public string ColorCode { get; set; }

	}
}