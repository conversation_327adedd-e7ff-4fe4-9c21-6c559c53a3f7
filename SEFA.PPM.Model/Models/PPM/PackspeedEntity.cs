using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PPM_B_PACKSPEED")]
    public class PackspeedEntity : EntityBase
    {
        public PackspeedEntity () {
        }
        /// <summary>
        /// Desc:工作中心
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WORK_CENTER")]
        public string WorkCenter { get; set; }
        /// <summary>
        /// Desc:配方代码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FORMULA_CODE")]
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:包装规格
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SALESCONTAINER_CODE")]
        public string SalescontainerCode { get; set; }
        /// <summary>
        /// Desc:包装描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SALESCONTAINER_NAME")]
        public string SalescontainerName { get; set; }
        /// <summary>
        /// Desc:包装单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PACKINGUNIT")]
        public string Packingunit { get; set; }
        /// <summary>
        /// Desc:包装单位描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PACKINGUNIT_NAME")]
        public string PackingunitName { get; set; }
        /// <summary>
        /// Desc:产品类别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_CATEGORY")]
        public string ProductionCategory { get; set; }
        /// <summary>
        /// Desc:MRP控制者
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MRP")]
        public string Mrp { get; set; }
        /// <summary>
        /// Desc:生产速度 个/小时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SPEED")]
        public decimal? Speed { get; set; }

        /// <summary>
        /// Desc:OEE速度
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OEE_SPEED")]
        public int? OeeSpeed { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:客户类型 SNB等
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CUSTOMER_TYPE")]
        public string CustomerType { get; set; }

    }
}