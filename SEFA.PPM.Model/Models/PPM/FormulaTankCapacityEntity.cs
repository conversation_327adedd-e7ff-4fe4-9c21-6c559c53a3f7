using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PPM_M_FORMULA_TANK_CAPACITY")]
    public class FormulaTankCapacityEntity : EntityBase
    {
        public FormulaTankCapacityEntity () {
        }
        /// <summary>
        /// Desc:配方编号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FORMULA_CODE")]
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_NAME")]
        public string LineName { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:物料编号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:煮缸容量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TANK_CAPACITY")]
        public int? TankCapacity { get; set; }
        /// <summary>
        /// Desc:最小重量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MIN_WEIGHT")]
        public int? MinWeight { get; set; }
        /// <summary>
        /// Desc:最大重量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAX_WEIGHT")]
        public int? MaxWeight { get; set; }

    }
}