using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_B_LOGSHEET")]
	public class LogsheetEntity : EntityBase
	{
		public LogsheetEntity()
		{
		}
		/// <summary>
		/// Desc:批次ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "BATCH_ID")]
		public string BatchId { get; set; }
		/// <summary>
		/// Desc:参数组ID（日志表名）
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PARAMETER_GROUP_ID")]
		public string ParameterGroupId { get; set; }
		/// <summary>
		/// Desc:执行ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "PO_EXECUTION_ID")]
		public string PoExecutionId { get; set; }
		/// <summary>
		/// Desc:设备ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "EQUIPMENT_ID")]
		public string EquipmentId { get; set; }
		/// <summary>
		/// Desc:状态(0初始化，1待QA审核，2完成，3无效，4Qa审核不通过)
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "STATUS")]
		public int Status { get; set; }
		/// <summary>
		/// Desc:评论
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "COMMENT")]
		public string Comment { get; set; }
		/// <summary>
		/// Desc:班组ID
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SHIFT_ID")]
		public string ShiftId { get; set; }
		/// <summary>
		/// Desc:频率
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "FREQUENCY")]
		public string Frequency { get; set; }
		/// <summary>
		/// Desc:频率值
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "FREQUENCY_VALUE")]
		public decimal FrequencyValue { get; set; }
		/// <summary>
		/// Desc:审批时间
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "APPROVEDATE")]
		public DateTime Approvedate { get; set; }
		/// <summary>
		/// Desc:审批人
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "APPROVEUSERID")]
		public string Approveuserid { get; set; }
		/// <summary>
		/// Desc:是否需要QA审批 0：不需要 ；1：需要
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "QA_ADUIT")]
		public string QaAduit { get; set; }

		/// <summary>
		/// Desc:取样类型(可为空)：1正常样 2未煮样 3离心样 4特检样 5重调样  来源于数据字典
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public int? Type { get; set; }

		/// <summary>
		/// Desc:检测时间
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "TestingTime")]
		public DateTime? TestingTime { get; set; }
		/// <summary>
		/// Desc:
		/// Default: 
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TestingUser")]
		public string TestingUser { get; set; }

	}
}