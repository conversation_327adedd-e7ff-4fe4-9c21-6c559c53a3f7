using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.ViewModels;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    public class LogsheetAndDetailEntity
    {
        public LogsheetAndDetailEntity()
        {
        }
        public LogsheetEntity LogsheetEntity { get; set; }
        public LogsheetReturnEntity LogsheetReturnEntity { get; set; }
        public List<LogsheetDetailEntity> LogsheetDetailList { get; set; }
        public List<DetailAndParamModel> DetailAndParamModelList { get; set; }
        public class DetailAndParamModel
        {
          public LogsheetDetailEntity LogsheetDetail { get; set; }
          public List<ParameterLimitEntity> LimitList { get; set; }
          public CheckValue CheckValue { get; set; }
          public List<SafeData> SafeData { get; set; }
          //public List<DataItemDetailEntity> DataItemDetailEntityList { get; set; }
        }
        public class SafeData
        {
           public string SafeItem {  get; set; }
           public string Color { get; set;}
        }
        public class CheckValue 
        {
         public List<WrongData> WrongData { get; set; }

         public List<string> WrongSelectData { get; set; }
        }
        public class WrongData
        {
            public decimal? Max { get; set; }
            public decimal? Min { get; set; }
            public bool MaxIsInclude { get; set; }
            public bool MinIsInclude { get; set;}
            public string MaxIsRemark { get; set; }
        }
    }
}