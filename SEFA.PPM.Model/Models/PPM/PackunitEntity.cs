using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_I_PackUnit")] 
    public class PackunitEntity : EntityBase
    {
        public PackunitEntity()
        {
        }
           /// <summary>
           /// Desc:包装单位
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PACKINGUNIT")]
        public string Packingunit { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}