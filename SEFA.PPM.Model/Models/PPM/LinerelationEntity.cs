using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_M_LineRelation")] 
    public class LinerelationEntity : EntityBase
    {
        public LinerelationEntity()
        {
        }
           /// <summary>
           /// Desc:前生产线ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRELINE_ID")]
        public string PrelineId { get; set; }
           /// <summary>
           /// Desc:前生产线代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRELINE_CODE")]
        public string PrelineCode { get; set; }
           /// <summary>
           /// Desc:前生产线名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRELINE_NAME")]
        public string PrelineName { get; set; }
           /// <summary>
           /// Desc:后生产线ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:后生产线代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_CODE")]
        public string LineCode { get; set; }
           /// <summary>
           /// Desc:后生产线名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_NAME")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}