using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PPM_B_PLAN_ORDER")]
    public class PlanOrderEntity : EntityBase
    {
        public PlanOrderEntity()
        {
        }
        /// <summary>
        /// Desc:生产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_id")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:生产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_NAME")]
        public string LineName { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAT_ID")]
        public string MatId { get; set; }
        /// <summary>
        /// Desc:配方code
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAT_CODE")]
        public string MatCode { get; set; }

        /// <summary>
        /// Desc:生产版本
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProdVersion")]
        public string ProdVersion { get; set; }

        /// <summary>
        /// Desc:SAP配方
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SapFormula")]
        public string SapFormula { get; set; }
        /// <summary>
        /// Desc:配方名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MAT_NAME")]
        public string MatName { get; set; }
        /// <summary>
        /// Desc:计划表重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PLANWEIGHT")]
        public decimal Planweight { get; set; }
        /// <summary>
        /// Desc:损耗重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOSSWEIGHT")]
        public decimal Lossweight { get; set; }
        /// <summary>
        /// Desc:喉头添加量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "THROATWEIGHT")]
        public decimal Throatweight { get; set; }
        /// <summary>
        /// Desc:调节重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ADJUSTWEIGHT")]
        public decimal Adjustweight { get; set; }
        /// <summary>
        /// Desc:入轻重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "INLIGHTWEIGHT")]
        public decimal Inlightweight { get; set; }
        /// <summary>
        /// Desc:排产重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SCHEDULEWEIGHT")]
        public decimal Scheduleweight { get; set; }
        /// <summary>
        /// Desc:缸数
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TANKQUANTITY")]
        public decimal Tankquantity { get; set; }
        /// <summary>
        /// Desc:每缸数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TANKWEIGHT")]
        public decimal Tankweight { get; set; }
        /// <summary>
        /// Desc:生产日期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCE_DATE")]
        public DateTime ProduceDate { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:SAP瓶型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SapContainer")]
        public string SapContainer { get; set; }

        /// <summary>
        /// Desc:SAP工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SapOrderNo")]
        public string SapOrderNo { get; set; }

        /// <summary>
        /// Desc:mes工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MesOrderNo")]
        public string MesOrderNo { get; set; }

        /// <summary>
        /// Desc:SAP状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SapStatus")]
        public string SapStatus { get; set; }

        /// <summary>
        /// Desc:操作状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OpStatus")]
        public string OpStatus { get; set; }
        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 同步中 3 同步成功 4 同步失败 
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SAP_FLAG")]
        public int SapFlag { get; set; }


        /// <summary>
        /// Desc:工作中心
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WORK_CENTER")]
        public string WorkCenter { get; set; }

        
        /// <summary>
        /// Desc:生产版本文本描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ProdVersionText { get; set; }

        /// <summary>
        /// Desc:罐装线
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FillLine { get; set; }



    }
}