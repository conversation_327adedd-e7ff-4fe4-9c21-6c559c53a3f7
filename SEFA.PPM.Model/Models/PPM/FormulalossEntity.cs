using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_FORMULALOSS")] 
    public class FormulalossEntity : EntityBase
    {
        public FormulalossEntity()
        {
        }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAT_ID")]
        public string MatId { get; set; }
           /// <summary>
           /// Desc:配方名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAT_NAME")]
        public string MatName { get; set; }
           /// <summary>
           /// Desc:使用的缸重
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TANK_WEIGHT")]
        public decimal? TankWeight { get; set; }
           /// <summary>
           /// Desc:锅数
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TANKQUANTITY")]
        public int? Tankquantity { get; set; }
           /// <summary>
           /// Desc:预计损耗
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ESTIMATE_LOSS")]
        public decimal EstimateLoss { get; set; }
           /// <summary>
           /// Desc:生产日期
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTDATE")]
        public DateTime Productdate { get; set; }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_NAME")]
        public string LineName { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}