using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PPM_B_FORMULASCHEDULE_DETAIL")] 
    public class FormulascheduleDetailEntity : EntityBase
    {
        public FormulascheduleDetailEntity()
        {
        }
        /// <summary>
        /// Desc:配方排产ID
        /// Default:
        /// Nullable:false
        /// </summary>
        [SugarColumn(ColumnName="SCHEDULE_ID")]
        public string ScheduleId { get; set; }
        /// <summary>
        /// Desc:生产版本
        /// Default:
        /// Nullable:true
        /// </summary>
        [SugarColumn(ColumnName = "PROD_VERSION")]
        public string ProdVersion { get; set; }
        /// <summary>
        /// Desc:损耗重量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOSSWEIGHT")]
        public decimal Lossweight { get; set; }
        /// <summary>
        /// Desc:喉头添加量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "THROATWEIGHT")]
        public decimal Throatweight { get; set; }

        /// <summary>
        /// Desc:缸数
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="TANKQUANTITY")]
        public decimal Tankquantity { get; set; }
           /// <summary>
           /// Desc:每缸数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TANKWEIGHT")]
        public decimal Tankweight { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}