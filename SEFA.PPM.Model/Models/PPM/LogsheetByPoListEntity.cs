using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.Collections.Generic;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    public class LogsheetByPoListEntity
    {
        public LogsheetByPoListEntity()
        {
            
         
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:订单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoId { get; set; }
        /// <summary>
        /// Desc:订单状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string PoStatus { get; set; }
        /// <summary>
        /// Desc:配方
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Recipe { get; set; }
        /// <summary>
        /// Desc:部分
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Section { get; set; }
        /// <summary>
        /// Desc:上下文类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ContextType { get; set; }
        /// <summary>
        /// Desc:设备Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MachineId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Machine { get; set; }
        /// <summary>
        /// Desc:参数名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Paramater { get; set; }
        /// <summary>
        /// Desc:阶段
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Phase { get; set; }
        /// <summary>
        /// Desc:分组id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string GroupId { get; set; }
        /// <summary>
        /// Desc:分组名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string GroupName { get; set; }
        /// <summary>
        /// Desc:图标
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Icon { get; set; }
        /// <summary>
        /// Desc:简短描述
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ShortDescription { get; set; }
        /// <summary>
        /// Desc:限制
        /// Default:
        /// Nullable:False
        /// </summary>
        public List<KeyAndValueModel> Limits { get; set; }
        /// <summary>
        /// Desc:输入类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string InputType { get; set; }
        /// <summary>
        /// Desc:目标
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Target { get; set; }
        /// <summary>
        /// Desc:默认
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Default { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string UOM { get; set; }
        /// <summary>
        /// Desc:限制
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Required { get; set; }
        /// <summary>
        /// Desc:更新依据
        /// Default:
        /// Nullable:False
        /// </summary>
        public string UpdateBy { get; set; }
        /// <summary>
        /// Desc:上下文版本
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ContextVersion { get; set; }
        
        /// <summary>
        /// Desc:频次
        /// Default:
        /// Nullable:False
        /// </summary>
        public KeyAndValueModel Frequency { get; set; }
        /// <summary>
        /// Desc:是否需要QA审核
        /// Default:
        /// Nullable:False
        /// </summary>
        public string IsQAConfir { get; set; }
        public class KeyAndValueModel 
        {
            public int key { get; set; }
            public string value { get; set; }
        }
    }
}