using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// CIP
	/// </summary>
	public class MMI_CIP
	{
		/// <summary>
		/// 设备
		/// </summary>
		public string Equipment { get; set; }
		/// <summary>
		/// CIP工单号
		/// </summary>
		public string CIPOrderNo { get; set; }
		/// <summary>
		/// CIP方式
		/// </summary>
		public string CIPClass { get; set; }
		/// <summary>
		/// CIP工单开始时间
		/// </summary>
		public DateTime? TotalStartTime { get; set; }
		/// <summary>
		/// CIP工单结束时间
		/// </summary>
		public DateTime? TotalEndTime { get; set; }
		/// <summary>
		/// CIP关键步骤
		/// </summary>
		public string CIPStepKey { get; set; }
		/// <summary>
		/// 步骤开始时间
		/// </summary>
		public DateTime? StepStartTime { get; set; }
		/// <summary>
		/// 步骤结束时间
		/// </summary>
		public DateTime? StepEndTime { get; set; }
	}
}
