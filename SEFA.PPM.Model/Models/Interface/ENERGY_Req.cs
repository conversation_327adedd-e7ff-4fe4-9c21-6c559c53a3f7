using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace SEFA.PPM.Model.Models.Interface
{

	public class ENERGY_Req
	{

	}

	public class TokenReq
	{
		public string username { get; set; }

		public string password { get; set; }
	}
	public class ENERGY_InstrumentList_ReqPage
	{
		public int pageIndex { get; set; } = 1;

		public int pageSize { get; set; } = 20;

		public ENERGY_InstrumentList_Req req { get; set; }

	}

	public class ENERGY_InstrumentList_Req
	{
		/// <summary>
		/// 能管账号ID 必须
		/// </summary>
		//public string ForeignId { get; set; }
		/// <summary>
		/// 仪表ID
		/// </summary>
		public string device_id { get; set; }
		/// <summary>
		/// 仪表编号
		/// </summary>
		public string MeterCode { get; set; }
		/// <summary>
		/// 仪表名称
		/// </summary>
		public string MeterCode_Name { get; set; }
		/// <summary>
		/// 仪表类型:"电”、”水”、”蒸汽”
		/// </summary>
		public string MeterType { get; set; }
		/// <summary>
		/// 能管规则名 必须 (能源管理员——包装三厂)
		/// </summary>
		//public string roleName { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string token { get; set; }
	}

	public class ENERGY_Data_Req
	{
		/// <summary>
		/// 能管规则名 必须
		/// </summary>
		//public string roleName { get; set; }
		/// <summary>
		/// 能管账号ID 必须
		/// </summary>
		//public string ForeignId { get; set; }
		/// <summary>
		/// 开始时间
		/// </summary>
		public string StartDate { get; set; }
		/// <summary>
		/// 结束时间
		/// </summary>
		public string EndDate { get; set; }

		public string token { get; set; }

		public List<ENERGY_Data_IitemReq> DataList { get; set; }
	}

	public class ENERGY_Data_IitemReq
	{
		/// <summary>
		/// 仪表ID
		/// </summary>
		public string device_id { get; set; }
		/// <summary>
		/// 数据采集点代码
		/// </summary>
		public string MeterCode { get; set; }
		/// <summary>
		/// 数据采集点名称
		/// </summary>
		public string MeterCode_Name { get; set; }
		/// <summary>
		/// 仪表类型代码
		/// </summary>
		public string MeterType { get; set; }
		/// <summary>
		/// 工作中心名称
		/// </summary>
		public string MeterTypeName { get; set; }

		public string dataItemId { get; set; }
	}
}