using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	public class StartSession
	{
		public int MsgId { get; set; }

		public string Msg { get; set; }

		public string Result { get; set; }
	}

	public class Paras
	{
		/// <summary>
		/// 开始日期
		/// </summary>
		public string begindate { get; set; }
		/// <summary>
		/// 结束日期
		/// </summary>
		public string enddate { get; set; }
		/// <summary>
		/// 工号
		/// </summary>
		public string badge { get; set; }
		/// <summary>
		/// 成本中心
		/// </summary>
		public string costcenter { get; set; }
		/// <summary>
		/// 三级部门，例如：包装三厂
		/// </summary>
		public string DPTITLE3 { get; set; }
		/// <summary>
		/// 员工性质
		/// </summary>
		public string EMPTYPE { get; set; }
		/// <summary>
		/// 工司代号
		/// </summary>
		public string cpcode { get; set; }
	}

	public class HRSReqModel
	{
		/// <summary>
		/// 
		/// </summary>
		public string funcId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Paras paras { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string dataFormat { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string accessToken { get; set; }
	}

	public class RowItem
	{
		/// <summary>
		/// 异常
		/// </summary>
		public int ISEXCEPTION { get; set; }
		/// <summary>
		/// 异常原因
		/// </summary>
		public string REMARK { get; set; }
		/// <summary>
		/// *日期
		/// </summary>
		public DateTime TERM { get; set; }
		/// <summary>
		/// 员工ID
		/// </summary>
		public string BADGE { get; set; }
		/// <summary>
		/// 姓名
		/// </summary>
		public string NAME { get; set; }
		/// <summary>
		/// 三级部门
		/// </summary>
		public string DPTITLE3 { get; set; }
		/// <summary>
		/// 组别
		/// </summary>
		public string DPTITLE4 { get; set; }
		/// <summary>
		/// 小组
		/// </summary>
		public string DPTITLE5 { get; set; }
		/// <summary>
		/// 职位
		/// </summary>
		public string JBID { get; set; }
		/// <summary>
		/// 员工性质
		/// </summary>
		public string EMPTYPE { get; set; }
		/// <summary>
		/// 级别2
		/// </summary>
		public string EMPGRADE2 { get; set; }
		/// <summary>
		/// 员工加班类型
		/// </summary>
		public string OTTYPE { get; set; }
		/// <summary>
		/// 成本中心
		/// </summary>
		public string COSTCENTER { get; set; }
		/// <summary>
		/// 班次
		/// </summary>
		public string SHIFT { get; set; }
		/// <summary>
		/// 日历类型
		/// </summary>
		public string DAYTYPE { get; set; }
		/// <summary>
		/// 班次开始时间
		/// </summary>
		public DateTime BEGINTIME1 { get; set; }
		/// <summary>
		/// 班次结束时间
		/// </summary>
		public DateTime ENDTIME1 { get; set; }
		/// <summary>
		/// 加班开始时间
		/// </summary>
		public DateTime OTBEGINTIME1 { get; set; }
		/// <summary>
		/// 加班结束时间
		/// </summary>
		public DateTime OTENDTIME1 { get; set; }
		/// <summary>
		/// 上班开始时间
		/// </summary>
		public DateTime CARDBEGINTIME { get; set; }
		/// <summary>
		/// 下班结束时间
		/// </summary>
		public DateTime CARDENDTIME { get; set; }
		/// <summary>
		/// 原始刷卡上班时间
		/// </summary>
		public DateTime CARDBEGINTIME2 { get; set; }
		/// <summary>
		/// 原始刷卡结束时间
		/// </summary>
		public DateTime CARDENDTIME2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string ID { get; set; }
		/// <summary>
		/// 正常工作时数
		/// </summary>
		[JsonProperty("AMOUNT$467556")]
		public decimal? AMOUNT467556 { get; set; }
		/// <summary>
		/// 实际工作时数
		/// </summary>
		[JsonProperty("AMOUNT$467515")]
		public decimal? AMOUNT467515 { get; set; }
		/// <summary>
		/// 缺勤
		/// </summary>
		[JsonProperty("AMOUNT$365645D4")]
		public decimal? AMOUNT365645D4 { get; set; }
		/// <summary>
		/// 迟到
		/// </summary>
		[JsonProperty("AMOUNT$D51546C4A4")]
		public decimal? AMOUNTD51546C4A4 { get; set; }
		/// <summary>
		/// 早退
		/// </summary>
		[JsonProperty("AMOUNT$6594D5C425")]
		public decimal? AMOUNT6594D5C425 { get; set; }
		/// <summary>
		/// 年假
		/// </summary>
		[JsonProperty("AMOUNT$B6A638D4")]
		public decimal? AMOUNTB6A638D4 { get; set; }
		/// <summary>
		/// 补休
		/// </summary>
		[JsonProperty("AMOUNT$9445")]
		public decimal? AMOUNT9445 { get; set; }
		/// <summary>
		/// 事假
		/// </summary>
		[JsonProperty("AMOUNT$94783706")]
		public decimal? AMOUNT94783706 { get; set; }
		/// <summary>
		/// 病假
		/// </summary>
		[JsonProperty("AMOUNT$C6676656")]
		public decimal? AMOUNTC6676656 { get; set; }
		/// <summary>
		/// 丧假
		/// </summary>
		[JsonProperty("AMOUNT$9435")]
		public decimal? AMOUNT9435 { get; set; }
		/// <summary>
		/// 婚假
		/// </summary>
		[JsonProperty("AMOUNT$9468E694")]
		public decimal? AMOUNT9468E694 { get; set; }
		/// <summary>
		/// 产假
		/// </summary>
		[JsonProperty("AMOUNT$94C4")]
		public decimal? AMOUNT94C4 { get; set; }
		/// <summary>
		/// 陪产假
		/// </summary>
		[JsonProperty("AAMOUNT$C66764")]
		public decimal? AMOUNTC66764 { get; set; }
		/// <summary>
		/// 哺乳时间
		/// </summary>
		[JsonProperty("AMOUNT$A456C4")]
		public decimal? AMOUNTA456C4 { get; set; }
		/// <summary>
		/// 工伤假
		/// </summary>
		[JsonProperty("AMOUNT$9454")]
		public decimal? AMOUNT9454 { get; set; }
		/// <summary>
		/// 特殊假期
		/// </summary>
		[JsonProperty("AMOUNT$9416")]
		public decimal? AMOUNT9416 { get; set; }
		/// <summary>
		/// 1.5倍加班
		/// </summary>
		[JsonProperty("AMOUNT$04C33616")]
		public decimal? AMOUNT04C33616 { get; set; }
		/// <summary>
		/// 2倍加班
		/// </summary>
		[JsonProperty("AMOUNT$C3B33616")]
		public decimal? AMOUNTC3B33616 { get; set; }
		/// <summary>
		/// 3倍加班
		/// </summary>
		[JsonProperty("AMOUNT$C3343616")]
		public decimal? AMOUNTC3343616 { get; set; }
		/// <summary>
		/// 加班转补休
		/// </summary>
		[JsonProperty("AMOUNT$36169445")]
		public decimal? AMOUNT36169445 { get; set; }
		/// <summary>
		/// 中班次数
		/// </summary>
		[JsonProperty("AMOUNT$3584")]
		public decimal? AMOUNT3584 { get; set; }
		/// <summary>
		/// 夜班次数
		/// </summary>
		[JsonProperty("AMOUNT$3525")]
		public decimal? AMOUNT3525 { get; set; }
		/// <summary>
		/// 膳食津贴次数
		/// </summary>
		[JsonProperty("AMOUNT$454656")]

		public decimal? AMOUNT454656 { get; set; }
		/// <summary>
		/// 预计加班时间
		/// </summary>
		[JsonProperty("AMOUNT$460665")]

		public decimal? AMOUNT460665 { get; set; }
		/// <summary>
		/// 加班扣餐时数
		/// </summary>
		[JsonProperty("AMOUNT$4535B4")]

		public decimal? AMOUNT4535B4 { get; set; }
		/// <summary>
		/// 护理假
		/// </summary>
		[JsonProperty("AMOUNT$68783818284856")]

		public decimal? AMOUNT68783818284856 { get; set; }
		/// <summary>
		/// 生产排休
		/// </summary>
		[JsonProperty("AMOUNT$A43556")]

		public decimal? AMOUNTA43556 { get; set; }
		/// <summary>
		/// 产检假
		/// </summary>
		[JsonProperty("AMOUNT$44B445")]
		public decimal? AMOUNT44B445 { get; set; }
		/// <summary>
		/// 流产假
		/// </summary>
		[JsonProperty("AMOUNT$4435A4")]
		public decimal? AMOUNT4435A4 { get; set; }
		/// <summary>
		/// 计划生育假
		/// </summary>
		[JsonProperty("AMOUNT$55565444")]

		public decimal? AMOUNT55565444 { get; set; }
		/// <summary>
		/// 兵检假
		/// </summary>
		[JsonProperty("AMOUNT$44B4C4")]

		public decimal? AMOUNT44B4C4 { get; set; }
		/// <summary>
		/// 三八假
		/// </summary>
		[JsonProperty("AMOUNT$444556")]
		public decimal? AMOUNT444556 { get; set; }
		/// <summary>
		/// 疫情假
		/// </summary>
		[JsonProperty("AMOUNT$44D565")]
		public decimal? AMOUNT44D565 { get; set; }
		/// <summary>
		/// 育儿假
		/// </summary>
		[JsonProperty("AMOUNT$3708E606")]

		public decimal? AMOUNT3708E606 { get; set; }
		/// <summary>
		/// 加班是否扣餐（空值为是/1为否）
		/// </summary>
		[JsonProperty("AMOUNT$35B4")]
		public int? AMOUNT35B4 { get; set; }
	}

	public class AttendData
	{
		/// <summary>
		/// 
		/// </summary>
		public List<RowItem> Row { get; set; }
	}

	public class ResultModel
	{
		/// <summary>
		/// 
		/// </summary>
		public AttendData attendData { get; set; }
	}

	public class HRSResModel
	{
		/// <summary>
		/// 
		/// </summary>
		public int MsgId { get; set; }
		/// <summary>
		/// Success(读取数据成功！)
		/// </summary>
		public string Msg { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public ResultModel Result { get; set; }
	}
}
