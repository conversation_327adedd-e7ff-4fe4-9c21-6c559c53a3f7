using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{

	public class FWS_NewStdPallet
	{
		/// <summary>
		/// 
		/// </summary>
		public string WorkOrderNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string PalletNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string BatchNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int PackBoxCount { get; set; }
	}

	public class FWS_NewStdPallet_Res
	{
		public FWS_NewStdPallet_Res()
		{
		
		}

		public FWS_NewStdPallet_Res(bool succeeded, Error error, string sitUafExecutionDetail)
		{
			Succeeded = succeeded;
			Error = error;
			SitUafExecutionDetail = sitUafExecutionDetail;
		}

		/// <summary>
		/// 
		/// </summary>
		public bool Succeeded { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Error Error { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string SitUafExecutionDetail { get; set; }
	}

	public class Error
	{
		public Error()
		{

		}

		public Error(int errorCode, string errorMessage)
		{
			ErrorCode = errorCode;
			ErrorMessage = errorMessage;
		}

		/// <summary>
		/// 
		/// </summary>
		public int ErrorCode { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string ErrorMessage { get; set; }
	}

}