using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{
	public class FWS_WorkOrderInfo
	{
		public FWS_WorkOrderInfo()
		{

		}

		public FWS_WorkOrderInfo(ResultData resultData, bool isSuccess, string message)
		{
			ResultData = resultData;
			IsSuccess = isSuccess;
			Message = message;
			StatusCode = isSuccess == true ? 200 : -1;
			ReasonPhrase = isSuccess == true ? "OK" : "NOK";
			StatusCode = isSuccess == true ? 200 : -1;
			IsSuccessStatusCode = isSuccess;
			Version = new Version()
			{
				_Major = 1,
				_Minor = 1,
				_Build = -1,
				_Revision = -1,
			};
		}

		/// <summary>
		/// 
		/// </summary>
		public ResultData ResultData { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsSuccess { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string Message { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Version Version { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string Content { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int StatusCode { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string ReasonPhrase { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public List<string> Headers { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string RequestMessage { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsSuccessStatusCode { get; set; }
	}

	public class ResultData
	{
		/// <summary>
		/// 
		/// </summary>
		public string aufnr { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string stibez { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string batch { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal gamng { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string gstrp { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string shelf { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string normt { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string veran { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string vtext { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string lhmg1 { get; set; }
		/// <summary>
		/// 椰香喇沙濃湯(減鹽)
		/// </summary>
		public string maktx { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string txt04 { get; set; }
	}

	public class Version
	{
		/// <summary>
		/// 
		/// </summary>
		public int _Major { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int _Minor { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int _Build { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int _Revision { get; set; }
	}

	public class FWS_WorkOrderInfo_Res
	{
		/// <summary>
		/// 
		/// </summary>
		public string werks { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string veran { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string type { get; set; }
	}
}