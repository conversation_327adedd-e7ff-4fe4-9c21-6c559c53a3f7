using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.Models.Interface
{
	/// <summary>
	/// 物料消耗反馈_Proleit
	/// </summary>
	public class MMI_ConsumeFeedback
	{
		/// <summary>
		///执行设备ID
		/// </summary>
		public string EquipmentId { get; set; }
		/// <summary>
		///料号
		/// </summary>
		public string MaterialCode { get; set; }
		/// <summary>
		///物料消耗需求数量
		/// </summary>
		public decimal Qty { get; set; }
		/// <summary>
		///操作人员信息
		/// </summary>
		public string OperatorId { get; set; }
		/// <summary>
		///消耗完成时间
		/// </summary>
		public DateTime DateTime { get; set; }


	}
}
