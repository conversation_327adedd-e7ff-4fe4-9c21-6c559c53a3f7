using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{

	public class FWS_DeletePallet
	{
	
		/// <summary>
		/// 
		/// </summary>
		public string PalletNo { get; set; }
	}

	public class FWS_DeletePallet_Res
	{
		public FWS_DeletePallet_Res()
		{
		}

		public FWS_DeletePallet_Res(bool succeeded, Error error)
		{
			Succeeded = succeeded;
			Error = error;
		}

		/// <summary>
		/// 
		/// </summary>
		public bool Succeeded { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Error Error { get; set; }
	}

}