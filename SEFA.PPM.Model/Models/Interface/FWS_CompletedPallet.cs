using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{

	public class FWS_CompletedPallet
	{
		/// <summary>
		/// 
		/// </summary>
		public string WorkOrderNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string PalletNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string BatchNo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int BoxCount { get; set; }
	}

	public class FWS_CompletedPallet_Res
	{
		public FWS_CompletedPallet_Res()
		{
		}

		public FWS_CompletedPallet_Res(bool succeeded, Error error)
		{
			Succeeded = succeeded;
			Error = error;
		}

		/// <summary>
		/// 
		/// </summary>
		public bool Succeeded { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public Error Error { get; set; }
	}

}