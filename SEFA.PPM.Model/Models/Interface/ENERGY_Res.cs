using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;

namespace SEFA.PPM.Model.Models.Interface
{

	public class ENERGY_Res
	{
		public int code { get; set; }

		public object data { get; set; }

		public string msg { get; set; }
	}

	public class EnergyUser
	{
		/// <summary>
		/// 
		/// </summary>
		public string avatar { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public long createTime { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string email { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string enabled { get; set; }
		/// <summary>
		/// 包装三厂能源管理员
		/// </summary>
		public string realName { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public List<string> roles { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string username { get; set; }
	}

	public class TokenRes
	{
		/// <summary>
		/// token 有效时间单位:"秒"
		/// </summary>
		public string t { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string token { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public EnergyUser user { get; set; }
	}

	public class ENERGY_InstrumentList
	{
		/// <summary>
		/// 仪表ID
		/// </summary>
		public string device_id { get; set; }
		/// <summary>
		/// 数据采集点代码
		/// </summary>
		public string MeterCode { get; set; }
		/// <summary>
		/// 数据采集点名称
		/// </summary>
		public string MeterCode_Name { get; set; }
		/// <summary>
		/// 仪表类型代码
		/// </summary>
		public string MeterType { get; set; }
		/// <summary>
		/// 工作中心名称
		/// </summary>
		public string MeterTypeName { get; set; }
		/// <summary>
		/// 成本中心编码
		/// </summary>
		//public string Workcenter_Code { get; set; }
		/// <summary>
		/// 工作中心编码
		/// </summary>
		//public string WorkCenter_Name { get; set; }
		/// <summary>
		/// 成本中心编码
		/// </summary>
		//public string CostCenter_Code { get; set; }
		/// <summary>
		/// 成本中心名称
		/// </summary>
		//public string CostCenter_Name { get; set; }

	}

	public class ENERGY_Data
	{
		/// <summary>
		/// 开始时间
		/// </summary>
		public string StartDate { get; set; }
		/// <summary>
		/// 结束时间
		/// </summary>
		public string EndDate { get; set; }
		/// <summary>
		/// 数据列表
		/// </summary>
		public List<ENERGY_Data_Item> DataList { get; set; }
	}

	public class ENERGY_Data_Item
	{
		/// <summary>
		/// 
		/// </summary>
		public string device_id { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int data_item_id { get; set; }
		/// <summary>
		/// 能耗汇总
		/// </summary>
		public decimal Qty { get; set; }
		/// <summary>
		/// 数据采集点
		/// </summary>
		public string MeterCode { get; set; }
		/// <summary>
		/// 数据采集点名称
		/// </summary>
		public string MeterCode_Name { get; set; }
		/// <summary>
		/// 单位
		/// </summary>
		public string Unit { get; set; }

	}
}