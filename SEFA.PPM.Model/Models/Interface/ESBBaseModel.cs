using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.Models.Interface
{
	public class ESBBaseModel<T> : FlagUpdateModel
	{
		/// <summary>
		/// key
		/// </summary>
		public T data { get; set; }

		public string remainingNum { get; set; }

		public string tranNo { get; set; }

		public string messageId { get; set; }

	}

	public class FlagUpdateModel
	{
		public FlagUpdateModel()
		{

		}

		public FlagUpdateModel(string dataType, List<string> ids)
		{
			this.dataType = dataType;
			this.ids = ids;
		}

		public string dataType { get; set; }

		public List<string> ids { get; set; }
	}


}