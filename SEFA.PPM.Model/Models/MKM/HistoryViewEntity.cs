using System;
using System.Collections.Generic;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.MKM
{
    ///<summary>
    ///产出消耗统计主表
    ///</summary>

    [SugarTable("V_CONSUMPTION_HISTORY_VIEW")]
    public class HistoryViewEntity : EntityBase
    {
        public HistoryViewEntity()
        {
        }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUPPIERNAME")]
        public string Suppiername { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUPPIERCODE")]
        public string Suppiercode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PID")]
        public string Pid { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "IS_REVERSE")]
        public string IsReverse { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        // [SugarColumn(ColumnName = "REVERSE_STATE")]
        // public string ReverseState { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEND_STATES")]
        public string SendStates { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        // [SugarColumn(ColumnName = "CanReversedQty")]
        [SugarColumn(IsIgnore = true)]
        public decimal CanReversedQty { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROCESS_ORDER")]
        public string ProcessOrder { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "M_ID")]
		public string MId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "M_NAME")]
        public string MName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "M_CODE")]
        public string MCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "L_BATCH")]
        public string LBatch { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "BATCH_ID")]
		public string BatchId { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "BATCH_CODE")]
        public string BatchCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LOT_ID")]
        public string LotId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "L_STATUS")]
        public string LStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUB_SSCC")]
        public string SubSscc { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SUB_ID")]
        public string SubId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "U_STATUS")]
        public int? UStatus { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "I_QUANTITY")]
        public decimal? IQuantity { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        // [SugarColumn(ColumnName = "Out_Qty")]
        [SugarColumn(IsIgnore = true)]
        public decimal? OutQty { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_ID")]
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY_UNIT")]
        public string QuantityUnit { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CONTAINER_ID")]
        public string ContainerId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "C_CLASS")]
        public string CClass { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "C_NAME")]
        public string CName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE_NAME")]
        public string MachineName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE_CODE")]
        public string MachineCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SOURCE_NAME")]
        public string SourceName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SOURCE_CODE")]
        public string SourceCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SOURCEBIN_CODE")]
        public string SourcebinCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SOURCEBIN_ID")]
        public string SourcebinId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_NAME")]
        public string ShiftName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_SNAME")]
        public string ShiftSname { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "C_DATE")]
        public DateTime CDate { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MBLNR")]
        public string Mblnr { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAPORDERTYPE")]
        public string Sapordertype { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "TYPE")]
		public string Type { get; set; }
		/// <summary>
		/// Desc:
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MSG")]
		public string Msg { get; set; }
		/// <summary>
		/// Desc:物料类型
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MaterialType")]
		public string MaterialType { get; set; }
		/// <summary>
		/// Desc:发送时间
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "SEND_TIME")]
		public DateTime? SendTime { get; set; }
	}

	public class GroupData
	{
		/// <summary>
		/// 工单ID
		/// </summary>
		public string Pid { get; set; }

		/// <summary>
		/// 批次ID
		/// </summary>
		public string BatchId { get; set; }

		/// <summary>
		/// 工单号
		/// </summary>
		public string ProcessOrder { get; set; }

		/// <summary>
		/// 物料Id
		/// </summary>
		public string MId { get; set; }

		/// <summary>
		/// 物料编码
		/// </summary>
		public string MCode { get; set; }

		/// <summary>
		/// 物料名称
		/// </summary>
		public string MName { get; set; }

		/// <summary>
		/// 物料批次
		/// </summary>
		public string LBatch { get; set; }

		/// <summary>
		/// 单位
		/// </summary>
		public string Unit { get; set; }

		/// <summary>
		/// 批次需求量
		/// </summary>
		public decimal Q0 { get; set; }

		/// <summary>
		/// 实际消耗量
		/// </summary>
		public decimal Q1 { get; set; }

		/// <summary>
		/// 已发送数量
		/// </summary>
		public decimal Q2 { get; set; }

		/// <summary>
		/// 等待SAP返回结果数量
		/// </summary>
		public decimal Q3 { get; set; }

		/// <summary>
		/// 未发送数量
		/// </summary>
		public decimal Q4 { get; set; }

		/// <summary>
		/// 发送失败数量
		/// </summary>
		public decimal Q5 { get; set; }
		/// <summary>
		/// 差异
		/// </summary>
		public decimal Q6 { get; set; }
		/// <summary>
		/// 发送失败数量
		/// </summary>
		public List<GroupDataDetail> Details { get; set; }
	}

	public class GroupDataDetail
	{
		/// <summary>
		/// 工单ID
		/// </summary>
		public string Pid { get; set; }

		/// <summary>
		/// 批次ID
		/// </summary>
		public string BatchId { get; set; }

		/// <summary>
		/// 工单号
		/// </summary>
		public string ProcessOrder { get; set; }

		/// <summary>
		/// 物料Id
		/// </summary>
		public string MId { get; set; }

		/// <summary>
		/// 物料编码
		/// </summary>
		public string MCode { get; set; }

		/// <summary>
		/// 物料名称
		/// </summary>
		public string MName { get; set; }

		/// <summary>
		/// 物料批次
		/// </summary>
		public string LBatch { get; set; }

		/// <summary>
		/// 追溯码
		/// </summary>
		public string TraceCode { get; set; }

		/// <summary>
		/// 数量
		/// </summary>
		public decimal Quantity { get; set; }

		/// <summary>
		/// 单位
		/// </summary>
		public string Unit { get; set; }
	}
}