using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.MKM
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIPMENT")] 
    public class EquipmentEntity : EntityBase
    {
        public EquipmentEntity()
        {
        }
           /// <summary>
           /// Desc:父级ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PARENT_ID")]
        public string ParentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_CODE")]
        public string EquipmentCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LEVEL")]
        public string Level { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:删除标记
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
           /// <summary>
           /// Desc:是否启用   0-启用  1-禁用
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ENABLED")]
        public int Enabled { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }

        /// <summary>
        /// Desc:排序号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SORT_NUMBER")]
        public int SortNumber { get; set; } = 0;
    }
}