using System;
using SEFA.Base.Model.BASE;
using SqlSugar;

namespace SEFA.PPM.Model.Models.MKM
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_MATERIAL")]
    public class MaterialEntity : EntityBase
    {
        public MaterialEntity()
        {
        }
        /// <summary>
        /// Desc:父节点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_ID")]
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public string Type { get; set; }
        /// <summary>
        /// Desc:JED唯一ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_ID")]
        public string CompanyId { get; set; }
        /// <summary>
        /// Desc:厂别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLANT")]
        public string Plant { get; set; }
        /// <summary>
        /// Desc:物料料号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CODE")]
        public string Code { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:物料描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:版本号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VERSION")]
        public string Version { get; set; }
        /// <summary>
        /// Desc:系列编号（酱料系列）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SERIESCODE")]
        public string Seriescode { get; set; }
        /// <summary>
        /// Desc:机种编号（酱料类别）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CATEGORYCODE")]
        public string Categorycode { get; set; }
        /// <summary>
        /// Desc:FAMILY
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FAMILY")]
        public string Family { get; set; }
        /// <summary>
        /// Desc:客户料号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CUSTOMER_MATERIALCODE")]
        public string CustomerMaterialcode { get; set; }
        /// <summary>
        /// Desc:客户版次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CUSTOMER_EDITION")]
        public string CustomerEdition { get; set; }
        /// <summary>
        /// Desc:鸿海料号(预删除,暂保留)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HH_MATERIAL_CODE")]
        public string HhMaterialCode { get; set; }
        /// <summary>
        /// Desc:鸿海版次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HH_EDITION")]
        public string HhEdition { get; set; }
        /// <summary>
        /// Desc:厂商
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MANUFACTOR")]
        public string Manufactor { get; set; }
        /// <summary>
        /// Desc:规格
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SPEC")]
        public string Spec { get; set; }
        /// <summary>
        /// Desc:基础单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:销售码2
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CUSTOMER_CODE")]
        public string CustomerCode { get; set; }
        /// <summary>
        /// Desc:销售码3
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAIN_WH_ID")]
        public string MainWhId { get; set; }
        /// <summary>
        /// Desc:计划族
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CATEGORY_NAME")]
        public string CategoryName { get; set; }
        /// <summary>
        /// Desc:材质或采购执行标准
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CATEGORY_DESCRIPTION")]
        public string CategoryDescription { get; set; }
        /// <summary>
        /// Desc:物料分类
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VENDOR_PN")]
        public string VendorPn { get; set; }
        /// <summary>
        /// Desc:器械工单类别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VENDOR_CODE")]
        public string VendorCode { get; set; }
        /// <summary>
        /// Desc:总帐 类别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCT_TYPE")]
        public string ProductType { get; set; }
        /// <summary>
        /// Desc:类别码6（生产仓储地点）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SOURCE_TYPE")]
        public string SourceType { get; set; }
        /// <summary>
        /// Desc:类别码9
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CT_VERSION")]
        public string CtVersion { get; set; }
        /// <summary>
        /// Desc:储存 类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_TYPE")]
        public string MaterialType { get; set; }
        /// <summary>
        /// Desc:安全库存
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_OF_MEASURE")]
        public string UnitOfMeasure { get; set; }
        /// <summary>
        /// Desc:货架天数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_GROUP")]
        public string ProductionGroup { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARKS")]
        public string Remarks { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_SPECIAL")]
        public string IsSpecial { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SPECIAL_TYPE")]
        public string SpecialType { get; set; }
        /// <summary>
        /// Desc:是否有效
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Invalid { get; set; }
        /// <summary>
        /// Desc:删除标志
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:访问时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ACCESSTIME")]
        public DateTime? Accesstime { get; set; }
        /// <summary>
        /// Desc:2nd level Material Group Hierarchy (ZF)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "KSCHL_1")]
        public string Kschl1 { get; set; }
        /// <summary>
        /// Desc:1st level Material Group Hierarchy (ZF)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "KSCHL_2_M")]
        public string Kschl2M { get; set; }
        /// <summary>
        /// Desc:2nd level Material Group Hierarchy (EN)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "KSCHL_1_E")]
        public string Kschl1E { get; set; }
        /// <summary>
        /// Desc:1st level Material Group Hierarchy (EN)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "KSCHL_2_E")]
        public string Kschl2E { get; set; }
        /// <summary>
        /// Desc:Material Group 中文说明
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATKL_EN")]
        public string MatklEn { get; set; }
        /// <summary>
        /// Desc:Material Group 中文说明
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATKL_ZH")]
        public string MatklZh { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "iprkz")]
        public string Iprkz { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "mhdhb")]
        public int? Mhdhb { get; set; }
        /// <summary>
        /// Desc:目标仓位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LGPRO")]
        public string Lgpro { get; set; }
    }
}