using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("MKM_B_MATERIAL_TRANSFER")] 
    public class MaterialTransferEntity : EntityBase
    {
        public MaterialTransferEntity()
        {
        }
           /// <summary>
           /// Desc:原存储位置
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_STORAGE_LOCATION")]
        public string OldStorageLocation { get; set; }
           /// <summary>
           /// Desc:原物料批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOT_ID")]
        public string OldLotId { get; set; }
           /// <summary>
           /// Desc:原物料子批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_SUBLOT_ID")]
        public string OldSublotId { get; set; }
           /// <summary>
           /// Desc:原过期日期
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_EXPIRATION_DATE")]
        public DateTime? OldExpirationDate { get; set; }
           /// <summary>
           /// Desc:新存储位置
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_STORAGE_LOCATION")]
        public string NewStorageLocation { get; set; }
           /// <summary>
           /// Desc:新物料批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOT_ID")]
        public string NewLotId { get; set; }
           /// <summary>
           /// Desc:新物料子批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_SUBLOT_ID")]
        public string NewSublotId { get; set; }
           /// <summary>
           /// Desc:新过期日期
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_EXPIRATION_DATE")]
        public DateTime? NewExpirationDate { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal? Quantity { get; set; }
           /// <summary>
           /// Desc:数量单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY_UOM_ID")]
        public string QuantityUomId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_BATCH_ID")]
        public string NewBatchId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_BATCH_ID")]
        public string OldBatchId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_PRODUCTION_EXECUTION_ID")]
        public string NewProductionExecutionId { get; set; }
           /// <summary>
           /// Desc:生产执行ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_EXECUTION_ID")]
        public string ProductionExecutionId { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMMENT")]
        public string Comment { get; set; }
           /// <summary>
           /// Desc:原设备存储BinID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_EQUIPMENT_REQUIREMENT_ID")]
        public string OldEquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:新设备存储BinID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_EQUIPMENT_REQUIREMENT_ID")]
        public string NewEquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:转移组ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TRANSFER_GROUP_ID")]
        public string TransferGroupId { get; set; }
           /// <summary>
           /// Desc:原设备ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_EQUIPMENT_ID")]
        public string OldEquipmentId { get; set; }
           /// <summary>
           /// Desc:新设备ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_EQUIPMENT_ID")]
        public string NewEquipmentId { get; set; }
           /// <summary>
           /// Desc:原容器ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_CONTAINER_ID")]
        public string OldContainerId { get; set; }
           /// <summary>
           /// Desc:新容器ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_CONTAINER_ID")]
        public string NewContainerId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:原物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_MATERIAL_ID")]
        public string OldMaterialId { get; set; }
           /// <summary>
           /// Desc:新物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_MATERIAL_ID")]
        public string NewMaterialId { get; set; }
           /// <summary>
           /// Desc:原批次状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOT_EXTERNAL_STATUS")]
        public string OldLotExternalStatus { get; set; }
           /// <summary>
           /// Desc:原子批次状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_SUBLOT_EXTERNAL_STATUS")]
        public string OldSublotExternalStatus { get; set; }
           /// <summary>
           /// Desc:新批次状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOT_EXTERNAL_STATUS")]
        public string NewLotExternalStatus { get; set; }
           /// <summary>
           /// Desc:新子批次状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_SUBLOT_EXTERNAL_STATUS")]
        public string NewSublotExternalStatus { get; set; }
           /// <summary>
           /// Desc:物理数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PHYSICAL_QUANTITY")]
        public string PhysicalQuantity { get; set; }
           /// <summary>
           /// Desc:皮数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TARE_QUANTITY")]
        public decimal TareQuantity { get; set; }
           /// <summary>
           /// Desc:WMS打印单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WMS_PRINTNO")]
        public string WmsPrintno { get; set; }
           /// <summary>
           /// Desc:SAP退仓单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAP_PRINTNO")]
        public string SapPrintno { get; set; }
           /// <summary>
           /// Desc:MES单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MES_PRONO")]
        public string MesProno { get; set; }
           /// <summary>
           /// Desc:模式（手工/自动）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MODE")]
        public string Mode { get; set; }
           /// <summary>
           /// Desc:备注用来显示传给WMS的表头
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:工作中心
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WORKCENTER")]
        public string Workcenter { get; set; }
           /// <summary>
           /// Desc:转移备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TRANREMARKS")]
        public string Tranremarks { get; set; }
    }
}