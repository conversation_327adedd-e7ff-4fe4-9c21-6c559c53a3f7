using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_MKM_MATERIAL_TRANSFER_LIST")] 
    public class MaterialTransferListEntity : EntityBase
    {
        public MaterialTransferListEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOCATION_CODE")]
        public string OldLocationCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOCATION_NAME")]
        public string OldLocationName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOCATION_CODE")]
        public string NewLocationCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOCATION_NAME")]
        public string NewLocationName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_MATERIAL_CODE")]
        public string OldMaterialCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_MATERIAL_NAME")]
        public string OldMaterialName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_MATERIAL_CODE")]
        public string NewMaterialCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_MATERIAL_NAME")]
        public string NewMaterialName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOT_ID")]
        public string OldLotId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOT_ID")]
        public string NewLotId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_SUB_LOT_ID")]
        public string OldSubLotId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_SUB_LOT_ID")]
        public string NewSubLotId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_CONTAINER_NAME")]
        public string OldContainerName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_CONTAINER_NAME")]
        public string NewContainerName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
        public decimal? Quantity { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_LOT_EXTERNAL_STATUS")]
        public string OldLotExternalStatus { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_SUBLOT_EXTERNAL_STATUS")]
        public string OldSublotExternalStatus { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_LOT_EXTERNAL_STATUS")]
        public string NewLotExternalStatus { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_SUBLOT_EXTERNAL_STATUS")]
        public string NewSublotExternalStatus { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_EQUIPMENT_ID")]
        public string OldEquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_EQUIPMENT_ID")]
        public string NewEquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_EQUIPMENT_REQUIREMENT_ID")]
        public string OldEquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NEW_EQUIPMENT_REQUIREMENT_ID")]
        public string NewEquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OLD_MATERIAL_ID")]
        public string OldMaterialId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT")]
        public string Unit { get; set; }

    }
}