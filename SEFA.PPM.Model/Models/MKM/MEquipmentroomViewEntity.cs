using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("V_DFM_M_EQUIPMENTROOM_VIEW")] 
    public class MEquipmentroomViewEntity : EntityBase
    {
        public MEquipmentroomViewEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_CODE")]
        public string EquipmentCode { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
    }
}