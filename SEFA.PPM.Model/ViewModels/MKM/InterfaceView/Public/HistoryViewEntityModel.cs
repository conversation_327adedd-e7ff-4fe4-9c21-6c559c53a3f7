using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using Magicodes.ExporterAndImporter.Core;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///物料库存管理
    ///</summary>


    public class HistoryViewEntityModel : EntityBase
    {
        public HistoryViewEntityModel()
        {
        }

        public List<HistoryViewEntity> historyViewEntities;
        public decimal Total { get; set; }


    }
}