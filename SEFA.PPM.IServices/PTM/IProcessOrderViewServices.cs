using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PTM.IServices
{	
	/// <summary>
	/// IProcessOrderViewServices
	/// </summary>	
    public interface IProcessOrderViewServices :IBaseServices<ProcessOrderViewEntity>
	{
		Task<PageModel<ProcessOrderViewEntity>> GetPageList(ProcessOrderViewRequestModel reqModel);

        Task<List<ProcessOrderViewEntity>> GetList(ProcessOrderViewRequestModel reqModel);

		Task<bool> SaveForm(ProcessOrderViewEntity entity);
		Task<List<ProcessOrderViewEntity>> GetList2(ProcessOrderViewRequestModel reqModel);
		Task<PageModel<ProcessOrderViewEntity>> GetPageList2(ProcessOrderViewRequestModel reqModel);
		Task<MessageModel<List<EquipmentEntity>>> GetSegmentUnits(string segment);
		Task<MessageModel<List<Select>>> GetSegments(ProcessOrderViewRequestModel reqModel);
		Task<string> GetLine(string equipmentId);
	}
}