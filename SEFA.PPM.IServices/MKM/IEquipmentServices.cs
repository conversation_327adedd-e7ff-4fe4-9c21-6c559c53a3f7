using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IEquipmentServices
	/// </summary>	
    public interface IEquipmentServices :IBaseServices<EquipmentEntity>
	{
		Task<PageModel<EquipmentEntity>> GetPageList(EquipmentRequestModel reqModel);

        Task<List<EquipmentEntity>> GetList(EquipmentRequestModel reqModel);
        Task<List<EquipmentEntity>> GetMachine();

		Task<bool> SaveForm(EquipmentEntity entity);
    }
}