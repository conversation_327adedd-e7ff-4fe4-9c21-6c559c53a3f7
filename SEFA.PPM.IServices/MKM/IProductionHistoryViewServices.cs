using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Model.Models.MKM;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;

namespace SEFA.MKM.IServices
{
    /// <summary>
    /// IProductionHistoryViewServices
    /// </summary>	
    public interface IProductionHistoryViewServices : IBaseServices<ProductionHistoryViewEntity>
    {
        Task<PageModel<ProductionHistoryViewEntity>> GetPageList(ProductionHistoryViewRequestModel reqModel);

        Task<List<ProductionHistoryViewEntity>> GetList(ProductionHistoryViewRequestModel reqModel);

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        Task<List<Select>> GetProductionMachine(BatchPalletModel reqModel);
        Task<bool> SaveForm(ProductionHistoryViewEntity entity);

         Task<List<ProductionConsumExports>> GetPHisExport(ProductionHistoryViewRequestModel reqModel);
        Task<PageModel<ProductionHistoryViewEntityModel>> GetPageLists(ProductionHistoryViewRequestModel reqModel);
        Task<MessageModel<string>> Reverse(ReverseModel reqModel);

        Task<MessageModel<string>> RepeatPoProducedActual(string[] id);

        Task<MessageModel<string>> GetReverseQty(ReverseModel reqModel);

        Task<MessageModel<string>> ScanWMSPrint(string sscc);

          Task<MessageModel<string>> ScanWMSPrintGBZ(string sscc);

        /// <summary>
        /// 根据节点插叙是否管理库存（返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存）
        /// </summary>
        /// <param name="equipmentID"></param>
        /// <returns></returns>
        Task<string> GetEquipmentStorege(string equipmentID);

        Task<string> GetEqupmentID(string equpmentID);
		Task<List<GroupData>> GetProduceSumList(ProductionHistoryViewRequestModel reqModel);
	}
}