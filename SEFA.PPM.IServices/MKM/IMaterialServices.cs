using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.IServices
{	
	/// <summary>
	/// IMaterialServices
	/// </summary>	
    public interface IMaterialServices :IBaseServices<MaterialEntity>
	{
		Task<PageModel<MaterialEntity>> GetPageList(MaterialRequestModel reqModel);

        Task<List<MaterialEntity>> GetList(MaterialRequestModel reqModel);

		Task<bool> SaveForm(MaterialEntity entity);
    }
}