using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System;
using SEFA.MKM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models.MKM;
using SEFA.PPM.Model.ViewModels.PPM;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IFormulascheduleServices
	/// </summary>	
    public interface IFormulascheduleServices :IBaseServices<FormulascheduleEntity>
	{
		Task<PageModel<FormulascheduleEntity>> GetPageList(FormulascheduleRequestModel reqModel);

        Task<List<FormulascheduleEntity>> GetList(FormulascheduleRequestModel reqModel);

		Task<bool> SaveForm(FormulascheduleEntity entity);
		//Task<MessageModel<string>> GetSapPackOrder();
        Task<MessageModel<string>> MergeFormulaOrder();
        Task<MessageModel<string>> AutoMergeFormulaOrder ();
        Task<MessageModel<string>> SplitFormulaOrder(string orderId, decimal splitCount, string newProdVersion);
        Task<MessageModel<string>> ChangeOrderRecipeVersion(string orderId, string newRecipeId);
        Task<MessageModel<string>> ComputeOrderRequire(string lineId);
        Task<MessageModel<string>> CreatePlanOrder(string lineId ,DateTime? startTime, DateTime? endTime);
        Task<MessageModel<string>> CreatePlanOrderByID(string[] orderIds);
        Task<MessageModel<string>> SendOrderToSap (string OperationCode = "");
        Task<MessageModel<string>> SendCkOrderToSapByOrderId(string[] orderIds);
        Task<MessageModel<string>> SendCkOrderToSap();
        Task<MessageModel<string>> CreatePlanTable(string lineId, DateTime start, DateTime end);
        Task<MessageModel<string>> FirstPlan(string lineId, DateTime start, DateTime end);
        Task<MessageModel<string>> LossChoose(string orderId, string lossId);
        Task<MessageModel<string>> CreateScheduleByIds(string[] ids);
        Task<MessageModel<string>> CreateScheduleByDate (string lineId, DateTime start, DateTime end);

        Task<List<EquipmentEntity>> GetLineByAreaCode(string areaCode, string level);

        Task<MessageModel<string>> CancleProductionOrder(string[] orderId);

        Task<List<SapSegmentMaterialStepModel>> GetOrderBomByOrderId(string orderId);

        Task<List<SapSegmentMaterialStepModel>> GetProOrderBomByOrderId(string orderId);

        Task<List<SapSegmentMaterialStepModel>> GetOrderBom(string mtrNo, string version);
        Task<MessageModel<string>> FormulascheduleChange(FormulascheduleEntity reqModel);
        Task<MessageModel<string>> FormulascheduleCompute(FormulascheduleEntity reqModel);
        Task<PageModel<ProductionOrderViewModel>> GetProductionOrderList(ProductionOrderViewModel reqModel);
        Task<MessageModel<string>> ProductionOrderChange(ProductionOrderViewModel reqModel);
        Task<MessageModel<string>> PublishOrder(string[] orderId);

        Task<List<ProductionOrderViewModel>> GetOrderToSort( string lindId, DateTime? start, DateTime? end);

        Task<MessageModel<string>> SaveOrderSortList(List<ProductionOrderViewModel> req);

        Task<List<PackOrderViewModel>> GetPackOrderToSort(PackOrderViewModel reqModel);

        Task<MessageModel<string>> SavePackOrderSortList(List<ProductionOrderViewModel> req);

        Task<List<BeatInfo>> GetBeatInfo();
        Task<MessageModel<string>> CreateProductionOrder(ProductionOrderViewModel request);
        Task<PageModel<PackOrderViewModel>> GetPackOrderList(PackOrderViewModel reqModel);

        Task<MessageModel<string>> PackOrderSort(string lindId, DateTime start, DateTime end);

        Task<ProductionOrderPropertyEntity> GetOrderProperty(string orderId);
        Task<MessageModel<string>> AddPackOrderProperty(OrderChangeProperyModel req);

        Task<MessageModel<string>> EditPackOrderRemark (OrderChangeProperyModel req);
        Task<List<PackOrderViewModel>> GetPackOrderByFillNo(string orderNo);
        Task<List<FormulascheduleDetailEntity>> GetFormulaScheduleDetail(string id);
        Task<MessageModel<string>> AddFormulascheduleDetail(List<FormulascheduleDetail> list);
        Task<FormulascheduleEntity> GetFormulaInfo(string id);
        Task<PackOrderViewModel> GetPackOrderInfo(string id);
        Task<ProductionOrderViewModel> GetProductionOrderInfo(string id);
        Task<List<SapprodversionEntity>> GetSapprodversionByLine(string lineID, string matNo);
        Task<PageModel<PackOrderViewModel>> GetPackOrderByFormula(FormulascheduleRequestModel reqModel);
        Task<MessageModel<string>> IgnorePackOrder(PackOrderIgnore reqmodel);
        Task<List<SortFormulaTreeModel>> GetSortFormulaTree(string lineId, DateTime prodate);
        Task<List<SortFormulaTreeModel>> GetSortCookTree(string lineId, DateTime prodate);
        Task<List<SortFormulaTreeModel>> GetHandPackTree(string lineId, DateTime prodate);
        Task<MessageModel<string>> SaveFormulaTree(List<SortFormulaTreeModel> list);
        Task<MessageModel<string>> UpdateFormulaProduceDate(FormulascheduleRequestModel reqModel);
        Task<MessageModel<string>> CancelFormula(string id);
        Task<MessageModel<string>> ChangePackOrderReworkInfo(PackOrderViewModel reqModel);
        Task<PageModel<ProductionOrderViewModel>> GetBatchOrderByParentId(ProductionOrderViewModel reqModel);
        Task<List<ThroarInventory>> GetOrderUsableThroarInventory(string orderId);
        Task<List<BeatInfo>> GetBeatInfosBydDay(string lineID, DateTime date);
        Task<MessageModel<string>> DeletePlanOrder(string orderId);
        Task<MessageModel<string>> CancelBatchOrder(string[] orderId);
        Task<MessageModel<string>> ChangeProductionBatchOrder(ProductionOrderViewModel reqModel);
        Task<MessageModel<string>> DeleteBatchOrder(string[] orderId);
        Task<MessageModel<string>> CreateBatchOrder(string parentId, decimal qty);
    }
}