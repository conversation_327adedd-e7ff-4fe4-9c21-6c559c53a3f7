-- 创建叫料申请单表
CREATE TABLE MKM_B_CALL_MATERIAL_SHEET (
    -- 主键标识
    ID VARCHAR(36) NOT NULL PRIMARY KEY,
    -- 叫料申请单
    REQUESTSHEETNO VARCHAR(50),
    -- 发送时间
    REQUESTTIME DATETIME,
    -- 产线编码（与库位码二选一必须传值）
    LINECODE VARCHAR(50),
    -- 库位码（与产线编码二选一必须传值）
    POSITIONCODE VARCHAR(50),
    -- 操作类型，1：叫料，-1：取消叫料
    OPERATIONTYPE INT,
    -- 叫料类型，正常：MATERIAL，IBC空桶:EMPTY_IBC
    REQUESTTYPE VARCHAR(50),
    -- 创建时间
    CREATEDATE DATETIME NOT NULL,
    -- 创建人ID
    CREATEUSERID VARCHAR(50) NOT NULL,
    -- 修改时间
    MODIFYDATE DATETIME NULL,
    -- 修改人ID
    MODIFYUSERID VARCHAR(50) NULL,
    -- 时间戳
    UPDATETIMESTAMP TIMESTAMP,
    -- 删除标识
    DELETED INT NOT NULL DEFAULT 0
);

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'叫料申请单表', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'主键标识', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'ID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'叫料申请单', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'REQUESTSHEETNO';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'发送时间', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'REQUESTTIME';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'产线编码（与库位码二选一必须传值）', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'LINECODE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'库位码（与产线编码二选一必须传值）', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'POSITIONCODE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'操作类型，1：叫料，-1：取消叫料', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'OPERATIONTYPE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'叫料类型，正常：MATERIAL，IBC空桶:EMPTY_IBC', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'REQUESTTYPE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'创建时间', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'CREATEDATE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'创建人ID', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'CREATEUSERID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'修改时间', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'MODIFYDATE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'修改人ID', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'MODIFYUSERID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'时间戳', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'UPDATETIMESTAMP';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'删除标识', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_SHEET',
    @level2type=N'COLUMN', @level2name=N'DELETED';

-- 创建请求明细表
CREATE TABLE MKM_B_CALL_MATERIAL_DETAIL (
    -- 主键标识
    ID VARCHAR(36) NOT NULL PRIMARY KEY,
    -- 明细号
    DETAILNUMBER INT,
    -- 工厂
    PLANT VARCHAR(50),
    -- 物料编码
    MATERIALCODE VARCHAR(50),
    -- 物料名称
    MATERIALNAME VARCHAR(100),
    -- 物料版本号
    MATERIALVERSIONCODE VARCHAR(50),
    -- 请求数量
    REQUESTQTY DECIMAL(18,6),
    -- 单位
    UNIT VARCHAR(20),
    -- 批次号
    BATCHNO VARCHAR(50),
    -- 托盘号
    PALLENO VARCHAR(50),
    -- 叫料单ID（关联到叫料申请单表的ID字段）
    CALLSHEETID VARCHAR(36),
    -- 创建时间
    CREATEDATE DATETIME NOT NULL,
    -- 创建人ID
    CREATEUSERID VARCHAR(50) NOT NULL,
    -- 修改时间
    MODIFYDATE DATETIME NULL,
    -- 修改人ID
    MODIFYUSERID VARCHAR(50) NULL,
    -- 时间戳
    UPDATETIMESTAMP TIMESTAMP,
    -- 删除标识
    DELETED INT NOT NULL DEFAULT 0
);

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'请求明细表', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'主键标识', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'ID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'明细号', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'DETAILNUMBER';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'工厂', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'PLANT';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'物料编码', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'MATERIALCODE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'物料名称', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'MATERIALNAME';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'物料版本号', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'MATERIALVERSIONCODE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'请求数量', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'REQUESTQTY';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'单位', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'UNIT';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'批次号', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'BATCHNO';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'托盘号', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'PALLENO';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'叫料单ID（关联到叫料申请单表的ID字段）', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'CALLSHEETID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'创建时间', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'CREATEDATE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'创建人ID', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'CREATEUSERID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'修改时间', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'MODIFYDATE';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'修改人ID', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'MODIFYUSERID';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'时间戳', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'UPDATETIMESTAMP';

EXEC sys.sp_addextendedproperty 
    @name=N'MS_Description', 
    @value=N'删除标识', 
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'TABLE',  @level1name=N'MKM_B_CALL_MATERIAL_DETAIL',
    @level2type=N'COLUMN', @level2name=N'DELETED';