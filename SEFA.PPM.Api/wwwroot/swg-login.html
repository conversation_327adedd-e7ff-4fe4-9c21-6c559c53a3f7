<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>默认首页</title>
    <script src="js/jquery-3.7.1.min.js"></script>

</head>
<body>
    <div id="requestMsg"></div>
    <div style="text-align: center;">
        <input id="name" placeholder="name" type="text" />
        <br />
        <input id="pwd" placeholder="pwd" type="password" />
        <br />
        <input type="submit" onclick="submit()" value="submit" />
    </div>
    <script>
        function submit() {
            var txtpwd = encodeData($("#pwd").val());
            console.log(txtpwd);
            let postdata = {
                "name": $("#name").val(),
                "pwd": txtpwd,
            };
            if (!(postdata.name && postdata.pwd)) {
                alert('参数不正确');
                return
            }
            $.ajax({
                url: "/api/Login/swgLogin",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(postdata),
                dataType: 'json',
                success: function (data) {
                    if (data?.result) {
                        window.location.href = "/index.html";
                    } else {
                        alert('参数不正确');
                    }
                }
            });
        }

        function encodeData(data) {
            // 使用Base64编码数据
            const encodedData = window.btoa(data);
            return encodedData;
        }
    </script>
</body>
</html>