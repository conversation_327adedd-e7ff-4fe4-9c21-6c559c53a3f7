(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-770e833a"],{c8c13:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[a("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.selectWeChat,callback:function(t){e.selectWeChat=t},expression:"selectWeChat"}},e._l(e.wechats,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),e.selectWeChat?a("el-form-item",[a("el-select",{attrs:{placeholder:"请选择要操作的客户"},model:{value:e.selectCompany,callback:function(t){e.selectCompany=t},expression:"selectCompany"}},e._l(e.companys,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1):e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:""==e.selectWeChat||""==e.selectCompany},on:{click:e.searchWeChatAccount}},[e._v("刷新")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":""},on:{"selection-change":e.selsChange}},[a("el-table-column",{attrs:{type:"index",width:"80"}}),a("el-table-column",{attrs:{prop:"SubFromPublicAccount",label:"来自公众号",width:"100"}}),a("el-table-column",{attrs:{prop:"CompanyID",label:"来自公司",width:"100"}}),a("el-table-column",{attrs:{prop:"SubUserOpenID",label:"OpenID",width:"300"}}),a("el-table-column",{attrs:{prop:"SubJobID",label:"员工ID",width:"150"}}),a("el-table-column",{attrs:{prop:"SubUserRegTime",label:"注册时间",width:"200"}}),a("el-table-column",{attrs:{prop:"SubUserRefTime",label:"更新时间",width:"200"}})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.page.pageIndex,"hide-on-single-page":!0,"page-sizes":[10,100,500,1e3],"page-size":e.page.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.page.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)},l=[],s=(a("ac6a"),a("4ec3")),o={name:"WeChatCompany",data:function(){return{wechats:[],companys:[],selectWeChat:"",selectCompany:"",listLoading:!1,tableData:[],sels:[],page:{pageSize:10,pageIndex:1,pageTotal:0}}},created:function(){this.getWeChats(),this.getWeCompanys()},methods:{selsChange:function(e){this.sels=e},handleCurrentChange:function(e){this.page.pageIndex=e,this.searchWeChatAccount()},handleSizeChange:function(e){this.page.pageIndex=1,this.page.pageSize=e,this.searchWeChatAccount()},searchWeChatAccount:function(){var e=this;if(""!=this.selectWeChat&&""!=this.selectCompany){this.listLoading=!0;var t={intPageIndex:this.page.pageIndex,intPageSize:this.page.pageSize,strOrderByFileds:"SubFromPublicAccount asc",conditions:"SubFromPublicAccount = "+this.selectWeChat+" & IsUnBind = false"};this.selectCompany&&(t.conditions+="& CompanyID = "+this.selectCompany),Object(s["T"])(t).then(function(t){e.listLoading=!1,console.log(t),t.data.success&&(e.tableData=t.data.response.data,e.page.pageTotal=t.data.response.dataCount,e.$message({type:"success",message:"获取成功!"}))})}},getWeChats:function(){var e=this;Object(s["S"])().then(function(t){e.wechats=[],t.data.response.data.forEach(function(t){e.wechats.push({value:t.publicAccount,label:t.publicNick})})})},getWeCompanys:function(){var e=this;Object(s["U"])().then(function(t){e.companys=[],console.log(t),t.data.response.data.forEach(function(t){e.companys.push({value:t.CompanyID,label:t.CompanyName})})})}},mounted:function(){},watch:{selectWeChat:function(e,t){this.page.pageIndex=1,this.searchWeChatAccount()},selectCompany:function(e,t){this.page.pageIndex=1,this.searchWeChatAccount()}}},c=o,i=a("2877"),p=Object(i["a"])(c,n,l,!1,null,null,null);p.options.__file="BindUser.vue";t["default"]=p.exports}}]);
//# sourceMappingURL=chunk-770e833a.0890b50d.js.map