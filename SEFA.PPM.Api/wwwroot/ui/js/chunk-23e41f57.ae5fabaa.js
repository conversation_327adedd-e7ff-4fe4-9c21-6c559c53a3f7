(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-23e41f57"],{"386d":function(e,t,a){"use strict";var n=a("cb7c"),i=a("83a1"),o=a("5f1b");a("214f")("search",1,function(e,t,a,s){return[function(a){var n=e(this),i=void 0==a?void 0:a[t];return void 0!==i?i.call(a,n):new RegExp(a)[t](String(n))},function(e){var t=s(a,e,this);if(t.done)return t.value;var l=n(e),r=String(this),c=l.lastIndex;i(c,0)||(l.lastIndex=0);var d=o(l,r);return i(l.lastIndex,c)||(l.lastIndex=c),null===d?-1:d.index}]})},5176:function(e,t,a){e.exports=a("51b6")},6908:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return null!=e.buttonList&&e.buttonList.length>0?a("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[a("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}})],1),e._l(e.buttonList,function(t){return a("el-form-item",{key:t.id},[t.IsHide?e._e():a("el-button",{attrs:{type:!t.Func||-1==t.Func.toLowerCase().indexOf("handledel")&&-1==t.Func.toLowerCase().indexOf("stop")?"primary":"danger"},on:{click:function(a){e.callFunc(t)}}},[e._v(e._s(t.name))])],1)})],2)],1):e._e()},i=[],o=(a("cadf"),a("551c"),a("097d"),{name:"Toolbar",data:function(){return{searchVal:""}},props:["buttonList"],methods:{callFunc:function(e){e.search=this.searchVal,this.$emit("callFunction",e)}}}),s=o,l=a("2877"),r=Object(l["a"])(s,n,i,!1,null,null,null);r.options.__file="Toolbar.vue";t["a"]=r.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"8a55":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("toolbar",{attrs:{buttonList:e.buttonList},on:{callFunction:e.callFunction}}),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":""},on:{"selection-change":e.selsChange,"current-change":e.selectCurrentRow}},[a("el-table-column",{attrs:{type:"selection",width:"60"}}),a("el-table-column",{attrs:{type:"index",width:"80"}}),a("el-table-column",{attrs:{prop:"CompanyID",label:"客户代码",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"CompanyName",label:"客户名称",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"CompanyIP",label:"客户IP",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"CompanyAPI",label:"客户API",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"CompanyRemark",label:"备注",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"Enabled",label:"状态",width:"",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:t.row.Enabled?"success":"danger","disable-transitions":""}},[e._v(e._s(t.row.Enabled?"正常":"禁用")+"\n                  ")])]}}])})],1),a("el-col",{staticClass:"toolbar",attrs:{span:24}},[a("el-button",{attrs:{type:"danger",disabled:0===this.sels.length},on:{click:e.batchRemove}},[e._v("批量删除")]),a("el-pagination",{staticStyle:{float:"right"},attrs:{layout:"prev, pager, next","page-size":e.page.pageSize,total:e.page.pageTotal},on:{"current-change":e.handleCurrentChange}})],1),a("el-dialog",{attrs:{title:e.editType,visible:e.editFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.editFormVisible=t}},model:{value:e.editFormVisible,callback:function(t){e.editFormVisible=t},expression:"editFormVisible"}},[a("el-form",{ref:"editForm",attrs:{model:e.editForm,"label-width":"200px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"客户代码",prop:"CompanyID"}},[a("el-input",{attrs:{"auto-complete":"off",disabled:"edit"==e.editType},model:{value:e.editForm.CompanyID,callback:function(t){e.$set(e.editForm,"CompanyID",t)},expression:"editForm.CompanyID"}})],1),a("el-form-item",{attrs:{label:"客户名称",prop:"CompanyName"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.CompanyName,callback:function(t){e.$set(e.editForm,"CompanyName",t)},expression:"editForm.CompanyName"}})],1),a("el-form-item",{attrs:{label:"客户IP",prop:"CompanyIP"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.CompanyIP,callback:function(t){e.$set(e.editForm,"CompanyIP",t)},expression:"editForm.CompanyIP"}})],1),a("el-form-item",{attrs:{label:"客户API",prop:"CompanyAPI"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.CompanyAPI,callback:function(t){e.$set(e.editForm,"CompanyAPI",t)},expression:"editForm.CompanyAPI"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"CompanyRemark"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.CompanyRemark,callback:function(t){e.$set(e.editForm,"CompanyRemark",t)},expression:"editForm.CompanyRemark"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"Enabled"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.editForm.Enabled,callback:function(t){e.$set(e.editForm,"Enabled",t)},expression:"editForm.Enabled"}},e._l(e.statusList,function(e){return a("el-option",{key:e.value,attrs:{label:e.LinkUrl,value:e.value}})}),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{nativeOn:{click:function(t){e.editFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.editLoading},nativeOn:{click:function(t){return e.editSubmit(t)}}},[e._v("提交")])],1)],1)],1)},i=[],o=(a("386d"),a("5176")),s=a.n(o),l=(a("7f7f"),a("4ec3")),r=a("cdc6"),c=a("6908"),d={components:{Toolbar:c["a"]},data:function(){return{filters:{name:""},listLoading:!1,tableData:[],sels:[],currentRow:null,page:{pageSize:50,pageIndex:1,pageTotal:50},editFormVisible:!1,editLoading:!1,editType:"add",editForm:{},statusList:[{LinkUrl:"激活",value:!0},{LinkUrl:"禁用",value:!1}],editFormRules:{CompanyID:[{required:!0,message:"请输入客户代码",trigger:"blur"}],Enabled:[{required:!0,message:"请选择状态",trigger:"blur"}]},buttonList:[]}},created:function(){this.getWeChatCompany()},methods:{selectCurrentRow:function(e){this.currentRow=e},selsChange:function(e){this.sels=e},handleCurrentChange:function(e){this.page.pageIndex=e,this.getWeChatCompany()},getWeChatCompany:function(){var e=this;this.listLoading=!0;var t={intPageIndex:this.page.pageIndex,intPageSize:this.page.pageSize,strOrderByFileds:""};this.filters.name&&(t.conditions="CompanyName like "+this.filters.name+" | CompanyName like "+this.filters.name),Object(l["U"])(t).then(function(t){e.listLoading=!1,t.data.success?e.tableData=t.data.response.data:e.$message.error(t.data.msg)})},handleDel:function(){var e=this;this.currentRow?this.$confirm("确认删除吗？","提示",{}).then(function(){Object(l["nb"])({id:e.currentRow.CompanyID}).then(function(t){t.data.success?(e.getWeChatCompany(),e.$message.success("删除成功!")):e.$message.error(t.data.msg)})}):this.$message.error("请选择要操作的数据行")},handleEdit:function(){this.currentRow?(this.editFormVisible=!0,this.editType="edit",this.editForm=s()({},this.currentRow)):this.$message.error("请选择要操作的数据行")},handleAdd:function(){this.editFormVisible=!0,this.editType="add",this.editForm=s()({})},editSubmit:function(){var e=this;this.$refs.editForm.validate(function(t){t&&e.$confirm("确认提交吗？","提示",{}).then(function(){e.editLoading=!0,"add"==e.editType?Object(l["k"])(e.editForm).then(function(t){e.editLoading=!1,t.data.success?(e.getWeChatCompany(),e.editFormVisible=!1,e.$message.success("添加成功!")):e.$message.error(t.data.msg)}):"edit"==e.editType&&Object(l["wb"])(e.editForm).then(function(t){e.editLoading=!1,t.data.success?(e.getWeChatCompany(),e.editFormVisible=!1,e.$message.success("修改成功!")):e.$message.error(t.data.msg)})})})},batchRemove:function(){var e=this;this.sels.length>0?this.$confirm("确认批量删除吗？","提示").then(function(){var t=e.sels.map(function(e){return e.CompanyID}).join(",");Object(l["m"])({ids:t}).then(function(t){t.data.success?(e.getWeChatCompany(),e.$message.success("批量成功!")):e.$message.error(t.data.msg)})}):this.$message.error("请选择要操作的数据行")},callFunction:function(e){this.filters={name:e.search},this[e.Func].apply(this,e)}},mounted:function(){var e=window.localStorage.router?JSON.parse(window.localStorage.router):[];this.buttonList=Object(r["a"])(this.$route.path,e)}},u=d,m=a("2877"),p=Object(m["a"])(u,n,i,!1,null,null,null);p.options.__file="Company.vue";t["default"]=p.exports}}]);
//# sourceMappingURL=chunk-23e41f57.ae5fabaa.js.map