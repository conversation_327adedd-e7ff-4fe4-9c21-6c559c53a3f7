{"version": 3, "sources": ["webpack:///./node_modules/@aspnet/signalr/dist/esm/TextMessageFormat.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/HandshakeProtocol.js", "webpack:///./node_modules/core-js/modules/es6.regexp.search.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/NodeHttpClient.js", "webpack:///./node_modules/core-js/modules/es6.regexp.constructor.js", "webpack:///./src/views/Logs/Index.vue?9e97", "webpack:///./node_modules/core-js/modules/es6.regexp.match.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/ILogger.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/Loggers.js", "webpack:///./node_modules/core-js/modules/_same-value.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/HttpClient.js", "webpack:///./src/views/Logs/Index.vue?df66", "webpack:///./node_modules/@aspnet/signalr/dist/esm/IHubProtocol.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/XhrHttpClient.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/DefaultHttpClient.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/HubConnection.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/ITransport.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/AbortController.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/LongPollingTransport.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/ServerSentEventsTransport.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/WebSocketTransport.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/HttpConnection.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/JsonHubProtocol.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/HubConnectionBuilder.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/index.js", "webpack:///src/views/Logs/Index.vue", "webpack:///./src/views/Logs/Index.vue?eee2", "webpack:///./src/views/Logs/Index.vue", "webpack:///./util/date.js", "webpack:///./node_modules/core-js/modules/_is-regexp.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/Errors.js", "webpack:///./node_modules/@aspnet/signalr/dist/esm/Utils.js"], "names": ["__webpack_require__", "d", "__webpack_exports__", "TextMessageFormat", "write", "output", "RecordSeparator", "parse", "input", "length", "Error", "messages", "split", "pop", "RecordSeparatorCode", "String", "fromCharCode", "<PERSON><PERSON><PERSON>", "HandshakeProtocol", "_TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__", "_Utils__WEBPACK_IMPORTED_MODULE_1__", "prototype", "writeHandshakeRequest", "handshakeRequest", "JSON", "stringify", "parseHandshakeResponse", "data", "responseMessage", "messageData", "remainingData", "Object", "binaryData", "Uint8Array", "separatorIndex", "indexOf", "responseLength", "apply", "slice", "byteLength", "buffer", "textData", "substring", "response", "type", "anObject", "sameValue", "regExpExec", "defined", "SEARCH", "$search", "maybeCallNative", "regexp", "O", "this", "fn", "undefined", "call", "RegExp", "res", "done", "value", "rx", "S", "previousLastIndex", "lastIndex", "result", "index", "NodeHttpClient", "requestModule", "_Errors__WEBPACK_IMPORTED_MODULE_0__", "_HttpClient__WEBPACK_IMPORTED_MODULE_1__", "_ILogger__WEBPACK_IMPORTED_MODULE_2__", "_Utils__WEBPACK_IMPORTED_MODULE_3__", "__extends", "extendStatics", "setPrototypeOf", "__proto__", "Array", "b", "p", "hasOwnProperty", "__", "constructor", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "XMLHttpRequest", "requireFunc", "require", "_super", "logger", "_this", "cookieJar", "jar", "request", "defaults", "send", "httpRequest", "Promise", "resolve", "reject", "requestBody", "content", "from", "currentRequest", "url", "body", "encoding", "responseType", "headers", "X-Requested-With", "method", "timeout", "error", "abortSignal", "<PERSON>ab<PERSON>", "code", "log", "Warning", "statusCode", "statusMessage", "abort", "getCookieString", "global", "inheritIfRequired", "dP", "f", "gOPN", "isRegExp", "$flags", "$RegExp", "Base", "proto", "re1", "re2", "CORRECT_NEW", "tiRE", "piRE", "fiU", "source", "proxy", "key", "configurable", "get", "set", "it", "keys", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_6e805334_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_6e805334_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "to<PERSON><PERSON><PERSON>", "advanceStringIndex", "MATCH", "$match", "fullUnicode", "unicode", "A", "matchStr", "LogLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_logLevel", "_message", "instance", "module", "exports", "is", "x", "y", "HttpResponse", "HttpClient", "statusText", "options", "post", "delete", "MessageType", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticStyle", "display", "ref", "margin", "width", "min-width", "attrs", "label-width", "on", "submit", "$event", "preventDefault", "onSubmit", "label", "model", "callback", "$$v", "userName", "expression", "userMessage", "_l", "item", "_v", "_s", "user", "message", "click", "submitCard", "getLogs", "directives", "name", "rawName", "tableData", "scopedSlots", "_u", "props", "staticClass", "label-position", "inline", "row", "datetime", "domProps", "innerHTML", "prop", "scope", "class", "logColor", "staticRenderFns", "XhrHttpClient_XhrHttpClient", "XhrHttpClient", "aborted", "Errors", "xhr", "open", "withCredentials", "setRequestHeader", "for<PERSON>ach", "header", "onload", "status", "responseText", "onerror", "<PERSON><PERSON><PERSON>", "ontimeout", "DefaultHttpClient_extends", "DefaultHttpClient_DefaultHttpClient", "DefaultHttpClient", "httpClient", "HubConnectionState", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "next", "e", "rejected", "then", "__generator", "g", "_", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "v", "op", "TypeError", "push", "DEFAULT_TIMEOUT_IN_MS", "DEFAULT_PING_INTERVAL_IN_MS", "HttpTransportType", "TransferFormat", "HubConnection_HubConnection", "HubConnection", "connection", "protocol", "Utils", "isRequired", "serverTimeoutInMilliseconds", "keepAliveIntervalInMilliseconds", "handshakeProtocol", "onreceive", "processIncomingData", "onclose", "connectionClosed", "callbacks", "methods", "closedCallbacks", "id", "receivedHandshakeResponse", "connectionState", "Disconnected", "cachedPingMessage", "writeMessage", "<PERSON>", "defineProperty", "enumerable", "start", "handshakePromise", "_a", "version", "Debug", "handshakeResolver", "handshak<PERSON><PERSON><PERSON><PERSON><PERSON>", "transferFormat", "sendMessage", "Information", "cleanupTimeout", "resetTimeoutPeriod", "resetKeepAliveInterval", "Connected", "stop", "cleanupPingTimer", "stream", "methodName", "args", "_i", "promiseQueue", "invocationDescriptor", "createStreamInvocation", "subject", "cancelCallback", "cancelInvocation", "createCancelInvocation", "invocationId", "cancelMessage", "invocationEvent", "Completion", "complete", "catch", "createInvocation", "invoke", "newMethod", "toLowerCase", "off", "handlers", "removeIdx", "splice", "processHandshakeResponse", "parseMessages", "messages_1", "Invocation", "invokeClientMethod", "StreamItem", "Close", "pingServerHandle", "setTimeout", "_b", "features", "inherentKeepAlive", "timeoutH<PERSON>le", "serverTimeout", "invocationMessage", "target", "m", "c", "clearTimeout", "nonblocking", "toString", "StreamInvocation", "CancelInvocation", "AbortController", "isAborted", "LongPollingTransport_awaiter", "LongPollingTransport_generator", "LongPollingTransport_LongPollingTransport", "LongPollingTransport", "accessTokenFactory", "logMessageContent", "pollAbort", "running", "connect", "pollOptions", "token", "pollUrl", "isIn", "Trace", "Binary", "signal", "getAccessToken", "updateHeaderToken", "Date", "now", "closeError", "receiving", "poll", "e_1", "pollAborted", "raiseOnClose", "deleteOptions", "logMessage", "ServerSentEventsTransport_awaiter", "ServerSentEventsTransport_generator", "ServerSentEventsTransport_ServerSentEventsTransport", "ServerSentEventsTransport", "eventSourceConstructor", "encodeURIComponent", "opened", "Text", "eventSource", "window", "cookies", "<PERSON><PERSON>", "onmessage", "close", "onopen", "WebSocketTransport_awaiter", "WebSocketTransport_generator", "WebSocketTransport_WebSocketTransport", "WebSocketTransport", "webSocketConstructor", "webSocket", "replace", "binaryType", "_event", "event", "ErrorEvent", "readyState", "OPEN", "<PERSON><PERSON><PERSON>", "reason", "HttpConnection_awaiter", "HttpConnection_generator", "MAX_REDIRECTS", "WebSocketModule", "EventSourceModule", "HttpConnection_HttpConnection", "HttpConnection", "baseUrl", "resolveUrl", "isNode", "WebSocket", "EventSource", "startPromise", "startInternal", "transport", "stopError", "negotiateResponse", "redirects", "_loop_1", "this_1", "state_1", "e_2", "skipNegotiation", "WebSockets", "constructTransport", "accessToken_1", "getNegotiationResponse", "ProtocolVersion", "accessToken", "createTransport", "stopConnection", "changeState", "negotiateUrl", "e_3", "resolveNegotiateUrl", "createConnectUrl", "connectionId", "requestedTransport", "requestedTransferFormat", "connectUrl", "transports", "transports_1", "endpoint", "ex_1", "isITransport", "availableTransports", "resolveTransport", "ServerSentEvents", "LongPolling", "transferFormats", "map", "transportMatches", "to", "lastIndexOf", "document", "aTag", "createElement", "href", "actualTransport", "JSON_HUB_PROTOCOL_NAME", "JsonHubProtocol_JsonHubProtocol", "JsonHubProtocol", "Loggers", "hubMessages", "parsedMessage", "isInvocationMessage", "isStreamItemMessage", "isCompletionMessage", "assertNotEmptyString", "errorMessage", "HubConnectionBuilder_HubConnectionBuilder", "HubConnectionBuilder", "configureLogging", "logging", "<PERSON><PERSON><PERSON><PERSON>", "withUrl", "transportTypeOrOptions", "httpConnectionOptions", "withHubProtocol", "build", "Indexvue_type_script_lang_js_", "filters", "LinkUrl", "listLoading", "formattdDetail", "column", "tdDetail", "formatCreateTime", "tdCreatetime", "date", "formatDate", "format", "handleCurrentChange", "val", "page", "getRoles", "thisvue", "para", "api", "err", "console", "created", "thisVue", "concat", "update", "info", "clearInterval", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "Logs_Indexvue_type_script_lang_js_", "component", "componentNormalizer", "__file", "SIGN_REGEXP", "DEFAULT_PATTERN", "padding", "len", "getQueryStringByName", "reg", "r", "location", "search", "substr", "match", "context", "pattern", "$0", "char<PERSON>t", "getFullYear", "getMonth", "getDate", "getDay", "getHours", "getMinutes", "getSeconds", "dateString", "matchs1", "matchs2", "_date", "_int", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_core_js_parse_int__WEBPACK_IMPORTED_MODULE_0___default", "sign", "setFullYear", "setMonth", "setDate", "setHours", "setMinutes", "setSeconds", "isEmt", "obj", "isObject", "cof", "HttpError", "TimeoutError", "AbortError", "_newTarget", "trueProto", "Arg", "getDataDetail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createLogger", "Subject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ILogger__WEBPACK_IMPORTED_MODULE_0__", "_Loggers__WEBPACK_IMPORTED_MODULE_1__", "values", "<PERSON><PERSON><PERSON><PERSON>", "detail", "formatA<PERSON>y<PERSON>uffer", "view", "str", "num", "pad", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transportName", "observers", "observer", "subscribe", "SubjectSubscription", "dispose", "minimumLogLevel", "logLevel", "Critical", "toISOString", "warn"], "mappings": "gHAAAA,EAAAC,EAAAC,EAAA,sBAAAC,IAIA,IAAAA,EAAA,WACA,SAAAA,KAeA,OAbAA,EAAAC,MAAA,SAAAC,GACA,SAAAA,EAAAF,EAAAG,iBAEAH,EAAAI,MAAA,SAAAC,GACA,GAAAA,IAAAC,OAAA,KAAAN,EAAAG,gBACA,UAAAI,MAAA,0BAEA,IAAAC,EAAAH,EAAAI,MAAAT,EAAAG,iBAEA,OADAK,EAAAE,MACAF,GAEAR,EAAAW,oBAAA,GACAX,EAAAG,gBAAAS,OAAAC,aAAAb,EAAAW,qBACAX,EAhBA,kECJA,SAAAc,GAAAjB,EAAAC,EAAAC,EAAA,sBAAAgB,IAAA,IAAAC,EAAAnB,EAAA,QAAAoB,EAAApB,EAAA,QAKAkB,EAAA,WACA,SAAAA,KA8CA,OA3CAA,EAAAG,UAAAC,sBAAA,SAAAC,GACA,OAAeJ,EAAA,KAAiBf,MAAAoB,KAAAC,UAAAF,KAEhCL,EAAAG,UAAAK,uBAAA,SAAAC,GACA,IAAAC,EACAC,EACAC,EACA,GAAYC,OAAAX,EAAA,KAAAW,CAAaJ,IAAA,qBAAAV,GAAAU,aAAAV,EAAA,CAEzB,IAAAe,EAAA,IAAAC,WAAAN,GACAO,EAAAF,EAAAG,QAAoDhB,EAAA,KAAiBL,qBACrE,QAAAoB,EACA,UAAAxB,MAAA,0BAIA,IAAA0B,EAAAF,EAAA,EACAL,EAAAd,OAAAC,aAAAqB,MAAA,KAAAL,EAAAM,MAAA,EAAAF,IACAN,EAAAE,EAAAO,WAAAH,EAAAJ,EAAAM,MAAAF,GAAAI,OAAA,SAEA,CACA,IAAAC,EAAAd,EACAO,EAAAO,EAAAN,QAAkDhB,EAAA,KAAiBb,iBACnE,QAAA4B,EACA,UAAAxB,MAAA,0BAIA0B,EAAAF,EAAA,EACAL,EAAAY,EAAAC,UAAA,EAAAN,GACAN,EAAAW,EAAAhC,OAAA2B,EAAAK,EAAAC,UAAAN,GAAA,KAGA,IAAAzB,EAAuBQ,EAAA,KAAiBZ,MAAAsB,GACxCc,EAAAnB,KAAAjB,MAAAI,EAAA,IACA,GAAAgC,EAAAC,KACA,UAAAlC,MAAA,kDAKA,OAHAkB,EAAAe,EAGA,CAAAb,EAAAF,IAEAV,EA/CA,sECHA,IAAA2B,EAAe7C,EAAQ,QACvB8C,EAAgB9C,EAAQ,QACxB+C,EAAiB/C,EAAQ,QAGzBA,EAAQ,OAARA,CAAuB,oBAAAgD,EAAAC,EAAAC,EAAAC,GACvB,OAGA,SAAAC,GACA,IAAAC,EAAAL,EAAAM,MACAC,OAAAC,GAAAJ,OAAAI,EAAAJ,EAAAH,GACA,YAAAO,IAAAD,IAAAE,KAAAL,EAAAC,GAAA,IAAAK,OAAAN,GAAAH,GAAAlC,OAAAsC,KAIA,SAAAD,GACA,IAAAO,EAAAR,EAAAD,EAAAE,EAAAE,MACA,GAAAK,EAAAC,KAAA,OAAAD,EAAAE,MACA,IAAAC,EAAAjB,EAAAO,GACAW,EAAAhD,OAAAuC,MACAU,EAAAF,EAAAG,UACAnB,EAAAkB,EAAA,KAAAF,EAAAG,UAAA,GACA,IAAAC,EAAAnB,EAAAe,EAAAC,GAEA,OADAjB,EAAAgB,EAAAG,UAAAD,KAAAF,EAAAG,UAAAD,GACA,OAAAE,GAAA,EAAAA,EAAAC,gDC3BA,SAAAlD,GAAAjB,EAAAC,EAAAC,EAAA,sBAAAkE,IAAA,IAwBAC,EAxBAC,EAAAtE,EAAA,QAAAuE,EAAAvE,EAAA,QAAAwE,EAAAxE,EAAA,QAAAyE,EAAAzE,EAAA,QAEA0E,EAA6B,WAC7B,IAAAC,EAAA5C,OAAA6C,gBACA,CAAUC,UAAA,cAAgBC,OAAA,SAAA7E,EAAA8E,GAAsC9E,EAAA4E,UAAAE,IAChE,SAAA9E,EAAA8E,GAAyB,QAAAC,KAAAD,IAAAE,eAAAD,KAAA/E,EAAA+E,GAAAD,EAAAC,KACzB,gBAAA/E,EAAA8E,GAEA,SAAAG,IAAuB5B,KAAA6B,YAAAlF,EADvB0E,EAAA1E,EAAA8E,GAEA9E,EAAAoB,UAAA,OAAA0D,EAAAhD,OAAAqD,OAAAL,IAAAG,EAAA7D,UAAA0D,EAAA1D,UAAA,IAAA6D,IAP6B,GAU7BG,EAA4BtD,OAAAuD,QAAA,SAAAC,GAC5B,QAAAC,EAAAC,EAAA,EAAAC,EAAAC,UAAAlF,OAA4CgF,EAAAC,EAAOD,IAEnD,QAAAT,KADAQ,EAAAG,UAAAF,GACAD,EAAAzD,OAAAV,UAAA4D,eAAAxB,KAAA+B,EAAAR,KACAO,EAAAP,GAAAQ,EAAAR,IAEA,OAAAO,GAOA,wBAAAK,eAAA,CAGA,IAAAC,EAAkEC,QAClEzB,EAAAwB,EAAA,WAEA,IAAAzB,EAAA,SAAA2B,GAEA,SAAA3B,EAAA4B,GACA,IAAAC,EAAAF,EAAAtC,KAAAH,YACA,wBAAAe,EACA,UAAA3D,MAAA,6CAKA,OAHAuF,EAAAD,SACAC,EAAAC,UAAA7B,EAAA8B,MACAF,EAAAG,QAAA/B,EAAAgC,SAAA,CAAgDF,IAAAF,EAAAC,YAChDD,EAoDA,OA7DAvB,EAAAN,EAAA2B,GAWA3B,EAAA/C,UAAAiF,KAAA,SAAAC,GACA,IAAAN,EAAA3C,KACA,WAAAkD,QAAA,SAAAC,EAAAC,GACA,IAAAC,EAEAA,EADgB5E,OAAA0C,EAAA,KAAA1C,CAAawE,EAAAK,SAC7B3F,EAAA4F,KAAAN,EAAAK,SAGAL,EAAAK,SAAA,GAEA,IAAAE,EAAAb,EAAAG,QAAAG,EAAAQ,IAAA,CACAC,KAAAL,EAEAM,SAAA,gBAAAV,EAAAW,aAAA,YACAC,QAAA9B,EAAA,CAEA+B,mBAAA,kBAA0Db,EAAAY,SAC1DE,OAAAd,EAAAc,OACAC,QAAAf,EAAAe,SACa,SAAAC,EAAA5E,EAAAqE,GAIb,GAHAT,EAAAiB,cACAjB,EAAAiB,YAAAC,QAAA,MAEAF,EAOA,MANA,cAAAA,EAAAG,OACAzB,EAAAD,OAAA2B,IAAyCnD,EAAA,KAAQoD,QAAA,8BACjDlB,EAAA,IAAmCpC,EAAA,OAEnC2B,EAAAD,OAAA2B,IAAqCnD,EAAA,KAAQoD,QAAA,4BAAAL,QAC7Cb,EAAAa,GAGA5E,EAAAkF,YAAA,KAAAlF,EAAAkF,WAAA,IACApB,EAAA,IAAgClC,EAAA,KAAY5B,EAAAkF,WAAAlF,EAAAmF,eAAA,GAAAd,IAG5CN,EAAA,IAA+BpC,EAAA,KAAS3B,EAAAmF,eAAA,GAAAnF,EAAAkF,YAAA,MAGxCtB,EAAAiB,cACAjB,EAAAiB,YAAAC,QAAA,WACAX,EAAAiB,QACArB,EAAA,IAA+BpC,EAAA,WAK/BF,EAAA/C,UAAA2G,gBAAA,SAAAjB,GACA,OAAAzD,KAAA4C,UAAA8B,gBAAAjB,IAEA3C,EA9DA,CA+DEG,EAAA,4DC9FF,IAAA0D,EAAajI,EAAQ,QACrBkI,EAAwBlI,EAAQ,QAChCmI,EAASnI,EAAQ,QAAcoI,EAC/BC,EAAWrI,EAAQ,QAAgBoI,EACnCE,EAAetI,EAAQ,QACvBuI,EAAavI,EAAQ,QACrBwI,EAAAP,EAAAvE,OACA+E,EAAAD,EACAE,EAAAF,EAAAnH,UACAsH,EAAA,KACAC,EAAA,KAEAC,EAAA,IAAAL,EAAAG,OAEA,GAAI3I,EAAQ,WAAgB6I,GAAsB7I,EAAQ,OAARA,CAAkB,WAGpE,OAFA4I,EAAM5I,EAAQ,OAARA,CAAgB,aAEtBwI,EAAAG,OAAAH,EAAAI,OAAA,QAAAJ,EAAAG,EAAA,QACC,CACDH,EAAA,SAAAxD,EAAAoD,GACA,IAAAU,EAAAxF,gBAAAkF,EACAO,EAAAT,EAAAtD,GACAgE,OAAAxF,IAAA4E,EACA,OAAAU,GAAAC,GAAA/D,EAAAG,cAAAqD,GAAAQ,EAAAhE,EACAkD,EAAAW,EACA,IAAAJ,EAAAM,IAAAC,EAAAhE,EAAAiE,OAAAjE,EAAAoD,GACAK,GAAAM,EAAA/D,aAAAwD,GAAAxD,EAAAiE,OAAAjE,EAAA+D,GAAAC,EAAAT,EAAA9E,KAAAuB,GAAAoD,GACAU,EAAAxF,KAAAoF,EAAAF,IASA,IAPA,IAAAU,EAAA,SAAAC,GACAA,KAAAX,GAAAL,EAAAK,EAAAW,EAAA,CACAC,cAAA,EACAC,IAAA,WAAwB,OAAAZ,EAAAU,IACxBG,IAAA,SAAAC,GAA0Bd,EAAAU,GAAAI,MAG1BC,EAAAnB,EAAAI,GAAAhD,EAAA,EAAoC+D,EAAA/I,OAAAgF,GAAiByD,EAAAM,EAAA/D,MACrDiD,EAAAvD,YAAAqD,EACAA,EAAAnH,UAAAqH,EACE1I,EAAQ,OAARA,CAAqBiI,EAAA,SAAAO,GAGvBxI,EAAQ,OAARA,CAAwB,+CC1CxB,IAAAyJ,EAAAzJ,EAAA,QAAA0J,EAAA1J,EAAA0F,EAAA+D,GAAmfC,EAAG,qCCEtf,IAAA7G,EAAe7C,EAAQ,QACvB2J,EAAe3J,EAAQ,QACvB4J,EAAyB5J,EAAQ,QACjC+C,EAAiB/C,EAAQ,QAGzBA,EAAQ,OAARA,CAAuB,mBAAAgD,EAAA6G,EAAAC,EAAA3G,GACvB,OAGA,SAAAC,GACA,IAAAC,EAAAL,EAAAM,MACAC,OAAAC,GAAAJ,OAAAI,EAAAJ,EAAAyG,GACA,YAAArG,IAAAD,IAAAE,KAAAL,EAAAC,GAAA,IAAAK,OAAAN,GAAAyG,GAAA9I,OAAAsC,KAIA,SAAAD,GACA,IAAAO,EAAAR,EAAA2G,EAAA1G,EAAAE,MACA,GAAAK,EAAAC,KAAA,OAAAD,EAAAE,MACA,IAAAC,EAAAjB,EAAAO,GACAW,EAAAhD,OAAAuC,MACA,IAAAQ,EAAAmE,OAAA,OAAAlF,EAAAe,EAAAC,GACA,IAAAgG,EAAAjG,EAAAkG,QACAlG,EAAAG,UAAA,EACA,IAEAC,EAFA+F,EAAA,GACAvE,EAAA,EAEA,cAAAxB,EAAAnB,EAAAe,EAAAC,IAAA,CACA,IAAAmG,EAAAnJ,OAAAmD,EAAA,IACA+F,EAAAvE,GAAAwE,EACA,KAAAA,IAAApG,EAAAG,UAAA2F,EAAA7F,EAAA4F,EAAA7F,EAAAG,WAAA8F,IACArE,IAEA,WAAAA,EAAA,KAAAuE,yCC7BO,IAAAE,EAPPnK,EAAAC,EAAAC,EAAA,sBAAAiK,IAQA,SAAAA,GAEAA,IAAA,oBAEAA,IAAA,oBAEAA,IAAA,gCAEAA,IAAA,wBAEAA,IAAA,oBAEAA,IAAA,0BAEAA,IAAA,kBAdA,CAeCA,MAAA,0CCvBDnK,EAAAC,EAAAC,EAAA,sBAAAkK,IAGA,IAAAA,EAAA,WACA,SAAAA,KAQA,OAJAA,EAAA/I,UAAAsG,IAAA,SAAA0C,EAAAC,KAGAF,EAAAG,SAAA,IAAAH,EACAA,EATA,yBCFAI,EAAAC,QAAA1I,OAAA2I,IAAA,SAAAC,EAAAC,GAEA,OAAAD,IAAAC,EAAA,IAAAD,GAAA,EAAAA,IAAA,EAAAC,EAAAD,MAAAC,2CCHA5K,EAAAC,EAAAC,EAAA,sBAAA2K,IAAA7K,EAAAC,EAAAC,EAAA,sBAAA4K,IAEA,IAAAzF,EAA4BtD,OAAAuD,QAAA,SAAAC,GAC5B,QAAAC,EAAAC,EAAA,EAAAC,EAAAC,UAAAlF,OAA4CgF,EAAAC,EAAOD,IAEnD,QAAAT,KADAQ,EAAAG,UAAAF,GACAD,EAAAzD,OAAAV,UAAA4D,eAAAxB,KAAA+B,EAAAR,KACAO,EAAAP,GAAAQ,EAAAR,IAEA,OAAAO,GAGAsF,EAAA,WACA,SAAAA,EAAAhD,EAAAkD,EAAAnE,GACAtD,KAAAuE,aACAvE,KAAAyH,aACAzH,KAAAsD,UAEA,OAAAiE,EANA,GAaAC,EAAA,WACA,SAAAA,KAoBA,OAlBAA,EAAAzJ,UAAAgI,IAAA,SAAAtC,EAAAiE,GACA,OAAA1H,KAAAgD,KAAAjB,EAAA,GAAoC2F,EAAA,CAAY3D,OAAA,MAAAN,UAEhD+D,EAAAzJ,UAAA4J,KAAA,SAAAlE,EAAAiE,GACA,OAAA1H,KAAAgD,KAAAjB,EAAA,GAAoC2F,EAAA,CAAY3D,OAAA,OAAAN,UAEhD+D,EAAAzJ,UAAA6J,OAAA,SAAAnE,EAAAiE,GACA,OAAA1H,KAAAgD,KAAAjB,EAAA,GAAoC2F,EAAA,CAAY3D,OAAA,SAAAN,UAQhD+D,EAAAzJ,UAAA2G,gBAAA,SAAAjB,GACA,UAEA+D,EArBA,6CCxBA,ICGOK,EDHPC,EAAA,WAA0B,IAAAC,EAAA/H,KAAagI,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAAA,EAAA,OAA+BE,YAAA,CAAaC,QAAA,UAAmB,CAAAH,EAAA,WAAgBI,IAAA,OAAAF,YAAA,CAAwBG,OAAA,OAAAC,MAAA,MAAAC,YAAA,SAAkDC,MAAA,CAAQC,cAAA,QAAqBC,GAAA,CAAKC,OAAA,SAAAC,GAAkD,OAAxBA,EAAAC,iBAAwBhB,EAAAiB,SAAAF,MAA8B,CAAAZ,EAAA,gBAAqBQ,MAAA,CAAOO,MAAA,QAAe,CAAAf,EAAA,YAAiBgB,MAAA,CAAO3I,MAAAwH,EAAA,SAAAoB,SAAA,SAAAC,GAA8CrB,EAAAsB,SAAAD,GAAiBE,WAAA,eAAwB,GAAApB,EAAA,gBAAyBQ,MAAA,CAAOO,MAAA,OAAc,CAAAf,EAAA,YAAiBgB,MAAA,CAAO3I,MAAAwH,EAAA,YAAAoB,SAAA,SAAAC,GAAiDrB,EAAAwB,YAAAH,GAAoBE,WAAA,kBAA2B,OAAAvB,EAAAyB,GAAAzB,EAAA,kBAAA0B,EAAA5I,GAAqD,OAAAqH,EAAA,MAAgBrC,IAAAhF,EAAA,eAA0B,CAAAqH,EAAA,MAAAA,EAAA,KAAAH,EAAA2B,GAAA,YAAA3B,EAAA2B,GAAA3B,EAAA4B,GAAAF,EAAAG,SAAA1B,EAAA,MAAAA,EAAA,KAAAH,EAAA2B,GAAA,eAAA3B,EAAA2B,GAAA3B,EAAA4B,GAAAF,EAAAI,gBAA0I3B,EAAA,aAAkBQ,MAAA,CAAOpJ,KAAA,WAAiBsJ,GAAA,CAAKkB,MAAA/B,EAAAgC,aAAwB,CAAAhC,EAAA2B,GAAA,QAAAxB,EAAA,aAAiCQ,MAAA,CAAOpJ,KAAA,WAAiBsJ,GAAA,CAAKkB,MAAA/B,EAAAiC,UAAqB,CAAAjC,EAAA2B,GAAA,YAAAxB,EAAA,YAAoC+B,WAAA,EAAaC,KAAA,UAAAC,QAAA,YAAA5J,MAAAwH,EAAA,YAAAuB,WAAA,gBAAoFlB,YAAA,CAAeI,MAAA,QAAeE,MAAA,CAAQrK,KAAA0J,EAAAqC,YAAsB,CAAAlC,EAAA,mBAAwBQ,MAAA,CAAOpJ,KAAA,UAAgB+K,YAAAtC,EAAAuC,GAAA,EAAsBzE,IAAA,UAAA5F,GAAA,SAAAsK,GAAiC,OAAArC,EAAA,WAAsBsC,YAAA,oBAAA9B,MAAA,CAAuC+B,iBAAA,OAAAC,OAAA,KAAqC,CAAAxC,EAAA,gBAAqBQ,MAAA,CAAOO,MAAA,aAAoB,CAAAf,EAAA,QAAAH,EAAA2B,GAAA3B,EAAA4B,GAAAY,EAAAI,IAAAC,eAAA1C,EAAA,gBAAuEQ,MAAA,CAAOO,MAAA,YAAmB,CAAAf,EAAA,QAAa2C,SAAA,CAAUC,UAAA/C,EAAA4B,GAAAY,EAAAI,IAAArH,eAAuC,UAAa4E,EAAA,mBAAwBQ,MAAA,CAAOO,MAAA,WAAA8B,KAAA,cAAsC7C,EAAA,mBAAwBQ,MAAA,CAAOO,MAAA,WAAkBoB,YAAAtC,EAAAuC,GAAA,EAAsBzE,IAAA,UAAA5F,GAAA,SAAA+K,GAAiC,OAAA9C,EAAA,QAAmB+C,MAAAD,EAAAL,IAAAO,SAAAL,SAAA,CAAmCC,UAAA/C,EAAA4B,GAAAqB,EAAAL,IAAArH,oBAA8C,QAC75D6H,EAAA,2EECA/J,EAA6B,WAC7B,IAAAC,EAAA5C,OAAA6C,gBACA,CAAUC,UAAA,cAAgBC,OAAA,SAAA7E,EAAA8E,GAAsC9E,EAAA4E,UAAAE,IAChE,SAAA9E,EAAA8E,GAAyB,QAAAC,KAAAD,IAAAE,eAAAD,KAAA/E,EAAA+E,GAAAD,EAAAC,KACzB,gBAAA/E,EAAA8E,GAEA,SAAAG,IAAuB5B,KAAA6B,YAAAlF,EADvB0E,EAAA1E,EAAA8E,GAEA9E,EAAAoB,UAAA,OAAA0D,EAAAhD,OAAAqD,OAAAL,IAAAG,EAAA7D,UAAA0D,EAAA1D,UAAA,IAAA6D,IAP6B,GAazBwJ,EAAa,SAAA3I,GAEjB,SAAA4I,EAAA3I,GACA,IAAAC,EAAAF,EAAAtC,KAAAH,YAEA,OADA2C,EAAAD,SACAC,EA+DA,OAnEAvB,EAAAiK,EAAA5I,GAOA4I,EAAAtN,UAAAiF,KAAA,SAAAF,GACA,IAAAH,EAAA3C,KAEA,OAAA8C,EAAAoB,aAAApB,EAAAoB,YAAAoH,QACApI,QAAAE,OAAA,IAAsCmI,EAAA,MAEtCzI,EAAAiB,OAGAjB,EAAAW,IAGA,IAAAP,QAAA,SAAAC,EAAAC,GACA,IAAAoI,EAAA,IAAAlJ,eACAkJ,EAAAC,KAAA3I,EAAAiB,OAAAjB,EAAAW,KAAA,GACA+H,EAAAE,iBAAA,EACAF,EAAAG,iBAAA,qCAEAH,EAAAG,iBAAA,2CACA,IAAA9H,EAAAf,EAAAe,QACAA,GACApF,OAAAyH,KAAArC,GACA+H,QAAA,SAAAC,GACAL,EAAAG,iBAAAE,EAAAhI,EAAAgI,MAGA/I,EAAAc,eACA4H,EAAA5H,aAAAd,EAAAc,cAEAd,EAAAoB,cACApB,EAAAoB,YAAAC,QAAA,WACAqH,EAAA/G,QACArB,EAAA,IAA+BmI,EAAA,QAG/BzI,EAAAkB,UACAwH,EAAAxH,QAAAlB,EAAAkB,SAEAwH,EAAAM,OAAA,WACAhJ,EAAAoB,cACApB,EAAAoB,YAAAC,QAAA,MAEAqH,EAAAO,QAAA,KAAAP,EAAAO,OAAA,IACA5I,EAAA,IAAgCqE,EAAA,KAAYgE,EAAAO,OAAAP,EAAA/D,WAAA+D,EAAAnM,UAAAmM,EAAAQ,eAG5C5I,EAAA,IAA+BmI,EAAA,KAASC,EAAA/D,WAAA+D,EAAAO,UAGxCP,EAAAS,QAAA,WACAtJ,EAAAD,OAAA2B,IAAiC6H,EAAA,KAAQ5H,QAAA,4BAAAkH,EAAAO,OAAA,KAAAP,EAAA/D,WAAA,KACzCrE,EAAA,IAA2BmI,EAAA,KAASC,EAAA/D,WAAA+D,EAAAO,UAEpCP,EAAAW,UAAA,WACAxJ,EAAAD,OAAA2B,IAAiC6H,EAAA,KAAQ5H,QAAA,8BACzClB,EAAA,IAA2BmI,EAAA,OAE3BC,EAAAxI,KAAAF,EAAAQ,SAAA,MA/CAJ,QAAAE,OAAA,IAAAhG,MAAA,oBAHA8F,QAAAE,OAAA,IAAAhG,MAAA,wBAqDAiO,EApEiB,CAqEf7D,EAAA,MClFE4E,EAAyB,WAC7B,IAAA/K,EAAA5C,OAAA6C,gBACA,CAAUC,UAAA,cAAgBC,OAAA,SAAA7E,EAAA8E,GAAsC9E,EAAA4E,UAAAE,IAChE,SAAA9E,EAAA8E,GAAyB,QAAAC,KAAAD,IAAAE,eAAAD,KAAA/E,EAAA+E,GAAAD,EAAAC,KACzB,gBAAA/E,EAAA8E,GAEA,SAAAG,IAAuB5B,KAAA6B,YAAAlF,EADvB0E,EAAA1E,EAAA8E,GAEA9E,EAAAoB,UAAA,OAAA0D,EAAAhD,OAAAqD,OAAAL,IAAAG,EAAA7D,UAAA0D,EAAA1D,UAAA,IAAA6D,IAP6B,GAezByK,EAAiB,SAAA5J,GAGrB,SAAA6J,EAAA5J,GACA,IAAAC,EAAAF,EAAAtC,KAAAH,YAOA,MANA,qBAAAsC,eACAK,EAAA4J,WAAA,IAAmCnB,EAAa1I,GAGhDC,EAAA4J,WAAA,IAAmCzL,EAAA,KAAc4B,GAEjDC,EAmBA,OA7BIyJ,EAASE,EAAA7J,GAab6J,EAAAvO,UAAAiF,KAAA,SAAAF,GAEA,OAAAA,EAAAoB,aAAApB,EAAAoB,YAAAoH,QACApI,QAAAE,OAAA,IAAsCmI,EAAA,MAEtCzI,EAAAiB,OAGAjB,EAAAW,IAGAzD,KAAAuM,WAAAvJ,KAAAF,GAFAI,QAAAE,OAAA,IAAAhG,MAAA,oBAHA8F,QAAAE,OAAA,IAAAhG,MAAA,wBAOAkP,EAAAvO,UAAA2G,gBAAA,SAAAjB,GACA,OAAAzD,KAAAuM,WAAA7H,gBAAAjB,IAEA6I,EA9BqB,CA+BnB9E,EAAA,mBF5CF,SAAAK,GAEAA,IAAA,8BAEAA,IAAA,8BAEAA,IAAA,8BAEAA,IAAA,0CAEAA,IAAA,0CAEAA,IAAA,kBAEAA,IAAA,qBAdA,CAeCA,MAAA,SGyBM2E,cA1CPC,EAA6B,SAAAC,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGAI,EAA+B,SAAAV,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAO5C6N,EAAA,IACAC,EAAA,MAGA,SAAA5B,GAEAA,IAAA,kCAEAA,IAAA,6BAJA,CAKCA,MAAA,KAED,IChDO6B,EAYAC,EDoCHC,EAAa,WACjB,SAAAC,EAAAC,EAAA/L,EAAAgM,GACA,IAAA/L,EAAA3C,KACQ2O,EAAA,KAAGC,WAAAH,EAAA,cACHE,EAAA,KAAGC,WAAAlM,EAAA,UACHiM,EAAA,KAAGC,WAAAF,EAAA,YACX1O,KAAA6O,4BAAAV,EACAnO,KAAA8O,gCAAAV,EACApO,KAAA0C,SACA1C,KAAA0O,WACA1O,KAAAyO,aACAzO,KAAA+O,kBAAA,IAAqCnR,EAAA,KACrCoC,KAAAyO,WAAAO,UAAA,SAAA3Q,GAAqD,OAAAsE,EAAAsM,oBAAA5Q,IACrD2B,KAAAyO,WAAAS,QAAA,SAAAjL,GAAoD,OAAAtB,EAAAwM,iBAAAlL,IACpDjE,KAAAoP,UAAA,GACApP,KAAAqP,QAAA,GACArP,KAAAsP,gBAAA,GACAtP,KAAAuP,GAAA,EACAvP,KAAAwP,2BAAA,EACAxP,KAAAyP,gBAAAjD,EAAAkD,aACA1P,KAAA2P,kBAAA3P,KAAA0O,SAAAkB,aAAA,CAA6DtQ,KAAOuI,EAAWgI,OA2b/E,OApbArB,EAAA1M,OAAA,SAAA2M,EAAA/L,EAAAgM,GACA,WAAAF,EAAAC,EAAA/L,EAAAgM,IAEAjQ,OAAAqR,eAAAtB,EAAAzQ,UAAA,SAEAgI,IAAA,WACA,OAAA/F,KAAAyP,iBAEAM,YAAA,EACAjK,cAAA,IAMA0I,EAAAzQ,UAAAiS,MAAA,WACA,OAAAvD,EAAAzM,UAAA,oBACA,IAAA/B,EAAAgS,EACAtN,EAAA3C,KACA,OAAAoN,EAAApN,KAAA,SAAAkQ,GACA,OAAAA,EAAAjH,OACA,OAWA,OAVAhL,EAAA,CACAyQ,SAAA1O,KAAA0O,SAAAxE,KACAiG,QAAAnQ,KAAA0O,SAAAyB,SAEAnQ,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,2BAChDpQ,KAAAwP,2BAAA,EACAS,EAAA,IAAA/M,QAAA,SAAAC,EAAAC,GACAT,EAAA0N,kBAAAlN,EACAR,EAAA2N,kBAAAlN,IAEA,GAAApD,KAAAyO,WAAAuB,MAAAhQ,KAAA0O,SAAA6B,iBACA,OAGA,OAFAL,EAAA3C,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,8BAChD,GAAApQ,KAAAwQ,YAAAxQ,KAAA+O,kBAAA/Q,sBAAAC,KACA,OAQA,OAPAiS,EAAA3C,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQuE,YAAA,sBAAAzQ,KAAA0O,SAAAxE,KAAA,MAEhDlK,KAAA0Q,iBACA1Q,KAAA2Q,qBACA3Q,KAAA4Q,yBAEA,GAAAX,GACA,OAIA,OAFAC,EAAA3C,OACAvN,KAAAyP,gBAAAjD,EAAAqE,UACA,UASArC,EAAAzQ,UAAA+S,KAAA,WAIA,OAHA9Q,KAAA0C,OAAA2B,IAAwB6H,EAAA,KAAQkE,MAAA,2BAChCpQ,KAAA0Q,iBACA1Q,KAAA+Q,mBACA/Q,KAAAyO,WAAAqC,QASAtC,EAAAzQ,UAAAiT,OAAA,SAAAC,GAGA,IAFA,IAAAtO,EAAA3C,KACAkR,EAAA,GACAC,EAAA,EAAwBA,EAAA9O,UAAAlF,OAAuBgU,IAC/CD,EAAAC,EAAA,GAAA9O,UAAA8O,GAEA,IACAC,EADAC,EAAArR,KAAAsR,uBAAAL,EAAAC,GAEAK,EAAA,IAA0B5C,EAAA,KAC1B4C,EAAAC,eAAA,WACA,IAAAC,EAAA9O,EAAA+O,uBAAAL,EAAAM,cACAC,EAAAjP,EAAA+L,SAAAkB,aAAA6B,GAEA,cADA9O,EAAAyM,UAAAiC,EAAAM,cACAP,EAAAjE,KAAA,WACA,OAAAxK,EAAA6N,YAAAoB,MAGA5R,KAAAoP,UAAAiC,EAAAM,cAAA,SAAAE,EAAA5N,GACAA,EACAsN,EAAAtN,SAGA4N,IAEAA,EAAAvS,OAA6CuI,EAAWiK,WACxDD,EAAA5N,MACAsN,EAAAtN,MAAA,IAAA7G,MAAAyU,EAAA5N,QAGAsN,EAAAQ,WAIAR,EAAAvE,KAAA6E,EAAA,QAIA,IAAAhI,EAAA7J,KAAA0O,SAAAkB,aAAAyB,GAMA,OALAD,EAAApR,KAAAwQ,YAAA3G,GACAmI,MAAA,SAAA/E,GACAsE,EAAAtN,MAAAgJ,UACAtK,EAAAyM,UAAAiC,EAAAM,gBAEAJ,GAEA/C,EAAAzQ,UAAAyS,YAAA,SAAA3G,GAEA,OADA7J,KAAA4Q,yBACA5Q,KAAAyO,WAAAzL,KAAA6G,IAWA2E,EAAAzQ,UAAAiF,KAAA,SAAAiO,GAEA,IADA,IAAAC,EAAA,GACAC,EAAA,EAAwBA,EAAA9O,UAAAlF,OAAuBgU,IAC/CD,EAAAC,EAAA,GAAA9O,UAAA8O,GAEA,IAAAE,EAAArR,KAAAiS,iBAAAhB,EAAAC,GAAA,GACArH,EAAA7J,KAAA0O,SAAAkB,aAAAyB,GACA,OAAArR,KAAAwQ,YAAA3G,IAaA2E,EAAAzQ,UAAAmU,OAAA,SAAAjB,GAGA,IAFA,IAAAtO,EAAA3C,KACAkR,EAAA,GACAC,EAAA,EAAwBA,EAAA9O,UAAAlF,OAAuBgU,IAC/CD,EAAAC,EAAA,GAAA9O,UAAA8O,GAEA,IAAAE,EAAArR,KAAAiS,iBAAAhB,EAAAC,GAAA,GACAxP,EAAA,IAAAwB,QAAA,SAAAC,EAAAC,GAEAT,EAAAyM,UAAAiC,EAAAM,cAAA,SAAAE,EAAA5N,GACAA,EACAb,EAAAa,GAGA4N,IAEAA,EAAAvS,OAAiDuI,EAAWiK,WAC5DD,EAAA5N,MACAb,EAAA,IAAAhG,MAAAyU,EAAA5N,QAGAd,EAAA0O,EAAAjR,QAIAwC,EAAA,IAAAhG,MAAA,4BAAAyU,EAAAvS,SAIA,IAAAuK,EAAAlH,EAAA+L,SAAAkB,aAAAyB,GACA1O,EAAA6N,YAAA3G,GACAmI,MAAA,SAAA/E,GACA7J,EAAA6J,UAEAtK,EAAAyM,UAAAiC,EAAAM,kBAGA,OAAAjQ,GAOA8M,EAAAzQ,UAAA6K,GAAA,SAAAqI,EAAAkB,GACAlB,GAAAkB,IAGAlB,IAAAmB,cACApS,KAAAqP,QAAA4B,KACAjR,KAAAqP,QAAA4B,GAAA,KAGA,IAAAjR,KAAAqP,QAAA4B,GAAApS,QAAAsT,IAGAnS,KAAAqP,QAAA4B,GAAA/C,KAAAiE,KAEA3D,EAAAzQ,UAAAsU,IAAA,SAAApB,EAAAlN,GACA,GAAAkN,EAAA,CAGAA,IAAAmB,cACA,IAAAE,EAAAtS,KAAAqP,QAAA4B,GACA,GAAAqB,EAGA,GAAAvO,EAAA,CACA,IAAAwO,EAAAD,EAAAzT,QAAAkF,IACA,IAAAwO,IACAD,EAAAE,OAAAD,EAAA,GACA,IAAAD,EAAAnV,eACA6C,KAAAqP,QAAA4B,gBAKAjR,KAAAqP,QAAA4B,KAOAzC,EAAAzQ,UAAAmR,QAAA,SAAA/F,GACAA,GACAnJ,KAAAsP,gBAAApB,KAAA/E,IAGAqF,EAAAzQ,UAAAkR,oBAAA,SAAA5Q,GAOA,GANA2B,KAAA0Q,iBACA1Q,KAAAwP,4BACAnR,EAAA2B,KAAAyS,yBAAApU,GACA2B,KAAAwP,2BAAA,GAGAnR,EAGA,IADA,IAAAhB,EAAA2C,KAAA0O,SAAAgE,cAAArU,EAAA2B,KAAA0C,QACAyO,EAAA,EAAAwB,EAAAtV,EAAmD8T,EAAAwB,EAAAxV,OAAwBgU,IAAA,CAC3E,IAAAtH,EAAA8I,EAAAxB,GACA,OAAAtH,EAAAvK,MACA,KAAyBuI,EAAW+K,WACpC5S,KAAA6S,mBAAAhJ,GACA,MACA,KAAyBhC,EAAWiL,WACpC,KAAyBjL,EAAWiK,WACpC,IAAA3I,EAAAnJ,KAAAoP,UAAAvF,EAAA8H,cACA,MAAAxI,IACAU,EAAAvK,OAAiDuI,EAAWiK,mBAC5D9R,KAAAoP,UAAAvF,EAAA8H,cAEAxI,EAAAU,IAEA,MACA,KAAyBhC,EAAWgI,KAEpC,MACA,KAAyBhI,EAAWkL,MACpC/S,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQuE,YAAA,uCAGhDzQ,KAAAyO,WAAAqC,KAAAjH,EAAA5F,MAAA,IAAA7G,MAAA,sCAAAyM,EAAA5F,YAAA/D,GACA,MACA,QACAF,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ5H,QAAA,yBAAAuF,EAAAvK,KAAA,KAChD,OAIAU,KAAA2Q,sBAEAnC,EAAAzQ,UAAA0U,yBAAA,SAAApU,GACA,IAAA6R,EACA5R,EACAE,EACA,IACA0R,EAAAlQ,KAAA+O,kBAAA3Q,uBAAAC,GAAAG,EAAA0R,EAAA,GAAA5R,EAAA4R,EAAA,GAEA,MAAAjD,GACA,IAAApD,EAAA,qCAAAoD,EACAjN,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ9O,MAAAyM,GACpC,IAAA5F,EAAA,IAAA7G,MAAAyM,GAKA,MAFA7J,KAAAyO,WAAAqC,KAAA7M,GACAjE,KAAAsQ,kBAAArM,GACAA,EAEA,GAAA3F,EAAA2F,MAAA,CACA4F,EAAA,oCAAAvL,EAAA2F,MAMA,MALAjE,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ9O,MAAAyM,GACpC7J,KAAAsQ,kBAAAzG,GAGA7J,KAAAyO,WAAAqC,KAAA,IAAA1T,MAAAyM,IACA,IAAAzM,MAAAyM,GAMA,OAHA7J,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQkE,MAAA,8BAEpCpQ,KAAAqQ,oBACA7R,GAEAgQ,EAAAzQ,UAAA6S,uBAAA,WACA,IAAAjO,EAAA3C,KACAA,KAAA+Q,mBACA/Q,KAAAgT,iBAAAC,WAAA,WAAwD,OAAAxG,EAAA9J,OAAA,oBAExD,OAAAyK,EAAApN,KAAA,SAAAkT,GACA,OAAAA,EAAAjK,OACA,OACA,GAAAjJ,KAAAyP,kBAAAjD,EAAAqE,UAAA,YACAqC,EAAAjK,MAAA,EACA,OAEA,OADAiK,EAAA1F,KAAAU,KAAA,UACA,GAAAlO,KAAAwQ,YAAAxQ,KAAA2P,oBACA,OAEA,OADAuD,EAAA3F,OACA,MACA,OAKA,OAJA2F,EAAA3F,OAGAvN,KAAA+Q,mBACA,MACA,uBAGa/Q,KAAA8O,kCAEbN,EAAAzQ,UAAA4S,mBAAA,WACA,IAAAhO,EAAA3C,KACAA,KAAAyO,WAAA0E,UAAAnT,KAAAyO,WAAA0E,SAAAC,oBAEApT,KAAAqT,cAAAJ,WAAA,WAAyD,OAAAtQ,EAAA2Q,iBAAgCtT,KAAA6O,+BAGzFL,EAAAzQ,UAAAuV,cAAA,WAIAtT,KAAAyO,WAAAqC,KAAA,IAAA1T,MAAA,yEAEAoR,EAAAzQ,UAAA8U,mBAAA,SAAAU,GACA,IAAA5Q,EAAA3C,KACAqP,EAAArP,KAAAqP,QAAAkE,EAAAC,OAAApB,eACA,GAAA/C,GAEA,GADAA,EAAAzD,QAAA,SAAA6H,GAA0C,OAAAA,EAAA1U,MAAA4D,EAAA4Q,EAAAlR,aAC1CkR,EAAA5B,aAAA,CAEA,IAAA9H,EAAA,qFACA7J,KAAA0C,OAAA2B,IAAgC6H,EAAA,KAAQ9O,MAAAyM,GAGxC7J,KAAAyO,WAAAqC,KAAA,IAAA1T,MAAAyM,UAIA7J,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ5H,QAAA,mCAAAiP,EAAAC,OAAA,aAGpChF,EAAAzQ,UAAAoR,iBAAA,SAAAlL,GACA,IAAAtB,EAAA3C,KACAoP,EAAApP,KAAAoP,UACApP,KAAAoP,UAAA,GACApP,KAAAyP,gBAAAjD,EAAAkD,aAGA1P,KAAAsQ,mBACAtQ,KAAAsQ,kBAAArM,GAEAxF,OAAAyH,KAAAkJ,GACAxD,QAAA,SAAA/F,GACA,IAAAsD,EAAAiG,EAAAvJ,GACAsD,EAAA,KAAAlF,GAAA,IAAA7G,MAAA,0DAEA4C,KAAA0Q,iBACA1Q,KAAA+Q,mBACA/Q,KAAAsP,gBAAA1D,QAAA,SAAA8H,GAAmD,OAAAA,EAAA3U,MAAA4D,EAAA,CAAAsB,OAEnDuK,EAAAzQ,UAAAgT,iBAAA,WACA/Q,KAAAgT,kBACAW,aAAA3T,KAAAgT,mBAGAxE,EAAAzQ,UAAA2S,eAAA,WACA1Q,KAAAqT,eACAM,aAAA3T,KAAAqT,gBAGA7E,EAAAzQ,UAAAkU,iBAAA,SAAAhB,EAAAC,EAAA0C,GACA,GAAAA,EACA,OACAvR,UAAA6O,EACAsC,OAAAvC,EACA3R,KAAsBuI,EAAW+K,YAIjC,IAAArD,EAAAvP,KAAAuP,GAEA,OADAvP,KAAAuP,KACA,CACAlN,UAAA6O,EACAS,aAAApC,EAAAsE,WACAL,OAAAvC,EACA3R,KAAsBuI,EAAW+K,aAIjCpE,EAAAzQ,UAAAuT,uBAAA,SAAAL,EAAAC,GACA,IAAA3B,EAAAvP,KAAAuP,GAEA,OADAvP,KAAAuP,KACA,CACAlN,UAAA6O,EACAS,aAAApC,EAAAsE,WACAL,OAAAvC,EACA3R,KAAkBuI,EAAWiM,mBAG7BtF,EAAAzQ,UAAA2T,uBAAA,SAAAnC,GACA,OACAoC,aAAApC,EACAjQ,KAAkBuI,EAAWkM,mBAG7BvF,EA/ciB,IC/CjB,SAAAH,GAEAA,IAAA,kBAEAA,IAAA,8BAEAA,IAAA,0CAEAA,IAAA,iCARA,CASCA,MAAA,KAGD,SAAAC,GAEAA,IAAA,kBAEAA,IAAA,sBAJA,CAKCA,MAAA,KCfD,IAAA0F,EAAA,WACA,SAAAA,IACAhU,KAAAiU,WAAA,EACAjU,KAAAmE,QAAA,KAwBA,OAtBA6P,EAAAjW,UAAA0G,MAAA,WACAzE,KAAAiU,YACAjU,KAAAiU,WAAA,EACAjU,KAAAmE,SACAnE,KAAAmE,YAIA1F,OAAAqR,eAAAkE,EAAAjW,UAAA,UACAgI,IAAA,WACA,OAAA/F,MAEA+P,YAAA,EACAjK,cAAA,IAEArH,OAAAqR,eAAAkE,EAAAjW,UAAA,WACAgI,IAAA,WACA,OAAA/F,KAAAiU,WAEAlE,YAAA,EACAjK,cAAA,IAEAkO,EA3BA,GCLIE,EAAyB,SAAAxH,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGImH,EAA2B,SAAAzH,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAUxC8T,EAAoB,WACxB,SAAAC,EAAA9H,EAAA+H,EAAA5R,EAAA6R,GACAvU,KAAAuM,aACAvM,KAAAsU,qBACAtU,KAAA0C,SACA1C,KAAAwU,UAAA,IAA6BR,EAC7BhU,KAAAuU,oBACAvU,KAAAyU,SAAA,EACAzU,KAAAgP,UAAA,KACAhP,KAAAkP,QAAA,KAkOA,OAhOAzQ,OAAAqR,eAAAuE,EAAAtW,UAAA,eAEAgI,IAAA,WACA,OAAA/F,KAAAwU,UAAAlJ,SAEAyE,YAAA,EACAjK,cAAA,IAEAuO,EAAAtW,UAAA2W,QAAA,SAAAjR,EAAA8M,GACA,OAAe2D,EAASlU,UAAA,oBACxB,IAAA2U,EAAAC,EAAAC,EAAAxV,EACA,OAAmB8U,EAAWnU,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OAOA,GANwB0F,EAAA,KAAGC,WAAAnL,EAAA,OACHkL,EAAA,KAAGC,WAAA2B,EAAA,kBACH5B,EAAA,KAAGmG,KAAAvE,EAAsBjC,EAAc,kBAC/DtO,KAAAyD,MACAzD,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,uCAEhDxE,IAA+CjC,EAAc0G,QAC7D,qBAAA1S,gBAAA,sBAAAA,gBAAAsB,aACA,UAAAxG,MAAA,8FAUA,OARAuX,EAAA,CACAzQ,YAAAlE,KAAAwU,UAAAS,OACApR,QAAA,GACAG,QAAA,KAEAuM,IAA+CjC,EAAc0G,SAC7DL,EAAA/Q,aAAA,eAEA,GAAA5D,KAAAkV,kBACA,OAKA,OAJAN,EAAA1E,EAAA3C,OACAvN,KAAAmV,kBAAAR,EAAAC,GACAC,EAAApR,EAAA,MAAA2R,KAAAC,MACArV,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,oCAAAF,EAAA,KAChD,GAAA7U,KAAAuM,WAAAxG,IAAA8O,EAAAF,IACA,OAYA,OAXAtV,EAAA6Q,EAAA3C,OACA,MAAAlO,EAAAkF,YACAvE,KAAA0C,OAAA2B,IAA4C6H,EAAA,KAAQ9O,MAAA,qDAAAiC,EAAAkF,WAAA,KAEpDvE,KAAAsV,WAAA,IAAkD/J,EAAA,KAASlM,EAAAoI,YAAA,GAAApI,EAAAkF,YAC3DvE,KAAAyU,SAAA,GAGAzU,KAAAyU,SAAA,EAEAzU,KAAAuV,UAAAvV,KAAAwV,KAAAxV,KAAAyD,IAAAkR,GACA,UAKAN,EAAAtW,UAAAmX,eAAA,WACA,OAAehB,EAASlU,UAAA,oBACxB,OAAmBmU,EAAWnU,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OACA,OAAAjJ,KAAAsU,mBACA,GAAAtU,KAAAsU,sBADA,MAEA,gBAAApE,EAAA3C,QACA,4BAKA8G,EAAAtW,UAAAoX,kBAAA,SAAArS,EAAA8R,GACA9R,EAAAe,UACAf,EAAAe,QAAA,IAEA+Q,EAEA9R,EAAAe,QAAA,2BAAA+Q,EAIA9R,EAAAe,QAAA,yBAEAf,EAAAe,QAAA,kBAGAwQ,EAAAtW,UAAAyX,KAAA,SAAA/R,EAAAkR,GACA,OAAeT,EAASlU,UAAA,oBACxB,IAAA4U,EAAAC,EAAAxV,EAAAoW,EACA,OAAmBtB,EAAWnU,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OACAiH,EAAA1C,KAAAU,KAAA,UACAgC,EAAAjH,MAAA,EACA,OACA,OAAAjJ,KAAAyU,QACA,GAAAzU,KAAAkV,kBADA,MAEA,OACAN,EAAA1E,EAAA3C,OACAvN,KAAAmV,kBAAAR,EAAAC,GACA1E,EAAAjH,MAAA,EACA,OAIA,OAHAiH,EAAA1C,KAAAU,KAAA,UACA2G,EAAApR,EAAA,MAAA2R,KAAAC,MACArV,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,oCAAAF,EAAA,KAChD,GAAA7U,KAAAuM,WAAAxG,IAAA8O,EAAAF,IACA,OAyBA,OAxBAtV,EAAA6Q,EAAA3C,OACA,MAAAlO,EAAAkF,YACAvE,KAAA0C,OAAA2B,IAA4C6H,EAAA,KAAQuE,YAAA,sDACpDzQ,KAAAyU,SAAA,GAEA,MAAApV,EAAAkF,YACAvE,KAAA0C,OAAA2B,IAA4C6H,EAAA,KAAQ9O,MAAA,qDAAAiC,EAAAkF,WAAA,KAEpDvE,KAAAsV,WAAA,IAAkD/J,EAAA,KAASlM,EAAAoI,YAAA,GAAApI,EAAAkF,YAC3DvE,KAAAyU,SAAA,GAIApV,EAAAiE,SACAtD,KAAA0C,OAAA2B,IAAgD6H,EAAA,KAAQ6I,MAAA,0CAAoDtW,OAAAkQ,EAAA,KAAAlQ,CAAaY,EAAAiE,QAAAtD,KAAAuU,mBAAA,KACzHvU,KAAAgP,WACAhP,KAAAgP,UAAA3P,EAAAiE,UAKAtD,KAAA0C,OAAA2B,IAAgD6H,EAAA,KAAQ6I,MAAA,sDAGxD,MACA,OAiBA,OAhBAU,EAAAvF,EAAA3C,OACAvN,KAAAyU,QAKAgB,aAA+ClK,EAAA,KAE/CvL,KAAA0C,OAAA2B,IAAgD6H,EAAA,KAAQ6I,MAAA,uDAIxD/U,KAAAsV,WAAAG,EACAzV,KAAAyU,SAAA,GAVAzU,KAAA0C,OAAA2B,IAA4C6H,EAAA,KAAQ6I,MAAA,wDAAAU,EAAA5L,SAapD,MACA,mBACA,mBACA,OAOA,OANA7J,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,6CAGhD/U,KAAA0V,aACA1V,KAAA2V,eAEA,IACA,uBAKAtB,EAAAtW,UAAAiF,KAAA,SAAA3E,GACA,OAAe6V,EAASlU,UAAA,oBACxB,OAAmBmU,EAAWnU,KAAA,SAAAkQ,GAC9B,OAAAlQ,KAAAyU,QAGA,GAAsChW,OAAAkQ,EAAA,KAAAlQ,CAAWuB,KAAA0C,OAAA,cAAA1C,KAAAuM,WAAAvM,KAAAyD,IAAAzD,KAAAsU,mBAAAjW,EAAA2B,KAAAuU,oBAFjD,GAAArR,QAAAE,OAAA,IAAAhG,MAAA,uDAMAiX,EAAAtW,UAAA+S,KAAA,WACA,OAAeoD,EAASlU,UAAA,oBACxB,IAAA4V,EAAAhB,EACA,OAAmBT,EAAWnU,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OACAjJ,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,6CAEhD/U,KAAAyU,SAAA,EACAzU,KAAAwU,UAAA/P,QACAyL,EAAAjH,MAAA,EACA,OAEA,OADAiH,EAAA1C,KAAAU,KAAA,UACA,GAAAlO,KAAAuV,WACA,OAOA,OANArF,EAAA3C,OAEAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,qDAAA/U,KAAAyD,IAAA,KAChDmS,EAAA,CACA/R,QAAA,IAEA,GAAA7D,KAAAkV,kBACA,OAGA,OAFAN,EAAA1E,EAAA3C,OACAvN,KAAAmV,kBAAAS,EAAAhB,GACA,GAAA5U,KAAAuM,WAAA3E,OAAA5H,KAAAyD,IAAAmS,IACA,OAGA,OAFA1F,EAAA3C,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,gDAChD,MACA,OAKA,OAJA/U,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,0CAGhD/U,KAAA2V,eACA,IACA,uBAKAtB,EAAAtW,UAAA4X,aAAA,WACA,GAAA3V,KAAAkP,QAAA,CACA,IAAA2G,EAAA,gDACA7V,KAAAsV,aACAO,GAAA,WAAA7V,KAAAsV,YAEAtV,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ6I,MAAAc,GACpC7V,KAAAkP,QAAAlP,KAAAsV,cAGAjB,EA3OwB,GC1CpByB,EAAyB,SAAApJ,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGI+I,EAA2B,SAAArJ,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAOxC0V,EAAyB,WAC7B,SAAAC,EAAA1J,EAAA+H,EAAA5R,EAAA6R,EAAA2B,GACAlW,KAAAuM,aACAvM,KAAAsU,qBACAtU,KAAA0C,SACA1C,KAAAuU,oBACAvU,KAAAkW,yBACAlW,KAAAgP,UAAA,KACAhP,KAAAkP,QAAA,KAmGA,OAjGA+G,EAAAlY,UAAA2W,QAAA,SAAAjR,EAAA8M,GACA,OAAeuF,EAAS9V,UAAA,oBACxB,IAAA4U,EACAjS,EAAA3C,KACA,OAAmB+V,EAAW/V,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OAOA,OANwB0F,EAAA,KAAGC,WAAAnL,EAAA,OACHkL,EAAA,KAAGC,WAAA2B,EAAA,kBACH5B,EAAA,KAAGmG,KAAAvE,EAAsBjC,EAAc,kBAC/DtO,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,+BAEhD/U,KAAAyD,MACAzD,KAAAsU,mBACA,GAAAtU,KAAAsU,sBADA,MAEA,OACAM,EAAA1E,EAAA3C,OACAqH,IACAnR,MAAA5E,QAAA,gCAAAsX,mBAAAvB,IAEA1E,EAAAjH,MAAA,EACA,oBAAA/F,QAAA,SAAAC,EAAAC,GACA,IAAAgT,GAAA,EACA,GAAA7F,IAAmDjC,EAAc+H,KAAjE,CAIA,IAAAC,EACA,wBAAAC,OACAD,EAAA,IAAA3T,EAAAuT,uBAAAzS,EAAA,CAAqFiI,iBAAA,QAErF,CAEA,IAAA8K,EAAA7T,EAAA4J,WAAA7H,gBAAAjB,GACA6S,EAAA,IAAA3T,EAAAuT,uBAAAzS,EAAA,CAAqFiI,iBAAA,EAAA7H,QAAA,CAAkC4S,OAAAD,KAEvH,IACAF,EAAAI,UAAA,SAAAzJ,GACA,GAAAtK,EAAAqM,UACA,IACArM,EAAAD,OAAA2B,IAA6D6H,EAAA,KAAQ6I,MAAA,kCAA4CtW,OAAAkQ,EAAA,KAAAlQ,CAAawO,EAAA5O,KAAAsE,EAAA4R,mBAAA,KAC9H5R,EAAAqM,UAAA/B,EAAA5O,MAEA,MAAA4F,GAEA,YADAtB,EAAAgU,MAAA1S,KAKAqS,EAAArK,QAAA,SAAAgB,GACA,IAAAhJ,EAAA,IAAA7G,MAAA6P,EAAA5O,MAAA,kBACA+X,EACAzT,EAAAgU,MAAA1S,GAGAb,EAAAa,IAGAqS,EAAAM,OAAA,WACAjU,EAAAD,OAAA2B,IAAqD6H,EAAA,KAAQuE,YAAA,oBAAA9N,EAAAc,KAC7Dd,EAAA2T,cACAF,GAAA,EACAjT,KAGA,MAAA8J,GAEA,YADA7J,EAAA6J,SA1CA7J,EAAA,IAAAhG,MAAA,uFAkDA6Y,EAAAlY,UAAAiF,KAAA,SAAA3E,GACA,OAAeyX,EAAS9V,UAAA,oBACxB,OAAmB+V,EAAW/V,KAAA,SAAAkQ,GAC9B,OAAAlQ,KAAAsW,YAGA,GAAsC7X,OAAAkQ,EAAA,KAAAlQ,CAAWuB,KAAA0C,OAAA,MAAA1C,KAAAuM,WAAAvM,KAAAyD,IAAAzD,KAAAsU,mBAAAjW,EAAA2B,KAAAuU,oBAFjD,GAAArR,QAAAE,OAAA,IAAAhG,MAAA,uDAMA6Y,EAAAlY,UAAA+S,KAAA,WAEA,OADA9Q,KAAA2W,QACAzT,QAAAC,WAEA8S,EAAAlY,UAAA4Y,MAAA,SAAA1J,GACAjN,KAAAsW,cACAtW,KAAAsW,YAAAK,QACA3W,KAAAsW,iBAAApW,EACAF,KAAAkP,SACAlP,KAAAkP,QAAAjC,KAIAgJ,EA3G6B,GCvCzBY,EAAyB,SAAAnK,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGI8J,EAA2B,SAAApK,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAOxCyW,EAAkB,WACtB,SAAAC,EAAAzK,EAAA+H,EAAA5R,EAAA6R,EAAA0C,GACAjX,KAAA0C,SACA1C,KAAAsU,qBACAtU,KAAAuU,oBACAvU,KAAAiX,uBACAjX,KAAAuM,aACAvM,KAAAgP,UAAA,KACAhP,KAAAkP,QAAA,KAoGA,OAlGA8H,EAAAjZ,UAAA2W,QAAA,SAAAjR,EAAA8M,GACA,OAAesG,EAAS7W,UAAA,oBACxB,IAAA4U,EACAjS,EAAA3C,KACA,OAAmB8W,EAAW9W,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OAKA,OAJwB0F,EAAA,KAAGC,WAAAnL,EAAA,OACHkL,EAAA,KAAGC,WAAA2B,EAAA,kBACH5B,EAAA,KAAGmG,KAAAvE,EAAsBjC,EAAc,kBAC/DtO,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ6I,MAAA,sCAChD/U,KAAAsU,mBACA,GAAAtU,KAAAsU,sBADA,MAEA,OACAM,EAAA1E,EAAA3C,OACAqH,IACAnR,MAAA5E,QAAA,gCAAAsX,mBAAAvB,IAEA1E,EAAAjH,MAAA,EACA,oBAAA/F,QAAA,SAAAC,EAAAC,GAEA,IAAA8T,EADAzT,IAAA0T,QAAA,cAEA,IAAAX,EAAA7T,EAAA4J,WAAA7H,gBAAAjB,GACA,qBAAA8S,QAAAC,IAEAU,EAAA,IAAAvU,EAAAsU,qBAAAxT,OAAAvD,EAAA,CACA2D,QAAA,CACA4S,OAAA,GAAAD,MAIAU,IAEAA,EAAA,IAAAvU,EAAAsU,qBAAAxT,IAEA8M,IAAmDjC,EAAc0G,SACjEkC,EAAAE,WAAA,eAGAF,EAAAN,OAAA,SAAAS,GACA1U,EAAAD,OAAA2B,IAAiD6H,EAAA,KAAQuE,YAAA,0BAAAhN,EAAA,KACzDd,EAAAuU,YACA/T,KAEA+T,EAAAjL,QAAA,SAAAqL,GACA,IAAArT,EAAA,KAEA,qBAAAsT,YAAAD,aAAAC,aACAtT,EAAAqT,EAAArT,OAEAb,EAAAa,IAEAiT,EAAAR,UAAA,SAAA7M,GACAlH,EAAAD,OAAA2B,IAAiD6H,EAAA,KAAQ6I,MAAA,yCAAmDtW,OAAAkQ,EAAA,KAAAlQ,CAAaoL,EAAAxL,KAAAsE,EAAA4R,mBAAA,KACzH5R,EAAAqM,WACArM,EAAAqM,UAAAnF,EAAAxL,OAGA6Y,EAAAhI,QAAA,SAAAoI,GAAkE,OAAA3U,EAAAgU,MAAAW,aAMlEN,EAAAjZ,UAAAiF,KAAA,SAAA3E,GACA,OAAA2B,KAAAkX,WAAAlX,KAAAkX,UAAAM,aAAAxX,KAAAiX,qBAAAQ,MACAzX,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ6I,MAAA,wCAAkDtW,OAAAkQ,EAAA,KAAAlQ,CAAaJ,EAAA2B,KAAAuU,mBAAA,KACnGvU,KAAAkX,UAAAlU,KAAA3E,GACA6E,QAAAC,WAEAD,QAAAE,OAAA,uCAEA4T,EAAAjZ,UAAA+S,KAAA,WAYA,OAXA9Q,KAAAkX,YAEAlX,KAAAkX,UAAAhI,QAAA,aACAlP,KAAAkX,UAAAR,UAAA,aACA1W,KAAAkX,UAAAjL,QAAA,aACAjM,KAAAkX,UAAAP,QACA3W,KAAAkX,eAAAhX,EAGAF,KAAA2W,WAAAzW,IAEAgD,QAAAC,WAEA6T,EAAAjZ,UAAA4Y,MAAA,SAAAW,GAEAtX,KAAA0C,OAAA2B,IAAwB6H,EAAA,KAAQ6I,MAAA,yCAChC/U,KAAAkP,WACAoI,IAAA,IAAAA,EAAAI,UAAA,MAAAJ,EAAAlT,KAIApE,KAAAkP,UAHAlP,KAAAkP,QAAA,IAAA9R,MAAA,sCAAAka,EAAAlT,KAAA,KAAAkT,EAAAK,OAAA,SAOAX,EA5GsB,GCvClBY,EAAyB,SAAAlL,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGI6K,EAA2B,SAAAnL,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAU5CwX,EAAA,IACAC,EAAA,KACAC,EAAA,KACA,wBAAAzB,OAAmD,CAGnD,IAAAhU,EAAkEC,QAClEuV,EAAAxV,EAAA,MACAyV,EAAAzV,EAAA,eAGA,IAAI0V,EAAc,WAClB,SAAAC,EAAAzU,EAAAiE,QACA,IAAAA,IAAiCA,EAAA,IACjC1H,KAAAmT,SAAA,GACQxE,EAAA,KAAGC,WAAAnL,EAAA,OACXzD,KAAA0C,OAAsBjE,OAAAkQ,EAAA,KAAAlQ,CAAYiJ,EAAAhF,QAClC1C,KAAAmY,QAAAnY,KAAAoY,WAAA3U,GACAiE,KAAA,GACAA,EAAA6M,kBAAA7M,EAAA6M,oBAAA,EACA,IAAA8D,EAAA,qBAAA9B,OACA8B,GAAA,qBAAAC,WAAA5Q,EAAA4Q,UAGAD,IAAA3Q,EAAA4Q,WACAP,IACArQ,EAAA4Q,UAAAP,GAJArQ,EAAA4Q,oBAOAD,GAAA,qBAAAE,aAAA7Q,EAAA6Q,YAGAF,IAAA3Q,EAAA6Q,aACA,qBAAAP,IACAtQ,EAAA6Q,YAAAP,GAJAtQ,EAAA6Q,wBAOAvY,KAAAuM,WAAA7E,EAAA6E,YAAA,IAAoDF,EAAiBrM,KAAA0C,QACrE1C,KAAAyP,gBAAA,EACAzP,KAAA0H,UACA1H,KAAAgP,UAAA,KACAhP,KAAAkP,QAAA,KAgWA,OA9VAgJ,EAAAna,UAAAiS,MAAA,SAAAO,GAIA,OAHAA,KAA2CjC,EAAc0G,OACjDrG,EAAA,KAAGmG,KAAAvE,EAAsBjC,EAAc,kBAC/CtO,KAAA0C,OAAA2B,IAAwB6H,EAAA,KAAQkE,MAAA,6CAAuD9B,EAAciC,GAAA,MACrG,IAAAvQ,KAAAyP,gBACAvM,QAAAE,OAAA,IAAAhG,MAAA,wEAEA4C,KAAAyP,gBAAA,EACAzP,KAAAwY,aAAAxY,KAAAyY,cAAAlI,GACAvQ,KAAAwY,eAEAN,EAAAna,UAAAiF,KAAA,SAAA3E,GACA,OAAA2B,KAAAyP,gBACA,UAAArS,MAAA,uEAGA,OAAA4C,KAAA0Y,UAAA1V,KAAA3E,IAEA6Z,EAAAna,UAAA+S,KAAA,SAAA7M,GACA,OAAe2T,EAAS5X,UAAA,oBAExB,OAAmB6X,EAAW7X,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OACAjJ,KAAAyP,gBAAA,EAIAzP,KAAA2Y,UAAA1U,EACAiM,EAAAjH,MAAA,EACA,OAEA,OADAiH,EAAA1C,KAAAU,KAAA,UACA,GAAAlO,KAAAwY,cACA,OAEA,OADAtI,EAAA3C,OACA,MACA,OAEA,OADA2C,EAAA3C,OACA,MACA,OACA,OAAAvN,KAAA0Y,UACA,GAAA1Y,KAAA0Y,UAAA5H,QADA,MAEA,OACAZ,EAAA3C,OACAvN,KAAA0Y,eAAAxY,EACAgQ,EAAAjH,MAAA,EACA,uBAKAiP,EAAAna,UAAA0a,cAAA,SAAAlI,GACA,OAAeqH,EAAS5X,UAAA,oBACxB,IAAAyD,EAAAmV,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACAtW,EAAA3C,KACA,OAAmB6X,EAAW7X,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OACAxF,EAAAzD,KAAAmY,QACAnY,KAAAsU,mBAAAtU,KAAA0H,QAAA4M,mBACApE,EAAAjH,MAAA,EACA,OAEA,OADAiH,EAAA1C,KAAAU,KAAA,YACAlO,KAAA0H,QAAAwR,gBACAlZ,KAAA0H,QAAAgR,YAAyDrK,EAAiB8K,WAAA,OAE1EnZ,KAAA0Y,UAAA1Y,KAAAoZ,mBAAiE/K,EAAiB8K,YAGlF,GAAAnZ,KAAA0Y,UAAAhE,QAAAjR,EAAA8M,KANA,MAOA,OAIA,OADAL,EAAA3C,OACA,MACA,aAAAnQ,MAAA,gFACA,oBACA,OACAwb,EAAA,KACAC,EAAA,EACAC,EAAA,WACA,IAAAO,EACA,OAAmCxB,EAAW7X,KAAA,SAAAkQ,GAC9C,OAAAA,EAAAjH,OACA,gBAAA8P,EAAAO,uBAAA7V,IACA,OAGA,GAFAmV,EAAA1I,EAAA3C,OAEA,IAAAwL,EAAAtJ,gBACA,UAAmElP,WAAA,IAEnE,GAAAqY,EAAA3U,MACA,MAAA7G,MAAAwb,EAAA3U,OAEA,GAAA2U,EAAAW,gBACA,MAAAnc,MAAA,gMAUA,OARAwb,EAAAnV,MACAA,EAAAmV,EAAAnV,KAEAmV,EAAAY,cACAH,EAAAT,EAAAY,YACAT,EAAAzE,mBAAA,WAAqF,OAAA+E,IAErFR,IACA,QAIAE,EAAA/Y,KACAkQ,EAAAjH,MAAA,EACA,gBAAA6P,KACA,OAEA,GADAE,EAAA9I,EAAA3C,OACA,kBAAAyL,EACA,SAAAA,EAAAzY,OACA2P,EAAAjH,MAAA,EACA,OACA,GAAA2P,EAAAnV,KAAAoV,EAAAf,EAAA,YACA5H,EAAAjH,MAAA,EACA,OACA,GAAA4P,IAAAf,GAAAc,EAAAnV,IACA,MAAArG,MAAA,yCAEA,SAAA4C,KAAAyZ,gBAAAhW,EAAAzD,KAAA0H,QAAAgR,UAAAE,EAAArI,IACA,QACAL,EAAA3C,OACA2C,EAAAjH,MAAA,GACA,QASA,OARAjJ,KAAA0Y,qBAAsDtE,IACtDpU,KAAAmT,SAAAC,mBAAA,GAEApT,KAAA0Y,UAAA1J,UAAAhP,KAAAgP,UACAhP,KAAA0Y,UAAAxJ,QAAA,SAAAjC,GAA+D,OAAAtK,EAAA+W,eAAAzM,IAG/DjN,KAAA2Z,YAAA,KACA,OACA,QAKA,MAJAV,EAAA/I,EAAA3C,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ9O,MAAA,mCAAA6b,GAChDjZ,KAAAyP,gBAAA,EACAzP,KAAA0Y,eAAAxY,EACA+Y,EACA,wBAKAf,EAAAna,UAAAub,uBAAA,SAAA7V,GACA,OAAemU,EAAS5X,UAAA,oBACxB,IAAAkQ,EAAArM,EAAA+Q,EAAAgF,EAAAva,EAAAwa,EACA,OAAmBhC,EAAW7X,KAAA,SAAAkT,GAC9B,OAAAA,EAAAjK,OACA,OACA,OAAAjJ,KAAAsU,mBACA,GAAAtU,KAAAsU,sBADA,MAEA,OACAM,EAAA1B,EAAA3F,OACAqH,IACA1E,EAAA,GACAA,EAAA,2BAAA0E,EADA/Q,EAEAqM,GAEAgD,EAAAjK,MAAA,EACA,OACA2Q,EAAA5Z,KAAA8Z,oBAAArW,GACAzD,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,gCAAAwJ,EAAA,KAChD1G,EAAAjK,MAAA,EACA,OAEA,OADAiK,EAAA1F,KAAAU,KAAA,UACA,GAAAlO,KAAAuM,WAAA5E,KAAAiS,EAAA,CACAtW,QAAA,GACAO,aAEA,OAEA,GADAxE,EAAA6T,EAAA3F,OACA,MAAAlO,EAAAkF,WACA,MAAAnH,MAAA,kDAAAiC,EAAAkF,YAEA,SAAArG,KAAAjB,MAAAoC,EAAAiE,UACA,OAGA,MAFAuW,EAAA3G,EAAA3F,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ9O,MAAA,mDAAAyc,GAChDA,EACA,uBAKA3B,EAAAna,UAAAgc,iBAAA,SAAAtW,EAAAuW,GACA,OAAAA,EAGAvW,IAAA,IAAAA,EAAA5E,QAAA,oBAAAmb,EAFAvW,GAIAyU,EAAAna,UAAA0b,gBAAA,SAAAhW,EAAAwW,EAAArB,EAAAsB,GACA,OAAetC,EAAS5X,UAAA,oBACxB,IAAAma,EAAAC,EAAAjJ,EAAAkJ,EAAAC,EAAA5B,EAAA6B,EACA,OAAmB1C,EAAW7X,KAAA,SAAAkQ,GAC9B,OAAAA,EAAAjH,OACA,OAEA,OADAkR,EAAAna,KAAA+Z,iBAAAtW,EAAAmV,EAAAoB,cACAha,KAAAwa,aAAAP,IACAja,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,2EAChDpQ,KAAA0Y,UAAAuB,EACA,GAAAja,KAAA0Y,UAAAhE,QAAAyF,EAAAD,KAHA,MAIA,OAKA,OAJAhK,EAAA3C,OAGAvN,KAAA2Z,YAAA,KACA,IACA,OACAS,EAAAxB,EAAA6B,qBAAA,GACAtJ,EAAA,EAAAkJ,EAAAD,EACAlK,EAAAjH,MAAA,EACA,OACA,OAAAkI,EAAAkJ,EAAAld,QACAmd,EAAAD,EAAAlJ,GACAnR,KAAAyP,gBAAA,EACAiJ,EAAA1Y,KAAA0a,iBAAAJ,EAAAL,EAAAC,GACA,kBAAAxB,EAAA,OACA1Y,KAAA0Y,UAAA1Y,KAAAoZ,mBAAAV,GACAE,EAAAoB,aAAA,MACA,GAAAha,KAAAsZ,uBAAA7V,MAPA,MAQA,OACAmV,EAAA1I,EAAA3C,OACA4M,EAAAna,KAAA+Z,iBAAAtW,EAAAmV,EAAAoB,cACA9J,EAAAjH,MAAA,EACA,OAEA,OADAiH,EAAA1C,KAAAU,KAAA,UACA,GAAAlO,KAAA0Y,UAAAhE,QAAAyF,EAAAD,IACA,OAGA,OAFAhK,EAAA3C,OACAvN,KAAA2Z,YAAA,KACA,IACA,OAKA,OAJAY,EAAArK,EAAA3C,OACAvN,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQ9O,MAAA,kCAA4CiR,EAAiBqK,GAAA,MAAA6B,GAC7Gva,KAAAyP,gBAAA,EACAmJ,EAAAoB,kBAAA9Z,EACA,MACA,OAEA,OADAiR,IACA,MACA,iBAAA/T,MAAA,+DAKA8a,EAAAna,UAAAqb,mBAAA,SAAAV,GACA,OAAAA,GACA,KAAiBrK,EAAiB8K,WAClC,IAAAnZ,KAAA0H,QAAA4Q,UACA,UAAAlb,MAAA,qDAEA,WAA2B2Z,EAAkB/W,KAAAuM,WAAAvM,KAAAsU,mBAAAtU,KAAA0C,OAAA1C,KAAA0H,QAAA6M,oBAAA,EAAAvU,KAAA0H,QAAA4Q,WAC7C,KAAiBjK,EAAiBsM,iBAClC,IAAA3a,KAAA0H,QAAA6Q,YACA,UAAAnb,MAAA,uDAEA,WAA2B4Y,EAAyBhW,KAAAuM,WAAAvM,KAAAsU,mBAAAtU,KAAA0C,OAAA1C,KAAA0H,QAAA6M,oBAAA,EAAAvU,KAAA0H,QAAA6Q,aACpD,KAAiBlK,EAAiBuM,YAClC,WAA2BxG,EAAoBpU,KAAAuM,WAAAvM,KAAAsU,mBAAAtU,KAAA0C,OAAA1C,KAAA0H,QAAA6M,oBAAA,GAC/C,QACA,UAAAnX,MAAA,sBAAAsb,EAAA,OAGAR,EAAAna,UAAA2c,iBAAA,SAAAJ,EAAAL,EAAAC,GACA,IAAAxB,EAAwBrK,EAAiBiM,EAAA5B,WACzC,UAAAA,QAAAxY,IAAAwY,EACA1Y,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQkE,MAAA,uBAAAkK,EAAA5B,UAAA,qDAEpC,CACA,IAAAmC,EAAAP,EAAAO,gBAAAC,IAAA,SAAA5Y,GAA6E,OAAQoM,EAAcpM,KACnG,GAAA6Y,EAAAd,EAAAvB,GACA,GAAAmC,EAAAhc,QAAAqb,IAAA,GACA,IAAAxB,IAAuCrK,EAAiB8K,YAAAnZ,KAAA0H,QAAA4Q,aACxDI,IAAuCrK,EAAiBsM,kBAAA3a,KAAA0H,QAAA6Q,aAKxD,OADAvY,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,wBAAkC/B,EAAiBqK,GAAA,MACnGA,EAJA1Y,KAAA0C,OAAA2B,IAAwC6H,EAAA,KAAQkE,MAAA,uBAAiC/B,EAAiBqK,GAAA,4DAQlG1Y,KAAA0C,OAAA2B,IAAoC6H,EAAA,KAAQkE,MAAA,uBAAiC/B,EAAiBqK,GAAA,gEAAgFpK,EAAc4L,GAAA,WAI5Lla,KAAA0C,OAAA2B,IAAgC6H,EAAA,KAAQkE,MAAA,uBAAiC/B,EAAiBqK,GAAA,4CAG1F,aAEAR,EAAAna,UAAAyc,aAAA,SAAA9B,GACA,OAAAA,GAAA,iCAAAA,GAEAR,EAAAna,UAAA4b,YAAA,SAAApW,EAAAyX,GACA,OAAAhb,KAAAyP,kBAAAlM,IACAvD,KAAAyP,gBAAAuL,GACA,IAIA9C,EAAAna,UAAA2b,eAAA,SAAAzV,GACAjE,KAAA0Y,eAAAxY,EAEA+D,EAAAjE,KAAA2Y,WAAA1U,EACAA,EACAjE,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQ9O,MAAA,uCAAA6G,EAAA,MAGpCjE,KAAA0C,OAAA2B,IAA4B6H,EAAA,KAAQuE,YAAA,4BAEpCzQ,KAAAyP,gBAAA,EACAzP,KAAAkP,SACAlP,KAAAkP,QAAAjL,IAGAiU,EAAAna,UAAAqa,WAAA,SAAA3U,GAEA,OAAAA,EAAAwX,YAAA,mBAAAxX,EAAAwX,YAAA,aACA,OAAAxX,EAEA,wBAAA8S,yBAAA2E,SACA,UAAA9d,MAAA,mBAAAqG,EAAA,MAOA,IAAA0X,EAAA5E,OAAA2E,SAAAE,cAAA,KAGA,OAFAD,EAAAE,KAAA5X,EACAzD,KAAA0C,OAAA2B,IAAwB6H,EAAA,KAAQuE,YAAA,gBAAAhN,EAAA,SAAA0X,EAAAE,KAAA,MAChCF,EAAAE,MAEAnD,EAAAna,UAAA+b,oBAAA,SAAArW,GACA,IAAA5C,EAAA4C,EAAA5E,QAAA,KACA+a,EAAAnW,EAAArE,UAAA,OAAAyB,EAAA4C,EAAAtG,OAAA0D,GAMA,MALA,MAAA+Y,IAAAzc,OAAA,KACAyc,GAAA,KAEAA,GAAA,YACAA,IAAA,IAAA/Y,EAAA,GAAA4C,EAAArE,UAAAyB,GACA+Y,GAEA1B,EA9XkB,GAiYlB,SAAA6C,EAAAd,EAAAqB,GACA,OAAArB,GAAA,KAAAqB,EAAArB,+BClbAsB,EAAA,OAEIC,EAAe,WACnB,SAAAC,IAEAzb,KAAAkK,KAAAqR,EAEAvb,KAAAmQ,QAAA,EAEAnQ,KAAAuQ,eAA8BjC,EAAc+H,KAsF5C,OA/EAoF,EAAA1d,UAAA2U,cAAA,SAAAxV,EAAAwF,GAEA,qBAAAxF,EACA,UAAAE,MAAA,2DAEA,IAAAF,EACA,SAEA,OAAAwF,IACAA,EAAqBgZ,EAAA,KAAUzU,UAK/B,IAFA,IAAA5J,EAAuBR,EAAA,KAAiBI,MAAAC,GACxCye,EAAA,GACAxK,EAAA,EAAAwB,EAAAtV,EAA+C8T,EAAAwB,EAAAxV,OAAwBgU,IAAA,CACvE,IAAAtH,EAAA8I,EAAAxB,GACAyK,EAAA1d,KAAAjB,MAAA4M,GACA,qBAAA+R,EAAAtc,KACA,UAAAlC,MAAA,oBAEA,OAAAwe,EAAAtc,MACA,KAAqBuI,EAAW+K,WAChC5S,KAAA6b,oBAAAD,GACA,MACA,KAAqB/T,EAAWiL,WAChC9S,KAAA8b,oBAAAF,GACA,MACA,KAAqB/T,EAAWiK,WAChC9R,KAAA+b,oBAAAH,GACA,MACA,KAAqB/T,EAAWgI,KAEhC,MACA,KAAqBhI,EAAWkL,MAEhC,MACA,QAEArQ,EAAA2B,IAA+B6H,EAAA,KAAQuE,YAAA,yBAAAmL,EAAAtc,KAAA,cACvC,SAEAqc,EAAAzN,KAAA0N,GAEA,OAAAD,GAOAF,EAAA1d,UAAA6R,aAAA,SAAA/F,GACA,OAAehN,EAAA,KAAiBC,MAAAoB,KAAAC,UAAA0L,KAEhC4R,EAAA1d,UAAA8d,oBAAA,SAAAhS,GACA7J,KAAAgc,qBAAAnS,EAAA2J,OAAA,gDACAtT,IAAA2J,EAAA8H,cACA3R,KAAAgc,qBAAAnS,EAAA8H,aAAA,4CAGA8J,EAAA1d,UAAA+d,oBAAA,SAAAjS,GAEA,GADA7J,KAAAgc,qBAAAnS,EAAA8H,aAAA,gDACAzR,IAAA2J,EAAAJ,KACA,UAAArM,MAAA,4CAGAqe,EAAA1d,UAAAge,oBAAA,SAAAlS,GACA,GAAAA,EAAAjJ,QAAAiJ,EAAA5F,MACA,UAAA7G,MAAA,4CAEAyM,EAAAjJ,QAAAiJ,EAAA5F,OACAjE,KAAAgc,qBAAAnS,EAAA5F,MAAA,2CAEAjE,KAAAgc,qBAAAnS,EAAA8H,aAAA,4CAEA8J,EAAA1d,UAAAie,qBAAA,SAAAzb,EAAA0b,GACA,qBAAA1b,GAAA,KAAAA,EACA,UAAAnD,MAAA6e,IAGAR,EA7FmB,GCDfS,EAAoB,WACxB,SAAAC,KAwDA,OAtDAA,EAAApe,UAAAqe,iBAAA,SAAAC,GAQA,OAPQ1N,EAAA,KAAGC,WAAAyN,EAAA,WACXC,EAAAD,GACArc,KAAA0C,OAAA2Z,EAGArc,KAAA0C,OAAA,IAA8BiM,EAAA,KAAa0N,GAE3Crc,MAEAmc,EAAApe,UAAAwe,QAAA,SAAA9Y,EAAA+Y,GAaA,OAZQ7N,EAAA,KAAGC,WAAAnL,EAAA,OACXzD,KAAAyD,MAIAzD,KAAAyc,sBADA,kBAAAD,EACAA,EAGA,CACA9D,UAAA8D,GAGAxc,MAMAmc,EAAApe,UAAA2e,gBAAA,SAAAhO,GAGA,OAFQC,EAAA,KAAGC,WAAAF,EAAA,YACX1O,KAAA0O,WACA1O,MAMAmc,EAAApe,UAAA4e,MAAA,WAGA,IAAAF,EAAAzc,KAAAyc,uBAAA,GAOA,QALAvc,IAAAuc,EAAA/Z,SAEA+Z,EAAA/Z,OAAA1C,KAAA0C,SAGA1C,KAAAyD,IACA,UAAArG,MAAA,4FAEA,IAAAqR,EAAA,IAA6BwJ,EAAcjY,KAAAyD,IAAAgZ,GAC3C,OAAelO,EAAazM,OAAA2M,EAAAzO,KAAA0C,QAAmCgZ,EAAA,KAAUzU,SAAAjH,KAAA0O,UAAA,IAAgC8M,IAEzGW,EAzDwB,GA4DxB,SAAAG,EAAA5Z,GACA,YAAAxC,IAAAwC,EAAA2B,ICjEO,IC2DPuY,EAAA,CACAve,KADA,WAEA,OACAwe,QAAA,CACAC,QAAA,IAEAC,aAAA,EACA3S,UAAA,GACAf,SAAA,MACAE,YAAA,MACAkF,WAAA,GACApR,SAAA,GACA4E,EAAA,KAIAoN,QAAA,CAEA2N,eAAA,SAAArS,EAAAsS,GACA,OAAAtS,EAAAuS,SAAAvS,EAAAuS,SAAA9d,UAAA,aAEA+d,iBAAA,SAAAxS,EAAAsS,GACA,OAAAtS,EAAAyS,cAAA,IAAAzS,EAAAyS,aAAAC,EAAA,KAAAC,WAAAC,OAAA,IAAAnI,KAAAzK,EAAAyS,cAAA,kBAEAI,oBARA,SAQAC,GACAzd,KAAA0d,KAAAD,EACAzd,KAAA2d,YAGAA,SAbA,WAcA,IAAAC,EAAA5d,KACA6d,EAAA,CACAH,KAAA1d,KAAA0d,KACA7X,IAAA7F,KAAA6c,QAAAC,SAEA9c,KAAA+c,aAAA,EAEAte,OAAAqf,EAAA,KAAArf,CAAAof,GAAA1Q,KAAA,SAAA9M,GAEAud,EAAAnP,WAAAuB,QAAA7C,KAAA,WAEAyQ,EAAAnP,WAAAyD,OAAA,oBAAAF,MAAA,SAAA+L,GACA,OAAAC,QAAA/Z,MAAA8Z,UAUAhU,WAAA,WACA/J,KAAAqJ,UAAArJ,KAAAuJ,aACAvJ,KAAAyO,WAAAyD,OAAA,cAAAlS,KAAAqJ,SAAArJ,KAAAuJ,aAAAyI,MAAA,SAAA+L,GACA,OAAAC,QAAA/Z,MAAA8Z,MAKA/T,QAAA,WACAhK,KAAA+c,aAAA,EACA/c,KAAAyO,WAAAyD,OAAA,oBAAAF,MAAA,SAAA+L,GACA,OAAAC,QAAA/Z,MAAA8Z,OAMAE,QAAA,WACA,IAAAC,EAAAle,KAEAke,EAAAzP,YAAA,IAAAyN,GACAK,QADA,GAAA4B,OACAL,EAAA,KADA,kBAEA1B,iBAAAlQ,EAAA,KAAAuE,aACAkM,QAIAuB,EAAAzP,WAAA7F,GAAA,0BAAAgB,EAAAC,GACAqU,EAAA7gB,SAAA6Q,KAAA,CAAAtE,OAAAC,cAGAqU,EAAAzP,WAAA7F,GAAA,yBAAAwV,GACAJ,QAAAK,KAAA,mBACAH,EAAAnB,aAAA,EACAmB,EAAA9T,UAAAgU,EACA7H,OAAA+H,cAAAte,KAAAiC,MAGAsc,QA1FA,WA2FAve,KAAA2d,YAOAa,cAlGA,WAmGAjI,OAAA+H,cAAAte,KAAAiC,GACAjC,KAAAyO,WAAAqC,SCnK8V2N,EAAA,0BCQ9VC,EAAgBjgB,OAAAkgB,EAAA,KAAAlgB,CACdggB,EACA3W,EACAqD,GACF,EACA,KACA,WACA,MAIAuT,EAAAhX,QAAAkX,OAAA,YACehiB,EAAA,WAAA8hB,sECpBXG,2CAAc,oBACdC,EAAkB,aACtB,SAASC,EAAQ7c,EAAG8c,GACZA,IAAa9c,EAAI,IAAI/E,OACzB,IADA,IACSgF,EAAI,EAAGA,EAAI6c,EAAK7c,IAAOD,EAAI,IAAMA,EAC1C,OAAOA,EAGItF,EAAA,MACXqiB,qBAAsB,SAAU/U,GAC5B,IAAIgV,EAAM,IAAI9e,OAAO,QAAU8J,EAAO,gBAAiB,KACnDiV,EAAI5I,OAAO6I,SAASC,OAAOC,OAAO,GAAGC,MAAML,GAC3CM,EAAU,GAKd,OAJS,MAALL,IACAK,EAAUL,EAAE,IAChBD,EAAM,KACNC,EAAI,KACc,MAAXK,GAA8B,IAAXA,GAA4B,aAAXA,EAAyB,GAAKA,GAE7ElC,WAAY,CAGRC,OAAQ,SAAUF,EAAMoC,GAEpB,OADAA,EAAUA,GAAWX,EACdW,EAAQtI,QAAQ0H,EAAa,SAAUa,GAC1C,OAAQA,EAAGC,OAAO,IACd,IAAK,IAAK,OAAOZ,EAAQ1B,EAAKuC,cAAeF,EAAGviB,QAChD,IAAK,IAAK,OAAO4hB,EAAQ1B,EAAKwC,WAAa,EAAGH,EAAGviB,QACjD,IAAK,IAAK,OAAO4hB,EAAQ1B,EAAKyC,UAAWJ,EAAGviB,QAC5C,IAAK,IAAK,OAAOkgB,EAAK0C,SAAW,EACjC,IAAK,IAAK,OAAOhB,EAAQ1B,EAAK2C,WAAYN,EAAGviB,QAC7C,IAAK,IAAK,OAAO4hB,EAAQ1B,EAAK4C,aAAcP,EAAGviB,QAC/C,IAAK,IAAK,OAAO4hB,EAAQ1B,EAAK6C,aAAcR,EAAGviB,YAI3DF,MAAO,SAAUkjB,EAAYV,GACzB,IAAIW,EAAUX,EAAQF,MAAMV,GACxBwB,EAAUF,EAAWZ,MAAM,UAC/B,GAAIa,EAAQjjB,QAAUkjB,EAAQljB,OAAQ,CAElC,IADA,IAAImjB,EAAQ,IAAIlL,KAAK,KAAM,EAAG,GACrBjT,EAAI,EAAGA,EAAIie,EAAQjjB,OAAQgF,IAAK,CACrC,IAAIoe,EAAOC,IAASH,EAAQle,IACxBse,EAAOL,EAAQje,GACnB,OAAQse,EAAKd,OAAO,IAChB,IAAK,IAAKW,EAAMI,YAAYH,GAAO,MACnC,IAAK,IAAKD,EAAMK,SAASJ,EAAO,GAAI,MACpC,IAAK,IAAKD,EAAMM,QAAQL,GAAO,MAC/B,IAAK,IAAKD,EAAMO,SAASN,GAAO,MAChC,IAAK,IAAKD,EAAMQ,WAAWP,GAAO,MAClC,IAAK,IAAKD,EAAMS,WAAWR,GAAO,OAG1C,OAAOD,EAEX,OAAO,OAIfU,MAAM,CACFzD,OAAQ,SAAU0D,GACd,MAAiB,oBAAPA,GAA6B,MAAPA,GAAsB,IAAPA,2BC5D3D,IAAAC,EAAexkB,EAAQ,QACvBykB,EAAUzkB,EAAQ,QAClB6J,EAAY7J,EAAQ,OAARA,CAAgB,SAC5BwK,EAAAC,QAAA,SAAAlB,GACA,IAAAjB,EACA,OAAAkc,EAAAjb,UAAA/F,KAAA8E,EAAAiB,EAAAM,MAAAvB,EAAA,UAAAmc,EAAAlb,wCCNAvJ,EAAAC,EAAAC,EAAA,sBAAAwkB,IAAA1kB,EAAAC,EAAAC,EAAA,sBAAAykB,IAAA3kB,EAAAC,EAAAC,EAAA,sBAAA0kB,IAEA,IAAAlgB,EAA6B,WAC7B,IAAAC,EAAA5C,OAAA6C,gBACA,CAAUC,UAAA,cAAgBC,OAAA,SAAA7E,EAAA8E,GAAsC9E,EAAA4E,UAAAE,IAChE,SAAA9E,EAAA8E,GAAyB,QAAAC,KAAAD,IAAAE,eAAAD,KAAA/E,EAAA+E,GAAAD,EAAAC,KACzB,gBAAA/E,EAAA8E,GAEA,SAAAG,IAAuB5B,KAAA6B,YAAAlF,EADvB0E,EAAA1E,EAAA8E,GAEA9E,EAAAoB,UAAA,OAAA0D,EAAAhD,OAAAqD,OAAAL,IAAAG,EAAA7D,UAAA0D,EAAA1D,UAAA,IAAA6D,IAP6B,GAW7Bwf,EAAA,SAAA3e,GAOA,SAAA2e,EAAAnF,EAAA1X,GACA,IAAAgd,EAAAvhB,KAAA6B,YACAc,EAAA3C,KACAwhB,EAAAD,EAAAxjB,UAMA,OALA4E,EAAAF,EAAAtC,KAAAH,KAAAic,IAAAjc,KACA2C,EAAA4B,aAGA5B,EAAApB,UAAAigB,EACA7e,EAEA,OAjBAvB,EAAAggB,EAAA3e,GAiBA2e,EAlBA,CAmBChkB,OAGDikB,EAAA,SAAA5e,GAMA,SAAA4e,EAAApF,GACA,IAAAsF,EAAAvhB,KAAA6B,iBACA,IAAAoa,IAAsCA,EAAA,uBACtC,IAAAtZ,EAAA3C,KACAwhB,EAAAD,EAAAxjB,UAKA,OAJA4E,EAAAF,EAAAtC,KAAAH,KAAAic,IAAAjc,KAGA2C,EAAApB,UAAAigB,EACA7e,EAEA,OAhBAvB,EAAAigB,EAAA5e,GAgBA4e,EAjBA,CAkBCjkB,OAGDkkB,EAAA,SAAA7e,GAMA,SAAA6e,EAAArF,GACA,IAAAsF,EAAAvhB,KAAA6B,iBACA,IAAAoa,IAAsCA,EAAA,sBACtC,IAAAtZ,EAAA3C,KACAwhB,EAAAD,EAAAxjB,UAKA,OAJA4E,EAAAF,EAAAtC,KAAAH,KAAAic,IAAAjc,KAGA2C,EAAApB,UAAAigB,EACA7e,EAEA,OAhBAvB,EAAAkgB,EAAA7e,GAgBA6e,EAjBA,CAkBClkB,0CC1EDV,EAAAC,EAAAC,EAAA,sBAAA6kB,IAAA/kB,EAAAC,EAAAC,EAAA,sBAAA8kB,IAAAhlB,EAAAC,EAAAC,EAAA,sBAAA+kB,IAAAjlB,EAAAC,EAAAC,EAAA,sBAAA4T,IAAA9T,EAAAC,EAAAC,EAAA,sBAAAglB,IAAAllB,EAAAC,EAAAC,EAAA,sBAAAilB,IAAAnlB,EAAAC,EAAAC,EAAA,sBAAAklB,IAAA,IAAAC,EAAArlB,EAAA,QAAAslB,EAAAtlB,EAAA,QAEA+P,EAA6B,SAAAC,EAAAC,EAAAC,EAAAC,GAC7B,WAAAD,MAAA1J,UAAA,SAAAC,EAAAC,GACA,SAAA0J,EAAAvM,GAAmC,IAAMwM,EAAAF,EAAAG,KAAAzM,IAA+B,MAAA0M,GAAY7J,EAAA6J,IACpF,SAAAC,EAAA3M,GAAkC,IAAMwM,EAAAF,EAAA,SAAAtM,IAAmC,MAAA0M,GAAY7J,EAAA6J,IACvF,SAAAF,EAAAnM,GAA+BA,EAAAN,KAAA6C,EAAAvC,EAAAL,OAAA,IAAAqM,EAAA,SAAAzJ,GAAiEA,EAAAvC,EAAAL,SAAyB4M,KAAAL,EAAAI,GACzHH,GAAAF,IAAA9N,MAAA2N,EAAAC,GAAA,KAAAK,WAGAI,EAA+B,SAAAV,EAAAhJ,GAC/B,IAAwGoB,EAAAwC,EAAArF,EAAAoL,EAAxGC,EAAA,CAAarE,MAAA,EAAAsE,KAAA,WAA6B,KAAAtL,EAAA,SAAAA,EAAA,GAA0B,OAAAA,EAAA,IAAeuL,KAAA,GAAAC,IAAA,IACnF,OAAAJ,EAAA,CAAgBL,KAAAU,EAAA,GAAAC,MAAAD,EAAA,GAAAE,OAAAF,EAAA,IAAqD,oBAAAG,SAAAR,EAAAQ,OAAAC,UAAA,WAAoE,OAAA9N,OAAeqN,EACxJ,SAAAK,EAAAtL,GAAsB,gBAAA2L,GAAsB,OAAAhB,EAAA,CAAA3K,EAAA2L,KAC5C,SAAAhB,EAAAiB,GACA,GAAAlJ,EAAA,UAAAmJ,UAAA,mCACA,MAAAX,EAAA,IACA,GAAAxI,EAAA,EAAAwC,IAAArF,EAAA,EAAA+L,EAAA,GAAA1G,EAAA,UAAA0G,EAAA,GAAA1G,EAAA,YAAArF,EAAAqF,EAAA,YAAArF,EAAA9B,KAAAmH,GAAA,GAAAA,EAAA0F,SAAA/K,IAAA9B,KAAAmH,EAAA0G,EAAA,KAAA1N,KAAA,OAAA2B,EAEA,OADAqF,EAAA,EAAArF,IAAA+L,EAAA,GAAAA,EAAA,GAAA/L,EAAA1B,QACAyN,EAAA,IACA,cAAA/L,EAAA+L,EAAuC,MACvC,OAAkC,OAAlCV,EAAArE,QAAkC,CAAS1I,MAAAyN,EAAA,GAAA1N,MAAA,GAC3C,OAAAgN,EAAArE,QAAkC3B,EAAA0G,EAAA,GAAWA,EAAA,IAAU,SACvD,OAAAA,EAAAV,EAAAG,IAAAlQ,MAAyC+P,EAAAE,KAAAjQ,MAAc,SACvD,QACA,GAAA0E,EAAAqL,EAAAE,OAAAvL,IAAA9E,OAAA,GAAA8E,IAAA9E,OAAA,UAAA6Q,EAAA,QAAAA,EAAA,KAA6GV,EAAA,EAAO,SACpH,OAAAU,EAAA,MAAA/L,GAAA+L,EAAA,GAAA/L,EAAA,IAAA+L,EAAA,GAAA/L,EAAA,KAAgFqL,EAAArE,MAAA+E,EAAA,GAAiB,MACjG,OAAAA,EAAA,IAAAV,EAAArE,MAAAhH,EAAA,IAAwDqL,EAAArE,MAAAhH,EAAA,GAAgBA,EAAA+L,EAAQ,MAChF,GAAA/L,GAAAqL,EAAArE,MAAAhH,EAAA,IAA8CqL,EAAArE,MAAAhH,EAAA,GAAgBqL,EAAAG,IAAAS,KAAAF,GAAgB,MAC9E/L,EAAA,IAAAqL,EAAAG,IAAAlQ,MACA+P,EAAAE,KAAAjQ,MAAiC,SAEjCyQ,EAAAtK,EAAAvD,KAAAuM,EAAAY,GACS,MAAAL,GAAYe,EAAA,GAAAf,GAAa3F,EAAA,EAAS,QAAUxC,EAAA7C,EAAA,EACrD,KAAA+L,EAAA,SAAAA,EAAA,GAAmC,OAASzN,MAAAyN,EAAA,GAAAA,EAAA,UAAA1N,MAAA,KAM5CmhB,EAAA,WACA,SAAAA,KAaA,OAXAA,EAAA7S,WAAA,SAAA6O,EAAAvT,GACA,UAAAuT,QAAAvd,IAAAud,EACA,UAAArgB,MAAA,QAAA8M,EAAA,4BAGAuX,EAAA3M,KAAA,SAAA2I,EAAAwE,EAAA/X,GAEA,KAAAuT,KAAAwE,GACA,UAAA7kB,MAAA,WAAA8M,EAAA,WAAAuT,EAAA,MAGAgE,EAdA,GAkBO,SAAAC,EAAArjB,EAAA6jB,GACP,IAAAC,EAAA,GAaA,OAZAR,EAAAtjB,IACA8jB,EAAA,yBAAA9jB,EAAAY,WACAijB,IACAC,GAAA,eAAAC,EAAA/jB,GAAA,MAGA,kBAAAA,IACA8jB,EAAA,yBAAA9jB,EAAAlB,OACA+kB,IACAC,GAAA,eAAA9jB,EAAA,MAGA8jB,EAGO,SAAAC,EAAA/jB,GACP,IAAAgkB,EAAA,IAAA1jB,WAAAN,GAEAikB,EAAA,GAMA,OALAD,EAAAzW,QAAA,SAAA2W,GACA,IAAAC,EAAAD,EAAA,UACAD,GAAA,KAAAE,EAAAD,EAAA1O,SAAA,UAGAyO,EAAAhD,OAAA,EAAAgD,EAAAnlB,OAAA,GAIO,SAAAwkB,EAAAlE,GACP,OAAAA,GAAA,qBAAAgF,cACAhF,aAAAgF,aAEAhF,EAAA5b,aAAA,gBAAA4b,EAAA5b,YAAAqI,MAGO,SAAAsG,EAAA9N,EAAAggB,EAAAnW,EAAA9I,EAAA6Q,EAAAhR,EAAAiR,GACP,OAAA9H,EAAAzM,UAAA,oBACA,IAAAkQ,EAAArM,EAAA+Q,EAAAhR,EAAAvE,EACA,OAAA+N,EAAApN,KAAA,SAAAkT,GACA,OAAAA,EAAAjK,OACA,OACA,OAAAqL,EACA,GAAAA,KADA,MAEA,OACAM,EAAA1B,EAAA3F,OACAqH,IACA1E,EAAA,GACAA,EAAA,2BAAA0E,EADA/Q,EAEAqM,GAEAgD,EAAAjK,MAAA,EACA,OAGA,OAFAvG,EAAA2B,IAA+B0d,EAAA,KAAQhN,MAAA,IAAA2N,EAAA,6BAAAhB,EAAApe,EAAAiR,GAAA,KACvC3Q,EAAA+d,EAAAre,GAAA,qBACA,GAAAiJ,EAAA5E,KAAAlE,EAAA,CACAH,UACAO,UACAD,kBAEA,OAGA,OAFAvE,EAAA6T,EAAA3F,OACA7K,EAAA2B,IAA+B0d,EAAA,KAAQhN,MAAA,IAAA2N,EAAA,kDAAArjB,EAAAkF,WAAA,KACvC,SAMO,SAAAqd,EAAAlf,GACP,YAAAxC,IAAAwC,EACA,IAAAof,EAAiCC,EAAA,KAAQtR,aAEzC,OAAA/N,EACesf,EAAA,KAAU/a,SAEzBvE,EAAA2B,IACA3B,EAEA,IAAAof,EAAApf,GAGA,IAAAmf,EAAA,WACA,SAAAA,IACA7hB,KAAA2iB,UAAA,GA4BA,OA1BAd,EAAA9jB,UAAAiP,KAAA,SAAAvD,GACA,QAAA0H,EAAA,EAAAjB,EAAAlQ,KAAA2iB,UAA6CxR,EAAAjB,EAAA/S,OAAgBgU,IAAA,CAC7D,IAAAyR,EAAA1S,EAAAiB,GACAyR,EAAA5V,KAAAvD,KAGAoY,EAAA9jB,UAAAkG,MAAA,SAAA8Z,GACA,QAAA5M,EAAA,EAAAjB,EAAAlQ,KAAA2iB,UAA6CxR,EAAAjB,EAAA/S,OAAgBgU,IAAA,CAC7D,IAAAyR,EAAA1S,EAAAiB,GACAyR,EAAA3e,OACA2e,EAAA3e,MAAA8Z,KAIA8D,EAAA9jB,UAAAgU,SAAA,WACA,QAAAZ,EAAA,EAAAjB,EAAAlQ,KAAA2iB,UAA6CxR,EAAAjB,EAAA/S,OAAgBgU,IAAA,CAC7D,IAAAyR,EAAA1S,EAAAiB,GACAyR,EAAA7Q,UACA6Q,EAAA7Q,aAIA8P,EAAA9jB,UAAA8kB,UAAA,SAAAD,GAEA,OADA5iB,KAAA2iB,UAAAzU,KAAA0U,GACA,IAAAE,EAAA9iB,KAAA4iB,IAEAf,EA9BA,GAkCAiB,EAAA,WACA,SAAAA,EAAAvR,EAAAqR,GACA5iB,KAAAuR,UACAvR,KAAA4iB,WAWA,OATAE,EAAA/kB,UAAAglB,QAAA,WACA,IAAAliB,EAAAb,KAAAuR,QAAAoR,UAAA9jB,QAAAmB,KAAA4iB,UACA/hB,GAAA,GACAb,KAAAuR,QAAAoR,UAAAnQ,OAAA3R,EAAA,GAEA,IAAAb,KAAAuR,QAAAoR,UAAAxlB,QAAA6C,KAAAuR,QAAAC,gBACAxR,KAAAuR,QAAAC,iBAAAQ,MAAA,SAAA1E,OAGAwV,EAdA,GAkBAhB,EAAA,WACA,SAAAA,EAAAkB,GACAhjB,KAAAgjB,kBAsBA,OApBAlB,EAAA/jB,UAAAsG,IAAA,SAAA4e,EAAApZ,GACA,GAAAoZ,GAAAjjB,KAAAgjB,gBACA,OAAAC,GACA,KAAqBlB,EAAA,KAAQmB,SAC7B,KAAqBnB,EAAA,KAAQ3kB,MAC7B4gB,QAAA/Z,MAAA,SAAAmR,MAAA+N,cAAA,KAA0EpB,EAAA,KAAQkB,GAAA,KAAApZ,GAClF,MACA,KAAqBkY,EAAA,KAAQzd,QAC7B0Z,QAAAoF,KAAA,SAAAhO,MAAA+N,cAAA,KAAyEpB,EAAA,KAAQkB,GAAA,KAAApZ,GACjF,MACA,KAAqBkY,EAAA,KAAQtR,YAC7BuN,QAAAK,KAAA,SAAAjJ,MAAA+N,cAAA,KAAyEpB,EAAA,KAAQkB,GAAA,KAAApZ,GACjF,MACA,QAEAmU,QAAA3Z,IAAA,SAAA+Q,MAAA+N,cAAA,KAAwEpB,EAAA,KAAQkB,GAAA,KAAApZ,GAChF,QAIAiY,EAxBA", "file": "js/chunk-ef28925c.a547d73e.js", "sourcesContent": ["// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n// Not exported from index\r\n/** @private */\r\nvar TextMessageFormat = /** @class */ (function () {\r\n    function TextMessageFormat() {\r\n    }\r\n    TextMessageFormat.write = function (output) {\r\n        return \"\" + output + TextMessageFormat.RecordSeparator;\r\n    };\r\n    TextMessageFormat.parse = function (input) {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n        var messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    };\r\n    TextMessageFormat.RecordSeparatorCode = 0x1e;\r\n    TextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n    return TextMessageFormat;\r\n}());\r\nexport { TextMessageFormat };\r\n//# sourceMappingURL=TextMessageFormat.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n/** @private */\r\nvar HandshakeProtocol = /** @class */ (function () {\r\n    function HandshakeProtocol() {\r\n    }\r\n    // Handshake request is always JSON\r\n    HandshakeProtocol.prototype.writeHandshakeRequest = function (handshakeRequest) {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    };\r\n    HandshakeProtocol.prototype.parseHandshakeResponse = function (data) {\r\n        var responseMessage;\r\n        var messageData;\r\n        var remainingData;\r\n        if (isArrayBuffer(data) || (typeof Buffer !== \"undefined\" && data instanceof Buffer)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            var binaryData = new Uint8Array(data);\r\n            var separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            var responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, binaryData.slice(0, responseLength));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        }\r\n        else {\r\n            var textData = data;\r\n            var separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            var responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n        // At this point we should have just the single handshake message\r\n        var messages = TextMessageFormat.parse(messageData);\r\n        var response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        responseMessage = response;\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    };\r\n    return HandshakeProtocol;\r\n}());\r\nexport { HandshakeProtocol };\r\n//# sourceMappingURL=HandshakeProtocol.js.map", "'use strict';\n\nvar anObject = require('./_an-object');\nvar sameValue = require('./_same-value');\nvar regExpExec = require('./_regexp-exec-abstract');\n\n// @@search logic\nrequire('./_fix-re-wks')('search', 1, function (defined, SEARCH, $search, maybeCallNative) {\n  return [\n    // `String.prototype.search` method\n    // https://tc39.github.io/ecma262/#sec-string.prototype.search\n    function search(regexp) {\n      var O = defined(this);\n      var fn = regexp == undefined ? undefined : regexp[SEARCH];\n      return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[SEARCH](String(O));\n    },\n    // `RegExp.prototype[@@search]` method\n    // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@search\n    function (regexp) {\n      var res = maybeCallNative($search, regexp, this);\n      if (res.done) return res.value;\n      var rx = anObject(regexp);\n      var S = String(this);\n      var previousLastIndex = rx.lastIndex;\n      if (!sameValue(previousLastIndex, 0)) rx.lastIndex = 0;\n      var result = regExpExec(rx, S);\n      if (!sameValue(rx.lastIndex, previousLastIndex)) rx.lastIndex = previousLastIndex;\n      return result === null ? -1 : result.index;\n    }\n  ];\n});\n", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __assign = (this && this.__assign) || Object.assign || function(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n            t[p] = s[p];\r\n    }\r\n    return t;\r\n};\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\nvar requestModule;\r\nif (typeof XMLHttpRequest === \"undefined\") {\r\n    // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n    // @ts-ignore: TS doesn't know about these names\r\n    var requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n    requestModule = requireFunc(\"request\");\r\n}\r\nvar NodeHttpClient = /** @class */ (function (_super) {\r\n    __extends(NodeHttpClient, _super);\r\n    function NodeHttpClient(logger) {\r\n        var _this = _super.call(this) || this;\r\n        if (typeof requestModule === \"undefined\") {\r\n            throw new Error(\"The 'request' module could not be loaded.\");\r\n        }\r\n        _this.logger = logger;\r\n        _this.cookieJar = requestModule.jar();\r\n        _this.request = requestModule.defaults({ jar: _this.cookieJar });\r\n        return _this;\r\n    }\r\n    NodeHttpClient.prototype.send = function (httpRequest) {\r\n        var _this = this;\r\n        return new Promise(function (resolve, reject) {\r\n            var requestBody;\r\n            if (isArrayBuffer(httpRequest.content)) {\r\n                requestBody = Buffer.from(httpRequest.content);\r\n            }\r\n            else {\r\n                requestBody = httpRequest.content || \"\";\r\n            }\r\n            var currentRequest = _this.request(httpRequest.url, {\r\n                body: requestBody,\r\n                // If binary is expected 'null' should be used, otherwise for text 'utf8'\r\n                encoding: httpRequest.responseType === \"arraybuffer\" ? null : \"utf8\",\r\n                headers: __assign({ \r\n                    // Tell auth middleware to 401 instead of redirecting\r\n                    \"X-Requested-With\": \"XMLHttpRequest\" }, httpRequest.headers),\r\n                method: httpRequest.method,\r\n                timeout: httpRequest.timeout,\r\n            }, function (error, response, body) {\r\n                if (httpRequest.abortSignal) {\r\n                    httpRequest.abortSignal.onabort = null;\r\n                }\r\n                if (error) {\r\n                    if (error.code === \"ETIMEDOUT\") {\r\n                        _this.logger.log(LogLevel.Warning, \"Timeout from HTTP request.\");\r\n                        reject(new TimeoutError());\r\n                    }\r\n                    _this.logger.log(LogLevel.Warning, \"Error from HTTP request. \" + error);\r\n                    reject(error);\r\n                    return;\r\n                }\r\n                if (response.statusCode >= 200 && response.statusCode < 300) {\r\n                    resolve(new HttpResponse(response.statusCode, response.statusMessage || \"\", body));\r\n                }\r\n                else {\r\n                    reject(new HttpError(response.statusMessage || \"\", response.statusCode || 0));\r\n                }\r\n            });\r\n            if (httpRequest.abortSignal) {\r\n                httpRequest.abortSignal.onabort = function () {\r\n                    currentRequest.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n        });\r\n    };\r\n    NodeHttpClient.prototype.getCookieString = function (url) {\r\n        return this.cookieJar.getCookieString(url);\r\n    };\r\n    return NodeHttpClient;\r\n}(HttpClient));\r\nexport { NodeHttpClient };\r\n//# sourceMappingURL=NodeHttpClient.js.map", "var global = require('./_global');\nvar inheritIfRequired = require('./_inherit-if-required');\nvar dP = require('./_object-dp').f;\nvar gOPN = require('./_object-gopn').f;\nvar isRegExp = require('./_is-regexp');\nvar $flags = require('./_flags');\nvar $RegExp = global.RegExp;\nvar Base = $RegExp;\nvar proto = $RegExp.prototype;\nvar re1 = /a/g;\nvar re2 = /a/g;\n// \"new\" creates a new object, old webkit buggy here\nvar CORRECT_NEW = new $RegExp(re1) !== re1;\n\nif (require('./_descriptors') && (!CORRECT_NEW || require('./_fails')(function () {\n  re2[require('./_wks')('match')] = false;\n  // RegExp constructor can alter flags and IsRegExp works correct with @@match\n  return $RegExp(re1) != re1 || $RegExp(re2) == re2 || $RegExp(re1, 'i') != '/a/i';\n}))) {\n  $RegExp = function RegExp(p, f) {\n    var tiRE = this instanceof $RegExp;\n    var piRE = isRegExp(p);\n    var fiU = f === undefined;\n    return !tiRE && piRE && p.constructor === $RegExp && fiU ? p\n      : inheritIfRequired(CORRECT_NEW\n        ? new Base(piRE && !fiU ? p.source : p, f)\n        : Base((piRE = p instanceof $RegExp) ? p.source : p, piRE && fiU ? $flags.call(p) : f)\n      , tiRE ? this : proto, $RegExp);\n  };\n  var proxy = function (key) {\n    key in $RegExp || dP($RegExp, key, {\n      configurable: true,\n      get: function () { return Base[key]; },\n      set: function (it) { Base[key] = it; }\n    });\n  };\n  for (var keys = gOPN(Base), i = 0; keys.length > i;) proxy(keys[i++]);\n  proto.constructor = $RegExp;\n  $RegExp.prototype = proto;\n  require('./_redefine')(global, 'RegExp', $RegExp);\n}\n\nrequire('./_set-species')('RegExp');\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=0&id=6e805334&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=0&id=6e805334&scoped=true&lang=css&\"", "'use strict';\n\nvar anObject = require('./_an-object');\nvar toLength = require('./_to-length');\nvar advanceStringIndex = require('./_advance-string-index');\nvar regExpExec = require('./_regexp-exec-abstract');\n\n// @@match logic\nrequire('./_fix-re-wks')('match', 1, function (defined, MATCH, $match, maybeCallNative) {\n  return [\n    // `String.prototype.match` method\n    // https://tc39.github.io/ecma262/#sec-string.prototype.match\n    function match(regexp) {\n      var O = defined(this);\n      var fn = regexp == undefined ? undefined : regexp[MATCH];\n      return fn !== undefined ? fn.call(regexp, O) : new RegExp(regexp)[MATCH](String(O));\n    },\n    // `RegExp.prototype[@@match]` method\n    // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@match\n    function (regexp) {\n      var res = maybeCallNative($match, regexp, this);\n      if (res.done) return res.value;\n      var rx = anObject(regexp);\n      var S = String(this);\n      if (!rx.global) return regExpExec(rx, S);\n      var fullUnicode = rx.unicode;\n      rx.lastIndex = 0;\n      var A = [];\n      var n = 0;\n      var result;\n      while ((result = regExpExec(rx, S)) !== null) {\n        var matchStr = String(result[0]);\n        A[n] = matchStr;\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n        n++;\n      }\n      return n === 0 ? null : A;\n    }\n  ];\n});\n", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport var LogLevel;\r\n(function (LogLevel) {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\r\n    /** Log level for low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\r\n    /** Log level for informational diagnostic messages. */\r\n    LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    LogLevel[LogLevel[\"None\"] = 6] = \"None\";\r\n})(LogLevel || (LogLevel = {}));\r\n//# sourceMappingURL=ILogger.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n/** A logger that does nothing when log messages are sent to it. */\r\nvar NullLogger = /** @class */ (function () {\r\n    function NullLogger() {\r\n    }\r\n    /** @inheritDoc */\r\n    // tslint:disable-next-line\r\n    NullLogger.prototype.log = function (_logLevel, _message) {\r\n    };\r\n    /** The singleton instance of the {@link @aspnet/signalr.NullLogger}. */\r\n    NullLogger.instance = new NullLogger();\r\n    return NullLogger;\r\n}());\r\nexport { NullLogger };\r\n//# sourceMappingURL=Loggers.js.map", "// 7.2.9 SameValue(x, y)\nmodule.exports = Object.is || function is(x, y) {\n  // eslint-disable-next-line no-self-compare\n  return x === y ? x !== 0 || 1 / x === 1 / y : x != x && y != y;\n};\n", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __assign = (this && this.__assign) || Object.assign || function(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n            t[p] = s[p];\r\n    }\r\n    return t;\r\n};\r\n/** Represents an HTTP response. */\r\nvar HttpResponse = /** @class */ (function () {\r\n    function HttpResponse(statusCode, statusText, content) {\r\n        this.statusCode = statusCode;\r\n        this.statusText = statusText;\r\n        this.content = content;\r\n    }\r\n    return HttpResponse;\r\n}());\r\nexport { HttpResponse };\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nvar HttpClient = /** @class */ (function () {\r\n    function HttpClient() {\r\n    }\r\n    HttpClient.prototype.get = function (url, options) {\r\n        return this.send(__assign({}, options, { method: \"GET\", url: url }));\r\n    };\r\n    HttpClient.prototype.post = function (url, options) {\r\n        return this.send(__assign({}, options, { method: \"POST\", url: url }));\r\n    };\r\n    HttpClient.prototype.delete = function (url, options) {\r\n        return this.send(__assign({}, options, { method: \"DELETE\", url: url }));\r\n    };\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    HttpClient.prototype.getCookieString = function (url) {\r\n        return \"\";\r\n    };\r\n    return HttpClient;\r\n}());\r\nexport { HttpClient };\r\n//# sourceMappingURL=HttpClient.js.map", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',[_c('div',{staticStyle:{\"display\":\"none1\"}},[_c('el-form',{ref:\"form\",staticStyle:{\"margin\":\"20px\",\"width\":\"60%\",\"min-width\":\"600px\"},attrs:{\"label-width\":\"80px\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.onSubmit($event)}}},[_c('el-form-item',{attrs:{\"label\":\"用户名\"}},[_c('el-input',{model:{value:(_vm.userName),callback:function ($$v) {_vm.userName=$$v},expression:\"userName\"}})],1),_c('el-form-item',{attrs:{\"label\":\"密码\"}},[_c('el-input',{model:{value:(_vm.userMessage),callback:function ($$v) {_vm.userMessage=$$v},expression:\"userMessage\"}})],1)],1),_vm._l((_vm.messages),function(item,index){return _c('ul',{key:index + 'itemMessage'},[_c('li',[_c('b',[_vm._v(\"Name: \")]),_vm._v(_vm._s(item.user))]),_c('li',[_c('b',[_vm._v(\"Message: \")]),_vm._v(_vm._s(item.message))])])}),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.submitCard}},[_vm._v(\"登录\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.getLogs}},[_vm._v(\"查询\")])],2),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData}},[_c('el-table-column',{attrs:{\"type\":\"expand\"},scopedSlots:_vm._u([{key:\"default\",fn:function(props){return [_c('el-form',{staticClass:\"demo-table-expand\",attrs:{\"label-position\":\"left\",\"inline\":\"\"}},[_c('el-form-item',{attrs:{\"label\":\"Datetime\"}},[_c('span',[_vm._v(_vm._s(props.row.datetime))])]),_c('el-form-item',{attrs:{\"label\":\"Content\"}},[_c('span',{domProps:{\"innerHTML\":_vm._s(props.row.content)}})])],1)]}}])}),_c('el-table-column',{attrs:{\"label\":\"Datetime\",\"prop\":\"datetime\"}}),_c('el-table-column',{attrs:{\"label\":\"Content\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{class:scope.row.logColor,domProps:{\"innerHTML\":_vm._s(scope.row.content)}})]}}])})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n/** Defines the type of a Hub Message. */\r\nexport var MessageType;\r\n(function (MessageType) {\r\n    /** Indicates the message is an Invocation message and implements the {@link @aspnet/signalr.InvocationMessage} interface. */\r\n    MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\r\n    /** Indicates the message is a StreamItem message and implements the {@link @aspnet/signalr.StreamItemMessage} interface. */\r\n    MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\r\n    /** Indicates the message is a Completion message and implements the {@link @aspnet/signalr.CompletionMessage} interface. */\r\n    MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @aspnet/signalr.StreamInvocationMessage} interface. */\r\n    MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @aspnet/signalr.CancelInvocationMessage} interface. */\r\n    MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\r\n    /** Indicates the message is a Ping message and implements the {@link @aspnet/signalr.PingMessage} interface. */\r\n    MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\r\n    /** Indicates the message is a Close message and implements the {@link @aspnet/signalr.CloseMessage} interface. */\r\n    MessageType[MessageType[\"Close\"] = 7] = \"Close\";\r\n})(MessageType || (MessageType = {}));\r\n//# sourceMappingURL=IHubProtocol.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nvar XhrHttpClient = /** @class */ (function (_super) {\r\n    __extends(XhrHttpClient, _super);\r\n    function XhrHttpClient(logger) {\r\n        var _this = _super.call(this) || this;\r\n        _this.logger = logger;\r\n        return _this;\r\n    }\r\n    /** @inheritDoc */\r\n    XhrHttpClient.prototype.send = function (request) {\r\n        var _this = this;\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return new Promise(function (resolve, reject) {\r\n            var xhr = new XMLHttpRequest();\r\n            xhr.open(request.method, request.url, true);\r\n            xhr.withCredentials = true;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n            var headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach(function (header) {\r\n                    xhr.setRequestHeader(header, headers[header]);\r\n                });\r\n            }\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = function () {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n            xhr.onload = function () {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                }\r\n                else {\r\n                    reject(new HttpError(xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n            xhr.onerror = function () {\r\n                _this.logger.log(LogLevel.Warning, \"Error from HTTP request. \" + xhr.status + \": \" + xhr.statusText + \".\");\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n            xhr.ontimeout = function () {\r\n                _this.logger.log(LogLevel.Warning, \"Timeout from HTTP request.\");\r\n                reject(new TimeoutError());\r\n            };\r\n            xhr.send(request.content || \"\");\r\n        });\r\n    };\r\n    return XhrHttpClient;\r\n}(HttpClient));\r\nexport { XhrHttpClient };\r\n//# sourceMappingURL=XhrHttpClient.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nimport { AbortError } from \"./Errors\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { NodeHttpClient } from \"./NodeHttpClient\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n/** Default implementation of {@link @aspnet/signalr.HttpClient}. */\r\nvar DefaultHttpClient = /** @class */ (function (_super) {\r\n    __extends(DefaultHttpClient, _super);\r\n    /** Creates a new instance of the {@link @aspnet/signalr.DefaultHttpClient}, using the provided {@link @aspnet/signalr.ILogger} to log messages. */\r\n    function DefaultHttpClient(logger) {\r\n        var _this = _super.call(this) || this;\r\n        if (typeof XMLHttpRequest !== \"undefined\") {\r\n            _this.httpClient = new XhrHttpClient(logger);\r\n        }\r\n        else {\r\n            _this.httpClient = new NodeHttpClient(logger);\r\n        }\r\n        return _this;\r\n    }\r\n    /** @inheritDoc */\r\n    DefaultHttpClient.prototype.send = function (request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return this.httpClient.send(request);\r\n    };\r\n    DefaultHttpClient.prototype.getCookieString = function (url) {\r\n        return this.httpClient.getCookieString(url);\r\n    };\r\n    return DefaultHttpClient;\r\n}(HttpClient));\r\nexport { DefaultHttpClient };\r\n//# sourceMappingURL=DefaultHttpClient.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { HandshakeProtocol } from \"./HandshakeProtocol\";\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { Arg, Subject } from \"./Utils\";\r\nvar DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\r\nvar DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport var HubConnectionState;\r\n(function (HubConnectionState) {\r\n    /** The hub connection is disconnected. */\r\n    HubConnectionState[HubConnectionState[\"Disconnected\"] = 0] = \"Disconnected\";\r\n    /** The hub connection is connected. */\r\n    HubConnectionState[HubConnectionState[\"Connected\"] = 1] = \"Connected\";\r\n})(HubConnectionState || (HubConnectionState = {}));\r\n/** Represents a connection to a SignalR Hub. */\r\nvar HubConnection = /** @class */ (function () {\r\n    function HubConnection(connection, logger, protocol) {\r\n        var _this = this;\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n        this.logger = logger;\r\n        this.protocol = protocol;\r\n        this.connection = connection;\r\n        this.handshakeProtocol = new HandshakeProtocol();\r\n        this.connection.onreceive = function (data) { return _this.processIncomingData(data); };\r\n        this.connection.onclose = function (error) { return _this.connectionClosed(error); };\r\n        this.callbacks = {};\r\n        this.methods = {};\r\n        this.closedCallbacks = [];\r\n        this.id = 0;\r\n        this.receivedHandshakeResponse = false;\r\n        this.connectionState = HubConnectionState.Disconnected;\r\n        this.cachedPingMessage = this.protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    HubConnection.create = function (connection, logger, protocol) {\r\n        return new HubConnection(connection, logger, protocol);\r\n    };\r\n    Object.defineProperty(HubConnection.prototype, \"state\", {\r\n        /** Indicates the state of the {@link HubConnection} to the server. */\r\n        get: function () {\r\n            return this.connectionState;\r\n        },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    HubConnection.prototype.start = function () {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var handshakeRequest, handshakePromise;\r\n            var _this = this;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        handshakeRequest = {\r\n                            protocol: this.protocol.name,\r\n                            version: this.protocol.version,\r\n                        };\r\n                        this.logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n                        this.receivedHandshakeResponse = false;\r\n                        handshakePromise = new Promise(function (resolve, reject) {\r\n                            _this.handshakeResolver = resolve;\r\n                            _this.handshakeRejecter = reject;\r\n                        });\r\n                        return [4 /*yield*/, this.connection.start(this.protocol.transferFormat)];\r\n                    case 1:\r\n                        _a.sent();\r\n                        this.logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n                        return [4 /*yield*/, this.sendMessage(this.handshakeProtocol.writeHandshakeRequest(handshakeRequest))];\r\n                    case 2:\r\n                        _a.sent();\r\n                        this.logger.log(LogLevel.Information, \"Using HubProtocol '\" + this.protocol.name + \"'.\");\r\n                        // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n                        this.cleanupTimeout();\r\n                        this.resetTimeoutPeriod();\r\n                        this.resetKeepAliveInterval();\r\n                        // Wait for the handshake to complete before marking connection as connected\r\n                        return [4 /*yield*/, handshakePromise];\r\n                    case 3:\r\n                        // Wait for the handshake to complete before marking connection as connected\r\n                        _a.sent();\r\n                        this.connectionState = HubConnectionState.Connected;\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    HubConnection.prototype.stop = function () {\r\n        this.logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n        this.cleanupTimeout();\r\n        this.cleanupPingTimer();\r\n        return this.connection.stop();\r\n    };\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    HubConnection.prototype.stream = function (methodName) {\r\n        var _this = this;\r\n        var args = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            args[_i - 1] = arguments[_i];\r\n        }\r\n        var invocationDescriptor = this.createStreamInvocation(methodName, args);\r\n        var promiseQueue;\r\n        var subject = new Subject();\r\n        subject.cancelCallback = function () {\r\n            var cancelInvocation = _this.createCancelInvocation(invocationDescriptor.invocationId);\r\n            var cancelMessage = _this.protocol.writeMessage(cancelInvocation);\r\n            delete _this.callbacks[invocationDescriptor.invocationId];\r\n            return promiseQueue.then(function () {\r\n                return _this.sendMessage(cancelMessage);\r\n            });\r\n        };\r\n        this.callbacks[invocationDescriptor.invocationId] = function (invocationEvent, error) {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            }\r\n            else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    }\r\n                    else {\r\n                        subject.complete();\r\n                    }\r\n                }\r\n                else {\r\n                    subject.next((invocationEvent.item));\r\n                }\r\n            }\r\n        };\r\n        var message = this.protocol.writeMessage(invocationDescriptor);\r\n        promiseQueue = this.sendMessage(message)\r\n            .catch(function (e) {\r\n            subject.error(e);\r\n            delete _this.callbacks[invocationDescriptor.invocationId];\r\n        });\r\n        return subject;\r\n    };\r\n    HubConnection.prototype.sendMessage = function (message) {\r\n        this.resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    };\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    HubConnection.prototype.send = function (methodName) {\r\n        var args = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            args[_i - 1] = arguments[_i];\r\n        }\r\n        var invocationDescriptor = this.createInvocation(methodName, args, true);\r\n        var message = this.protocol.writeMessage(invocationDescriptor);\r\n        return this.sendMessage(message);\r\n    };\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    HubConnection.prototype.invoke = function (methodName) {\r\n        var _this = this;\r\n        var args = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            args[_i - 1] = arguments[_i];\r\n        }\r\n        var invocationDescriptor = this.createInvocation(methodName, args, false);\r\n        var p = new Promise(function (resolve, reject) {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            _this.callbacks[invocationDescriptor.invocationId] = function (invocationEvent, error) {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                }\r\n                else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        }\r\n                        else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    }\r\n                    else {\r\n                        reject(new Error(\"Unexpected message type: \" + invocationEvent.type));\r\n                    }\r\n                }\r\n            };\r\n            var message = _this.protocol.writeMessage(invocationDescriptor);\r\n            _this.sendMessage(message)\r\n                .catch(function (e) {\r\n                reject(e);\r\n                // invocationId will always have a value for a non-blocking invocation\r\n                delete _this.callbacks[invocationDescriptor.invocationId];\r\n            });\r\n        });\r\n        return p;\r\n    };\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    HubConnection.prototype.on = function (methodName, newMethod) {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        if (!this.methods[methodName]) {\r\n            this.methods[methodName] = [];\r\n        }\r\n        // Preventing adding the same handler multiple times.\r\n        if (this.methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n        this.methods[methodName].push(newMethod);\r\n    };\r\n    HubConnection.prototype.off = function (methodName, method) {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        var handlers = this.methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            var removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this.methods[methodName];\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            delete this.methods[methodName];\r\n        }\r\n    };\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    HubConnection.prototype.onclose = function (callback) {\r\n        if (callback) {\r\n            this.closedCallbacks.push(callback);\r\n        }\r\n    };\r\n    HubConnection.prototype.processIncomingData = function (data) {\r\n        this.cleanupTimeout();\r\n        if (!this.receivedHandshakeResponse) {\r\n            data = this.processHandshakeResponse(data);\r\n            this.receivedHandshakeResponse = true;\r\n        }\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            var messages = this.protocol.parseMessages(data, this.logger);\r\n            for (var _i = 0, messages_1 = messages; _i < messages_1.length; _i++) {\r\n                var message = messages_1[_i];\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this.invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion:\r\n                        var callback = this.callbacks[message.invocationId];\r\n                        if (callback != null) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this.callbacks[message.invocationId];\r\n                            }\r\n                            callback(message);\r\n                        }\r\n                        break;\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close:\r\n                        this.logger.log(LogLevel.Information, \"Close message received from server.\");\r\n                        // We don't want to wait on the stop itself.\r\n                        // tslint:disable-next-line:no-floating-promises\r\n                        this.connection.stop(message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined);\r\n                        break;\r\n                    default:\r\n                        this.logger.log(LogLevel.Warning, \"Invalid message type: \" + message.type + \".\");\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n        this.resetTimeoutPeriod();\r\n    };\r\n    HubConnection.prototype.processHandshakeResponse = function (data) {\r\n        var _a;\r\n        var responseMessage;\r\n        var remainingData;\r\n        try {\r\n            _a = this.handshakeProtocol.parseHandshakeResponse(data), remainingData = _a[0], responseMessage = _a[1];\r\n        }\r\n        catch (e) {\r\n            var message = \"Error parsing handshake response: \" + e;\r\n            this.logger.log(LogLevel.Error, message);\r\n            var error = new Error(message);\r\n            // We don't want to wait on the stop itself.\r\n            // tslint:disable-next-line:no-floating-promises\r\n            this.connection.stop(error);\r\n            this.handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            var message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this.logger.log(LogLevel.Error, message);\r\n            this.handshakeRejecter(message);\r\n            // We don't want to wait on the stop itself.\r\n            // tslint:disable-next-line:no-floating-promises\r\n            this.connection.stop(new Error(message));\r\n            throw new Error(message);\r\n        }\r\n        else {\r\n            this.logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n        this.handshakeResolver();\r\n        return remainingData;\r\n    };\r\n    HubConnection.prototype.resetKeepAliveInterval = function () {\r\n        var _this = this;\r\n        this.cleanupPingTimer();\r\n        this.pingServerHandle = setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\r\n            var _a;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        if (!(this.connectionState === HubConnectionState.Connected)) return [3 /*break*/, 4];\r\n                        _b.label = 1;\r\n                    case 1:\r\n                        _b.trys.push([1, 3, , 4]);\r\n                        return [4 /*yield*/, this.sendMessage(this.cachedPingMessage)];\r\n                    case 2:\r\n                        _b.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3:\r\n                        _a = _b.sent();\r\n                        // We don't care about the error. It should be seen elsewhere in the client.\r\n                        // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                        this.cleanupPingTimer();\r\n                        return [3 /*break*/, 4];\r\n                    case 4: return [2 /*return*/];\r\n                }\r\n            });\r\n        }); }, this.keepAliveIntervalInMilliseconds);\r\n    };\r\n    HubConnection.prototype.resetTimeoutPeriod = function () {\r\n        var _this = this;\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this.timeoutHandle = setTimeout(function () { return _this.serverTimeout(); }, this.serverTimeoutInMilliseconds);\r\n        }\r\n    };\r\n    HubConnection.prototype.serverTimeout = function () {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise.\r\n        // tslint:disable-next-line:no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    };\r\n    HubConnection.prototype.invokeClientMethod = function (invocationMessage) {\r\n        var _this = this;\r\n        var methods = this.methods[invocationMessage.target.toLowerCase()];\r\n        if (methods) {\r\n            methods.forEach(function (m) { return m.apply(_this, invocationMessage.arguments); });\r\n            if (invocationMessage.invocationId) {\r\n                // This is not supported in v1. So we return an error to avoid blocking the server waiting for the response.\r\n                var message = \"Server requested a response, which is not supported in this version of the client.\";\r\n                this.logger.log(LogLevel.Error, message);\r\n                // We don't need to wait on this Promise.\r\n                // tslint:disable-next-line:no-floating-promises\r\n                this.connection.stop(new Error(message));\r\n            }\r\n        }\r\n        else {\r\n            this.logger.log(LogLevel.Warning, \"No client method with the name '\" + invocationMessage.target + \"' found.\");\r\n        }\r\n    };\r\n    HubConnection.prototype.connectionClosed = function (error) {\r\n        var _this = this;\r\n        var callbacks = this.callbacks;\r\n        this.callbacks = {};\r\n        this.connectionState = HubConnectionState.Disconnected;\r\n        // if handshake is in progress start will be waiting for the handshake promise, so we complete it\r\n        // if it has already completed this should just noop\r\n        if (this.handshakeRejecter) {\r\n            this.handshakeRejecter(error);\r\n        }\r\n        Object.keys(callbacks)\r\n            .forEach(function (key) {\r\n            var callback = callbacks[key];\r\n            callback(null, error ? error : new Error(\"Invocation canceled due to connection being closed.\"));\r\n        });\r\n        this.cleanupTimeout();\r\n        this.cleanupPingTimer();\r\n        this.closedCallbacks.forEach(function (c) { return c.apply(_this, [error]); });\r\n    };\r\n    HubConnection.prototype.cleanupPingTimer = function () {\r\n        if (this.pingServerHandle) {\r\n            clearTimeout(this.pingServerHandle);\r\n        }\r\n    };\r\n    HubConnection.prototype.cleanupTimeout = function () {\r\n        if (this.timeoutHandle) {\r\n            clearTimeout(this.timeoutHandle);\r\n        }\r\n    };\r\n    HubConnection.prototype.createInvocation = function (methodName, args, nonblocking) {\r\n        if (nonblocking) {\r\n            return {\r\n                arguments: args,\r\n                target: methodName,\r\n                type: MessageType.Invocation,\r\n            };\r\n        }\r\n        else {\r\n            var id = this.id;\r\n            this.id++;\r\n            return {\r\n                arguments: args,\r\n                invocationId: id.toString(),\r\n                target: methodName,\r\n                type: MessageType.Invocation,\r\n            };\r\n        }\r\n    };\r\n    HubConnection.prototype.createStreamInvocation = function (methodName, args) {\r\n        var id = this.id;\r\n        this.id++;\r\n        return {\r\n            arguments: args,\r\n            invocationId: id.toString(),\r\n            target: methodName,\r\n            type: MessageType.StreamInvocation,\r\n        };\r\n    };\r\n    HubConnection.prototype.createCancelInvocation = function (id) {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    };\r\n    return HubConnection;\r\n}());\r\nexport { HubConnection };\r\n//# sourceMappingURL=HubConnection.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport var HttpTransportType;\r\n(function (HttpTransportType) {\r\n    /** Specifies no transport preference. */\r\n    HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\r\n    /** Specifies the WebSockets transport. */\r\n    HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\r\n    /** Specifies the Server-Sent Events transport. */\r\n    HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\r\n    /** Specifies the Long Polling transport. */\r\n    HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\r\n})(HttpTransportType || (HttpTransportType = {}));\r\n/** Specifies the transfer format for a connection. */\r\nexport var TransferFormat;\r\n(function (TransferFormat) {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\r\n})(TransferFormat || (TransferFormat = {}));\r\n//# sourceMappingURL=ITransport.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n// Not exported from index.\r\n/** @private */\r\nvar AbortController = /** @class */ (function () {\r\n    function AbortController() {\r\n        this.isAborted = false;\r\n        this.onabort = null;\r\n    }\r\n    AbortController.prototype.abort = function () {\r\n        if (!this.isAborted) {\r\n            this.isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    };\r\n    Object.defineProperty(AbortController.prototype, \"signal\", {\r\n        get: function () {\r\n            return this;\r\n        },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(AbortController.prototype, \"aborted\", {\r\n        get: function () {\r\n            return this.isAborted;\r\n        },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    return AbortController;\r\n}());\r\nexport { AbortController };\r\n//# sourceMappingURL=AbortController.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, sendMessage } from \"./Utils\";\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nvar LongPollingTransport = /** @class */ (function () {\r\n    function LongPollingTransport(httpClient, accessTokenFactory, logger, logMessageContent) {\r\n        this.httpClient = httpClient;\r\n        this.accessTokenFactory = accessTokenFactory;\r\n        this.logger = logger;\r\n        this.pollAbort = new AbortController();\r\n        this.logMessageContent = logMessageContent;\r\n        this.running = false;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    Object.defineProperty(LongPollingTransport.prototype, \"pollAborted\", {\r\n        // This is an internal type, not exported from 'index' so this is really just internal.\r\n        get: function () {\r\n            return this.pollAbort.aborted;\r\n        },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    LongPollingTransport.prototype.connect = function (url, transferFormat) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var pollOptions, token, pollUrl, response;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        Arg.isRequired(url, \"url\");\r\n                        Arg.isRequired(transferFormat, \"transferFormat\");\r\n                        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n                        this.url = url;\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n                        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n                        if (transferFormat === TransferFormat.Binary &&\r\n                            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n                            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n                        }\r\n                        pollOptions = {\r\n                            abortSignal: this.pollAbort.signal,\r\n                            headers: {},\r\n                            timeout: 100000,\r\n                        };\r\n                        if (transferFormat === TransferFormat.Binary) {\r\n                            pollOptions.responseType = \"arraybuffer\";\r\n                        }\r\n                        return [4 /*yield*/, this.getAccessToken()];\r\n                    case 1:\r\n                        token = _a.sent();\r\n                        this.updateHeaderToken(pollOptions, token);\r\n                        pollUrl = url + \"&_=\" + Date.now();\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) polling: \" + pollUrl + \".\");\r\n                        return [4 /*yield*/, this.httpClient.get(pollUrl, pollOptions)];\r\n                    case 2:\r\n                        response = _a.sent();\r\n                        if (response.statusCode !== 200) {\r\n                            this.logger.log(LogLevel.Error, \"(LongPolling transport) Unexpected response code: \" + response.statusCode + \".\");\r\n                            // Mark running as false so that the poll immediately ends and runs the close logic\r\n                            this.closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                            this.running = false;\r\n                        }\r\n                        else {\r\n                            this.running = true;\r\n                        }\r\n                        this.receiving = this.poll(this.url, pollOptions);\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    LongPollingTransport.prototype.getAccessToken = function () {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        if (!this.accessTokenFactory) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.accessTokenFactory()];\r\n                    case 1: return [2 /*return*/, _a.sent()];\r\n                    case 2: return [2 /*return*/, null];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    LongPollingTransport.prototype.updateHeaderToken = function (request, token) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (token) {\r\n            // tslint:disable-next-line:no-string-literal\r\n            request.headers[\"Authorization\"] = \"Bearer \" + token;\r\n            return;\r\n        }\r\n        // tslint:disable-next-line:no-string-literal\r\n        if (request.headers[\"Authorization\"]) {\r\n            // tslint:disable-next-line:no-string-literal\r\n            delete request.headers[\"Authorization\"];\r\n        }\r\n    };\r\n    LongPollingTransport.prototype.poll = function (url, pollOptions) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var token, pollUrl, response, e_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        _a.trys.push([0, , 8, 9]);\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        if (!this.running) return [3 /*break*/, 7];\r\n                        return [4 /*yield*/, this.getAccessToken()];\r\n                    case 2:\r\n                        token = _a.sent();\r\n                        this.updateHeaderToken(pollOptions, token);\r\n                        _a.label = 3;\r\n                    case 3:\r\n                        _a.trys.push([3, 5, , 6]);\r\n                        pollUrl = url + \"&_=\" + Date.now();\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) polling: \" + pollUrl + \".\");\r\n                        return [4 /*yield*/, this.httpClient.get(pollUrl, pollOptions)];\r\n                    case 4:\r\n                        response = _a.sent();\r\n                        if (response.statusCode === 204) {\r\n                            this.logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n                            this.running = false;\r\n                        }\r\n                        else if (response.statusCode !== 200) {\r\n                            this.logger.log(LogLevel.Error, \"(LongPolling transport) Unexpected response code: \" + response.statusCode + \".\");\r\n                            // Unexpected status code\r\n                            this.closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                            this.running = false;\r\n                        }\r\n                        else {\r\n                            // Process the response\r\n                            if (response.content) {\r\n                                this.logger.log(LogLevel.Trace, \"(LongPolling transport) data received. \" + getDataDetail(response.content, this.logMessageContent) + \".\");\r\n                                if (this.onreceive) {\r\n                                    this.onreceive(response.content);\r\n                                }\r\n                            }\r\n                            else {\r\n                                // This is another way timeout manifest.\r\n                                this.logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                            }\r\n                        }\r\n                        return [3 /*break*/, 6];\r\n                    case 5:\r\n                        e_1 = _a.sent();\r\n                        if (!this.running) {\r\n                            // Log but disregard errors that occur after stopping\r\n                            this.logger.log(LogLevel.Trace, \"(LongPolling transport) Poll errored after shutdown: \" + e_1.message);\r\n                        }\r\n                        else {\r\n                            if (e_1 instanceof TimeoutError) {\r\n                                // Ignore timeouts and reissue the poll.\r\n                                this.logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                            }\r\n                            else {\r\n                                // Close the connection with the error as the result.\r\n                                this.closeError = e_1;\r\n                                this.running = false;\r\n                            }\r\n                        }\r\n                        return [3 /*break*/, 6];\r\n                    case 6: return [3 /*break*/, 1];\r\n                    case 7: return [3 /*break*/, 9];\r\n                    case 8:\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n                        // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n                        // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n                        if (!this.pollAborted) {\r\n                            this.raiseOnClose();\r\n                        }\r\n                        return [7 /*endfinally*/];\r\n                    case 9: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    LongPollingTransport.prototype.send = function (data) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                if (!this.running) {\r\n                    return [2 /*return*/, Promise.reject(new Error(\"Cannot send until the transport is connected\"))];\r\n                }\r\n                return [2 /*return*/, sendMessage(this.logger, \"LongPolling\", this.httpClient, this.url, this.accessTokenFactory, data, this.logMessageContent)];\r\n            });\r\n        });\r\n    };\r\n    LongPollingTransport.prototype.stop = function () {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var deleteOptions, token;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n                        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n                        this.running = false;\r\n                        this.pollAbort.abort();\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        _a.trys.push([1, , 5, 6]);\r\n                        return [4 /*yield*/, this.receiving];\r\n                    case 2:\r\n                        _a.sent();\r\n                        // Send DELETE to clean up long polling on the server\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) sending DELETE request to \" + this.url + \".\");\r\n                        deleteOptions = {\r\n                            headers: {},\r\n                        };\r\n                        return [4 /*yield*/, this.getAccessToken()];\r\n                    case 3:\r\n                        token = _a.sent();\r\n                        this.updateHeaderToken(deleteOptions, token);\r\n                        return [4 /*yield*/, this.httpClient.delete(this.url, deleteOptions)];\r\n                    case 4:\r\n                        _a.sent();\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request sent.\");\r\n                        return [3 /*break*/, 6];\r\n                    case 5:\r\n                        this.logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n                        // Raise close event here instead of in polling\r\n                        // It needs to happen after the DELETE request is sent\r\n                        this.raiseOnClose();\r\n                        return [7 /*endfinally*/];\r\n                    case 6: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    LongPollingTransport.prototype.raiseOnClose = function () {\r\n        if (this.onclose) {\r\n            var logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this.closeError) {\r\n                logMessage += \" Error: \" + this.closeError;\r\n            }\r\n            this.logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this.closeError);\r\n        }\r\n    };\r\n    return LongPollingTransport;\r\n}());\r\nexport { LongPollingTransport };\r\n//# sourceMappingURL=LongPollingTransport.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, sendMessage } from \"./Utils\";\r\n/** @private */\r\nvar ServerSentEventsTransport = /** @class */ (function () {\r\n    function ServerSentEventsTransport(httpClient, accessTokenFactory, logger, logMessageContent, eventSourceConstructor) {\r\n        this.httpClient = httpClient;\r\n        this.accessTokenFactory = accessTokenFactory;\r\n        this.logger = logger;\r\n        this.logMessageContent = logMessageContent;\r\n        this.eventSourceConstructor = eventSourceConstructor;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    ServerSentEventsTransport.prototype.connect = function (url, transferFormat) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var token;\r\n            var _this = this;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        Arg.isRequired(url, \"url\");\r\n                        Arg.isRequired(transferFormat, \"transferFormat\");\r\n                        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n                        this.logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n                        // set url before accessTokenFactory because this.url is only for send and we set the auth header instead of the query string for send\r\n                        this.url = url;\r\n                        if (!this.accessTokenFactory) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.accessTokenFactory()];\r\n                    case 1:\r\n                        token = _a.sent();\r\n                        if (token) {\r\n                            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + (\"access_token=\" + encodeURIComponent(token));\r\n                        }\r\n                        _a.label = 2;\r\n                    case 2: return [2 /*return*/, new Promise(function (resolve, reject) {\r\n                            var opened = false;\r\n                            if (transferFormat !== TransferFormat.Text) {\r\n                                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                                return;\r\n                            }\r\n                            var eventSource;\r\n                            if (typeof window !== \"undefined\") {\r\n                                eventSource = new _this.eventSourceConstructor(url, { withCredentials: true });\r\n                            }\r\n                            else {\r\n                                // Non-browser passes cookies via the dictionary\r\n                                var cookies = _this.httpClient.getCookieString(url);\r\n                                eventSource = new _this.eventSourceConstructor(url, { withCredentials: true, headers: { Cookie: cookies } });\r\n                            }\r\n                            try {\r\n                                eventSource.onmessage = function (e) {\r\n                                    if (_this.onreceive) {\r\n                                        try {\r\n                                            _this.logger.log(LogLevel.Trace, \"(SSE transport) data received. \" + getDataDetail(e.data, _this.logMessageContent) + \".\");\r\n                                            _this.onreceive(e.data);\r\n                                        }\r\n                                        catch (error) {\r\n                                            _this.close(error);\r\n                                            return;\r\n                                        }\r\n                                    }\r\n                                };\r\n                                eventSource.onerror = function (e) {\r\n                                    var error = new Error(e.data || \"Error occurred\");\r\n                                    if (opened) {\r\n                                        _this.close(error);\r\n                                    }\r\n                                    else {\r\n                                        reject(error);\r\n                                    }\r\n                                };\r\n                                eventSource.onopen = function () {\r\n                                    _this.logger.log(LogLevel.Information, \"SSE connected to \" + _this.url);\r\n                                    _this.eventSource = eventSource;\r\n                                    opened = true;\r\n                                    resolve();\r\n                                };\r\n                            }\r\n                            catch (e) {\r\n                                reject(e);\r\n                                return;\r\n                            }\r\n                        })];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    ServerSentEventsTransport.prototype.send = function (data) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                if (!this.eventSource) {\r\n                    return [2 /*return*/, Promise.reject(new Error(\"Cannot send until the transport is connected\"))];\r\n                }\r\n                return [2 /*return*/, sendMessage(this.logger, \"SSE\", this.httpClient, this.url, this.accessTokenFactory, data, this.logMessageContent)];\r\n            });\r\n        });\r\n    };\r\n    ServerSentEventsTransport.prototype.stop = function () {\r\n        this.close();\r\n        return Promise.resolve();\r\n    };\r\n    ServerSentEventsTransport.prototype.close = function (e) {\r\n        if (this.eventSource) {\r\n            this.eventSource.close();\r\n            this.eventSource = undefined;\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    };\r\n    return ServerSentEventsTransport;\r\n}());\r\nexport { ServerSentEventsTransport };\r\n//# sourceMappingURL=ServerSentEventsTransport.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail } from \"./Utils\";\r\n/** @private */\r\nvar WebSocketTransport = /** @class */ (function () {\r\n    function WebSocketTransport(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor) {\r\n        this.logger = logger;\r\n        this.accessTokenFactory = accessTokenFactory;\r\n        this.logMessageContent = logMessageContent;\r\n        this.webSocketConstructor = webSocketConstructor;\r\n        this.httpClient = httpClient;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    WebSocketTransport.prototype.connect = function (url, transferFormat) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var token;\r\n            var _this = this;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        Arg.isRequired(url, \"url\");\r\n                        Arg.isRequired(transferFormat, \"transferFormat\");\r\n                        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n                        this.logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n                        if (!this.accessTokenFactory) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.accessTokenFactory()];\r\n                    case 1:\r\n                        token = _a.sent();\r\n                        if (token) {\r\n                            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + (\"access_token=\" + encodeURIComponent(token));\r\n                        }\r\n                        _a.label = 2;\r\n                    case 2: return [2 /*return*/, new Promise(function (resolve, reject) {\r\n                            url = url.replace(/^http/, \"ws\");\r\n                            var webSocket;\r\n                            var cookies = _this.httpClient.getCookieString(url);\r\n                            if (typeof window === \"undefined\" && cookies) {\r\n                                // Only pass cookies when in non-browser environments\r\n                                webSocket = new _this.webSocketConstructor(url, undefined, {\r\n                                    headers: {\r\n                                        Cookie: \"\" + cookies,\r\n                                    },\r\n                                });\r\n                            }\r\n                            if (!webSocket) {\r\n                                // Chrome is not happy with passing 'undefined' as protocol\r\n                                webSocket = new _this.webSocketConstructor(url);\r\n                            }\r\n                            if (transferFormat === TransferFormat.Binary) {\r\n                                webSocket.binaryType = \"arraybuffer\";\r\n                            }\r\n                            // tslint:disable-next-line:variable-name\r\n                            webSocket.onopen = function (_event) {\r\n                                _this.logger.log(LogLevel.Information, \"WebSocket connected to \" + url + \".\");\r\n                                _this.webSocket = webSocket;\r\n                                resolve();\r\n                            };\r\n                            webSocket.onerror = function (event) {\r\n                                var error = null;\r\n                                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                                    error = event.error;\r\n                                }\r\n                                reject(error);\r\n                            };\r\n                            webSocket.onmessage = function (message) {\r\n                                _this.logger.log(LogLevel.Trace, \"(WebSockets transport) data received. \" + getDataDetail(message.data, _this.logMessageContent) + \".\");\r\n                                if (_this.onreceive) {\r\n                                    _this.onreceive(message.data);\r\n                                }\r\n                            };\r\n                            webSocket.onclose = function (event) { return _this.close(event); };\r\n                        })];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    WebSocketTransport.prototype.send = function (data) {\r\n        if (this.webSocket && this.webSocket.readyState === this.webSocketConstructor.OPEN) {\r\n            this.logger.log(LogLevel.Trace, \"(WebSockets transport) sending data. \" + getDataDetail(data, this.logMessageContent) + \".\");\r\n            this.webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    };\r\n    WebSocketTransport.prototype.stop = function () {\r\n        if (this.webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this.webSocket.onclose = function () { };\r\n            this.webSocket.onmessage = function () { };\r\n            this.webSocket.onerror = function () { };\r\n            this.webSocket.close();\r\n            this.webSocket = undefined;\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this.close(undefined);\r\n        }\r\n        return Promise.resolve();\r\n    };\r\n    WebSocketTransport.prototype.close = function (event) {\r\n        // webSocket will be null if the transport did not start successfully\r\n        this.logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (event && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(\"WebSocket closed with status code: \" + event.code + \" (\" + event.reason + \").\"));\r\n            }\r\n            else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    };\r\n    return WebSocketTransport;\r\n}());\r\nexport { WebSocketTransport };\r\n//# sourceMappingURL=WebSocketTransport.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\nvar MAX_REDIRECTS = 100;\r\nvar WebSocketModule = null;\r\nvar EventSourceModule = null;\r\nif (typeof window === \"undefined\" && typeof require !== \"undefined\") {\r\n    // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n    // @ts-ignore: TS doesn't know about these names\r\n    var requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n    WebSocketModule = requireFunc(\"ws\");\r\n    EventSourceModule = requireFunc(\"eventsource\");\r\n}\r\n/** @private */\r\nvar HttpConnection = /** @class */ (function () {\r\n    function HttpConnection(url, options) {\r\n        if (options === void 0) { options = {}; }\r\n        this.features = {};\r\n        Arg.isRequired(url, \"url\");\r\n        this.logger = createLogger(options.logger);\r\n        this.baseUrl = this.resolveUrl(url);\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent || false;\r\n        var isNode = typeof window === \"undefined\";\r\n        if (!isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        }\r\n        else if (isNode && !options.WebSocket) {\r\n            if (WebSocketModule) {\r\n                options.WebSocket = WebSocketModule;\r\n            }\r\n        }\r\n        if (!isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        }\r\n        else if (isNode && !options.EventSource) {\r\n            if (typeof EventSourceModule !== \"undefined\") {\r\n                options.EventSource = EventSourceModule;\r\n            }\r\n        }\r\n        this.httpClient = options.httpClient || new DefaultHttpClient(this.logger);\r\n        this.connectionState = 2 /* Disconnected */;\r\n        this.options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    HttpConnection.prototype.start = function (transferFormat) {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this.logger.log(LogLevel.Debug, \"Starting connection with transfer format '\" + TransferFormat[transferFormat] + \"'.\");\r\n        if (this.connectionState !== 2 /* Disconnected */) {\r\n            return Promise.reject(new Error(\"Cannot start a connection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this.connectionState = 0 /* Connecting */;\r\n        this.startPromise = this.startInternal(transferFormat);\r\n        return this.startPromise;\r\n    };\r\n    HttpConnection.prototype.send = function (data) {\r\n        if (this.connectionState !== 1 /* Connected */) {\r\n            throw new Error(\"Cannot send data if the connection is not in the 'Connected' State.\");\r\n        }\r\n        // Transport will not be null if state is connected\r\n        return this.transport.send(data);\r\n    };\r\n    HttpConnection.prototype.stop = function (error) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var e_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        this.connectionState = 2 /* Disconnected */;\r\n                        // Set error as soon as possible otherwise there is a race between\r\n                        // the transport closing and providing an error and the error from a close message\r\n                        // We would prefer the close message error.\r\n                        this.stopError = error;\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        _a.trys.push([1, 3, , 4]);\r\n                        return [4 /*yield*/, this.startPromise];\r\n                    case 2:\r\n                        _a.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3:\r\n                        e_1 = _a.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 4:\r\n                        if (!this.transport) return [3 /*break*/, 6];\r\n                        return [4 /*yield*/, this.transport.stop()];\r\n                    case 5:\r\n                        _a.sent();\r\n                        this.transport = undefined;\r\n                        _a.label = 6;\r\n                    case 6: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    HttpConnection.prototype.startInternal = function (transferFormat) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var url, negotiateResponse, redirects, _loop_1, this_1, state_1, e_2;\r\n            var _this = this;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        url = this.baseUrl;\r\n                        this.accessTokenFactory = this.options.accessTokenFactory;\r\n                        _a.label = 1;\r\n                    case 1:\r\n                        _a.trys.push([1, 12, , 13]);\r\n                        if (!this.options.skipNegotiation) return [3 /*break*/, 5];\r\n                        if (!(this.options.transport === HttpTransportType.WebSockets)) return [3 /*break*/, 3];\r\n                        // No need to add a connection ID in this case\r\n                        this.transport = this.constructTransport(HttpTransportType.WebSockets);\r\n                        // We should just call connect directly in this case.\r\n                        // No fallback or negotiate in this case.\r\n                        return [4 /*yield*/, this.transport.connect(url, transferFormat)];\r\n                    case 2:\r\n                        // We should just call connect directly in this case.\r\n                        // No fallback or negotiate in this case.\r\n                        _a.sent();\r\n                        return [3 /*break*/, 4];\r\n                    case 3: throw Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                    case 4: return [3 /*break*/, 11];\r\n                    case 5:\r\n                        negotiateResponse = null;\r\n                        redirects = 0;\r\n                        _loop_1 = function () {\r\n                            var accessToken_1;\r\n                            return __generator(this, function (_a) {\r\n                                switch (_a.label) {\r\n                                    case 0: return [4 /*yield*/, this_1.getNegotiationResponse(url)];\r\n                                    case 1:\r\n                                        negotiateResponse = _a.sent();\r\n                                        // the user tries to stop the connection when it is being started\r\n                                        if (this_1.connectionState === 2 /* Disconnected */) {\r\n                                            return [2 /*return*/, { value: void 0 }];\r\n                                        }\r\n                                        if (negotiateResponse.error) {\r\n                                            throw Error(negotiateResponse.error);\r\n                                        }\r\n                                        if (negotiateResponse.ProtocolVersion) {\r\n                                            throw Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                                        }\r\n                                        if (negotiateResponse.url) {\r\n                                            url = negotiateResponse.url;\r\n                                        }\r\n                                        if (negotiateResponse.accessToken) {\r\n                                            accessToken_1 = negotiateResponse.accessToken;\r\n                                            this_1.accessTokenFactory = function () { return accessToken_1; };\r\n                                        }\r\n                                        redirects++;\r\n                                        return [2 /*return*/];\r\n                                }\r\n                            });\r\n                        };\r\n                        this_1 = this;\r\n                        _a.label = 6;\r\n                    case 6: return [5 /*yield**/, _loop_1()];\r\n                    case 7:\r\n                        state_1 = _a.sent();\r\n                        if (typeof state_1 === \"object\")\r\n                            return [2 /*return*/, state_1.value];\r\n                        _a.label = 8;\r\n                    case 8:\r\n                        if (negotiateResponse.url && redirects < MAX_REDIRECTS) return [3 /*break*/, 6];\r\n                        _a.label = 9;\r\n                    case 9:\r\n                        if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                            throw Error(\"Negotiate redirection limit exceeded.\");\r\n                        }\r\n                        return [4 /*yield*/, this.createTransport(url, this.options.transport, negotiateResponse, transferFormat)];\r\n                    case 10:\r\n                        _a.sent();\r\n                        _a.label = 11;\r\n                    case 11:\r\n                        if (this.transport instanceof LongPollingTransport) {\r\n                            this.features.inherentKeepAlive = true;\r\n                        }\r\n                        this.transport.onreceive = this.onreceive;\r\n                        this.transport.onclose = function (e) { return _this.stopConnection(e); };\r\n                        // only change the state if we were connecting to not overwrite\r\n                        // the state if the connection is already marked as Disconnected\r\n                        this.changeState(0 /* Connecting */, 1 /* Connected */);\r\n                        return [3 /*break*/, 13];\r\n                    case 12:\r\n                        e_2 = _a.sent();\r\n                        this.logger.log(LogLevel.Error, \"Failed to start the connection: \" + e_2);\r\n                        this.connectionState = 2 /* Disconnected */;\r\n                        this.transport = undefined;\r\n                        throw e_2;\r\n                    case 13: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    HttpConnection.prototype.getNegotiationResponse = function (url) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var _a, headers, token, negotiateUrl, response, e_3;\r\n            return __generator(this, function (_b) {\r\n                switch (_b.label) {\r\n                    case 0:\r\n                        if (!this.accessTokenFactory) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, this.accessTokenFactory()];\r\n                    case 1:\r\n                        token = _b.sent();\r\n                        if (token) {\r\n                            headers = (_a = {},\r\n                                _a[\"Authorization\"] = \"Bearer \" + token,\r\n                                _a);\r\n                        }\r\n                        _b.label = 2;\r\n                    case 2:\r\n                        negotiateUrl = this.resolveNegotiateUrl(url);\r\n                        this.logger.log(LogLevel.Debug, \"Sending negotiation request: \" + negotiateUrl + \".\");\r\n                        _b.label = 3;\r\n                    case 3:\r\n                        _b.trys.push([3, 5, , 6]);\r\n                        return [4 /*yield*/, this.httpClient.post(negotiateUrl, {\r\n                                content: \"\",\r\n                                headers: headers,\r\n                            })];\r\n                    case 4:\r\n                        response = _b.sent();\r\n                        if (response.statusCode !== 200) {\r\n                            throw Error(\"Unexpected status code returned from negotiate \" + response.statusCode);\r\n                        }\r\n                        return [2 /*return*/, JSON.parse(response.content)];\r\n                    case 5:\r\n                        e_3 = _b.sent();\r\n                        this.logger.log(LogLevel.Error, \"Failed to complete negotiation with the server: \" + e_3);\r\n                        throw e_3;\r\n                    case 6: return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    };\r\n    HttpConnection.prototype.createConnectUrl = function (url, connectionId) {\r\n        if (!connectionId) {\r\n            return url;\r\n        }\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + (\"id=\" + connectionId);\r\n    };\r\n    HttpConnection.prototype.createTransport = function (url, requestedTransport, negotiateResponse, requestedTransferFormat) {\r\n        return __awaiter(this, void 0, void 0, function () {\r\n            var connectUrl, transports, _i, transports_1, endpoint, transport, ex_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        connectUrl = this.createConnectUrl(url, negotiateResponse.connectionId);\r\n                        if (!this.isITransport(requestedTransport)) return [3 /*break*/, 2];\r\n                        this.logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n                        this.transport = requestedTransport;\r\n                        return [4 /*yield*/, this.transport.connect(connectUrl, requestedTransferFormat)];\r\n                    case 1:\r\n                        _a.sent();\r\n                        // only change the state if we were connecting to not overwrite\r\n                        // the state if the connection is already marked as Disconnected\r\n                        this.changeState(0 /* Connecting */, 1 /* Connected */);\r\n                        return [2 /*return*/];\r\n                    case 2:\r\n                        transports = negotiateResponse.availableTransports || [];\r\n                        _i = 0, transports_1 = transports;\r\n                        _a.label = 3;\r\n                    case 3:\r\n                        if (!(_i < transports_1.length)) return [3 /*break*/, 9];\r\n                        endpoint = transports_1[_i];\r\n                        this.connectionState = 0 /* Connecting */;\r\n                        transport = this.resolveTransport(endpoint, requestedTransport, requestedTransferFormat);\r\n                        if (!(typeof transport === \"number\")) return [3 /*break*/, 8];\r\n                        this.transport = this.constructTransport(transport);\r\n                        if (!!negotiateResponse.connectionId) return [3 /*break*/, 5];\r\n                        return [4 /*yield*/, this.getNegotiationResponse(url)];\r\n                    case 4:\r\n                        negotiateResponse = _a.sent();\r\n                        connectUrl = this.createConnectUrl(url, negotiateResponse.connectionId);\r\n                        _a.label = 5;\r\n                    case 5:\r\n                        _a.trys.push([5, 7, , 8]);\r\n                        return [4 /*yield*/, this.transport.connect(connectUrl, requestedTransferFormat)];\r\n                    case 6:\r\n                        _a.sent();\r\n                        this.changeState(0 /* Connecting */, 1 /* Connected */);\r\n                        return [2 /*return*/];\r\n                    case 7:\r\n                        ex_1 = _a.sent();\r\n                        this.logger.log(LogLevel.Error, \"Failed to start the transport '\" + HttpTransportType[transport] + \"': \" + ex_1);\r\n                        this.connectionState = 2 /* Disconnected */;\r\n                        negotiateResponse.connectionId = undefined;\r\n                        return [3 /*break*/, 8];\r\n                    case 8:\r\n                        _i++;\r\n                        return [3 /*break*/, 3];\r\n                    case 9: throw new Error(\"Unable to initialize any of the available transports.\");\r\n                }\r\n            });\r\n        });\r\n    };\r\n    HttpConnection.prototype.constructTransport = function (transport) {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this.options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this.httpClient, this.accessTokenFactory, this.logger, this.options.logMessageContent || false, this.options.WebSocket);\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this.options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this.httpClient, this.accessTokenFactory, this.logger, this.options.logMessageContent || false, this.options.EventSource);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this.httpClient, this.accessTokenFactory, this.logger, this.options.logMessageContent || false);\r\n            default:\r\n                throw new Error(\"Unknown transport: \" + transport + \".\");\r\n        }\r\n    };\r\n    HttpConnection.prototype.resolveTransport = function (endpoint, requestedTransport, requestedTransferFormat) {\r\n        var transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this.logger.log(LogLevel.Debug, \"Skipping transport '\" + endpoint.transport + \"' because it is not supported by this client.\");\r\n        }\r\n        else {\r\n            var transferFormats = endpoint.transferFormats.map(function (s) { return TransferFormat[s]; });\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this.options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this.options.EventSource)) {\r\n                        this.logger.log(LogLevel.Debug, \"Skipping transport '\" + HttpTransportType[transport] + \"' because it is not supported in your environment.'\");\r\n                    }\r\n                    else {\r\n                        this.logger.log(LogLevel.Debug, \"Selecting transport '\" + HttpTransportType[transport] + \"'.\");\r\n                        return transport;\r\n                    }\r\n                }\r\n                else {\r\n                    this.logger.log(LogLevel.Debug, \"Skipping transport '\" + HttpTransportType[transport] + \"' because it does not support the requested transfer format '\" + TransferFormat[requestedTransferFormat] + \"'.\");\r\n                }\r\n            }\r\n            else {\r\n                this.logger.log(LogLevel.Debug, \"Skipping transport '\" + HttpTransportType[transport] + \"' because it was disabled by the client.\");\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n    HttpConnection.prototype.isITransport = function (transport) {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    };\r\n    HttpConnection.prototype.changeState = function (from, to) {\r\n        if (this.connectionState === from) {\r\n            this.connectionState = to;\r\n            return true;\r\n        }\r\n        return false;\r\n    };\r\n    HttpConnection.prototype.stopConnection = function (error) {\r\n        this.transport = undefined;\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this.stopError || error;\r\n        if (error) {\r\n            this.logger.log(LogLevel.Error, \"Connection disconnected with error '\" + error + \"'.\");\r\n        }\r\n        else {\r\n            this.logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n        this.connectionState = 2 /* Disconnected */;\r\n        if (this.onclose) {\r\n            this.onclose(error);\r\n        }\r\n    };\r\n    HttpConnection.prototype.resolveUrl = function (url) {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n        if (typeof window === \"undefined\" || !window || !window.document) {\r\n            throw new Error(\"Cannot resolve '\" + url + \"'.\");\r\n        }\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative  path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        var aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n        this.logger.log(LogLevel.Information, \"Normalizing '\" + url + \"' to '\" + aTag.href + \"'.\");\r\n        return aTag.href;\r\n    };\r\n    HttpConnection.prototype.resolveNegotiateUrl = function (url) {\r\n        var index = url.indexOf(\"?\");\r\n        var negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n        return negotiateUrl;\r\n    };\r\n    return HttpConnection;\r\n}());\r\nexport { HttpConnection };\r\nfunction transportMatches(requestedTransport, actualTransport) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n//# sourceMappingURL=HttpConnection.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nvar JSON_HUB_PROTOCOL_NAME = \"json\";\r\n/** Implements the JSON Hub Protocol. */\r\nvar JsonHubProtocol = /** @class */ (function () {\r\n    function JsonHubProtocol() {\r\n        /** @inheritDoc */\r\n        this.name = JSON_HUB_PROTOCOL_NAME;\r\n        /** @inheritDoc */\r\n        this.version = 1;\r\n        /** @inheritDoc */\r\n        this.transferFormat = TransferFormat.Text;\r\n    }\r\n    /** Creates an array of {@link @aspnet/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    JsonHubProtocol.prototype.parseMessages = function (input, logger) {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n        if (!input) {\r\n            return [];\r\n        }\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n        // Parse the messages\r\n        var messages = TextMessageFormat.parse(input);\r\n        var hubMessages = [];\r\n        for (var _i = 0, messages_1 = messages; _i < messages_1.length; _i++) {\r\n            var message = messages_1[_i];\r\n            var parsedMessage = JSON.parse(message);\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this.isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this.isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this.isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n        return hubMessages;\r\n    };\r\n    /** Writes the specified {@link @aspnet/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    JsonHubProtocol.prototype.writeMessage = function (message) {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    };\r\n    JsonHubProtocol.prototype.isInvocationMessage = function (message) {\r\n        this.assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n        if (message.invocationId !== undefined) {\r\n            this.assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    };\r\n    JsonHubProtocol.prototype.isStreamItemMessage = function (message) {\r\n        this.assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    };\r\n    JsonHubProtocol.prototype.isCompletionMessage = function (message) {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n        if (!message.result && message.error) {\r\n            this.assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n        this.assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    };\r\n    JsonHubProtocol.prototype.assertNotEmptyString = function (value, errorMessage) {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    };\r\n    return JsonHubProtocol;\r\n}());\r\nexport { JsonHubProtocol };\r\n//# sourceMappingURL=JsonHubProtocol.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n/** A builder for configuring {@link @aspnet/signalr.HubConnection} instances. */\r\nvar HubConnectionBuilder = /** @class */ (function () {\r\n    function HubConnectionBuilder() {\r\n    }\r\n    HubConnectionBuilder.prototype.configureLogging = function (logging) {\r\n        Arg.isRequired(logging, \"logging\");\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        }\r\n        else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n        return this;\r\n    };\r\n    HubConnectionBuilder.prototype.withUrl = function (url, transportTypeOrOptions) {\r\n        Arg.isRequired(url, \"url\");\r\n        this.url = url;\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = transportTypeOrOptions;\r\n        }\r\n        else {\r\n            this.httpConnectionOptions = {\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n        return this;\r\n    };\r\n    /** Configures the {@link @aspnet/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @aspnet/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    HubConnectionBuilder.prototype.withHubProtocol = function (protocol) {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n        this.protocol = protocol;\r\n        return this;\r\n    };\r\n    /** Creates a {@link @aspnet/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @aspnet/signalr.HubConnection}.\r\n     */\r\n    HubConnectionBuilder.prototype.build = function () {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        var httpConnectionOptions = this.httpConnectionOptions || {};\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        var connection = new HttpConnection(this.url, httpConnectionOptions);\r\n        return HubConnection.create(connection, this.logger || NullLogger.instance, this.protocol || new JsonHubProtocol());\r\n    };\r\n    return HubConnectionBuilder;\r\n}());\r\nexport { HubConnectionBuilder };\r\nfunction isLogger(logger) {\r\n    return logger.log !== undefined;\r\n}\r\n//# sourceMappingURL=HubConnectionBuilder.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\nexport var VERSION = \"1.1.4\";\r\nexport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nexport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nexport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nexport { HubConnection, HubConnectionState } from \"./HubConnection\";\r\nexport { HubConnectionBuilder } from \"./HubConnectionBuilder\";\r\nexport { MessageType } from \"./IHubProtocol\";\r\nexport { LogLevel } from \"./ILogger\";\r\nexport { HttpTransportType, TransferFormat } from \"./ITransport\";\r\nexport { NullLogger } from \"./Loggers\";\r\nexport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\n//# sourceMappingURL=index.js.map", "<template>\r\n    <section>\r\n        <div style=\"display: none1\">\r\n            <el-form ref=\"form\" label-width=\"80px\" @submit.prevent=\"onSubmit\"\r\n                     style=\"margin:20px;width:60%;min-width:600px;\">\r\n                <el-form-item label=\"用户名\">\r\n                    <el-input v-model=\"userName\"></el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"密码\">\r\n                    <el-input v-model=\"userMessage\"></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <ul v-for=\"(item, index) in messages\" v-bind:key=\"index + 'itemMessage'\">\r\n                <li><b>Name: </b>{{item.user}}</li>\r\n                <li><b>Message: </b>{{item.message}}</li>\r\n            </ul>\r\n            <el-button type=\"primary\" @click=\"submitCard\">登录</el-button>\r\n\r\n            <el-button type=\"primary\" @click=\"getLogs\">查询</el-button>\r\n        </div>\r\n\r\n\r\n        <el-table\r\n                :data=\"tableData\"\r\n                v-loading=\"listLoading\"\r\n                style=\"width: 100%\">\r\n            <el-table-column type=\"expand\">\r\n                <template slot-scope=\"props\">\r\n                    <el-form label-position=\"left\" inline class=\"demo-table-expand\">\r\n                        <el-form-item label=\"Datetime\">\r\n                            <span>{{ props.row.datetime }}</span>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"Content\">\r\n                            <span v-html=\"props.row.content\"></span>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n                    label=\"Datetime\"\r\n                    prop=\"datetime\">\r\n            </el-table-column>\r\n            <el-table-column\r\n                    label=\"Content\">\r\n                <template scope=\"scope\">\r\n                    <span :class=\"scope.row.logColor\"\r\n                          v-html=\"scope.row.content\"></span>\r\n                </template>\r\n            </el-table-column>\r\n            <!--<el-table-column-->\r\n                    <!--label=\"IP\"-->\r\n                    <!--prop=\"ip\">-->\r\n            <!--</el-table-column>-->\r\n        </el-table>\r\n    </section>\r\n</template>\r\n\r\n<script>\r\n    import util from '../../../util/date'\r\n    import {getLogs,BaseApiUrl} from '../../api/api';\r\n    import * as signalR from \"@aspnet/signalr\";\r\n\r\n    export default {\r\n        data() {\r\n            return {\r\n                filters: {\r\n                    LinkUrl: ''\r\n                },\r\n                listLoading: true,\r\n                tableData: [],\r\n                userName: \"Tom\",\r\n                userMessage: \"123\",\r\n                connection: \"\",\r\n                messages: [],\r\n                t: \"\"\r\n\r\n            }\r\n        },\r\n        methods: {\r\n            //性别显示转换\r\n            formattdDetail: function (row, column) {\r\n                return row.tdDetail ? row.tdDetail.substring(0, 20) : \"N/A\";\r\n            },\r\n            formatCreateTime: function (row, column) {\r\n                return (!row.tdCreatetime || row.tdCreatetime == '') ? '' : util.formatDate.format(new Date(row.tdCreatetime), 'yyyy-MM-dd');\r\n            },\r\n            handleCurrentChange(val) {\r\n                this.page = val;\r\n                this.getRoles();\r\n            },\r\n            //获取用户列表\r\n            getRoles() {\r\n                let thisvue=this;\r\n                let para = {\r\n                    page: this.page,\r\n                    key: this.filters.LinkUrl\r\n                };\r\n                this.listLoading = true;\r\n\r\n                getLogs(para).then((res) => {\r\n                    // this.tableData = res.data.response;\r\n                    thisvue.connection.start().then(() => {\r\n\r\n                        thisvue.connection.invoke('GetLatestCount', 1).catch(function (err) {\r\n                            return console.error(err);\r\n                        });\r\n\r\n                        // transactionConnection.invoke('JoinGroup', 'ClientAccountTransaction').catch(err => console.error(err.toString()));\r\n\r\n                    });\r\n\r\n\r\n                });\r\n            },\r\n            submitCard: function () {\r\n                if (this.userName && this.userMessage) {\r\n                    this.connection.invoke('SendMessage', this.userName, this.userMessage).catch(function (err) {\r\n                        return console.error(err);\r\n                    });\r\n\r\n                }\r\n            },\r\n            getLogs: function () {\r\n                this.listLoading = true;\r\n                this.connection.invoke('GetLatestCount', 1).catch(function (err) {\r\n                    return console.error(err);\r\n                });\r\n\r\n            }\r\n\r\n        },\r\n        created: function () {\r\n            let thisVue = this;\r\n\r\n            thisVue.connection = new signalR.HubConnectionBuilder()\r\n                .withUrl(`${BaseApiUrl}/api2/chatHub`)\r\n                .configureLogging(signalR.LogLevel.Information)\r\n                .build();\r\n\r\n\r\n\r\n            thisVue.connection.on('ReceiveMessage', function (user, message) {\r\n                thisVue.messages.push({user, message});\r\n            });\r\n\r\n            thisVue.connection.on('ReceiveUpdate', function (update) {\r\n                console.info('update success!')\r\n                thisVue.listLoading = false;\r\n                thisVue.tableData = update;\r\n                window.clearInterval(this.t)\r\n            })\r\n        },\r\n        mounted() {\r\n            this.getRoles();\r\n\r\n            //  this.t =  setTimeout(() => {\r\n            //      this.getLogs();\r\n            // }, 1000);\r\n\r\n        },\r\n        beforeDestroy() {\r\n            window.clearInterval(this.t)\r\n            this.connection.stop();\r\n        }\r\n    }\r\n\r\n</script>\r\n\r\n<style scoped>\r\n    .demo-table-expand {\r\n        font-size: 0;\r\n    }\r\n\r\n    .demo-table-expand label {\r\n        width: 90px;\r\n        color: #99a9bf;\r\n    }\r\n\r\n    .demo-table-expand .el-form-item {\r\n        margin-right: 0;\r\n        margin-bottom: 0;\r\n        width: 30%;\r\n    }\r\n\r\n    .EXC {\r\n        color: red;\r\n    }\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Index.vue?vue&type=template&id=6e805334&scoped=true&\"\nimport script from \"./Index.vue?vue&type=script&lang=js&\"\nexport * from \"./Index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Index.vue?vue&type=style&index=0&id=6e805334&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e805334\",\n  null\n  \n)\n\ncomponent.options.__file = \"Index.vue\"\nexport default component.exports", "var SIGN_REGEXP = /([yMdhsm])(\\1*)/g;\r\nvar DEFAULT_PATTERN = 'yyyy-MM-dd';\r\nfunction padding(s, len) {\r\n    var len = len - (s + '').length;\r\n    for (var i = 0; i < len; i++) { s = '0' + s; }\r\n    return s;\r\n};\r\n\r\nexport default {\r\n    getQueryStringByName: function (name) {\r\n        var reg = new RegExp(\"(^|&)\" + name + \"=([^&]*)(&|$)\", \"i\");\r\n        var r = window.location.search.substr(1).match(reg);\r\n        var context = \"\";\r\n        if (r != null)\r\n            context = r[2];\r\n        reg = null;\r\n        r = null;\r\n        return context == null || context == \"\" || context == \"undefined\" ? \"\" : context;\r\n    },\r\n    formatDate: {\r\n\r\n\r\n        format: function (date, pattern) {\r\n            pattern = pattern || DEFAULT_PATTERN;\r\n            return pattern.replace(SIGN_REGEXP, function ($0) {\r\n                switch ($0.charAt(0)) {\r\n                    case 'y': return padding(date.getFullYear(), $0.length);\r\n                    case 'M': return padding(date.getMonth() + 1, $0.length);\r\n                    case 'd': return padding(date.getDate(), $0.length);\r\n                    case 'w': return date.getDay() + 1;\r\n                    case 'h': return padding(date.getHours(), $0.length);\r\n                    case 'm': return padding(date.getMinutes(), $0.length);\r\n                    case 's': return padding(date.getSeconds(), $0.length);\r\n                }\r\n            });\r\n        },\r\n        parse: function (dateString, pattern) {\r\n            var matchs1 = pattern.match(SIGN_REGEXP);\r\n            var matchs2 = dateString.match(/(\\d)+/g);\r\n            if (matchs1.length == matchs2.length) {\r\n                var _date = new Date(1970, 0, 1);\r\n                for (var i = 0; i < matchs1.length; i++) {\r\n                    var _int = parseInt(matchs2[i]);\r\n                    var sign = matchs1[i];\r\n                    switch (sign.charAt(0)) {\r\n                        case 'y': _date.setFullYear(_int); break;\r\n                        case 'M': _date.setMonth(_int - 1); break;\r\n                        case 'd': _date.setDate(_int); break;\r\n                        case 'h': _date.setHours(_int); break;\r\n                        case 'm': _date.setMinutes(_int); break;\r\n                        case 's': _date.setSeconds(_int); break;\r\n                    }\r\n                }\r\n                return _date;\r\n            }\r\n            return null;\r\n        }\r\n\r\n    },\r\n    isEmt:{\r\n        format: function (obj) {\r\n            if(typeof obj == \"undefined\" || obj == null || obj == \"\"){\r\n                return true;\r\n            }else{\r\n                return false;\r\n            }\r\n        },\r\n    }\r\n\r\n};\r\n", "// 7.2.8 IsRegExp(argument)\nvar isObject = require('./_is-object');\nvar cof = require('./_cof');\nvar MATCH = require('./_wks')('match');\nmodule.exports = function (it) {\n  var isRegExp;\n  return isObject(it) && ((isRegExp = it[MATCH]) !== undefined ? !!isRegExp : cof(it) == 'RegExp');\n};\n", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\n/** Error thrown when an HTTP request fails. */\r\nvar HttpError = /** @class */ (function (_super) {\r\n    __extends(HttpError, _super);\r\n    /** Constructs a new instance of {@link @aspnet/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    function HttpError(errorMessage, statusCode) {\r\n        var _newTarget = this.constructor;\r\n        var _this = this;\r\n        var trueProto = _newTarget.prototype;\r\n        _this = _super.call(this, errorMessage) || this;\r\n        _this.statusCode = statusCode;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        _this.__proto__ = trueProto;\r\n        return _this;\r\n    }\r\n    return HttpError;\r\n}(Error));\r\nexport { HttpError };\r\n/** Error thrown when a timeout elapses. */\r\nvar TimeoutError = /** @class */ (function (_super) {\r\n    __extends(TimeoutError, _super);\r\n    /** Constructs a new instance of {@link @aspnet/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    function TimeoutError(errorMessage) {\r\n        var _newTarget = this.constructor;\r\n        if (errorMessage === void 0) { errorMessage = \"A timeout occurred.\"; }\r\n        var _this = this;\r\n        var trueProto = _newTarget.prototype;\r\n        _this = _super.call(this, errorMessage) || this;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        _this.__proto__ = trueProto;\r\n        return _this;\r\n    }\r\n    return TimeoutError;\r\n}(Error));\r\nexport { TimeoutError };\r\n/** Error thrown when an action is aborted. */\r\nvar AbortError = /** @class */ (function (_super) {\r\n    __extends(AbortError, _super);\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    function AbortError(errorMessage) {\r\n        var _newTarget = this.constructor;\r\n        if (errorMessage === void 0) { errorMessage = \"An abort occurred.\"; }\r\n        var _this = this;\r\n        var trueProto = _newTarget.prototype;\r\n        _this = _super.call(this, errorMessage) || this;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        _this.__proto__ = trueProto;\r\n        return _this;\r\n    }\r\n    return AbortError;\r\n}(Error));\r\nexport { AbortError };\r\n//# sourceMappingURL=Errors.js.map", "// Copyright (c) .NET Foundation. All rights reserved.\r\n// Licensed under the Apache License, Version 2.0. See License.txt in the project root for license information.\r\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n};\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\n/** @private */\r\nvar Arg = /** @class */ (function () {\r\n    function Arg() {\r\n    }\r\n    Arg.isRequired = function (val, name) {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(\"The '\" + name + \"' argument is required.\");\r\n        }\r\n    };\r\n    Arg.isIn = function (val, values, name) {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(\"Unknown \" + name + \" value: \" + val + \".\");\r\n        }\r\n    };\r\n    return Arg;\r\n}());\r\nexport { Arg };\r\n/** @private */\r\nexport function getDataDetail(data, includeContent) {\r\n    var detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = \"Binary data of length \" + data.byteLength;\r\n        if (includeContent) {\r\n            detail += \". Content: '\" + formatArrayBuffer(data) + \"'\";\r\n        }\r\n    }\r\n    else if (typeof data === \"string\") {\r\n        detail = \"String data of length \" + data.length;\r\n        if (includeContent) {\r\n            detail += \". Content: '\" + data + \"'\";\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n/** @private */\r\nexport function formatArrayBuffer(data) {\r\n    var view = new Uint8Array(data);\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    var str = \"\";\r\n    view.forEach(function (num) {\r\n        var pad = num < 16 ? \"0\" : \"\";\r\n        str += \"0x\" + pad + num.toString(16) + \" \";\r\n    });\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val) {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n/** @private */\r\nexport function sendMessage(logger, transportName, httpClient, url, accessTokenFactory, content, logMessageContent) {\r\n    return __awaiter(this, void 0, void 0, function () {\r\n        var _a, headers, token, responseType, response;\r\n        return __generator(this, function (_b) {\r\n            switch (_b.label) {\r\n                case 0:\r\n                    if (!accessTokenFactory) return [3 /*break*/, 2];\r\n                    return [4 /*yield*/, accessTokenFactory()];\r\n                case 1:\r\n                    token = _b.sent();\r\n                    if (token) {\r\n                        headers = (_a = {},\r\n                            _a[\"Authorization\"] = \"Bearer \" + token,\r\n                            _a);\r\n                    }\r\n                    _b.label = 2;\r\n                case 2:\r\n                    logger.log(LogLevel.Trace, \"(\" + transportName + \" transport) sending data. \" + getDataDetail(content, logMessageContent) + \".\");\r\n                    responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n                    return [4 /*yield*/, httpClient.post(url, {\r\n                            content: content,\r\n                            headers: headers,\r\n                            responseType: responseType,\r\n                        })];\r\n                case 3:\r\n                    response = _b.sent();\r\n                    logger.log(LogLevel.Trace, \"(\" + transportName + \" transport) request complete. Response status: \" + response.statusCode + \".\");\r\n                    return [2 /*return*/];\r\n            }\r\n        });\r\n    });\r\n}\r\n/** @private */\r\nexport function createLogger(logger) {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n    if (logger.log) {\r\n        return logger;\r\n    }\r\n    return new ConsoleLogger(logger);\r\n}\r\n/** @private */\r\nvar Subject = /** @class */ (function () {\r\n    function Subject() {\r\n        this.observers = [];\r\n    }\r\n    Subject.prototype.next = function (item) {\r\n        for (var _i = 0, _a = this.observers; _i < _a.length; _i++) {\r\n            var observer = _a[_i];\r\n            observer.next(item);\r\n        }\r\n    };\r\n    Subject.prototype.error = function (err) {\r\n        for (var _i = 0, _a = this.observers; _i < _a.length; _i++) {\r\n            var observer = _a[_i];\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    };\r\n    Subject.prototype.complete = function () {\r\n        for (var _i = 0, _a = this.observers; _i < _a.length; _i++) {\r\n            var observer = _a[_i];\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    };\r\n    Subject.prototype.subscribe = function (observer) {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    };\r\n    return Subject;\r\n}());\r\nexport { Subject };\r\n/** @private */\r\nvar SubjectSubscription = /** @class */ (function () {\r\n    function SubjectSubscription(subject, observer) {\r\n        this.subject = subject;\r\n        this.observer = observer;\r\n    }\r\n    SubjectSubscription.prototype.dispose = function () {\r\n        var index = this.subject.observers.indexOf(this.observer);\r\n        if (index > -1) {\r\n            this.subject.observers.splice(index, 1);\r\n        }\r\n        if (this.subject.observers.length === 0 && this.subject.cancelCallback) {\r\n            this.subject.cancelCallback().catch(function (_) { });\r\n        }\r\n    };\r\n    return SubjectSubscription;\r\n}());\r\nexport { SubjectSubscription };\r\n/** @private */\r\nvar ConsoleLogger = /** @class */ (function () {\r\n    function ConsoleLogger(minimumLogLevel) {\r\n        this.minimumLogLevel = minimumLogLevel;\r\n    }\r\n    ConsoleLogger.prototype.log = function (logLevel, message) {\r\n        if (logLevel >= this.minimumLogLevel) {\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    console.error(\"[\" + new Date().toISOString() + \"] \" + LogLevel[logLevel] + \": \" + message);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    console.warn(\"[\" + new Date().toISOString() + \"] \" + LogLevel[logLevel] + \": \" + message);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    console.info(\"[\" + new Date().toISOString() + \"] \" + LogLevel[logLevel] + \": \" + message);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    console.log(\"[\" + new Date().toISOString() + \"] \" + LogLevel[logLevel] + \": \" + message);\r\n                    break;\r\n            }\r\n        }\r\n    };\r\n    return ConsoleLogger;\r\n}());\r\nexport { ConsoleLogger };\r\n//# sourceMappingURL=Utils.js.map"], "sourceRoot": ""}