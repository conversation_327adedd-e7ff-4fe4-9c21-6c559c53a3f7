{"version": 3, "sources": ["webpack:///./src/views/WeChat/SendMessage.vue?d645", "webpack:///src/views/WeChat/SendMessage.vue", "webpack:///./src/views/WeChat/SendMessage.vue?5eab", "webpack:///./src/views/WeChat/SendMessage.vue"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "staticStyle", "padding-bottom", "attrs", "span", "inline", "nativeOn", "submit", "$event", "preventDefault", "placeholder", "model", "value", "callback", "$$v", "selectWeChat", "expression", "_l", "item", "key", "label", "float", "_v", "_s", "color", "font-size", "selectOperate", "selectBindOrSub", "selectCompany", "_e", "selectMsgType", "selectUser", "type", "disabled", "checkIsOk", "on", "click", "searchWeChatAccount", "slot", "textContent", "$set", "picture<PERSON>ontent", "voiceContent", "videoContent", "staticRenderFns", "SendMessagevue_type_script_lang_js_", "name", "data", "wechats", "companys", "operate", "bindOrSub", "msgTypes", "text", "pictureMediaID", "voiceMediaID", "title", "videoMediaID", "linkMsgContent", "description", "viewUrl", "pictureUrl", "created", "getWeChats", "getWeCompanys", "methods", "_this", "Object", "api", "then", "res", "success", "$message", "msg", "error", "_this2", "response", "for<PERSON>ach", "element", "push", "publicAccount", "publicNick", "_this3", "CompanyID", "CompanyName", "mounted", "watch", "newName", "old<PERSON>ame", "WeChat_SendMessagevue_type_script_lang_js_", "component", "componentNormalizer", "options", "__file", "__webpack_exports__"], "mappings": "yHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAAA,EAAA,UAAkCE,YAAA,UAAAC,YAAA,CAAmCC,iBAAA,OAAuBC,MAAA,CAAQC,KAAA,KAAW,CAAAN,EAAA,WAAgBK,MAAA,CAAOE,QAAA,GAAcC,SAAA,CAAWC,OAAA,SAAAC,GAA0BA,EAAAC,oBAA2B,CAAAX,EAAA,gBAAAA,EAAA,aAAqCK,MAAA,CAAOO,YAAA,cAA2BC,MAAA,CAAQC,MAAAlB,EAAA,aAAAmB,SAAA,SAAAC,GAAkDpB,EAAAqB,aAAAD,GAAqBE,WAAA,iBAA4BtB,EAAAuB,GAAAvB,EAAA,iBAAAwB,GAAqC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,OAAAd,EAAA,gBAAAA,EAAA,aAA4CK,MAAA,CAAOO,YAAA,aAA0BC,MAAA,CAAQC,MAAAlB,EAAA,cAAAmB,SAAA,SAAAC,GAAmDpB,EAAAgC,cAAAZ,GAAsBE,WAAA,kBAA6BtB,EAAAuB,GAAAvB,EAAA,iBAAAwB,GAAqC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,OAAAd,EAAA,gBAAAA,EAAA,aAA4CK,MAAA,CAAOO,YAAA,aAA0BC,MAAA,CAAQC,MAAAlB,EAAA,gBAAAmB,SAAA,SAAAC,GAAqDpB,EAAAiC,gBAAAb,GAAwBE,WAAA,oBAA+BtB,EAAAuB,GAAAvB,EAAA,mBAAAwB,GAAuC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,eAAAlB,EAAAiC,gBAAA7B,EAAA,gBAAAA,EAAA,aAA0EK,MAAA,CAAOO,YAAA,aAA0BC,MAAA,CAAQC,MAAAlB,EAAA,cAAAmB,SAAA,SAAAC,GAAmDpB,EAAAkC,cAAAd,GAAsBE,WAAA,kBAA6BtB,EAAAuB,GAAAvB,EAAA,kBAAAwB,GAAsC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,OAAAlB,EAAAmC,KAAA/B,EAAA,gBAAAA,EAAA,aAAqDK,MAAA,CAAOO,YAAA,WAAwBC,MAAA,CAAQC,MAAAlB,EAAA,cAAAmB,SAAA,SAAAC,GAAmDpB,EAAAoC,cAAAhB,GAAsBE,WAAA,kBAA6BtB,EAAAuB,GAAAvB,EAAA,kBAAAwB,GAAsC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,cAAAlB,EAAAgC,cAAA5B,EAAA,gBAAAA,EAAA,YAAsEK,MAAA,CAAOO,YAAA,aAA0BC,MAAA,CAAQC,MAAAlB,EAAA,WAAAmB,SAAA,SAAAC,GAAgDpB,EAAAqC,WAAAjB,GAAmBE,WAAA,iBAA0B,GAAAtB,EAAAmC,KAAA/B,EAAA,gBAAAA,EAAA,aAAkDK,MAAA,CAAO6B,KAAA,UAAAC,SAAA,GAAAvC,EAAAwC,aAAmDC,GAAA,CAAKC,MAAA1C,EAAA2C,sBAAiC,CAAA3C,EAAA4B,GAAA,sBAAA5B,EAAAoC,cAAAhC,EAAA,WAAiEE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,WAAAG,MAAA,CAA8BmC,KAAA,UAAgBA,KAAA,UAAe,CAAAxC,EAAA,QAAAJ,EAAA4B,GAAA,YAAAxB,EAAA,gBAAmDK,MAAA,CAAOiB,MAAA,SAAgB,CAAAtB,EAAA,YAAiBa,MAAA,CAAOC,MAAAlB,EAAA6C,YAAA,KAAA1B,SAAA,SAAAC,GAAsDpB,EAAA8C,KAAA9C,EAAA6C,YAAA,OAAAzB,IAAuCE,WAAA,uBAAgC,OAAAtB,EAAAmC,KAAA,SAAAnC,EAAAoC,cAAAhC,EAAA,WAA8DE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,WAAAG,MAAA,CAA8BmC,KAAA,UAAgBA,KAAA,UAAe,CAAAxC,EAAA,QAAAJ,EAAA4B,GAAA,YAAAxB,EAAA,gBAAmDK,MAAA,CAAOiB,MAAA,cAAqB,CAAAtB,EAAA,YAAiBa,MAAA,CAAOC,MAAAlB,EAAA+C,eAAA,eAAA5B,SAAA,SAAAC,GAAmEpB,EAAA8C,KAAA9C,EAAA+C,eAAA,iBAAA3B,IAAoDE,WAAA,oCAA6C,OAAAtB,EAAAmC,KAAA,SAAAnC,EAAAoC,cAAAhC,EAAA,WAA8DE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,WAAAG,MAAA,CAA8BmC,KAAA,UAAgBA,KAAA,UAAe,CAAAxC,EAAA,QAAAJ,EAAA4B,GAAA,YAAAxB,EAAA,gBAAmDK,MAAA,CAAOiB,MAAA,cAAqB,CAAAtB,EAAA,YAAiBa,MAAA,CAAOC,MAAAlB,EAAAgD,aAAA,aAAA7B,SAAA,SAAAC,GAA+DpB,EAAA8C,KAAA9C,EAAAgD,aAAA,eAAA5B,IAAgDE,WAAA,gCAAyC,OAAAtB,EAAAmC,KAAA,WAAAnC,EAAAoC,cAAAhC,EAAA,WAAgEE,YAAA,YAAuB,CAAAF,EAAA,OAAYE,YAAA,WAAAG,MAAA,CAA8BmC,KAAA,UAAgBA,KAAA,UAAe,CAAAxC,EAAA,QAAAJ,EAAA4B,GAAA,YAAAxB,EAAA,gBAAmDK,MAAA,CAAOiB,MAAA,cAAqB,CAAAtB,EAAA,YAAiBa,MAAA,CAAOC,MAAAlB,EAAAiD,aAAA,aAAA9B,SAAA,SAAAC,GAA+DpB,EAAA8C,KAAA9C,EAAAiD,aAAA,eAAA7B,IAAgDE,WAAA,gCAAyC,OAAAtB,EAAAmC,MAAA,YAClrJe,EAAA,yDCmIAC,EAAA,CACAC,KAAA,gBACAC,KAFA,WAGA,OACAC,QAAA,GACAC,SAAA,GACAC,QAAA,EAAAtC,MAAA,MAAAQ,MAAA,SAAAR,MAAA,MAAAQ,MAAA,SACA+B,UAAA,EAAAvC,MAAA,MAAAQ,MAAA,SAAAR,MAAA,OAAAQ,MAAA,SACAgC,SAAA,EAAAxC,MAAA,OAAAQ,MAAA,SAAAR,MAAA,QAAAQ,MAAA,SAAAR,MAAA,QAAAQ,MAAA,SAAAR,MAAA,UAAAQ,MAAA,SAEAL,aAAA,GACAW,cAAA,GACAC,gBAAA,GACAC,cAAA,GACAE,cAAA,GACAC,WAAA,GAEAQ,YAAA,CACAc,KAAA,IAEAZ,eAAA,CACAa,eAAA,IAEAZ,aAAA,CACAa,aAAA,IAEAZ,aAAA,CACAa,MAAA,GACAF,eAAA,GACAG,aAAA,IAEAC,eAAA,CACAF,MAAA,GACAG,YAAA,GACAC,QAAA,GACAC,WAAA,MAKAC,QAxCA,WAyCAnE,KAAAoE,aACApE,KAAAqE,iBAEAC,QAAA,CACA/B,UADA,WAEA,UAAAvC,KAAAoB,cAAA,IAAApB,KAAA+B,eAAA,IAAA/B,KAAAgC,iBAAA,IAAAhC,KAAAmC,gBAEA,OAAAnC,KAAA+B,eAAA,OAAA/B,KAAAgC,iBAAA,IAAAhC,KAAAoC,aAEA,OAAApC,KAAA+B,eAAA,QAAA/B,KAAAgC,iBAAA,IAAAhC,KAAAiC,eAAA,IAAAjC,KAAAoC,aAEA,OAAApC,KAAA+B,eAAA,OAAA/B,KAAAgC,iBAEA,OAAAhC,KAAA+B,eAAA,QAAA/B,KAAAgC,iBAAA,IAAAhC,KAAAiC,kBAIAS,oBAdA,WAcA,IAAA6B,EAAAvE,KACAwE,OAAAC,EAAA,MAAAD,CACA,CACApD,aAAApB,KAAAoB,aACAW,cAAA/B,KAAA+B,cACAC,gBAAAhC,KAAAgC,gBACAC,cAAAjC,KAAAiC,cACAE,cAAAnC,KAAAmC,cACAC,WAAApC,KAAAoC,WACAQ,YAAA5C,KAAA4C,YACAE,eAAA9C,KAAA8C,eACAC,aAAA/C,KAAA+C,aACAC,aAAAhD,KAAAgD,aACAe,eAAA/D,KAAA+D,iBAEAW,KAAA,SAAAC,GACAA,EAAAvB,KAAAwB,QACAL,EAAAM,SAAAD,QAAAD,EAAAvB,KAAA0B,KAEAP,EAAAM,SAAAE,MAAAJ,EAAAvB,KAAA0B,QAIAV,WArCA,WAqCA,IAAAY,EAAAhF,KACAwE,OAAAC,EAAA,KAAAD,GAAAE,KAAA,SAAAC,GACAK,EAAA3B,QAAA,GACAsB,EAAAvB,KAAA6B,SAAA7B,KAAA8B,QAAA,SAAAC,GACAH,EAAA3B,QAAA+B,KAAA,CACAnE,MAAAkE,EAAAE,cACA5D,MAAA0D,EAAAG,kBAKAjB,cAhDA,WAgDA,IAAAkB,EAAAvF,KACAwE,OAAAC,EAAA,KAAAD,GAAAE,KAAA,SAAAC,GACAY,EAAAjC,SAAA,GACAqB,EAAAvB,KAAA6B,SAAA7B,KAAA8B,QAAA,SAAAC,GACAI,EAAAjC,SAAA8B,KAAA,CACAnE,MAAAkE,EAAAK,UACA/D,MAAA0D,EAAAM,oBAMAC,QAxGA,aA2GAC,MAAA,CACAvE,aAAA,SAAAwE,EAAAC,KAGA5D,cAAA,SAAA2D,EAAAC,OCnPoWC,EAAA,cCOpWC,EAAgBvB,OAAAwB,EAAA,KAAAxB,CACdsB,EACAhG,EACAmD,GACF,EACA,KACA,KACA,MAIA8C,EAAAE,QAAAC,OAAA,kBACeC,EAAA,WAAAJ", "file": "js/chunk-2d0d2f25.359b78e4.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',[_c('el-col',{staticClass:\"toolbar\",staticStyle:{\"padding-bottom\":\"0px\"},attrs:{\"span\":24}},[_c('el-form',{attrs:{\"inline\":true},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择要操作的公众号\"},model:{value:(_vm.selectWeChat),callback:function ($$v) {_vm.selectWeChat=$$v},expression:\"selectWeChat\"}},_vm._l((_vm.wechats),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1),_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择要发送的集合\"},model:{value:(_vm.selectOperate),callback:function ($$v) {_vm.selectOperate=$$v},expression:\"selectOperate\"}},_vm._l((_vm.operate),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1),_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择要发送的对象\"},model:{value:(_vm.selectBindOrSub),callback:function ($$v) {_vm.selectBindOrSub=$$v},expression:\"selectBindOrSub\"}},_vm._l((_vm.bindOrSub),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1),(_vm.selectBindOrSub=='bind')?_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择要操作的客户\"},model:{value:(_vm.selectCompany),callback:function ($$v) {_vm.selectCompany=$$v},expression:\"selectCompany\"}},_vm._l((_vm.companys),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1):_vm._e(),_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择消息类型\"},model:{value:(_vm.selectMsgType),callback:function ($$v) {_vm.selectMsgType=$$v},expression:\"selectMsgType\"}},_vm._l((_vm.msgTypes),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1),(_vm.selectOperate=='one')?_c('el-form-item',[_c('el-input',{attrs:{\"placeholder\":\"请输入要发送的用户\"},model:{value:(_vm.selectUser),callback:function ($$v) {_vm.selectUser=$$v},expression:\"selectUser\"}})],1):_vm._e(),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\",\"disabled\":_vm.checkIsOk()==false},on:{\"click\":_vm.searchWeChatAccount}},[_vm._v(\"发送消息\")])],1),(_vm.selectMsgType=='text')?_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"推送文本\")])]),_c('el-form-item',{attrs:{\"label\":\"文本内容\"}},[_c('el-input',{model:{value:(_vm.textContent.text),callback:function ($$v) {_vm.$set(_vm.textContent, \"text\", $$v)},expression:\"textContent.text\"}})],1)],1):_vm._e(),(_vm.selectMsgType=='image')?_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"推送图片\")])]),_c('el-form-item',{attrs:{\"label\":\"图片mediaID\"}},[_c('el-input',{model:{value:(_vm.pictureContent.pictureMediaID),callback:function ($$v) {_vm.$set(_vm.pictureContent, \"pictureMediaID\", $$v)},expression:\"pictureContent.pictureMediaID\"}})],1)],1):_vm._e(),(_vm.selectMsgType=='voice')?_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"推送语音\")])]),_c('el-form-item',{attrs:{\"label\":\"语音mediaID\"}},[_c('el-input',{model:{value:(_vm.voiceContent.voiceMediaID),callback:function ($$v) {_vm.$set(_vm.voiceContent, \"voiceMediaID\", $$v)},expression:\"voiceContent.voiceMediaID\"}})],1)],1):_vm._e(),(_vm.selectMsgType=='mpvideo')?_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"推送视频\")])]),_c('el-form-item',{attrs:{\"label\":\"视频mediaID\"}},[_c('el-input',{model:{value:(_vm.videoContent.videoMediaID),callback:function ($$v) {_vm.$set(_vm.videoContent, \"videoMediaID\", $$v)},expression:\"videoContent.videoMediaID\"}})],1)],1):_vm._e()],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <section>\r\n    <!--工具条-->\r\n    <el-col :span=\"24\" class=\"toolbar\" style=\"padding-bottom: 0px;\">\r\n      <el-form :inline=\"true\" @submit.native.prevent>\r\n\r\n        <el-form-item>\r\n          <el-select v-model=\"selectWeChat\" placeholder=\"请选择要操作的公众号\">\r\n            <el-option\r\n              v-for=\"item in wechats\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-select v-model=\"selectOperate\" placeholder=\"请选择要发送的集合\">\r\n            <el-option\r\n              v-for=\"item in operate\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-select v-model=\"selectBindOrSub\" placeholder=\"请选择要发送的对象\">\r\n            <el-option\r\n              v-for=\"item in bindOrSub\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item v-if=\"selectBindOrSub=='bind'\">\r\n          <el-select v-model=\"selectCompany\" placeholder=\"请选择要操作的客户\">\r\n            <el-option\r\n              v-for=\"item in companys\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-select v-model=\"selectMsgType\" placeholder=\"请选择消息类型\">\r\n            <el-option\r\n              v-for=\"item in msgTypes\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item v-if=\"selectOperate=='one'\">\r\n          <el-input v-model=\"selectUser\" placeholder=\"请输入要发送的用户\"></el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" :disabled=\"checkIsOk()==false\" @click=\"searchWeChatAccount\">发送消息</el-button>\r\n        </el-form-item>\r\n\r\n        <el-card class=\"box-card\" v-if=\"selectMsgType=='text'\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>推送文本</span>\r\n            </div>\r\n            <el-form-item label=\"文本内容\">\r\n              <el-input v-model=\"textContent.text\"></el-input>\r\n            </el-form-item> \r\n        </el-card> \r\n        <el-card class=\"box-card\" v-if=\"selectMsgType=='image'\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>推送图片</span>\r\n            </div>\r\n            <el-form-item label=\"图片mediaID\">\r\n              <el-input v-model=\"pictureContent.pictureMediaID\"></el-input>\r\n            </el-form-item> \r\n        </el-card> \r\n        <el-card class=\"box-card\" v-if=\"selectMsgType=='voice'\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>推送语音</span>\r\n            </div>\r\n            <el-form-item label=\"语音mediaID\">\r\n              <el-input v-model=\"voiceContent.voiceMediaID\"></el-input>\r\n            </el-form-item> \r\n        </el-card> \r\n        <el-card class=\"box-card\" v-if=\"selectMsgType=='mpvideo'\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>推送视频</span>\r\n            </div>\r\n            <!-- <el-form-item label=\"标题\">\r\n              <el-input v-model=\"videoContent.title\"></el-input>\r\n            </el-form-item> \r\n             <el-form-item label=\"封面mediaID\">\r\n              <el-input v-model=\"videoContent.pictureMediaID\"></el-input>\r\n            </el-form-item>  -->\r\n             <el-form-item label=\"视频mediaID\">\r\n              <el-input v-model=\"videoContent.videoMediaID\"></el-input>\r\n            </el-form-item> \r\n        </el-card> \r\n      </el-form>\r\n    </el-col>\r\n     \r\n    \r\n  </div> \r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport { getWeChatAccount,getWeChatCompany ,pushTestMsg} from \"../../api/api\";\r\nexport default {\r\n  name: \"WeChatCompany\",\r\n  data() {\r\n    return {\r\n      wechats: [], //微信公众号列表\r\n      companys: [], //客户列表\r\n      operate: [{value:\"one\",label:\"单个用户\"},{value:\"all\",label:\"所有用户\"}], //操作类型\r\n      bindOrSub: [{value:\"sub\",label:\"订阅用户\"},{value:\"bind\",label:\"绑定用户\"}], //发送的对象 \r\n      msgTypes:[{value:\"text\",label:\"文本消息\"},{value:\"image\",label:\"图片消息\"},{value:\"voice\",label:\"语音消息\"},{value:\"mpvideo\",label:\"视频消息\"}],\r\n\r\n      selectWeChat: \"\", //当前选中的微信公众号  \r\n      selectOperate: \"\", //当前选中的操作集合\r\n      selectBindOrSub: \"\", //当前选中的绑定还是订阅\r\n      selectCompany: \"\", //当前选中的微信客户\r\n      selectMsgType:\"\",//当前选中的消息类型\r\n      selectUser:\"\",//当前选中要发送的用户\r\n\r\n      textContent:{\r\n        text:''\r\n      },\r\n      pictureContent:{\r\n        pictureMediaID:''\r\n      },\r\n      voiceContent:{  \r\n        voiceMediaID:''\r\n      },\r\n      videoContent:{  \r\n        title:'',\r\n        pictureMediaID:'',\r\n        videoMediaID:''\r\n      },\r\n      linkMsgContent:{  \r\n        title:'',\r\n        description:'',\r\n        viewUrl:'',\r\n        pictureUrl:''\r\n      },\r\n      \r\n    };\r\n  },\r\n  created() {\r\n    this.getWeChats();\r\n    this.getWeCompanys();\r\n  },\r\n  methods: {   \r\n    checkIsOk(){\r\n      if(this.selectWeChat == '' || this.selectOperate == '' || this.selectBindOrSub == '' || this.selectMsgType == '') \r\n        return false; //必填 \r\n      if(this.selectOperate == 'one' && this.selectBindOrSub == 'sub' && this.selectUser != '')\r\n        return true;  \r\n      if(this.selectOperate == 'one' && this.selectBindOrSub == 'bind' && this.selectCompany != '' && this.selectUser != '')\r\n        return true;  \r\n      if(this.selectOperate == 'all' && this.selectBindOrSub =='sub')\r\n        return true; \r\n      if(this.selectOperate == 'all' && this.selectBindOrSub =='bind' && this.selectCompany != '')\r\n        return true; \r\n      return false;\r\n    },\r\n    searchWeChatAccount() {  \r\n      pushTestMsg(\r\n        {\r\n        selectWeChat : this.selectWeChat,\r\n        selectOperate : this.selectOperate,\r\n        selectBindOrSub : this.selectBindOrSub,\r\n        selectCompany : this.selectCompany,\r\n        selectMsgType : this.selectMsgType,\r\n        selectUser : this.selectUser,\r\n        textContent : this.textContent,\r\n        pictureContent : this.pictureContent,\r\n        voiceContent : this.voiceContent,\r\n        videoContent : this.videoContent,\r\n        linkMsgContent : this.linkMsgContent\r\n      }\r\n      ).then(res=>{\r\n        if(res.data.success){\r\n            this.$message.success(res.data.msg);\r\n        }else{ \r\n            this.$message.error(res.data.msg);\r\n        }\r\n      })\r\n    },\r\n    getWeChats() {\r\n      getWeChatAccount().then(res => {\r\n        this.wechats = [];\r\n        res.data.response.data.forEach(element => {\r\n          this.wechats.push({\r\n            value: element.publicAccount,\r\n            label: element.publicNick\r\n          });\r\n        });\r\n      });\r\n    },\r\n    getWeCompanys() {\r\n      getWeChatCompany().then(res => {\r\n        this.companys = []; \r\n        res.data.response.data.forEach(element => {\r\n          this.companys.push({\r\n            value: element.CompanyID,\r\n            label: element.CompanyName\r\n          });\r\n        });\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    let that = this;\r\n  },\r\n  watch: {\r\n    selectWeChat: function(newName, oldName) {\r\n      //this.searchWeChatAccount();\r\n    },\r\n    selectCompany: function(newName, oldName) {\r\n      //this.searchWeChatAccount();\r\n    }\r\n  }\r\n};\r\n</script> \r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SendMessage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SendMessage.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SendMessage.vue?vue&type=template&id=6d29db29&\"\nimport script from \"./SendMessage.vue?vue&type=script&lang=js&\"\nexport * from \"./SendMessage.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"SendMessage.vue\"\nexport default component.exports"], "sourceRoot": ""}