{"version": 3, "sources": ["webpack:///./src/views/Form/Form.vue?80af", "webpack:///src/views/Form/Form.vue", "webpack:///./src/views/Form/Form.vue?6f74", "webpack:///./src/views/Form/Form.vue"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "ref", "staticStyle", "margin", "width", "min-width", "attrs", "model", "form", "label-width", "on", "submit", "$event", "preventDefault", "onSubmit", "label", "value", "callback", "$$v", "$set", "expression", "placeholder", "span", "type", "staticClass", "_v", "on-text", "off-text", "name", "nativeOn", "click", "staticRenderFns", "Formvue_type_script_lang_js_", "data", "region", "date1", "date2", "delivery", "resource", "desc", "methods", "console", "log", "Form_Formvue_type_script_lang_js_", "component", "Object", "componentNormalizer", "options", "__file", "__webpack_exports__"], "mappings": "yHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAqBE,IAAA,OAAAC,YAAA,CAAwBC,OAAA,OAAAC,MAAA,MAAAC,YAAA,SAAkDC,MAAA,CAAQC,MAAAZ,EAAAa,KAAAC,cAAA,QAAsCC,GAAA,CAAKC,OAAA,SAAAC,GAAkD,OAAxBA,EAAAC,iBAAwBlB,EAAAmB,SAAAF,MAA8B,CAAAb,EAAA,gBAAqBO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,YAAiBQ,MAAA,CAAOS,MAAArB,EAAAa,KAAA,KAAAS,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAAa,KAAA,OAAAU,IAAgCE,WAAA,gBAAyB,GAAArB,EAAA,gBAAyBO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,aAAkBO,MAAA,CAAOe,YAAA,WAAwBd,MAAA,CAAQS,MAAArB,EAAAa,KAAA,OAAAS,SAAA,SAAAC,GAAiDvB,EAAAwB,KAAAxB,EAAAa,KAAA,SAAAU,IAAkCE,WAAA,gBAA2B,CAAArB,EAAA,aAAkBO,MAAA,CAAOS,MAAA,MAAAC,MAAA,cAAkCjB,EAAA,aAAkBO,MAAA,CAAOS,MAAA,MAAAC,MAAA,cAAiC,OAAAjB,EAAA,gBAA6BO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,UAAeO,MAAA,CAAOgB,KAAA,KAAW,CAAAvB,EAAA,kBAAuBG,YAAA,CAAaE,MAAA,QAAeE,MAAA,CAAQiB,KAAA,OAAAF,YAAA,QAAmCd,MAAA,CAAQS,MAAArB,EAAAa,KAAA,MAAAS,SAAA,SAAAC,GAAgDvB,EAAAwB,KAAAxB,EAAAa,KAAA,QAAAU,IAAiCE,WAAA,iBAA0B,GAAArB,EAAA,UAAmByB,YAAA,OAAAlB,MAAA,CAA0BgB,KAAA,IAAU,CAAA3B,EAAA8B,GAAA,OAAA1B,EAAA,UAA6BO,MAAA,CAAOgB,KAAA,KAAW,CAAAvB,EAAA,kBAAuBG,YAAA,CAAaE,MAAA,QAAeE,MAAA,CAAQiB,KAAA,aAAAF,YAAA,QAAyCd,MAAA,CAAQS,MAAArB,EAAAa,KAAA,MAAAS,SAAA,SAAAC,GAAgDvB,EAAAwB,KAAAxB,EAAAa,KAAA,QAAAU,IAAiCE,WAAA,iBAA0B,OAAArB,EAAA,gBAA6BO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,aAAkBO,MAAA,CAAOoB,UAAA,GAAAC,WAAA,IAA2BpB,MAAA,CAAQS,MAAArB,EAAAa,KAAA,SAAAS,SAAA,SAAAC,GAAmDvB,EAAAwB,KAAAxB,EAAAa,KAAA,WAAAU,IAAoCE,WAAA,oBAA6B,GAAArB,EAAA,gBAAyBO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,qBAA0BQ,MAAA,CAAOS,MAAArB,EAAAa,KAAA,KAAAS,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAAa,KAAA,OAAAU,IAAgCE,WAAA,cAAyB,CAAArB,EAAA,eAAoBO,MAAA,CAAOS,MAAA,YAAAa,KAAA,UAAmC7B,EAAA,eAAoBO,MAAA,CAAOS,MAAA,OAAAa,KAAA,UAA8B7B,EAAA,eAAoBO,MAAA,CAAOS,MAAA,SAAAa,KAAA,UAAgC7B,EAAA,eAAoBO,MAAA,CAAOS,MAAA,SAAAa,KAAA,WAAgC,OAAA7B,EAAA,gBAA6BO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,kBAAuBQ,MAAA,CAAOS,MAAArB,EAAAa,KAAA,SAAAS,SAAA,SAAAC,GAAmDvB,EAAAwB,KAAAxB,EAAAa,KAAA,WAAAU,IAAoCE,WAAA,kBAA6B,CAAArB,EAAA,YAAiBO,MAAA,CAAOS,MAAA,aAAmBhB,EAAA,YAAiBO,MAAA,CAAOS,MAAA,aAAkB,OAAAhB,EAAA,gBAA6BO,MAAA,CAAOS,MAAA,SAAgB,CAAAhB,EAAA,YAAiBO,MAAA,CAAOiB,KAAA,YAAkBhB,MAAA,CAAQS,MAAArB,EAAAa,KAAA,KAAAS,SAAA,SAAAC,GAA+CvB,EAAAwB,KAAAxB,EAAAa,KAAA,OAAAU,IAAgCE,WAAA,gBAAyB,GAAArB,EAAA,gBAAAA,EAAA,aAAyCO,MAAA,CAAOiB,KAAA,YAAkB,CAAA5B,EAAA8B,GAAA,UAAA1B,EAAA,aAAmC8B,SAAA,CAAUC,MAAA,SAAAlB,GAAyBA,EAAAC,oBAA2B,CAAAlB,EAAA8B,GAAA,iBAC5qFM,EAAA,GC+CAC,EAAA,CACAC,KADA,WAEA,OACAzB,KAAA,CACAoB,KAAA,GACAM,OAAA,GACAC,MAAA,aACAC,MAAA,GACAC,UAAA,EACAd,KAAA,GACAe,SAAA,GACAC,KAAA,MAIAC,QAAA,CACA1B,SADA,WAEA2B,QAAAC,IAAA,cCjE6VC,EAAA,cCO7VC,EAAgBC,OAAAC,EAAA,KAAAD,CACdF,EACAjD,EACAqC,GACF,EACA,KACA,KACA,MAIAa,EAAAG,QAAAC,OAAA,WACeC,EAAA,WAAAL", "file": "js/chunk-2d0cf4f3.4034e115.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-form',{ref:\"form\",staticStyle:{\"margin\":\"20px\",\"width\":\"60%\",\"min-width\":\"600px\"},attrs:{\"model\":_vm.form,\"label-width\":\"80px\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.onSubmit($event)}}},[_c('el-form-item',{attrs:{\"label\":\"活动名称\"}},[_c('el-input',{model:{value:(_vm.form.name),callback:function ($$v) {_vm.$set(_vm.form, \"name\", $$v)},expression:\"form.name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"活动区域\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择活动区域\"},model:{value:(_vm.form.region),callback:function ($$v) {_vm.$set(_vm.form, \"region\", $$v)},expression:\"form.region\"}},[_c('el-option',{attrs:{\"label\":\"区域一\",\"value\":\"shanghai\"}}),_c('el-option',{attrs:{\"label\":\"区域二\",\"value\":\"beijing\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"活动时间\"}},[_c('el-col',{attrs:{\"span\":11}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"date\",\"placeholder\":\"选择日期\"},model:{value:(_vm.form.date1),callback:function ($$v) {_vm.$set(_vm.form, \"date1\", $$v)},expression:\"form.date1\"}})],1),_c('el-col',{staticClass:\"line\",attrs:{\"span\":2}},[_vm._v(\"-\")]),_c('el-col',{attrs:{\"span\":11}},[_c('el-time-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"fixed-time\",\"placeholder\":\"选择时间\"},model:{value:(_vm.form.date2),callback:function ($$v) {_vm.$set(_vm.form, \"date2\", $$v)},expression:\"form.date2\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"即时配送\"}},[_c('el-switch',{attrs:{\"on-text\":\"\",\"off-text\":\"\"},model:{value:(_vm.form.delivery),callback:function ($$v) {_vm.$set(_vm.form, \"delivery\", $$v)},expression:\"form.delivery\"}})],1),_c('el-form-item',{attrs:{\"label\":\"活动性质\"}},[_c('el-checkbox-group',{model:{value:(_vm.form.type),callback:function ($$v) {_vm.$set(_vm.form, \"type\", $$v)},expression:\"form.type\"}},[_c('el-checkbox',{attrs:{\"label\":\"美食/餐厅线上活动\",\"name\":\"type\"}}),_c('el-checkbox',{attrs:{\"label\":\"地推活动\",\"name\":\"type\"}}),_c('el-checkbox',{attrs:{\"label\":\"线下主题活动\",\"name\":\"type\"}}),_c('el-checkbox',{attrs:{\"label\":\"单纯品牌曝光\",\"name\":\"type\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"特殊资源\"}},[_c('el-radio-group',{model:{value:(_vm.form.resource),callback:function ($$v) {_vm.$set(_vm.form, \"resource\", $$v)},expression:\"form.resource\"}},[_c('el-radio',{attrs:{\"label\":\"线上品牌商赞助\"}}),_c('el-radio',{attrs:{\"label\":\"线下场地免费\"}})],1)],1),_c('el-form-item',{attrs:{\"label\":\"活动形式\"}},[_c('el-input',{attrs:{\"type\":\"textarea\"},model:{value:(_vm.form.desc),callback:function ($$v) {_vm.$set(_vm.form, \"desc\", $$v)},expression:\"form.desc\"}})],1),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\"}},[_vm._v(\"立即创建\")]),_c('el-button',{nativeOn:{\"click\":function($event){$event.preventDefault();}}},[_vm._v(\"取消\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<el-form ref=\"form\" :model=\"form\" label-width=\"80px\" @submit.prevent=\"onSubmit\" style=\"margin:20px;width:60%;min-width:600px;\">\r\n\t\t<el-form-item label=\"活动名称\">\r\n\t\t\t<el-input v-model=\"form.name\"></el-input>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"活动区域\">\r\n\t\t\t<el-select v-model=\"form.region\" placeholder=\"请选择活动区域\">\r\n\t\t\t\t<el-option label=\"区域一\" value=\"shanghai\"></el-option>\r\n\t\t\t\t<el-option label=\"区域二\" value=\"beijing\"></el-option>\r\n\t\t\t</el-select>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"活动时间\">\r\n\t\t\t<el-col :span=\"11\">\r\n\t\t\t\t<el-date-picker type=\"date\" placeholder=\"选择日期\" v-model=\"form.date1\" style=\"width: 100%;\"></el-date-picker>\r\n\t\t\t</el-col>\r\n\t\t\t<el-col class=\"line\" :span=\"2\">-</el-col>\r\n\t\t\t<el-col :span=\"11\">\r\n\t\t\t\t<el-time-picker type=\"fixed-time\" placeholder=\"选择时间\" v-model=\"form.date2\" style=\"width: 100%;\"></el-time-picker>\r\n\t\t\t</el-col>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"即时配送\">\r\n\t\t\t<el-switch on-text=\"\" off-text=\"\" v-model=\"form.delivery\"></el-switch>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"活动性质\">\r\n\t\t\t<el-checkbox-group v-model=\"form.type\">\r\n\t\t\t\t<el-checkbox label=\"美食/餐厅线上活动\" name=\"type\"></el-checkbox>\r\n\t\t\t\t<el-checkbox label=\"地推活动\" name=\"type\"></el-checkbox>\r\n\t\t\t\t<el-checkbox label=\"线下主题活动\" name=\"type\"></el-checkbox>\r\n\t\t\t\t<el-checkbox label=\"单纯品牌曝光\" name=\"type\"></el-checkbox>\r\n\t\t\t</el-checkbox-group>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"特殊资源\">\r\n\t\t\t<el-radio-group v-model=\"form.resource\">\r\n\t\t\t\t<el-radio label=\"线上品牌商赞助\"></el-radio>\r\n\t\t\t\t<el-radio label=\"线下场地免费\"></el-radio>\r\n\t\t\t</el-radio-group>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item label=\"活动形式\">\r\n\t\t\t<el-input type=\"textarea\" v-model=\"form.desc\"></el-input>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item>\r\n\t\t\t<el-button type=\"primary\">立即创建</el-button>\r\n\t\t\t<el-button @click.native.prevent>取消</el-button>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tform: {\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\tregion: '',\r\n\t\t\t\t\tdate1: '2019-01-01',\r\n\t\t\t\t\tdate2: '',\r\n\t\t\t\t\tdelivery: false,\r\n\t\t\t\t\ttype: [],\r\n\t\t\t\t\tresource: '',\r\n\t\t\t\t\tdesc: ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonSubmit() {\r\n\t\t\t\tconsole.log('submit!');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Form.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Form.vue?vue&type=template&id=daa1f16a&\"\nimport script from \"./Form.vue?vue&type=script&lang=js&\"\nexport * from \"./Form.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"Form.vue\"\nexport default component.exports"], "sourceRoot": ""}