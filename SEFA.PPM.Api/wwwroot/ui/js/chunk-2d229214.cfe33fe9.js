(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d229214"],{dbaa:function(n,e,a){"use strict";a.r(e);var t=function(){var n=this,e=n.$createElement,a=n._self._c||e;return a("div",[n._v("我是测试的第一个页面. name is "+n._s(n.msg))])},s=[],o=(a("7f7f"),a("bc3a")),c=a.n(o),u={name:"TestOne",data:function(){return{msg:""}},mounted:function(){var n=this;c.a.post("/api/Values/TestPostPara?name=anson zhang",{}).then(function(e){n.msg=e.data.name}).catch(function(n){console.error(n)})}},i=u,r=a("2877"),d=Object(r["a"])(i,t,s,!1,null,"6a35dd41",null);d.options.__file="TestOne.vue";e["default"]=d.exports}}]);
//# sourceMappingURL=chunk-2d229214.cfe33fe9.js.map