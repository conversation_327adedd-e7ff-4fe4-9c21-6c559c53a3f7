{"version": 3, "sources": ["webpack:///./src/views/WeChat/Menu.vue?f62d", "webpack:///src/views/WeChat/Menu.vue", "webpack:///./src/views/WeChat/Menu.vue?2350", "webpack:///./src/views/WeChat/Menu.vue", "webpack:///./src/views/WeChat/Menu.vue?33c1"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "staticStyle", "padding-bottom", "attrs", "span", "inline", "nativeOn", "submit", "$event", "preventDefault", "placeholder", "model", "value", "callback", "$$v", "selectWeChat", "expression", "_l", "item", "key", "label", "float", "_v", "_s", "color", "font-size", "type", "disabled", "on", "click", "getWxMenu", "menu", "i", "class", "active", "isActive", "menuFun", "name", "isSubMenuFlag", "subItem", "k", "isSubMenuActive", "subMenuFun", "sub_button", "length", "addSubMenu", "_e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addMenu", "saveFun", "showRightFlag", "size", "icon", "deleteMenu", "tempObj", "margin-top", "clearable", "$set", "placement", "visible2", "width", "data", "tableData", "scopedSlots", "_u", "fn", "scope", "trigger", "row", "slot", "handleEdit", "$index", "staticRenderFns", "Menuvue_type_script_lang_js_", "wechats", "button", "url", "media_id", "tempSelfObj", "created", "getWeChats", "mockMediaFun", "mockMenuFun", "methods", "_this2", "Object", "api", "then", "res", "success", "response", "for<PERSON>ach", "element", "push", "publicAccount", "publicNick", "$message", "error", "msg", "id", "_this3", "console", "log", "index", "_this4", "<PERSON><PERSON><PERSON>", "errmsg", "grand", "secondIndex", "subMenuKeyLength", "obj", "_this5", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "deleteData", "catch", "e", "message", "splice", "mounted", "computed", "watch", "newName", "old<PERSON>ame", "WeChat_Menuvue_type_script_lang_js_", "component", "componentNormalizer", "options", "__file", "__webpack_exports__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Menu_vue_vue_type_style_index_0_id_f4008632_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "__webpack_require__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Menu_vue_vue_type_style_index_0_id_f4008632_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "n"], "mappings": "yHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,WAAAA,EAAA,UAAkCE,YAAA,UAAAC,YAAA,CAAmCC,iBAAA,OAAuBC,MAAA,CAAQC,KAAA,KAAW,CAAAN,EAAA,WAAgBK,MAAA,CAAOE,QAAA,GAAcC,SAAA,CAAWC,OAAA,SAAAC,GAA0BA,EAAAC,oBAA2B,CAAAX,EAAA,gBAAAA,EAAA,aAAqCK,MAAA,CAAOO,YAAA,cAA2BC,MAAA,CAAQC,MAAAlB,EAAA,aAAAmB,SAAA,SAAAC,GAAkDpB,EAAAqB,aAAAD,GAAqBE,WAAA,iBAA4BtB,EAAAuB,GAAAvB,EAAA,iBAAAwB,GAAqC,OAAApB,EAAA,aAAuBqB,IAAAD,EAAAN,MAAAT,MAAA,CAAsBiB,MAAAF,EAAAE,MAAAR,MAAAM,EAAAN,QAAuC,CAAAd,EAAA,QAAaG,YAAA,CAAaoB,MAAA,SAAgB,CAAA3B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAE,UAAAtB,EAAA,QAA0CG,YAAA,CAAaoB,MAAA,QAAAG,MAAA,UAAAC,YAAA,SAAsD,CAAA/B,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAN,cAAiC,OAAAd,EAAA,gBAAAA,EAAA,aAA4CK,MAAA,CAAOuB,KAAA,UAAAC,SAAA,IAAAjC,EAAAqB,cAAiDa,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAAoC,UAAApC,EAAAqB,iBAAkC,CAAArB,EAAA4B,GAAA,oBAAAxB,EAAA,OAAuCE,YAAA,sCAAiD,CAAAF,EAAA,OAAYE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,iBAA4B,CAAAN,EAAAuB,GAAAvB,EAAAqC,KAAA,gBAAAb,EAAAc,GAA4C,OAAAlC,EAAA,OAAiBqB,IAAAa,EAAAhC,YAAA,eAAgC,CAAAF,EAAA,OAAYE,YAAA,YAAAiC,MAAA,CAA+BC,OAAAxC,EAAAyC,UAAAH,GAA4BJ,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAA0C,QAAAJ,EAAAd,MAAsB,CAAAxB,EAAA4B,GAAA5B,EAAA6B,GAAAL,EAAAmB,SAAA3C,EAAA4C,eAAAN,EAAAlC,EAAA,OAAiEE,YAAA,WAAsB,CAAAN,EAAAuB,GAAAC,EAAA,oBAAAqB,EAAAC,GAA+C,OAAA1C,EAAA,OAAiBqB,IAAAqB,EAAAxC,YAAA,YAA6B,CAAAF,EAAA,OAAYE,YAAA,eAAAiC,MAAA,CAAkCC,OAAAxC,EAAA+C,iBAAAT,EAAA,GAAAQ,GAA4CZ,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAAgD,WAAAxB,EAAAqB,EAAAP,EAAAQ,MAAsC,CAAA9C,EAAA4B,GAAA5B,EAAA6B,GAAAgB,EAAAF,aAAmCnB,EAAAyB,WAAAC,OAAA,EAAA9C,EAAA,OAAyCE,YAAA,2BAAA4B,GAAA,CAA2CC,MAAA,SAAArB,GAAyBd,EAAAmD,WAAA3B,MAAuB,CAAApB,EAAA,KAAUE,YAAA,mBAA2BN,EAAAoD,MAAA,GAAApD,EAAAoD,SAA4BpD,EAAAqD,cAAA,EAAAjD,EAAA,OAAoCE,YAAA,2BAAA4B,GAAA,CAA2CC,MAAAnC,EAAAsD,UAAqB,CAAAlD,EAAA,KAAUE,YAAA,mBAA2BN,EAAAoD,MAAA,GAAAhD,EAAA,aAAiCE,YAAA,WAAAG,MAAA,CAA8BwB,SAAA,IAAAjC,EAAAqB,aAAAW,KAAA,WAAiDE,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAAuD,QAAAvD,EAAAqB,iBAAgC,CAAArB,EAAA4B,GAAA,kBAAA5B,EAAAwD,cAA2vIxD,EAAAoD,KAA3vIhD,EAAA,OAA0DE,YAAA,SAAoB,CAAAF,EAAA,OAAYE,YAAA,kBAA6B,CAAAF,EAAA,OAAYE,YAAA,cAAyB,CAAAF,EAAA,aAAkBK,MAAA,CAAOgD,KAAA,OAAAzB,KAAA,SAAA0B,KAAA,kBAAsDxB,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAA2D,WAAA3D,EAAA4D,YAA8B,CAAA5D,EAAA4B,GAAA,gBAAAxB,EAAA,OAAAA,EAAA,QAAAJ,EAAA4B,GAAA,WAAAxB,EAAA,YAA+EE,YAAA,cAAAC,YAAA,CAAuCsD,aAAA,QAAoBpD,MAAA,CAAQO,YAAA,UAAA8C,UAAA,IAAuC7C,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,KAAAzC,SAAA,SAAAC,GAAkDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,OAAAxC,IAAmCE,WAAA,mBAA4B,GAAAlB,EAAA,OAAAA,EAAA,OAA0BE,YAAA,gBAA2B,CAAAF,EAAA,QAAAJ,EAAA4B,GAAA,WAAAxB,EAAA,kBAAoDa,MAAA,CAAOC,MAAAlB,EAAA4D,QAAA,KAAAzC,SAAA,SAAAC,GAAkDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,OAAAxC,IAAmCE,WAAA,iBAA4B,CAAAlB,EAAA,YAAiBK,MAAA,CAAOiB,MAAA,aAAoB,CAAA1B,EAAA4B,GAAA,UAAAxB,EAAA,YAAkCK,MAAA,CAAOiB,MAAA,SAAgB,CAAA1B,EAAA4B,GAAA,UAAAxB,EAAA,YAAkCK,MAAA,CAAOiB,MAAA,UAAiB,CAAA1B,EAAA4B,GAAA,WAAAxB,EAAA,YAAmCK,MAAA,CAAOiB,MAAA,gBAAuB,CAAA1B,EAAA4B,GAAA,SAAAxB,EAAA,YAAiCK,MAAA,CAAOiB,MAAA,UAAiB,CAAA1B,EAAA4B,GAAA,kBAAAxB,EAAA,OAAqCE,YAAA,oBAA+B,aAAAN,EAAA4D,QAAA5B,KAAA5B,EAAA,OAA6CE,YAAA,YAAuB,CAAAF,EAAA,QAAAJ,EAAA4B,GAAA,WAAAxB,EAAA,YAA8CE,YAAA,cAAAG,MAAA,CAAiCwB,UAAA,EAAAjB,YAAA,QAAqCC,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,SAAAzC,SAAA,SAAAC,GAAsDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,WAAAxC,IAAuCE,WAAA,sBAAgClB,EAAA,cAAmBK,MAAA,CAAOuD,UAAA,OAAkB/C,MAAA,CAAQC,MAAAlB,EAAA,SAAAmB,SAAA,SAAAC,GAA8CpB,EAAAiE,SAAA7C,GAAiBE,WAAA,aAAwB,CAAAlB,EAAA,YAAiBG,YAAA,CAAa2D,MAAA,QAAezD,MAAA,CAAQ0D,KAAAnE,EAAAoE,YAAsB,CAAAhE,EAAA,mBAAwBK,MAAA,CAAOiB,MAAA,MAAAwC,MAAA,OAA4BG,YAAArE,EAAAsE,GAAA,EAAsB7C,IAAA,UAAA8C,GAAA,SAAAC,GAAiC,OAAApE,EAAA,cAAyBK,MAAA,CAAOgE,QAAA,QAAAT,UAAA,QAAqC,CAAA5D,EAAA,KAAAJ,EAAA4B,GAAA,QAAA5B,EAAA6B,GAAA2C,EAAAE,IAAA/B,SAAAvC,EAAA,OAA6DE,YAAA,eAAAG,MAAA,CAAkCkE,KAAA,aAAmBA,KAAA,aAAkB,CAAAvE,EAAA,UAAeK,MAAA,CAAOgD,KAAA,WAAiB,CAAAzD,EAAA4B,GAAA5B,EAAA6B,GAAA2C,EAAAE,IAAA/B,UAAA,YAA8CvC,EAAA,mBAAwBK,MAAA,CAAOiB,MAAA,MAAa2C,YAAArE,EAAAsE,GAAA,EAAsB7C,IAAA,UAAA8C,GAAA,SAAAC,GAAiC,OAAApE,EAAA,aAAwBK,MAAA,CAAOgD,KAAA,QAAcvB,GAAA,CAAKC,MAAA,SAAArB,GAAyBd,EAAA4E,WAAAJ,EAAAK,OAAAL,EAAAE,QAA0C,CAAA1E,EAAA4B,GAAA,gBAAsB,GAAAxB,EAAA,aAAsBK,MAAA,CAAOkE,KAAA,YAAA3C,KAAA,WAAoC2C,KAAA,aAAkB,CAAA3E,EAAA4B,GAAA,cAAAxB,EAAA,KAA+BE,YAAA,QAAmB,CAAAN,EAAA4B,GAAA,4CAAA5B,EAAAoD,KAAA,QAAApD,EAAA4D,QAAA5B,KAAA5B,EAAA,OAAAA,EAAA,QAAAJ,EAAA4B,GAAA,WAAAxB,EAAA,YAAiJE,YAAA,cAAAG,MAAA,CAAiCO,YAAA,QAAA8C,UAAA,IAAqC7C,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,IAAAzC,SAAA,SAAAC,GAAiDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,MAAAxC,IAAkCE,WAAA,kBAA2B,GAAAtB,EAAAoD,KAAA,SAAApD,EAAA4D,QAAA5B,KAAA5B,EAAA,OAAAA,EAAA,OAAAA,EAAA,QAAAJ,EAAA4B,GAAA,UAAAxB,EAAA,YAA4GE,YAAA,cAAAG,MAAA,CAAiCO,YAAA,SAAA8C,UAAA,IAAsC7C,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,IAAAzC,SAAA,SAAAC,GAAiDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,MAAAxC,IAAkCE,WAAA,kBAA2B,GAAAlB,EAAA,KAAcE,YAAA,QAAmB,CAAAN,EAAA4B,GAAA,8BAAA5B,EAAAoD,KAAA,eAAApD,EAAA4D,QAAA5B,KAAA5B,EAAA,OAAAA,EAAA,OAAwGE,YAAA,UAAqB,CAAAF,EAAA,QAAAJ,EAAA4B,GAAA,gBAAAxB,EAAA,YAAmDE,YAAA,cAAAG,MAAA,CAAiCO,YAAA,eAAA8C,UAAA,IAA4C7C,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,MAAAzC,SAAA,SAAAC,GAAmDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,QAAAxC,IAAoCE,WAAA,oBAA6B,GAAAlB,EAAA,OAAAA,EAAA,QAAAJ,EAAA4B,GAAA,eAAAxB,EAAA,YAAgEE,YAAA,cAAAG,MAAA,CAAiCO,YAAA,4BAAA8C,UAAA,IAAyD7C,MAAA,CAAQC,MAAAlB,EAAA4D,QAAA,SAAAzC,SAAA,SAAAC,GAAsDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,WAAAxC,IAAuCE,WAAA,uBAAgC,GAAAlB,EAAA,KAAcE,YAAA,QAAmB,CAAAN,EAAA4B,GAAA,wCAAA5B,EAAAoD,KAAA,SAAApD,EAAA4D,QAAA5B,KAAA5B,EAAA,OAAAA,EAAA,QAAAJ,EAAA4B,GAAA,WAAAxB,EAAA,kBAAoJa,MAAA,CAAOC,MAAAlB,EAAA4D,QAAA,IAAAzC,SAAA,SAAAC,GAAiDpB,EAAA+D,KAAA/D,EAAA4D,QAAA,MAAAxC,IAAkCE,WAAA,gBAA2B,CAAAlB,EAAA,YAAiBK,MAAA,CAAOiB,MAAA,eAAsB,CAAA1B,EAAA4B,GAAA,UAAAxB,EAAA,YAAkCK,MAAA,CAAOiB,MAAA,iBAAwB,CAAA1B,EAAA4B,GAAA,UAAAxB,EAAA,YAAkCK,MAAA,CAAOiB,MAAA,oBAA2B,CAAA1B,EAAA4B,GAAA,UAAAxB,EAAA,YAAkCK,MAAA,CAAOiB,MAAA,kBAAyB,CAAA1B,EAAA4B,GAAA,kBAAA5B,EAAAoD,WAAAhD,EAAA,OAAAJ,EAAA4B,GAAA,WAAA5B,EAAA6B,GAAA7B,EAAAqC,WAAArC,EAAA,cAAAI,EAAA,OAAmIE,YAAA,SAAoB,CAAAF,EAAA,KAAAJ,EAAA4B,GAAA,eAAA5B,EAAAoD,QAAA,IAC9iN0B,EAAA,mECkMAC,EAAA,CACApC,KAAA,aACAwB,KAFA,WAGA,OACAX,eAAA,EACAwB,QAAA,GACA3D,aAAA,GACAgB,KAAA,CAEA4C,OAAA,CACA,CACAjD,KAAA,QACAW,KAAA,MACAlB,IAAA,QACAyD,IAAA,GACAC,SAAA,GACAlC,WAAA,CACA,CAEAN,KAAA,YAQAF,UAAA,EACAM,iBAAA,EACAH,eAAA,EACAgB,QAAA,GAQAwB,YAAA,GAMAnB,UAAA,EACAG,UAAA,KAGAiB,QAhDA,WAkDApF,KAAAqF,aACArF,KAAAsF,eACAtF,KAAAuF,eAGAC,QAAA,CACAH,WADA,WACA,IAAAI,EAAAzF,KACA0F,OAAAC,EAAA,KAAAD,GAAAE,KAAA,SAAAC,GACAJ,EAAAV,QAAA,GACAc,EAAA3B,KAAA4B,QACAD,EAAA3B,KAAA6B,SAAA7B,KAAA8B,QAAA,SAAAC,GACAR,EAAAV,QAAAmB,KAAA,CACAjF,MAAAgF,EAAAE,cACA1E,MAAAwE,EAAAG,eAIAX,EAAAY,SAAAC,MAAAT,EAAA3B,KAAAqC,QAKApE,UAjBA,SAiBAqE,GAAA,IAAAC,EAAAzG,KAEA0F,OAAAC,EAAA,KAAAD,CAAA,CAAAc,OAAAZ,KAAA,SAAAC,GACAa,QAAAC,IAAAd,GACAA,EAAA3B,KAAA4B,SACAW,EAAArE,KAAAyD,EAAA3B,KAAA6B,SAAA3D,KACAqE,EAAAJ,SAAAP,QAAA,YAEAW,EAAAJ,SAAAC,MAAAT,EAAA3B,KAAAqC,QAKA5B,WA9BA,SA8BAiC,EAAAnC,GACAzE,KAAAgE,UAAA,EACAhE,KAAA2D,QAAAuB,SAAAT,EAAA/B,MAEAY,QAlCA,SAkCAkD,GAAA,IAAAK,EAAA7G,KAEA0F,OAAAC,EAAA,MAAAD,CAAA,CAAAc,KAAApE,KAAApC,KAAAoC,OAAAwD,KAAA,SAAAC,GACAA,EAAA3B,KAAA4B,QACA,GAAAD,EAAA3B,KAAA6B,SAAAe,QACAD,EAAAR,SAAAP,QAAA,SAEAe,EAAAR,SAAAC,MAAAT,EAAA3B,KAAA6B,SAAAgB,QAGAF,EAAAR,SAAAC,MAAAT,EAAA3B,KAAAqC,QAOA9D,QAnDA,SAmDAJ,EAAAd,GACAmF,QAAAC,IAAAtE,GACAqE,QAAAC,IAAApF,GACAvB,KAAAuD,eAAA,EAEAvD,KAAA2D,QAAApC,EACAvB,KAAAmF,YAAA6B,MAAA,IACAhH,KAAAmF,YAAAyB,MAAAvE,EACArC,KAAAwC,SAAAH,EACArC,KAAA2C,cAAAN,EACArC,KAAA8C,iBAAA,GAGAC,WAhEA,SAgEAxB,EAAAqB,EAAAgE,EAAA/D,GACA7C,KAAAuD,eAAA,EACAvD,KAAA2D,QAAAf,EACA5C,KAAAmF,YAAA6B,MAAA,IACAhH,KAAAmF,YAAAyB,QACA5G,KAAAmF,YAAA8B,YAAApE,EACA7C,KAAA8C,gBAAA8D,EAAA,GAAA/D,EACA7C,KAAAwC,UAAA,GAGAa,QA1EA,WA2EAqD,QAAAC,IAAA,WAEA,GAAA3G,KAAAoD,eACApD,KAAA8D,KAAA9D,KAAAoC,KAAA4C,OAAA,KACAtC,KAAA,MACAX,KAAA,QACAP,IAAA,MAGAwB,WAAA,KAGA,GAAAhD,KAAAoD,eACApD,KAAA8D,KAAA9D,KAAAoC,KAAA4C,OAAA,KACAtC,KAAA,MACAX,KAAA,QACAP,IAAA,MAGAwB,WAAA,KAGA,GAAAhD,KAAAoD,eACApD,KAAA8D,KAAA9D,KAAAoC,KAAA4C,OAAA,KACAtC,KAAA,MACAX,KAAA,QACAP,IAAA,MAGAwB,WAAA,MAKAE,WA7GA,SA6GA3B,GACA,IAAA2F,EAAA3F,EAAAyB,WAAAC,OACA,GAAAiE,GACAlH,KAAA8D,KAAAvC,EAAAyB,WAAA,KACAN,KAAA,OACAX,KAAA,QACAP,IAAA,SAKA,GAAA0F,GACAlH,KAAA8D,KAAAvC,EAAAyB,WAAA,KACAN,KAAA,OACAX,KAAA,QACAP,IAAA,SAKA,GAAA0F,GACAlH,KAAA8D,KAAAvC,EAAAyB,WAAA,KACAN,KAAA,OACAX,KAAA,QACAP,IAAA,SAKA,GAAA0F,GACAlH,KAAA8D,KAAAvC,EAAAyB,WAAA,KACAN,KAAA,OACAX,KAAA,QACAP,IAAA,SAKA,GAAA0F,GACAlH,KAAA8D,KAAAvC,EAAAyB,WAAA,KACAN,KAAA,OACAX,KAAA,QACAP,IAAA,UAOAkC,WA9JA,SA8JAyD,GAAA,IAAAC,EAAApH,KACAqH,EAAArH,KACAA,KAAAsH,SAAA,0BACAC,kBAAA,KACAC,iBAAA,KACAzF,KAAA,YAEA6D,KAAA,WACAyB,EAAAI,WAAAN,KAEAO,MAAA,SAAAC,GACAP,EAAAf,SAAA,CACAtE,KAAA,QACA6F,QAAAD,OAKAF,WAhLA,SAgLAN,GACAnH,KAAAuD,eACAvD,KAAAqG,SAAA,CACAtE,KAAA,OACA6F,QAAA,eAGA,KAAA5H,KAAAmF,YAAA6B,OAEAhH,KAAAoC,KAAA4C,OAAA6C,OAAA7H,KAAAmF,YAAAyB,MAAA,GACA5G,KAAAwC,UAAA,GACA,KAAAxC,KAAAmF,YAAA6B,OAEAhH,KAAAoC,KAAA4C,OAAAhF,KAAAmF,YAAAyB,OAAA5D,WAAA6E,OACA7H,KAAAmF,YAAA8B,YACA,GAEAjH,KAAA8C,iBAAA,GAEA4D,QAAAC,IAAA,YAAA3G,KAAAmF,YAAA6B,OAEAhH,KAAAuD,eAAA,EACAvD,KAAAqG,SAAA,CACAtE,KAAA,UACA6F,QAAA,YAIAE,QAnQA,aAoQAC,SAAA,CAEA3E,cAAA,WACA,OAAApD,KAAAoC,KAAA4C,OAAA/B,SAGA+E,MAAA,CACA5G,aAAA,SAAA6G,EAAAC,GACAxB,QAAAC,IAAAsB,GACAjI,KAAAmC,UAAA8F,MChd6VE,EAAA,0BCQ7VC,EAAgB1C,OAAA2C,EAAA,KAAA3C,CACdyC,EACArI,EACA+E,GACF,EACA,KACA,WACA,MAIAuD,EAAAE,QAAAC,OAAA,WACeC,EAAA,WAAAJ,+CCpBf,IAAAK,EAAAC,EAAA,QAAAC,EAAAD,EAAAE,EAAAH,GAAkfE,EAAG", "file": "js/chunk-4b6066be.f63d0f19.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',[_c('el-col',{staticClass:\"toolbar\",staticStyle:{\"padding-bottom\":\"0px\"},attrs:{\"span\":24}},[_c('el-form',{attrs:{\"inline\":true},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',[_c('el-select',{attrs:{\"placeholder\":\"请选择要操作的公众号\"},model:{value:(_vm.selectWeChat),callback:function ($$v) {_vm.selectWeChat=$$v},expression:\"selectWeChat\"}},_vm._l((_vm.wechats),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.label))]),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(_vm._s(item.value))])])}),1)],1),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\",\"disabled\":_vm.selectWeChat==''},on:{\"click\":function($event){_vm.getWxMenu(_vm.selectWeChat)}}},[_vm._v(\"刷新\")])],1)],1)],1),_c('div',{staticClass:\"public-account-management clearfix\"},[_c('div',{staticClass:\"left\"},[_c('div',{staticClass:\"menu clearfix\"},[_vm._l((_vm.menu.button),function(item,i){return _c('div',{key:i,staticClass:\"menu_bottom\"},[_c('div',{staticClass:\"menu_item\",class:{'active': _vm.isActive == i},on:{\"click\":function($event){_vm.menuFun(i,item)}}},[_vm._v(_vm._s(item.name))]),(_vm.isSubMenuFlag == i)?_c('div',{staticClass:\"submenu\"},[_vm._l((item.sub_button),function(subItem,k){return _c('div',{key:k,staticClass:\"subtitle\"},[_c('div',{staticClass:\"menu_subItem\",class:{'active': _vm.isSubMenuActive == i + '' + k},on:{\"click\":function($event){_vm.subMenuFun(item, subItem, i, k)}}},[_vm._v(_vm._s(subItem.name))])])}),(item.sub_button.length < 5)?_c('div',{staticClass:\"menu_bottom menu_addicon\",on:{\"click\":function($event){_vm.addSubMenu(item)}}},[_c('i',{staticClass:\"el-icon-plus\"})]):_vm._e()],2):_vm._e()])}),(_vm.menuKeyLength < 3)?_c('div',{staticClass:\"menu_bottom menu_addicon\",on:{\"click\":_vm.addMenu}},[_c('i',{staticClass:\"el-icon-plus\"})]):_vm._e()],2),_c('el-button',{staticClass:\"save_btn\",attrs:{\"disabled\":_vm.selectWeChat=='',\"type\":\"success\"},on:{\"click\":function($event){_vm.saveFun(_vm.selectWeChat)}}},[_vm._v(\"保存并发布至菜单\")])],1),(!_vm.showRightFlag)?_c('div',{staticClass:\"right\"},[_c('div',{staticClass:\"configure_page\"},[_c('div',{staticClass:\"delete_btn\"},[_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"danger\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){_vm.deleteMenu(_vm.tempObj)}}},[_vm._v(\"删除当前菜单\")])],1),_c('div',[_c('span',[_vm._v(\"菜单名称：\")]),_c('el-input',{staticClass:\"input_width\",staticStyle:{\"margin-top\":\"12px\"},attrs:{\"placeholder\":\"请输入菜单名称\",\"clearable\":\"\"},model:{value:(_vm.tempObj.name),callback:function ($$v) {_vm.$set(_vm.tempObj, \"name\", $$v)},expression:\"tempObj.name\"}})],1),_c('div',[_c('div',{staticClass:\"menu_content\"},[_c('span',[_vm._v(\"菜单内容：\")]),_c('el-radio-group',{model:{value:(_vm.tempObj.type),callback:function ($$v) {_vm.$set(_vm.tempObj, \"type\", $$v)},expression:\"tempObj.type\"}},[_c('el-radio',{attrs:{\"label\":'media_id'}},[_vm._v(\"发送素材\")]),_c('el-radio',{attrs:{\"label\":'view'}},[_vm._v(\"跳转链接\")]),_c('el-radio',{attrs:{\"label\":'click'}},[_vm._v(\"发送关键词\")]),_c('el-radio',{attrs:{\"label\":'miniprogram'}},[_vm._v(\"小程序\")]),_c('el-radio',{attrs:{\"label\":'event'}},[_vm._v(\"事件功能\")])],1)],1),_c('div',{staticClass:\"configur_content\"},[(_vm.tempObj.type == 'media_id')?_c('div',{staticClass:\"material\"},[_c('span',[_vm._v(\"素材内容：\")]),_c('el-input',{staticClass:\"input_width\",attrs:{\"disabled\":true,\"placeholder\":\"素材名称\"},model:{value:(_vm.tempObj.media_id),callback:function ($$v) {_vm.$set(_vm.tempObj, \"media_id\", $$v)},expression:\"tempObj.media_id\"}}),_c('el-popover',{attrs:{\"placement\":\"top\"},model:{value:(_vm.visible2),callback:function ($$v) {_vm.visible2=$$v},expression:\"visible2\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData}},[_c('el-table-column',{attrs:{\"label\":\"文件名\",\"width\":\"600\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-popover',{attrs:{\"trigger\":\"hover\",\"placement\":\"top\"}},[_c('p',[_vm._v(\"文件名: \"+_vm._s(scope.row.name))]),_c('div',{staticClass:\"name-wrapper\",attrs:{\"slot\":\"reference\"},slot:\"reference\"},[_c('el-tag',{attrs:{\"size\":\"medium\"}},[_vm._v(_vm._s(scope.row.name))])],1)])]}}])}),_c('el-table-column',{attrs:{\"label\":\"操作\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":function($event){_vm.handleEdit(scope.$index, scope.row)}}},[_vm._v(\"选择\")])]}}])})],1),_c('el-button',{attrs:{\"slot\":\"reference\",\"type\":\"success\"},slot:\"reference\"},[_vm._v(\"选择素材\")])],1),_c('p',{staticClass:\"blue\"},[_vm._v(\"tips:需要调后台获取到内容，弹框出来，然后选择，把名字赋值上去！\")])],1):_vm._e(),(_vm.tempObj.type == 'view')?_c('div',[_c('span',[_vm._v(\"跳转链接：\")]),_c('el-input',{staticClass:\"input_width\",attrs:{\"placeholder\":\"请输入链接\",\"clearable\":\"\"},model:{value:(_vm.tempObj.url),callback:function ($$v) {_vm.$set(_vm.tempObj, \"url\", $$v)},expression:\"tempObj.url\"}})],1):_vm._e(),(_vm.tempObj.type == 'click')?_c('div',[_c('div',[_c('span',[_vm._v(\"关键词：\")]),_c('el-input',{staticClass:\"input_width\",attrs:{\"placeholder\":\"请输入关键词\",\"clearable\":\"\"},model:{value:(_vm.tempObj.key),callback:function ($$v) {_vm.$set(_vm.tempObj, \"key\", $$v)},expression:\"tempObj.key\"}})],1),_c('p',{staticClass:\"blue\"},[_vm._v(\"tips:这里需要配合关键词推送消息一起使用\")])]):_vm._e(),(_vm.tempObj.type == 'miniprogram')?_c('div',[_c('div',{staticClass:\"applet\"},[_c('span',[_vm._v(\"小程序的appid：\")]),_c('el-input',{staticClass:\"input_width\",attrs:{\"placeholder\":\"请输入小程序的appid\",\"clearable\":\"\"},model:{value:(_vm.tempObj.appid),callback:function ($$v) {_vm.$set(_vm.tempObj, \"appid\", $$v)},expression:\"tempObj.appid\"}})],1),_c('div',[_c('span',[_vm._v(\"小程序的页面路径：\")]),_c('el-input',{staticClass:\"input_width\",attrs:{\"placeholder\":\"请输入小程序的页面路径，如：pages/index\",\"clearable\":\"\"},model:{value:(_vm.tempObj.pagepath),callback:function ($$v) {_vm.$set(_vm.tempObj, \"pagepath\", $$v)},expression:\"tempObj.pagepath\"}})],1),_c('p',{staticClass:\"blue\"},[_vm._v(\"tips:需要和公众号进行关联才可以把小程序绑定带微信菜单上哟！\")])]):_vm._e(),(_vm.tempObj.type == 'event')?_c('div',[_c('span',[_vm._v(\"功能事件：\")]),_c('el-radio-group',{model:{value:(_vm.tempObj.key),callback:function ($$v) {_vm.$set(_vm.tempObj, \"key\", $$v)},expression:\"tempObj.key\"}},[_c('el-radio',{attrs:{\"label\":'pic_weixin'}},[_vm._v(\"微信相册\")]),_c('el-radio',{attrs:{\"label\":'pic_sysphoto'}},[_vm._v(\"拍照发图\")]),_c('el-radio',{attrs:{\"label\":'location_select'}},[_vm._v(\"位置选择\")]),_c('el-radio',{attrs:{\"label\":'scancode_push'}},[_vm._v(\"微信扫码\")])],1)],1):_vm._e()])])]),_c('div',[_vm._v(\"menu对象值：\"+_vm._s(_vm.menu))])]):_vm._e(),(_vm.showRightFlag)?_c('div',{staticClass:\"right\"},[_c('p',[_vm._v(\"请选择菜单配置\")])]):_vm._e()])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <section>\r\n    <!--工具条-->\r\n    <el-col :span=\"24\" class=\"toolbar\" style=\"padding-bottom: 0px;\">\r\n      <el-form :inline=\"true\" @submit.native.prevent>\r\n        <el-form-item>\r\n          <el-select v-model=\"selectWeChat\" placeholder=\"请选择要操作的公众号\">\r\n            <el-option\r\n              v-for=\"item in wechats\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n              <span style=\"float: left\">{{ item.label }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ item.value }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" :disabled=\"selectWeChat==''\" @click=\"getWxMenu(selectWeChat)\">刷新</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-col>\r\n\r\n    <div class=\"public-account-management clearfix\">\r\n      <!--左边配置菜单-->\r\n      <div class=\"left\">\r\n        <div class=\"menu clearfix\">\r\n          <div v-for=\"(item, i) of menu.button\" :key=\"i\" class=\"menu_bottom\">\r\n            <!-- 一级菜单 -->\r\n            <div\r\n              :class=\"{'active': isActive == i}\"\r\n              class=\"menu_item\"\r\n              @click=\"menuFun(i,item)\"\r\n            >{{ item.name }}</div>\r\n            <!--以下为二级菜单-->\r\n            <div v-if=\"isSubMenuFlag == i\" class=\"submenu\">\r\n              <div v-for=\"(subItem, k) in item.sub_button\" :key=\"k\" class=\"subtitle\">\r\n                <div\r\n                  :class=\"{'active': isSubMenuActive == i + '' + k}\"\r\n                  class=\"menu_subItem\"\r\n                  @click=\"subMenuFun(item, subItem, i, k)\"\r\n                >{{ subItem.name }}</div>\r\n              </div>\r\n              <!--二级菜单加号， 当长度 小于  5 才显示二级菜单的加号  -->\r\n              <div\r\n                v-if=\"item.sub_button.length < 5\"\r\n                class=\"menu_bottom menu_addicon\"\r\n                @click=\"addSubMenu(item)\"\r\n              >\r\n                <i class=\"el-icon-plus\"></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 一级菜单加号 -->\r\n          <div v-if=\"menuKeyLength < 3\" class=\"menu_bottom menu_addicon\" @click=\"addMenu\">\r\n            <i class=\"el-icon-plus\"></i>\r\n          </div>\r\n        </div>\r\n        <el-button\r\n          :disabled=\"selectWeChat==''\"\r\n          class=\"save_btn\"\r\n          type=\"success\"\r\n          @click=\"saveFun(selectWeChat)\"\r\n        >保存并发布至菜单</el-button>\r\n      </div>\r\n      <!--右边配置-->\r\n      <div v-if=\"!showRightFlag\" class=\"right\">\r\n        <div class=\"configure_page\">\r\n          <div class=\"delete_btn\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"deleteMenu(tempObj)\"\r\n            >删除当前菜单</el-button>\r\n          </div>\r\n          <div>\r\n            <span>菜单名称：</span>\r\n            <el-input\r\n              v-model=\"tempObj.name\"\r\n              class=\"input_width\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              style=\"margin-top: 12px;\"\r\n            ></el-input>\r\n          </div>\r\n          <div>\r\n            <div class=\"menu_content\">\r\n              <span>菜单内容：</span>\r\n              <el-radio-group v-model=\"tempObj.type\">\r\n                <el-radio :label=\"'media_id'\">发送素材</el-radio>\r\n                <el-radio :label=\"'view'\">跳转链接</el-radio>\r\n                <el-radio :label=\"'click'\">发送关键词</el-radio>  \r\n                <el-radio :label=\"'miniprogram'\">小程序</el-radio>\r\n                <el-radio :label=\"'event'\">事件功能</el-radio> \r\n              </el-radio-group> \r\n            </div>\r\n            <div class=\"configur_content\">\r\n              <div v-if=\"tempObj.type == 'media_id'\" class=\"material\">\r\n                <span>素材内容：</span>\r\n                <el-input\r\n                  v-model=\"tempObj.media_id\"\r\n                  :disabled=\"true\"\r\n                  class=\"input_width\"\r\n                  placeholder=\"素材名称\"\r\n                ></el-input>\r\n                <!--下面点击“选择素材”按钮，弹框框-->\r\n                <el-popover v-model=\"visible2\" placement=\"top\">\r\n                  <el-table :data=\"tableData\" style=\"width: 100%\">\r\n                    <el-table-column label=\"文件名\" width=\"600\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-popover trigger=\"hover\" placement=\"top\">\r\n                          <p>文件名: {{ scope.row.name }}</p>\r\n                          <div slot=\"reference\" class=\"name-wrapper\">\r\n                            <el-tag size=\"medium\">{{ scope.row.name }}</el-tag>\r\n                          </div>\r\n                        </el-popover>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button size=\"mini\" @click=\"handleEdit(scope.$index, scope.row)\">选择</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                  <el-button slot=\"reference\" type=\"success\">选择素材</el-button>\r\n                </el-popover>\r\n                <p class=\"blue\">tips:需要调后台获取到内容，弹框出来，然后选择，把名字赋值上去！</p>\r\n              </div>\r\n              <div v-if=\"tempObj.type == 'view'\">\r\n                <span>跳转链接：</span>\r\n                <el-input v-model=\"tempObj.url\" class=\"input_width\" placeholder=\"请输入链接\" clearable></el-input>\r\n              </div>\r\n              <div v-if=\"tempObj.type == 'click'\">\r\n                <div>\r\n                  <span>关键词：</span>\r\n                  <el-input\r\n                    v-model=\"tempObj.key\"\r\n                    class=\"input_width\"\r\n                    placeholder=\"请输入关键词\"\r\n                    clearable\r\n                  ></el-input>\r\n                </div>\r\n                <p class=\"blue\">tips:这里需要配合关键词推送消息一起使用</p>\r\n              </div>\r\n              <div v-if=\"tempObj.type == 'miniprogram'\">\r\n                <div class=\"applet\">\r\n                  <span>小程序的appid：</span>\r\n                  <el-input\r\n                    v-model=\"tempObj.appid\"\r\n                    class=\"input_width\"\r\n                    placeholder=\"请输入小程序的appid\"\r\n                    clearable\r\n                  ></el-input>\r\n                </div>\r\n                <div>\r\n                  <span>小程序的页面路径：</span>\r\n                  <el-input\r\n                    v-model=\"tempObj.pagepath\"\r\n                    class=\"input_width\"\r\n                    placeholder=\"请输入小程序的页面路径，如：pages/index\"\r\n                    clearable\r\n                  ></el-input>\r\n                </div>\r\n                <p class=\"blue\">tips:需要和公众号进行关联才可以把小程序绑定带微信菜单上哟！</p>\r\n              </div>\r\n              <div v-if=\"tempObj.type == 'event'\">\r\n                 <span>功能事件：</span>\r\n                  <el-radio-group v-model=\"tempObj.key\">\r\n                  <el-radio :label=\"'pic_weixin'\">微信相册</el-radio>\r\n                  <el-radio :label=\"'pic_sysphoto'\">拍照发图</el-radio>\r\n                  <el-radio :label=\"'location_select'\">位置选择</el-radio>\r\n                  <el-radio :label=\"'scancode_push'\">微信扫码</el-radio> \r\n                </el-radio-group> \r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div>menu对象值：{{ menu }}</div>\r\n      </div>\r\n      <!--一进页面就显示的默认页面，当点击左边按钮的时候，就不显示了-->\r\n      <div v-if=\"showRightFlag\" class=\"right\">\r\n        <p>请选择菜单配置</p>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getWeChatMenu,\r\n  updateWeChatMenu,\r\n  getWeChatAccount\r\n} from \"../../api/api\";\r\nexport default {\r\n  name: \"WeChatMenu\",\r\n  data() {\r\n    return {\r\n      showRightFlag: true, // 右边配置显示默认详情还是配置详情\r\n      wechats: [], //微信公众号列表\r\n      selectWeChat: \"\", //当前选中的微信公众号\r\n      menu: {\r\n        // 一级菜单\r\n        button: [\r\n          {\r\n            type: \"click\",\r\n            name: \"菜单1\",\r\n            key: \"menu1\", // 关键词\r\n            url: \"\", // 跳转链接\r\n            media_id: \"\", // 素材名称--图文消息\r\n            sub_button: [\r\n              {\r\n                //type: \"\",//media_id:素材内容; view:跳转链接\r\n                name: \"子菜单1\"\r\n                // url: \"\",//跳转链接\r\n                // media_id:\"\",//素材名称--图文消息\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }, // 横向菜单\r\n      isActive: -1, // 一级菜单点中样式\r\n      isSubMenuActive: -1, // 一级菜单点中样式\r\n      isSubMenuFlag: -1, // 二级菜单显示标志\r\n      tempObj: {\r\n        // type: \"view\",\r\n        // media_id: 素材内容\r\n        // view: 跳转链接\r\n        // name: \"\",//菜单名称\r\n        // material: \"\",//素材名称\r\n        // link: \"\",//跳转链接\r\n      }, // 右边临时变量，作为中间值牵引关系\r\n      tempSelfObj: {\r\n        // 一些临时值放在这里进行判断，如果放在tempObj，由于引用关系，menu也会多了多余的参数\r\n        // grand:\"\" 1:表示一级菜单； 2:表示二级菜单\r\n        // index:\"\" 表示一级菜单索引\r\n        // secondIndex:\"\" 表示二级菜单索引\r\n      },\r\n      visible2: false, // 素材内容  \"选择素材\"按钮弹框显示隐藏\r\n      tableData: [] // 素材内容弹框数据\r\n    };\r\n  },\r\n  created() {\r\n    \r\n    this.getWeChats();\r\n    this.mockMediaFun(); // 模拟获取素材\r\n    this.mockMenuFun(); // 模拟调取菜单数据\r\n    //this.getWxMenu();\r\n  },\r\n  methods: {\r\n    getWeChats() {\r\n      getWeChatAccount().then(res => {\r\n        this.wechats = [];\r\n        if (res.data.success) {\r\n          res.data.response.data.forEach(element => {\r\n            this.wechats.push({\r\n              value: element.publicAccount,\r\n              label: element.publicNick\r\n            });\r\n          });\r\n        }else{\r\n          this.$message.error(res.data.msg);\r\n        }\r\n      });\r\n    },\r\n    // 模拟调取菜单数据\r\n    getWxMenu(id) {\r\n      //var id = \"wx8fd620fd3f84c227\";\r\n      getWeChatMenu({ id: id }).then(res => {\r\n        console.log(res);\r\n        if (res.data.success) {\r\n          this.menu = res.data.response.menu;\r\n          this.$message.success(\"菜单获取成功!\");\r\n        }else{\r\n          this.$message.error(res.data.msg);\r\n        }\r\n      }); \r\n    }, \r\n    // 素材内容弹框的选择按钮函数\r\n    handleEdit(index, row) {\r\n      this.visible2 = false;\r\n      this.tempObj.media_id = row.name;\r\n    },\r\n    saveFun(id) {\r\n      //var id = \"wx8fd620fd3f84c227\";\r\n      updateWeChatMenu({ id: id, menu: this.menu }).then(res => { \r\n        if (res.data.success) {\r\n          if (res.data.response.errcode == 0) {\r\n            this.$message.success(\"设置成功!\");\r\n          } else {\r\n            this.$message.error(res.data.response.errmsg); \r\n          }\r\n        }else{\r\n            this.$message.error(res.data.msg); \r\n        }\r\n      });\r\n\r\n       \r\n    },\r\n    // 一级菜单点击事件\r\n    menuFun(i, item) {\r\n      console.log(i);\r\n      console.log(item);\r\n      this.showRightFlag = false; //右边菜单隐藏\r\n      // console.log(i)\r\n      this.tempObj = item; //这个如果放在顶部，flag会没有。因为重新赋值了。\r\n      this.tempSelfObj.grand = \"1\"; //表示一级菜单\r\n      this.tempSelfObj.index = i; //表示一级菜单索引\r\n      this.isActive = i; //一级菜单选中样式\r\n      this.isSubMenuFlag = i; //二级菜单显示标志\r\n      this.isSubMenuActive = -1; //二级菜单去除选中样式\r\n    },\r\n    // 二级菜单点击事件\r\n    subMenuFun(item, subItem, index, k) {\r\n      this.showRightFlag = false; //右边菜单隐藏\r\n      this.tempObj = subItem; //将点击的数据放到临时变量，对象有引用作用\r\n      this.tempSelfObj.grand = \"2\"; //表示二级菜单\r\n      this.tempSelfObj.index = index; //表示一级菜单索引\r\n      this.tempSelfObj.secondIndex = k; //表示二级菜单索引\r\n      this.isSubMenuActive = index + \"\" + k; //二级菜单选中样式\r\n      this.isActive = -1; //一级菜单去除样式\r\n    },\r\n    // 添加横向一级菜单\r\n    addMenu() {\r\n      console.log(\"addMenu\");\r\n      // 先判断1，再判断2,对象增加，会进行往下计算，所以必须先判断2，再判断1\r\n      if (this.menuKeyLength == 2) {\r\n        this.$set(this.menu.button, \"2\", {\r\n          name: \"菜单3\",\r\n          type: \"click\", \r\n          key: \"菜单3\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n          sub_button: []\r\n        });\r\n      }\r\n      if (this.menuKeyLength == 1) {\r\n        this.$set(this.menu.button, \"1\", {\r\n          name: \"菜单2\",\r\n          type: \"click\", \r\n          key: \"菜单2\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n          sub_button: []\r\n        });\r\n      }\r\n      if (this.menuKeyLength == 0) {\r\n        this.$set(this.menu.button, \"0\", {\r\n          name: \"菜单1\",\r\n           type: \"click\", \r\n          key: \"菜单1\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n          sub_button: []\r\n        });\r\n      }\r\n    },\r\n    // 添加横向二级菜单\r\n    addSubMenu(item) {\r\n      let subMenuKeyLength = item.sub_button.length; //获取二级菜单key长度\r\n      if (subMenuKeyLength == 4) {\r\n        this.$set(item.sub_button, \"4\", {\r\n          name: \"子菜单5\",\r\n           type: \"click\", \r\n          key: \"子菜单5\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n        });\r\n      }\r\n      if (subMenuKeyLength == 3) {\r\n        this.$set(item.sub_button, \"3\", {\r\n          name: \"子菜单4\",\r\n           type: \"click\", \r\n          key: \"子菜单4\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n        });\r\n      }\r\n      if (subMenuKeyLength == 2) {\r\n        this.$set(item.sub_button, \"2\", {\r\n          name: \"子菜单3\",\r\n           type: \"click\", \r\n          key: \"子菜单3\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n        });\r\n      }\r\n      if (subMenuKeyLength == 1) {\r\n        this.$set(item.sub_button, \"1\", {\r\n          name: \"子菜单2\",\r\n           type: \"click\", \r\n          key: \"子菜单2\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n        });\r\n      }\r\n      if (subMenuKeyLength == 0) {\r\n        this.$set(item.sub_button, \"0\", {\r\n          name: \"子菜单1\",\r\n           type: \"click\", \r\n          key: \"子菜单1\",\r\n          // url: \"\",//跳转链接\r\n          // media_id:\"\",//素材名称--图文消息\r\n        });\r\n      }\r\n    },\r\n    //删除当前菜单入口\r\n    deleteMenu(obj) {\r\n      var _this = this;\r\n      this.$confirm(\"正在删除当前选中菜单, 是否继续?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      })\r\n        .then(() => {\r\n          _this.deleteData(obj); // 删除菜单数据\r\n        })\r\n        .catch(e => {\r\n          this.$message({\r\n            type: \"error\",\r\n            message: e\r\n          });\r\n        });\r\n    },\r\n    // 删除菜单数据\r\n    deleteData(obj) {\r\n      if (this.showRightFlag) {\r\n        this.$message({\r\n          type: \"info\",\r\n          message: \"请选择要删除的菜单!\"\r\n        });\r\n      }\r\n      if (this.tempSelfObj.grand == \"1\") {\r\n        // 一级菜单的删除方法\r\n        this.menu.button.splice(this.tempSelfObj.index, 1);\r\n        this.isActive = -1;\r\n      } else if (this.tempSelfObj.grand == \"2\") {\r\n        //二级菜单的删除方法\r\n        this.menu.button[this.tempSelfObj.index].sub_button.splice(\r\n          this.tempSelfObj.secondIndex,\r\n          1\r\n        );\r\n        this.isSubMenuActive = -1;\r\n      } else {\r\n        console.log(\"没有找到对应的值:\" + this.tempSelfObj.grand);\r\n      }\r\n      this.showRightFlag = true;\r\n      this.$message({\r\n        type: \"success\",\r\n        message: \"删除成功!\"\r\n      });\r\n    }\r\n  },\r\n  mounted() {},\r\n  computed: {\r\n    // menuObj的长度，当长度 小于 3 才显示一级菜单的加号\r\n    menuKeyLength: function() {\r\n      return this.menu.button.length;\r\n    }\r\n  },\r\n  watch: {\r\n    selectWeChat: function(newName, oldName) {\r\n      console.log(newName);\r\n      this.getWxMenu(newName);\r\n    }\r\n  }\r\n};\r\n</script> \r\n<style scoped>\r\n@charset \"UTF-8\";\r\n/* 公共颜色变量 */\r\n.clearfix {\r\n  *zoom: 1;\r\n}\r\n\r\n.clearfix::after {\r\n  content: \"\";\r\n  display: table;\r\n  clear: both;\r\n}\r\n\r\ndiv {\r\n  text-align: left;\r\n}\r\n\r\n.public-account-management {\r\n  padding: 20px;\r\n  min-width: 1200px;\r\n  width: 1200px;\r\n  margin: 0 auto;\r\n  /*右边菜单内容*/\r\n}\r\n.public-account-management .left {\r\n  font-family: \"Times New Roman\", Times, serif;\r\n  float: left;\r\n  width: 301px;\r\n  background-size: 100% auto;\r\n  padding: 490px 25px 88px;\r\n  position: relative;\r\n  box-sizing: border-box;\r\n  /*第一级菜单*/\r\n}\r\n.public-account-management .left .menu .menu_bottom {\r\n  position: relative;\r\n  float: left;\r\n  box-sizing: border-box;\r\n  width: 83px;\r\n  /*height: 44px;*/\r\n  /*line-height: 44px;*/\r\n  text-align: center;\r\n  background-color: #e2e2e2;\r\n  border: 1px solid #ebedee;\r\n  cursor: pointer;\r\n}\r\n.public-account-management .left .menu .menu_bottom.menu_addicon {\r\n  height: 46px;\r\n  line-height: 46px;\r\n}\r\n.public-account-management .left .menu .menu_bottom .menu_item {\r\n  height: 44px;\r\n  line-height: 44px;\r\n  /*padding: 14px 0;*/\r\n  text-align: center;\r\n  box-sizing: border-box;\r\n}\r\n.public-account-management .left .menu .menu_bottom .menu_item.active {\r\n  border: 1px solid #2bb673;\r\n}\r\n.public-account-management .left .menu .menu_bottom .menu_subItem {\r\n  height: 44px;\r\n  line-height: 44px;\r\n  text-align: center;\r\n  box-sizing: border-box;\r\n  /*padding: 14px 0;*/\r\n}\r\n.public-account-management .left .menu .menu_bottom .menu_subItem.active {\r\n  border: 1px solid #2bb673;\r\n}\r\n.public-account-management .left .menu i {\r\n  color: #2bb673;\r\n}\r\n.public-account-management .left .menu .submenu {\r\n  position: absolute;\r\n  width: 83px;\r\n  bottom: 45px;\r\n}\r\n.public-account-management .left .menu .submenu .subtitle {\r\n  background-color: #e8e7e7;\r\n  box-sizing: border-box;\r\n  margin-bottom: 2px;\r\n}\r\n.public-account-management .left .save_btn {\r\n  position: relative;\r\n  margin-top: 100px;\r\n  left: 45px;\r\n}\r\n.public-account-management .right {\r\n  font-family: \"Times New Roman\", Times, serif;\r\n  float: left;\r\n  width: 70%;\r\n  background-color: #e8e7e7;\r\n  padding: 25px 10px 0px 20px;\r\n  min-height: 610px;\r\n  margin-left: 20px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\n.public-account-management .right p {\r\n  font-size: 16px;\r\n  margin-top: -10px;\r\n  font-weight: bold;\r\n}\r\n.public-account-management .right .configure_page .delete_btn {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n.public-account-management .right .configure_page .menu_content {\r\n  margin-top: 20px;\r\n}\r\n.public-account-management .right .configure_page .configur_content {\r\n  margin-top: 20px;\r\n}\r\n.public-account-management .right .configure_page .blue {\r\n  color: #29b6f6;\r\n  margin-top: 10px;\r\n}\r\n.public-account-management .right .configure_page .applet {\r\n  margin-bottom: 20px;\r\n}\r\n.public-account-management .right .configure_page .applet span {\r\n  margin-right: 18px;\r\n}\r\n.public-account-management .right .configure_page .material .input_width {\r\n  width: 30%;\r\n}\r\n.public-account-management .right .configure_page .material .el-textarea {\r\n  width: 80%;\r\n}\r\n</style>\r\n \r\n\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Menu.vue?vue&type=template&id=f4008632&scoped=true&\"\nimport script from \"./Menu.vue?vue&type=script&lang=js&\"\nexport * from \"./Menu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Menu.vue?vue&type=style&index=0&id=f4008632&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f4008632\",\n  null\n  \n)\n\ncomponent.options.__file = \"Menu.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=style&index=0&id=f4008632&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Menu.vue?vue&type=style&index=0&id=f4008632&scoped=true&lang=css&\""], "sourceRoot": ""}