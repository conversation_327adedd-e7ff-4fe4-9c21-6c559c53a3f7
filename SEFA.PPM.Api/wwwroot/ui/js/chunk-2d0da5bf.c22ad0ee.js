(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0da5bf"],{"6aec":function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("section",[o("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[o("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[o("el-form-item",[o("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.selectWeChat,callback:function(t){e.selectWeChat=t},expression:"selectWeChat"}},e._l(e.wechats,function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[o("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",disabled:""==e.selectWeChat},on:{click:function(t){e.searchWeChatAccount(e.selectWeChat)}}},[e._v("刷新")]),o("el-button",{attrs:{type:"primary",disabled:""==e.selectWeChat},on:{click:e.handleSendCar}},[e._v("模拟消息")])],1)],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":""},on:{"current-change":e.selectCurrentRow}},[o("el-table-column",{attrs:{type:"index",width:"80"}}),o("el-table-column",{attrs:{prop:"title",label:"标题",width:"",sortable:""}}),o("el-table-column",{attrs:{prop:"template_id",label:"模板ID",width:"",sortable:""}}),o("el-table-column",{attrs:{prop:"content",label:"示例",width:"",sortable:""}}),o("el-table-column",{attrs:{prop:"example",label:"格式",width:"",sortable:""}})],1),o("el-col",{staticClass:"toolbar",attrs:{span:24}},[o("el-pagination",{staticStyle:{float:"right"},attrs:{layout:"prev, pager, next","page-size":e.page.pageSize,total:e.page.pageTotal},on:{"current-change":e.handleCurrentChange}})],1),o("el-dialog",{attrs:{title:"卡片消息",visible:e.editFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.editFormVisible=t}},model:{value:e.editFormVisible,callback:function(t){e.editFormVisible=t},expression:"editFormVisible"}},[o("el-form",{ref:"editForm",attrs:{model:e.editForm,"label-width":"200px",rules:e.editFormRules}},[o("el-form-item",{attrs:{label:"公众号",prop:"id"}},[o("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.editForm.info.id,callback:function(t){e.$set(e.editForm.info,"id",t)},expression:"editForm.info.id"}},e._l(e.wechats,function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[o("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),o("el-form-item",{attrs:{label:"选择客户",prop:"companyCode"}},[o("el-select",{attrs:{placeholder:"请选择要操作的客户"},model:{value:e.editForm.info.companyCode,callback:function(t){e.$set(e.editForm.info,"companyCode",t)},expression:"editForm.info.companyCode"}},e._l(e.companys,function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[o("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),o("el-form-item",{attrs:{label:"选择用户",prop:"userID"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.info.userID,callback:function(t){e.$set(e.editForm.info,"userID",t)},expression:"editForm.info.userID"}})],1),o("el-form-item",{attrs:{label:"模板ID",prop:"template_id"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.template_id,callback:function(t){e.$set(e.editForm.cardMsg,"template_id",t)},expression:"editForm.cardMsg.template_id"}})],1),o("el-form-item",{attrs:{label:"first",prop:"first"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.first,callback:function(t){e.$set(e.editForm.cardMsg,"first",t)},expression:"editForm.cardMsg.first"}})],1),o("el-form-item",{attrs:{label:"colorFirst",prop:"colorFirst"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.colorFirst,callback:function(t){e.$set(e.editForm.cardMsg,"colorFirst",t)},expression:"editForm.cardMsg.colorFirst"}})],1),o("el-form-item",{attrs:{label:"keyword1",prop:"keyword1"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.keyword1,callback:function(t){e.$set(e.editForm.cardMsg,"keyword1",t)},expression:"editForm.cardMsg.keyword1"}})],1),o("el-form-item",{attrs:{label:"color1",prop:"color1"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.color1,callback:function(t){e.$set(e.editForm.cardMsg,"color1",t)},expression:"editForm.cardMsg.color1"}})],1),o("el-form-item",{attrs:{label:"keyword2",prop:"keyword2"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.keyword2,callback:function(t){e.$set(e.editForm.cardMsg,"keyword2",t)},expression:"editForm.cardMsg.keyword2"}})],1),o("el-form-item",{attrs:{label:"color2",prop:"color2"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.color2,callback:function(t){e.$set(e.editForm.cardMsg,"color2",t)},expression:"editForm.cardMsg.color2"}})],1),o("el-form-item",{attrs:{label:"keyword3",prop:"keyword3"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.keyword3,callback:function(t){e.$set(e.editForm.cardMsg,"keyword3",t)},expression:"editForm.cardMsg.keyword3"}})],1),o("el-form-item",{attrs:{label:"color3",prop:"color3"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.color3,callback:function(t){e.$set(e.editForm.cardMsg,"color3",t)},expression:"editForm.cardMsg.color3"}})],1),o("el-form-item",{attrs:{label:"keyword4",prop:"keyword4"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.keyword4,callback:function(t){e.$set(e.editForm.cardMsg,"keyword4",t)},expression:"editForm.cardMsg.keyword4"}})],1),o("el-form-item",{attrs:{label:"color4",prop:"color4"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.color4,callback:function(t){e.$set(e.editForm.cardMsg,"color4",t)},expression:"editForm.cardMsg.color4"}})],1),o("el-form-item",{attrs:{label:"keyword5",prop:"keyword5"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.keyword5,callback:function(t){e.$set(e.editForm.cardMsg,"keyword5",t)},expression:"editForm.cardMsg.keyword5"}})],1),o("el-form-item",{attrs:{label:"color5",prop:"color5"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.color5,callback:function(t){e.$set(e.editForm.cardMsg,"color5",t)},expression:"editForm.cardMsg.color5"}})],1),o("el-form-item",{attrs:{label:"remark",prop:"remark"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.remark,callback:function(t){e.$set(e.editForm.cardMsg,"remark",t)},expression:"editForm.cardMsg.remark"}})],1),o("el-form-item",{attrs:{label:"colorRemark",prop:"colorRemark"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.colorRemark,callback:function(t){e.$set(e.editForm.cardMsg,"colorRemark",t)},expression:"editForm.cardMsg.colorRemark"}})],1),o("el-form-item",{attrs:{label:"url",prop:"url"}},[o("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.cardMsg.url,callback:function(t){e.$set(e.editForm.cardMsg,"url",t)},expression:"editForm.cardMsg.url"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{nativeOn:{click:function(t){e.editFormVisible=!1}}},[e._v("取消")]),o("el-button",{attrs:{type:"primary",loading:e.editLoading},nativeOn:{click:function(t){return e.editSubmit(t)}}},[e._v("发送")])],1)],1)],1)},r=[],l=(o("ac6a"),o("cadf"),o("551c"),o("097d"),o("4ec3")),i={name:"WeChatCompany",data:function(){return{page:{pageSize:50,pageIndex:1,pageTotal:50},listLoading:!1,tableData:[],wechats:[],companys:[],selectWeChat:"",currentRow:null,editFormVisible:!1,editLoading:!1,editForm:{info:{id:"",companyCode:"",userID:""},cardMsg:{template_id:"",first:"",colorFirst:"",keyword1:"",color1:"",keyword2:"",color2:"",keyword3:"",color3:"",keyword4:"",color4:"",keyword5:"",color5:"",remark:"",colorRemark:"",url:""}},editFormRules:{}}},created:function(){this.getWeChats(),this.getWeCompanys()},methods:{editSubmit:function(){var e=this;this.$refs.editForm.validate(function(t){t&&e.$confirm("确认发送吗？","提示",{}).then(function(){e.editLoading=!0,Object(l["ab"])(e.editForm).then(function(t){e.editLoading=!1,t.data.success?(e.editFormVisible=!1,e.$message.success("推送成功!")):e.$message.error(t.data.msg)})})})},getWeCompanys:function(){var e=this;Object(l["U"])().then(function(t){e.companys=[],t.data.response.data.forEach(function(t){e.companys.push({value:t.CompanyID,label:t.CompanyName})})})},selectCurrentRow:function(e){this.currentRow=e},handleSendCar:function(){this.editForm.info.id=this.selectWeChat,this.currentRow&&(this.editForm.cardMsg.template_id=this.currentRow.template_id),this.editFormVisible=!0},handleCurrentChange:function(e){this.page.pageIndex=e,this.searchWeChatAccount()},searchWeChatAccount:function(e){var t=this;this.listLoading=!0,this.tableData=[],Object(l["Y"])({id:e}).then(function(e){t.listLoading=!1,e.data.success?t.tableData=e.data.response.template_list:t.$message.error(e.data.msg)})},getWeChats:function(){var e=this;Object(l["S"])().then(function(t){t.data.success?(e.wechats=[],t.data.response.data.forEach(function(t){e.wechats.push({value:t.publicAccount,label:t.publicNick})})):e.$message.error(t.data.msg)})}},mounted:function(){},watch:{selectWeChat:function(e,t){this.searchWeChatAccount(e)}}},s=i,c=o("2877"),n=Object(c["a"])(s,a,r,!1,null,null,null);n.options.__file="Template.vue";t["default"]=n.exports}}]);
//# sourceMappingURL=chunk-2d0da5bf.c22ad0ee.js.map