(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d726e0f8"],{"8bd6":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("section",[e("el-scrollbar",{staticClass:"default-scrollbar",attrs:{"wrap-class":"default-scrollbar__wrap","view-class":"p20-scrollbar__view"}},[e("el-row",{attrs:{gutter:24}},[e("el-col",{staticClass:"echarts-item",attrs:{sm:12,xs:24}},[e("div",{staticClass:"content-title"},[t._v("周访问柱状图 Top8")]),e("ve-histogram",{attrs:{data:t.histogramChartDataWeek,settings:t.histogramChartSettingsWeek,"mark-line":t.histogramChartMarkLine}})],1),e("el-col",{staticClass:"echarts-item",attrs:{sm:12,xs:24}},[e("div",{staticClass:"content-title"},[t._v("7天访问曲线图")]),e("ve-line",{attrs:{data:t.lineChartData7Day,extend:t.extend,settings:t.lineChartSettings7Day,"mark-point":t.lineChartMarkPoint}})],1),e("el-col",{staticClass:"echarts-item",attrs:{sm:12,xs:24}},[e("div",{staticClass:"content-title"},[t._v("24小时访问图")]),e("ve-line",{attrs:{data:t.lineChartData24Hour,settings:t.lineChartSettings24Hour,"mark-point":t.lineChartMarkPoint}})],1),e("el-col",{staticClass:"echarts-item",attrs:{sm:12,xs:24}},[e("div",{staticClass:"content-title"},[t._v("环形图")]),e("ve-ring",{attrs:{data:t.ringChartData,settings:t.ringChartSettings}})],1)],1)],1)],1)},n=[],r=e("2b0e"),i=e("2819"),o=e.n(i),l=e("4ec3");r["default"].use(o.a);var c={name:"AdminDashboard",data:function(){return{histogramChartDataWeek:{columns:[],rows:[]},extend:{series:{label:{normal:{show:!0}}}},histogramChartSettingsWeek:{},histogramChartMarkLine:{},lineChartData7Day:{columns:[],rows:[]},lineChartSettings7Day:{metrics:["count"],dimension:["date"]},lineChartData24Hour:{columns:[],rows:[]},lineChartSettings24Hour:{metrics:["count"],dimension:["date"]},lineChartMarkPoint:{data:[{name:"最大值",type:"max"},{name:"最小值",type:"min"}]},pieChartData:{columns:["日期","成本","利润"],rows:[{"日期":"1月1号","成本":123,"利润":3},{"日期":"1月2号","成本":1223,"利润":6},{"日期":"1月3号","成本":2123,"利润":90},{"日期":"1月4号","成本":4123,"利润":12},{"日期":"1月5号","成本":3123,"利润":15},{"日期":"1月6号","成本":7123,"利润":20}]},pieChartSettings:{dimension:"成本",metrics:"利润"},ringChartData:{columns:["日期","成本","利润"],rows:[{"日期":"1月1号","成本":123,"利润":3},{"日期":"1月2号","成本":1223,"利润":6},{"日期":"1月3号","成本":2123,"利润":90},{"日期":"1月4号","成本":4123,"利润":12},{"日期":"1月5号","成本":3123,"利润":15},{"日期":"1月6号","成本":7123,"利润":20}]},ringChartSettings:{dimension:"成本",metrics:"利润"}}},created:function(){},methods:{},mounted:function(){var t=this,a={};Object(l["L"])(a).then(function(a){t.histogramChartDataWeek.columns=a.data.response.columns,t.histogramChartDataWeek.rows=JSON.parse(a.data.response.rows)}),Object(l["w"])(a).then(function(a){t.lineChartData7Day=a.data.response}),Object(l["x"])(a).then(function(a){t.lineChartData24Hour=a.data.response})}},h=c,m=(e("9896"),e("2877")),u=Object(m["a"])(h,s,n,!1,null,"20a4f040",null);u.options.__file="Charts.vue";a["default"]=u.exports},9896:function(t,a,e){"use strict";var s=e("a620"),n=e.n(s);n.a},a620:function(t,a,e){}}]);
//# sourceMappingURL=chunk-d726e0f8.c8fe5894.js.map