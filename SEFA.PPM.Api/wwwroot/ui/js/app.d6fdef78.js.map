{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?5b85", "webpack:///src/App.vue", "webpack:///./src/views/APIDoc.vue?3756", "webpack:///./src/Auth/applicationusermanager.js", "webpack:///./src/views/Layout/Layout.vue?81bb", "webpack:///./src/views/Layout/Layout.vue", "webpack:///./src/Auth/UserAuth.js", "webpack:///./src/views/APIDoc.vue?6791", "webpack:///src/views/APIDoc.vue", "webpack:///./src/views/APIDoc.vue?fa20", "webpack:///./src/views/APIDoc.vue", "webpack:///./src/api/api.js", "webpack:///src/views/Welcome.vue", "webpack:///./src/App.vue?f2c5", "webpack:///./src/App.vue?a7d1", "webpack:///./src/App.vue", "webpack:///./src/lang/en.js", "webpack:///./src/lang/zh.js", "webpack:///./src/lang/es.js", "webpack:///./src/lang/index.js", "webpack:///./src/main.js", "webpack:///./src/components/Sidebar.vue?8a72", "webpack:///./src/components/AppLink.vue?aba2", "webpack:///./util/validate.js", "webpack:///src/components/AppLink.vue", "webpack:///./src/components/AppLink.vue?400c", "webpack:///./src/components/AppLink.vue", "webpack:///src/components/Sidebar.vue", "webpack:///./src/components/Sidebar.vue?7ef4", "webpack:///./src/components/Sidebar.vue", "webpack:///./src lazy ^\\.\\/views.*\\.vue$ namespace object", "webpack:///./src/App.vue?39f1", "webpack:///./src/router/_import_production.js", "webpack:///./src/views/Welcome.vue?0914", "webpack:///./util/global.js", "webpack:///./src/views/LoginCallbackView.vue?dbd2", "webpack:///src/views/LoginCallbackView.vue", "webpack:///./src/views/LoginCallbackView.vue?2fc4", "webpack:///./src/views/LoginCallbackView.vue", "webpack:///./src/views/404.vue?906f", "webpack:///./src/views/404.vue", "webpack:///./src/App.vue?c072", "webpack:///./src/router/index.js", "webpack:///./src/views/Login.vue?3044", "webpack:///src/views/Login.vue", "webpack:///./src/views/Login.vue?09c2", "webpack:///./src/views/Login.vue", "webpack:///./src/App.vue?c76b", "webpack:///./src/views/404.vue?4be8", "webpack:///./src/store.js", "webpack:///./src/views/Welcome.vue?e7c2", "webpack:///./src/promissionRouter.js", "webpack:///./src/assets/logo.png", "webpack:///./src/components/ScrollPane.vue?1010", "webpack:///src/components/ScrollPane.vue", "webpack:///./src/components/ScrollPane.vue?7bba", "webpack:///./src/components/ScrollPane.vue", "webpack:///./src/views/Login.vue?ff49", "webpack:///./src/components/ScrollPane.vue?ef47", "webpack:///./src/App.vue?36f8", "webpack:///./src/views/Welcome.vue?56d4", "webpack:///./src/views/Welcome.vue?253e", "webpack:///./src/views/Welcome.vue"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "installedChunks", "push", "Object", "prototype", "hasOwnProperty", "call", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "installedCssChunks", "app", "jsonpScriptSrc", "p", "chunk-23e41f57", "chunk-276b085c", "chunk-2d0a4854", "chunk-2d0c0c66", "chunk-2d0c4aa3", "chunk-2d0cf4f3", "chunk-2d0d2f25", "chunk-2d0da5bf", "chunk-2d213196", "chunk-2d21f214", "chunk-2d229214", "chunk-2d22d746", "chunk-40df6ae2", "chunk-47211100", "chunk-479d738e", "chunk-47dd42da", "chunk-4b6066be", "chunk-6e83591c", "chunk-7287e918", "chunk-ef28925c", "chunk-6f1c3bea", "chunk-735deb8e", "chunk-770e833a", "chunk-77279526", "chunk-789b0e7e", "chunk-bf843d8a", "chunk-c5ac0cca", "chunk-c673e236", "chunk-c75b8e6e", "chunk-cae4df82", "chunk-d726e0f8", "exports", "module", "l", "e", "promises", "cssChunks", "Promise", "resolve", "reject", "href", "fullhref", "existingLinkTags", "document", "getElementsByTagName", "tag", "dataHref", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onload", "onerror", "event", "request", "target", "src", "err", "Error", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "clearTimeout", "chunk", "errorType", "realSrc", "error", "undefined", "setTimeout", "all", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "window", "oldJsonpFunction", "slice", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "__webpack_exports__", "components", "Sidebar", "_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__", "ScrollPane", "_components_ScrollPane__WEBPACK_IMPORTED_MODULE_6__", "mixins", "_Auth_UserAuth__WEBPACK_IMPORTED_MODULE_9__", "sysName", "sysNameShort", "NewsVisible", "collapsed", "zModalShadow", "SidebarVisible", "collapsedClass", "sysUserName", "newsDialogCss", "sysUserAvatar", "tagsList", "form", "region", "date1", "date2", "delivery", "resource", "desc", "routes", "tagNews", "visible", "top", "left", "selectedTag", "affixTags", "methods", "<PERSON><PERSON><PERSON>", "Path", "log", "this", "$router", "replace", "path", "handleOpen", "keyP<PERSON>", "toindex", "handleClose", "onSubmit", "myNews", "handleopen", "handleclose", "handleselect", "a", "b", "logout", "_this2", "_this", "$confirm", "localStorage", "removeItem", "sessionStorage", "global", "<PERSON><PERSON><PERSON><PERSON>", "$store", "commit", "IS_IDS4", "_Auth_applicationusermanager__WEBPACK_IMPORTED_MODULE_8__", "catch", "Setting", "go<PERSON><PERSON><PERSON>", "open", "collapse", "screen", "width", "showMenu", "status", "$refs", "menuCollapsed", "getElementsByClassName", "style", "display", "isActive", "$route", "fullPath", "closeTags", "index", "delItem", "item", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_core_js_json_stringify__WEBPACK_IMPORTED_MODULE_3___default", "closeAll", "closeOther", "_this3", "curItem", "filter", "setItem", "setTags", "route", "meta", "NoTabPage", "isExist", "some", "title", "handleTags", "command", "getUserInfoByToken", "token", "_this4", "loginParams", "_api_api__WEBPACK_IMPORTED_MODULE_7__", "logining", "success", "$notify", "message", "concat", "response", "uRealName", "duration", "user", "$message", "mounted", "tags", "getItem", "JSON", "parse", "NavigationBar", "router", "updated", "avatar", "computed", "showTags", "watch", "_$route", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__", "regeneratorRuntime", "mark", "_callee", "newValue", "from", "_this5", "wrap", "_context", "prev", "next", "refreshUserInfo", "$nextTick", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_step", "_iterator", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_core_js_get_iterator__WEBPACK_IMPORTED_MODULE_0___default", "done", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "return", "stop", "_x", "_x2", "arguments", "created", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_APIDoc_vue_vue_type_style_index_0_id_1759f995_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_APIDoc_vue_vue_type_style_index_0_id_1759f995_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "ApplicationUserManager", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_helpers_esm_possibleConstructorReturn__WEBPACK_IMPORTED_MODULE_4__", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_helpers_esm_getPrototypeOf__WEBPACK_IMPORTED_MODULE_5__", "authority", "client_id", "redirect_uri", "response_type", "scope", "post_logout_redirect_uri", "signinRedirect", "getUser", "signoutRedirect", "UserManager", "applicationUserManager", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "staticStyle", "height", "_e", "keepAlive", "staticRenderFns", "component", "componentNormalizer", "options", "__file", "userAuth", "isAuthenticated", "_refreshUserInfo", "sent", "profile", "_created", "_callee2", "_context2", "_m", "attrs", "scrolling", "id", "frameborder", "APIDocvue_type_script_lang_js_", "changeMobsfIframe", "apidoc", "getElementById", "deviceHeight", "body", "clientWidth", "clientHeight", "Number", "onresize", "views_APIDocvue_type_script_lang_js_", "base", "axios", "defaults", "storeTemp", "store", "interceptors", "use", "config", "curTime", "Date", "expiretime", "state", "tokenExpire", "headers", "Authorization", "saveRefreshtime", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_core_js_promise__WEBPACK_IMPORTED_MODULE_1___default", "errInfo", "originalRequest", "code", "indexOf", "_retry", "refreshtime", "refreshToken", "Token", "res", "<PERSON><PERSON>", "expiredate", "setSeconds", "getSeconds", "expires_in", "__isRetryRequest", "<PERSON><PERSON><PERSON><PERSON>", "BaseApiUrl", "requestLogin", "params", "requestLoginMock", "post", "nowtime", "lastRefreshtime", "TokenExpire", "refreshCount", "setMinutes", "getMinutes", "login", "query", "redirect", "currentRoute", "getUserByToken", "test<PERSON>i", "pa", "getUserListPage", "removeUser", "delete", "editUser", "put", "addUser", "batchRemoveUser", "getRoleListPage", "removeRole", "editRole", "addRole", "getModuleListPage", "removeModule", "editModule", "addModule", "getPermissionTreeTable", "removePermission", "editPermission", "addPermission", "getPermissionTree", "getPermissionIds", "addRolePermission", "getNavigationBar", "getBugListPage", "removeBug", "editBug", "addBug", "getBlogListPage", "getBlogDeatil", "editBlog", "removeBlog", "getLogs", "getRequestApiinfoByWeek", "getAccessApiByDate", "getAccessApiByHour", "getServerInfo", "getActiveUsers", "getTaskListPage", "removeTask", "editTask", "addTask", "startJob", "stopJob", "reCovery", "pauseJob", "<PERSON><PERSON><PERSON>", "getTaskNameSpace", "getAchieveUsers_IS4", "getWeChatAccount", "remove<PERSON><PERSON><PERSON>hatAccount", "batchDeleteChatAccount", "addWeChatAccount", "updateWeChatAccount", "getWeChatTemplate", "refreshWeChatToken", "getWeChatMenu", "updateWeChatMenu", "getWeChatCompany", "removeWeChatCompany", "batchDeleteWeChatCompany", "addWeWeChatCompany", "updateWeChatCompany", "getWeChatPushLog", "getWeChatSubUser", "getWeChatBindUser", "pushTestMsg", "pushCardMsg", "getDepartmentTreeTable", "removeDepartment", "editDepartment", "addDepartment", "getDepartmentTree", "vue__WEBPACK_IMPORTED_MODULE_1__", "v_charts__WEBPACK_IMPORTED_MODULE_2___default", "listLoading", "welcomeInitData", "activeUsers", "activeUserCount", "logs", "errorCount", "serverInfo", "extend", "series", "label", "normal", "show", "lineChartDataIDS4", "columns", "rows", "today", "lineChartSettings7Day", "metrics", "dimension", "lineChartMarkPoint", "getTypeName", "count", "getBck", "toL<PERSON>s", "_Auth_applicationusermanager__WEBPACK_IMPORTED_MODULE_3__", "_api_api__WEBPACK_IMPORTED_MODULE_4__", "Appvue_type_template_id_40132ba9_render", "NoNeedHome", "span", "class", "on", "click", "_v", "_s", "separator", "_l", "trigger", "slot", "nativeOn", "$event", "divided", "background", "border-right", "default-active", "unique-opened", "background-color", "text-color", "active-text-color", "close", "select", "menu", "ref", "refInFor", "active", "mouseup", "button", "preventDefault", "stopPropagation", "size", "close-on-click-modal", "update:visible", "model", "callback", "$$v", "expression", "closable", "directives", "rawName", "z-index", "tabindex", "src_Appvue_type_script_lang_js_", "Appvue_type_script_lang_js_", "App", "lang_en", "dashboard", "introduction", "documentation", "guide", "permission", "pagePermission", "rolePermission", "directivePermission", "icons", "componentIndex", "<PERSON><PERSON><PERSON>", "markdown", "jsonEditor", "dndList", "splitPane", "avatarUpload", "dropzone", "sticky", "countTo", "componentMixin", "backToTop", "dragDialog", "dragSelect", "drag<PERSON><PERSON><PERSON>", "charts", "<PERSON><PERSON><PERSON>", "lineChart", "mixChart", "example", "nested", "menu1", "menu1-1", "menu1-2", "menu1-2-1", "menu1-2-2", "menu1-3", "menu2", "Table", "dynamicTable", "dragTable", "inlineEditTable", "complexTable", "treeTable", "customTreeTable", "tab", "createArticle", "editArticle", "articleList", "errorPages", "page401", "page404", "errorLog", "excel", "exportExcel", "selectExcel", "mergeHeader", "uploadExcel", "zip", "pdf", "exportZip", "theme", "clipboardDemo", "i18n", "externalLink", "navbar", "logOut", "github", "logIn", "username", "password", "any", "thirdparty", "thirdpartyTips", "roles", "switchRoles", "tips", "confirm", "cancel", "description", "tinymceTips", "dropzoneTips", "stickyTips", "backToTopTips1", "backToTopTips2", "imageUploadTips", "table", "dynamicTips1", "dynamicTips2", "dragTips1", "dragTips2", "importance", "remark", "search", "add", "export", "reviewer", "date", "author", "readings", "actions", "edit", "publish", "draft", "selectedExport", "placeholder", "change", "tagsView", "refresh", "closeOthers", "zh", "lang_es", "VueI18n", "messages", "en", "objectSpread", "enLocale", "elementEnLocale", "zhLocale", "elementZhLocale", "es", "esLocale", "elementEsLocale", "locale", "Cookies", "ElementUI", "beforeEach", "productionTip", "h", "$mount", "IsButton", "isExternalLink", "iconCls", "children", "child", "IsHide", "cop", "AppLinkvue_type_template_id_212f2a8a_render", "_b", "linkProps", "_t", "AppLinkvue_type_template_id_212f2a8a_staticRenderFns", "isExternal", "test", "AppLinkvue_type_script_lang_js_", "props", "String", "required", "validate_isExternal", "components_AppLinkvue_type_script_lang_js_", "AppLink", "Sidebarvue_type_script_lang_js_", "$emit", "components_Sidebarvue_type_script_lang_js_", "Sidebar_component", "map", "./views/403.vue", "./views/404.vue", "./views/APIDoc.vue", "./views/About.vue", "./views/Blog/Blogs.vue", "./views/Blog/Detail.vue", "./views/Department/Department.vue", "./views/Form/Charts.vue", "./views/Form/Form.vue", "./views/I18n/index.vue", "./views/Layout/Layout.vue", "./views/Login.vue", "./views/LoginCallbackView.vue", "./views/Logs/Index.vue", "./views/Permission/Assign.vue", "./views/Permission/Module.vue", "./views/Permission/Permission.vue", "./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_1.vue", "./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_2.vue", "./views/Recursion/Menu_1/Menu_1_2.vue", "./views/System/My.vue", "./views/Task/QuartzJob.vue", "./views/TestShow/TestOne.vue", "./views/TestShow/TestTwo.vue", "./views/Tibug/Bugs.vue", "./views/User/Roles.vue", "./views/User/Users.vue", "./views/WeChat/BindUser.vue", "./views/WeChat/Company.vue", "./views/WeChat/Manager.vue", "./views/WeChat/Menu.vue", "./views/WeChat/PushLog.vue", "./views/WeChat/SendMessage.vue", "./views/WeChat/SubUser.vue", "./views/WeChat/Template.vue", "./views/Welcome.vue", "webpackAsyncContext", "req", "ids", "keys", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_2_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_2_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "file", "info", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Welcome_vue_vue_type_style_index_1_id_11b2dad1_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Welcome_vue_vue_type_style_index_1_id_11b2dad1_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "desktop", "tablet", "LoginCallbackViewvue_type_script_lang_js_", "asyncToGenerator", "applicationusermanager", "signinRedirectCallback", "access_token", "uLoginName", "preferred_username", "uID", "sub", "stringify_default", "GetNavigationBar", "t0", "$root", "uid", "api", "userinfo", "parse_int_default", "getRouter", "$addRoutes", "views_LoginCallbackViewvue_type_script_lang_js_", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_4_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_4_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "_import", "require", "Router", "createRouter", "process", "NoPage", "requireAuth", "hidden", "APIDoc", "Welcome", "<PERSON><PERSON>", "LoginCallbackView", "filterAsyncRouter", "asyncRouterMap", "accessedRouters", "Layout", "resetRouter", "newRouter", "matcher", "f", "addRoutes", "ruleForm2", "rules", "rules2", "label-position", "label-width", "prop", "auto-complete", "$set", "show-password", "checked", "margin-bottom", "loginAccount", "account3", "loading", "handleSubmit2", "loginStr", "loginingMock", "handleSubmitMock", "Loginvue_type_script_lang_js_", "instance", "account", "checkPass", "handleReset2", "resetFields", "ev", "validate", "valid", "msg", "openAlert", "position", "<PERSON><PERSON><PERSON><PERSON>", "pass", "views_Loginvue_type_script_lang_js_", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_1_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_1_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_404_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_404_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "vue__WEBPACK_IMPORTED_MODULE_0__", "vuex__WEBPACK_IMPORTED_MODULE_1__", "js_cookie__WEBPACK_IMPORTED_MODULE_2__", "js_cookie__WEBPACK_IMPORTED_MODULE_2___default", "Vuex", "Store", "tagsStoreList", "language", "mutations", "saveToken", "saveTokenExpire", "saveTagsData", "SET_LANGUAGE", "set", "setLanguage", "_ref", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Welcome_vue_vue_type_style_index_0_id_11b2dad1_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Welcome_vue_vue_type_style_index_0_id_11b2dad1_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "get<PERSON>bj<PERSON>rr", "saveObjArr", "routerGo", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_helpers_esm_objectSpread__WEBPACK_IMPORTED_MODULE_2__", "D_Code_Test_Vue_Blog_Admin_node_modules_babel_runtime_corejs2_core_js_json_stringify__WEBPACK_IMPORTED_MODULE_1___default", "buttonList", "getButtonList", "routePath", "routers", "for<PERSON>ach", "element", "toLowerCase", "vertical", "wheel", "handleScroll", "tagAndTagSpacing", "ScrollPanevue_type_script_lang_js_", "scrollWrapper", "scrollContainer", "event<PERSON>el<PERSON>", "wheelDelta", "deltaY", "$scrollWrapper", "scrollLeft", "currentTag", "tagList", "$container", "$el", "$containerWidth", "offsetWidth", "firstTag", "lastTag", "scrollWidth", "currentIndex", "findIndex", "prevTag", "nextTag", "afterNextTagOffsetLeft", "offsetLeft", "beforePrevTagOffsetLeft", "components_ScrollPanevue_type_script_lang_js_", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ScrollPane_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ScrollPane_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_3_lang_css___WEBPACK_IMPORTED_MODULE_0__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_App_vue_vue_type_style_index_3_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "margin-top", "float", "margin", "data-v-6723c96e", "color", "font-size", "margin-right", "EnvironmentName", "OSArchitecture", "ContentRootPath", "WebRootPath", "FrameworkDescription", "MemoryFootprint", "WorkingTime", "settings", "mark-point", "highlight-current-row", "border", "sortable", "show-overflow-tooltip", "scopedSlots", "_u", "fn", "text-decoration", "cursor", "row", "Agent", "overflow-y", "direction", "views_Welcomevue_type_script_lang_js_", "Welcomevue_type_script_lang_js_"], "mappings": "aACA,SAAAA,EAAAC,GAQA,IAPA,IAMAC,EAAAC,EANAC,EAAAH,EAAA,GACAI,EAAAJ,EAAA,GACAK,EAAAL,EAAA,GAIAM,EAAA,EAAAC,EAAA,GACQD,EAAAH,EAAAK,OAAoBF,IAC5BJ,EAAAC,EAAAG,GACAG,EAAAP,IACAK,EAAAG,KAAAD,EAAAP,GAAA,IAEAO,EAAAP,GAAA,EAEA,IAAAD,KAAAG,EACAO,OAAAC,UAAAC,eAAAC,KAAAV,EAAAH,KACAc,EAAAd,GAAAG,EAAAH,IAGAe,KAAAhB,GAEA,MAAAO,EAAAC,OACAD,EAAAU,OAAAV,GAOA,OAHAW,EAAAR,KAAAS,MAAAD,EAAAb,GAAA,IAGAe,IAEA,SAAAA,IAEA,IADA,IAAAC,EACAf,EAAA,EAAiBA,EAAAY,EAAAV,OAA4BF,IAAA,CAG7C,IAFA,IAAAgB,EAAAJ,EAAAZ,GACAiB,GAAA,EACAC,EAAA,EAAkBA,EAAAF,EAAAd,OAA2BgB,IAAA,CAC7C,IAAAC,EAAAH,EAAAE,GACA,IAAAf,EAAAgB,KAAAF,GAAA,GAEAA,IACAL,EAAAQ,OAAApB,IAAA,GACAe,EAAAM,IAAAC,EAAAN,EAAA,KAGA,OAAAD,EAIA,IAAAQ,EAAA,GAGAC,EAAA,CACAC,IAAA,GAMAtB,EAAA,CACAsB,IAAA,GAGAb,EAAA,GAGA,SAAAc,EAAA9B,GACA,OAAAyB,EAAAM,EAAA,UAA6C/B,OAAA,KAA6BgC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,YAAo2B9D,GAAA,MAI96B,SAAAyB,EAAA1B,GAGA,GAAA4B,EAAA5B,GACA,OAAA4B,EAAA5B,GAAAgE,QAGA,IAAAC,EAAArC,EAAA5B,GAAA,CACAK,EAAAL,EACAkE,GAAA,EACAF,QAAA,IAUA,OANAlD,EAAAd,GAAAa,KAAAoD,EAAAD,QAAAC,IAAAD,QAAAtC,GAGAuC,EAAAC,GAAA,EAGAD,EAAAD,QAKAtC,EAAAyC,EAAA,SAAAlE,GACA,IAAAmE,EAAA,GAIAC,EAAA,CAAoBvB,iBAAA,EAAAG,iBAAA,EAAAE,iBAAA,EAAAC,iBAAA,EAAAK,iBAAA,EAAAG,iBAAA,EAAAC,iBAAA,EAAAE,iBAAA,GACpBlC,EAAA5B,GAAAmE,EAAA3D,KAAAoB,EAAA5B,IACA,IAAA4B,EAAA5B,IAAAoE,EAAApE,IACAmE,EAAA3D,KAAAoB,EAAA5B,GAAA,IAAAqE,QAAA,SAAAC,EAAAC,GAIA,IAHA,IAAAC,EAAA,WAA4BxE,OAAA,KAA6BgC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,WAAAC,iBAAA,YAAo2B9D,GAAA,OAC75ByE,EAAAhD,EAAAM,EAAAyC,EACAE,EAAAC,SAAAC,qBAAA,QACAxE,EAAA,EAAmBA,EAAAsE,EAAApE,OAA6BF,IAAA,CAChD,IAAAyE,EAAAH,EAAAtE,GACA0E,EAAAD,EAAAE,aAAA,cAAAF,EAAAE,aAAA,QACA,kBAAAF,EAAAG,MAAAF,IAAAN,GAAAM,IAAAL,GAAA,OAAAH,IAEA,IAAAW,EAAAN,SAAAC,qBAAA,SACA,IAAAxE,EAAA,EAAmBA,EAAA6E,EAAA3E,OAA8BF,IAAA,CACjDyE,EAAAI,EAAA7E,GACA0E,EAAAD,EAAAE,aAAA,aACA,GAAAD,IAAAN,GAAAM,IAAAL,EAAA,OAAAH,IAEA,IAAAY,EAAAP,SAAAQ,cAAA,QACAD,EAAAF,IAAA,aACAE,EAAAE,KAAA,WACAF,EAAAG,OAAAf,EACAY,EAAAI,QAAA,SAAAC,GACA,IAAAC,EAAAD,KAAAE,QAAAF,EAAAE,OAAAC,KAAAjB,EACAkB,EAAA,IAAAC,MAAA,qBAAA5F,EAAA,cAAAwF,EAAA,KACAG,EAAAH,iBACA5D,EAAA5B,GACAkF,EAAAW,WAAAC,YAAAZ,GACAX,EAAAoB,IAEAT,EAAAV,KAAAC,EAEA,IAAAsB,EAAApB,SAAAC,qBAAA,WACAmB,EAAAC,YAAAd,KACKe,KAAA,WACLrE,EAAA5B,GAAA,KAMA,IAAAkG,EAAA3F,EAAAP,GACA,OAAAkG,EAGA,GAAAA,EACA/B,EAAA3D,KAAA0F,EAAA,QACK,CAEL,IAAAC,EAAA,IAAA9B,QAAA,SAAAC,EAAAC,GACA2B,EAAA3F,EAAAP,GAAA,CAAAsE,EAAAC,KAEAJ,EAAA3D,KAAA0F,EAAA,GAAAC,GAGA,IACAC,EADAC,EAAA1B,SAAAQ,cAAA,UAGAkB,EAAAC,QAAA,QACAD,EAAAE,QAAA,IACA9E,EAAA+E,IACAH,EAAAI,aAAA,QAAAhF,EAAA+E,IAEAH,EAAAX,IAAA5D,EAAA9B,GAEAoG,EAAA,SAAAb,GAEAc,EAAAf,QAAAe,EAAAhB,OAAA,KACAqB,aAAAH,GACA,IAAAI,EAAApG,EAAAP,GACA,OAAA2G,EAAA,CACA,GAAAA,EAAA,CACA,IAAAC,EAAArB,IAAA,SAAAA,EAAAH,KAAA,UAAAG,EAAAH,MACAyB,EAAAtB,KAAAE,QAAAF,EAAAE,OAAAC,IACAoB,EAAA,IAAAlB,MAAA,iBAAA5F,EAAA,cAAA4G,EAAA,KAAAC,EAAA,KACAC,EAAA1B,KAAAwB,EACAE,EAAAtB,QAAAqB,EACAF,EAAA,GAAAG,GAEAvG,EAAAP,QAAA+G,IAGA,IAAAR,EAAAS,WAAA,WACAZ,EAAA,CAAwBhB,KAAA,UAAAK,OAAAY,KAClB,MACNA,EAAAf,QAAAe,EAAAhB,OAAAe,EACAzB,SAAAoB,KAAAC,YAAAK,GAGA,OAAAhC,QAAA4C,IAAA9C,IAIA1C,EAAAyF,EAAArG,EAGAY,EAAA0F,EAAAxF,EAGAF,EAAA2F,EAAA,SAAArD,EAAAsD,EAAAC,GACA7F,EAAA8F,EAAAxD,EAAAsD,IACA5G,OAAA+G,eAAAzD,EAAAsD,EAAA,CAA0CI,YAAA,EAAAC,IAAAJ,KAK1C7F,EAAAkG,EAAA,SAAA5D,GACA,qBAAA6D,eAAAC,aACApH,OAAA+G,eAAAzD,EAAA6D,OAAAC,YAAA,CAAwDC,MAAA,WAExDrH,OAAA+G,eAAAzD,EAAA,cAAiD+D,OAAA,KAQjDrG,EAAAsG,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAArG,EAAAqG,IACA,EAAAE,EAAA,OAAAF,EACA,KAAAE,GAAA,kBAAAF,QAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAzH,OAAA0H,OAAA,MAGA,GAFA1G,EAAAkG,EAAAO,GACAzH,OAAA+G,eAAAU,EAAA,WAAyCT,YAAA,EAAAK,UACzC,EAAAE,GAAA,iBAAAF,EAAA,QAAAM,KAAAN,EAAArG,EAAA2F,EAAAc,EAAAE,EAAA,SAAAA,GAAgH,OAAAN,EAAAM,IAAqBC,KAAA,KAAAD,IACrI,OAAAF,GAIAzG,EAAA6G,EAAA,SAAAtE,GACA,IAAAsD,EAAAtD,KAAAiE,WACA,WAA2B,OAAAjE,EAAA,YAC3B,WAAiC,OAAAA,GAEjC,OADAvC,EAAA2F,EAAAE,EAAA,IAAAA,GACAA,GAIA7F,EAAA8F,EAAA,SAAAgB,EAAAC,GAAsD,OAAA/H,OAAAC,UAAAC,eAAAC,KAAA2H,EAAAC,IAGtD/G,EAAAM,EAAA,OAGAN,EAAAgH,GAAA,SAAA9C,GAA8D,MAApB+C,QAAA5B,MAAAnB,GAAoBA,GAE9D,IAAAgD,EAAAC,OAAA,gBAAAA,OAAA,oBACAC,EAAAF,EAAAnI,KAAA6H,KAAAM,GACAA,EAAAnI,KAAAX,EACA8I,IAAAG,QACA,QAAA1I,EAAA,EAAgBA,EAAAuI,EAAArI,OAAuBF,IAAAP,EAAA8I,EAAAvI,IACvC,IAAAU,EAAA+H,EAIA7H,EAAAR,KAAA,qBAEAU,kFCrQA,IAAA6H,EAAAtH,EAAA,QAAAuH,EAAAvH,EAAA6G,EAAAS,GAAqbC,EAAG,oPC8JxbC,EAAA,MACAC,WAAA,CAAAC,QAAAC,EAAA,KAAAC,WAAAC,EAAA,MACAC,OAAA,CAAAC,EAAA,MACA1J,KAHA,WAIA,OACA2J,QAAA,YACAC,aAAA,KACAC,aAAA,EACAC,WAAA,EACAC,cAAA,EACAC,gBAAA,EACAC,eAAA,gBACAC,YAAA,GACAC,cAAA,cACAC,cAAA,GACAC,SAAA,GACAC,KAAA,CACA/C,KAAA,GACAgD,OAAA,GACAC,MAAA,GACAC,MAAA,GACAC,UAAA,EACApF,KAAA,GACAqF,SAAA,GACAC,KAAA,IAEAC,OAAA,GACAC,QAAA,CACA,CAAAvD,KAAA,QAAAjC,KAAA,IACA,CAAAiC,KAAA,SAAAjC,KAAA,IACA,CAAAiC,KAAA,YAAAjC,KAAA,WACA,CAAAiC,KAAA,MAAAjC,KAAA,QACA,CAAAiC,KAAA,QAAAjC,KAAA,WACA,CAAAiC,KAAA,QAAAjC,KAAA,WAEAyF,SAAA,EACAC,IAAA,EACAC,KAAA,EACAC,YAAA,GACAC,UAAA,KAIAC,QAAA,CACAC,UADA,SACAC,GACA1C,QAAA2C,IAAAD,GACAE,KAAAC,QAAAC,QAAA,CACAC,KAAAL,KAGAM,WAPA,SAOAtD,EAAAuD,GACAjD,QAAA2C,IAAAjD,EAAAuD,IAEAC,QAVA,WAWAN,KAAAC,QAAAC,QAAA,CACAC,KAAA,OAGAI,YAfA,SAeAzD,EAAAuD,GACAjD,QAAA2C,IAAAjD,EAAAuD,IAEAG,SAlBA,WAmBApD,QAAA2C,IAAA,YAEAU,OArBA,WAuBAT,KAAArB,eAAA,SACAqB,KAAA3B,aAAA,GAEAqC,WA1BA,aA6BAC,YA7BA,aAgCAC,aAAA,SAAAC,EAAAC,KAGAC,OAAA,eAAAC,EAAAhB,KACAiB,EAAAjB,KACAA,KAAAkB,SAAA,kBAEAvG,KAAA,WACA2C,OAAA6D,aAAAC,WAAA,QACA9D,OAAA6D,aAAAC,WAAA,SACA9D,OAAA6D,aAAAC,WAAA,eACA9D,OAAA6D,aAAAC,WAAA,iBACA9D,OAAA6D,aAAAC,WAAA,eACA9D,OAAA6D,aAAAC,WAAA,UACAC,eAAAD,WAAA,QAEAE,EAAAC,UAAA,GAEAP,EAAAnC,SAAA,GACAmC,EAAA3B,OAAA,GACA2B,EAAAQ,OAAAC,OAAA,mBAEAH,EAAAI,QACAC,EAAA,KAAAZ,SAEAE,EAAAhB,QAAA/K,KAAA,YAIA0M,MAAA,eAOAC,QAAA,WACA,IAAAZ,EAAAjB,KACAiB,EAAAhB,QAAA/K,KAAA,eAGA4M,SAzEA,WA0EAxE,OAAAyE,KAAA,yCAGAC,SAAA,WACAhC,KAAA1B,WAAA0B,KAAA1B,UAEA0B,KAAA1B,WACA0B,KAAAvB,eAAA,iBACAuB,KAAAxB,gBAAA,IAEAwB,KAAAvB,eAAA,gBACAnB,OAAA2E,OAAAC,MAAA,MACAlC,KAAAxB,gBAAA,IAIAlB,OAAA6D,aAAAa,SAAAhC,KAAA1B,WAEA6D,SA5FA,SA4FArN,EAAAsN,GACApC,KAAAqC,MAAAC,cAAAC,uBAAA,gBAAAzN,GAAA,GAAA0N,MAAAC,QAAAL,EAAA,gBAEAM,SA/FA,SA+FAvC,GACA,OAAAA,IAAAH,KAAA2C,OAAAC,UAGAC,UAnGA,SAmGAC,GACA,IAAAC,EAAA/C,KAAAnB,SAAA3I,OAAA4M,EAAA,MACAE,EAAAhD,KAAAnB,SAAAiE,GAAA9C,KAAAnB,SAAAiE,GAAA9C,KAAAnB,SAAAiE,EAAA,GACAE,GACAD,EAAA5C,OAAAH,KAAA2C,OAAAC,UAAA5C,KAAAC,QAAA/K,KAAA8N,EAAA7C,MAEAH,KAAAwB,OAAAC,OAAA,eAAAwB,IAAAjD,KAAAnB,YAEAmB,KAAAC,QAAA/K,KAAA,MAIAgO,SA/GA,WAgHAlD,KAAAnB,SAAA,GACAmB,KAAAC,QAAA/K,KAAA,KACAmM,eAAAD,WAAA,SAGA+B,WArHA,WAqHA,IAAAC,EAAApD,KACAqD,EAAArD,KAAAnB,SAAAyE,OAAA,SAAAN,GACA,OAAAA,EAAA7C,OAAAiD,EAAAT,OAAAC,WAEA5C,KAAAnB,SAAAwE,EAGAhC,eAAAkC,QAAA,OAAAN,IAAAjD,KAAAnB,YAGA2E,QA/HA,SA+HAC,GACA,IAAAA,EAAAC,KAAAC,UAAA,CACA,IAAAC,EAAA5D,KAAAnB,SAAAgF,KAAA,SAAAb,GACA,OAAAA,EAAA7C,OAAAsD,EAAAb,YAEAgB,GAAA5D,KAAAnB,SAAA3J,KAAA,CACA4O,MAAAL,EAAAC,KAAAI,MACA3D,KAAAsD,EAAAb,aAMAmB,WA5IA,SA4IAC,GACA,UAAAA,EAAAhE,KAAAmD,aAAAnD,KAAAkD,YAEAe,mBA/IA,SA+IAC,GAAA,IAAAC,EAAAnE,KACAiB,EAAAjB,KACAoE,EAAA,CAAAF,SACA/O,OAAAkP,EAAA,KAAAlP,CAAAiP,GAAAzJ,KAAA,SAAAnG,GACA2P,EAAAG,UAAA,EACA9P,EAAA+P,SAOAtD,EAAAuD,QAAA,CACA1K,KAAA,UACA2K,QAAA,SAAAC,OAAAlQ,EAAAmQ,SAAAC,UAAA,MACAC,SAAA,MAGA5D,EAAAvC,YAAAlK,EAAAmQ,SAAAC,UACAtH,OAAA6D,aAAA2D,KAAA7B,IAAAzO,EAAAmQ,WAbA1D,EAAA8D,SAAA,CACAN,QAAAjQ,EAAAiQ,QACA3K,KAAA,cAgBAkL,QAlNA,WAmNA5H,QAAA2C,IAAAC,KAAA2C,QAEA,IAAAsC,EAAA5D,eAAA6D,QAAA,QAAAC,KAAAC,MAAA/D,eAAA6D,QAAA,YAEAD,KAAAjQ,OAAA,IACAgL,KAAAnB,SAAAoG,GAIA,IAAAI,EAAAF,KAAAC,MAAA9H,OAAA6D,aAAAmE,OAAAhI,OAAA6D,aAAAmE,OAAA,MAGAtF,KAAAX,OAAArK,QAAA,GAAAqQ,KAAArQ,QAAA,IACAgL,KAAAX,OAAAgG,IAOAE,QAvOA,WAwOA,IAAAT,EAAAK,KAAAC,MAAA9H,OAAA6D,aAAA2D,KAAAxH,OAAA6D,aAAA2D,KAAA,MACAA,IACA9E,KAAAtB,YAAAoG,EAAAF,WAAA,QACA5E,KAAApB,cAAAkG,EAAAU,QAAA,sBAIA,IAAAH,EAAAF,KAAAC,MAAA9H,OAAA6D,aAAAmE,OAAAhI,OAAA6D,aAAAmE,OAAA,MAEAD,KAAArQ,QAAA,IACAgL,KAAAX,OAAArK,QAAA,GAAAiO,IAAAjD,KAAAX,SAAA4D,IAAAoC,MACArF,KAAAX,OAAAgG,IAKAI,SAAA,CACAC,SADA,WAKA,OAHA1F,KAAAnB,SAAA7J,OAAA,GACAgL,KAAAwB,OAAAC,OAAA,eAAAwB,IAAAjD,KAAAnB,WAEAmB,KAAAnB,SAAA7J,OAAA,IAGA2Q,MAAA,CAEAhD,OAAA,eAAAiD,EAAAzQ,OAAA0Q,EAAA,KAAA1Q,CAAA2Q,mBAAAC,KAAA,SAAAC,EAAAC,EAAAC,GAAA,IAAAjB,EAAAkB,EAAAnG,KAAA,OAAA8F,mBAAAM,KAAA,SAAAC,GAAA,eAAAA,EAAAC,KAAAD,EAAAE,MAAA,WAEAjF,EAAAI,QAFA,CAAA2E,EAAAE,KAAA,eAAAF,EAAAE,KAAA,EAGAvG,KAAAwG,kBAHA,OAMAxG,KAAAwD,QAAAyC,GAEAhB,EAAAjF,KAAAqC,MAAA9I,IACAyG,KAAAyG,UAAA,WACA,GAAAxB,EAAA,KAAAyB,GAAA,EAAAC,GAAA,EAAAC,OAAAnL,EAAA,IACA,QAAAoL,EAAAC,EAAAC,IAAA9B,KAAAyB,GAAAG,EAAAC,EAAAP,QAAAS,MAAAN,GAAA,OAAAnN,EAAAsN,EAAArK,MACA,GAAAjD,EAAA0N,GAAA9G,OAAAgG,EAAAxD,OAAAxC,KAAA,CACAgG,EAAA9D,MAAA6E,WAAAC,aAAA5N,EAAA0L,GAKA,QARA,MAAA5K,GAAAsM,GAAA,EAAAC,EAAAvM,EAAA,YAAAqM,GAAA,MAAAI,EAAAM,QAAAN,EAAAM,SAAA,WAAAT,EAAA,MAAAC,OAVA,wBAAAP,EAAAgB,SAAArB,EAAAhG,SAAA,SAAA2C,EAAA2E,EAAAC,GAAA,OAAA3B,EAAAjQ,MAAAqK,KAAAwH,WAAA,OAAA7E,EAAA,IA2BA8E,QA7RA,WA+RAzH,KAAAwD,QAAAxD,KAAA2C,QAEA,QAAArF,OAAA6D,aAAAa,UACAhC,KAAA1B,WAAA,EACA0B,KAAAgC,aAEAhC,KAAA1B,WAAA,EACA0B,KAAAgC,0ECpcA,IAAA0F,EAAAvR,EAAA,QAAAwR,EAAAxR,EAAA6G,EAAA0K,GAAkeC,EAAG,uKCG/dC,cACJ,SAAAA,IAAe,OAAAzS,OAAA0S,EAAA,KAAA1S,CAAA6K,KAAA4H,GAAAzS,OAAA2S,EAAA,KAAA3S,CAAA6K,KAAA7K,OAAA4S,EAAA,KAAA5S,CAAAyS,GAAAtS,KAAA0K,KACP,CACJgI,UAAW,0BACXC,UAAW,cACXC,aAAc,wCACdC,cAAe,iBACfC,MAAO,qCACPC,yBAA0B,uQAKtBrI,KAAKsI,iDACJtI,KAAKuI,oTAILvI,KAAKwI,+HAlBqBC,kBAsB/BC,EAAyB,IAAId,+CCzBnC,IAAAe,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,gBAA0BE,YAAA,mBAAAC,YAAA,CAA4CC,OAAA,SAAiB,CAAAJ,EAAA,cAAAH,EAAAjG,OAAAe,KAAA,UAAAqF,EAAA,eAAAH,EAAAQ,MAAA,GAAAR,EAAAjG,OAAAe,KAAA2F,UAAAT,EAAAQ,KAAAL,EAAA,oBAChLO,EAAA,eCAAvO,EAAA,GAKAwO,EAAgBpU,OAAAqU,EAAA,KAAArU,CAChB4F,EACE4N,EACAW,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,aACe/L,EAAA,WAAA4L,wHCjBTI,EAAW,CACfnV,KADe,WAEb,MAAO,CACLsQ,KAAM,CACJ/I,KAAM,GACN6N,iBAAiB,KAIvBhK,QAAS,CACD4G,gBADC,eAAAqD,EAAA1U,OAAA0Q,EAAA,KAAA1Q,CAAA2Q,mBAAAC,KAAA,SAAAC,IAAA,IAAAlB,EAAA,OAAAgB,mBAAAM,KAAA,SAAAC,GAAA,eAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EAEcmC,OAAuBH,UAFrC,OAECzD,EAFDuB,EAAAyD,KAGDhF,GACF9E,KAAK8E,KAAK/I,KAAO+I,EAAKiF,QAAQhO,KAC9BiE,KAAK8E,KAAK8E,iBAAkB,IAE5B5J,KAAK8E,KAAK/I,KAAO,GACjBiE,KAAK8E,KAAK8E,iBAAkB,GARzB,wBAAAvD,EAAAgB,SAAArB,EAAAhG,SAAA,SAAAwG,IAAA,OAAAqD,EAAAlU,MAAAqK,KAAAwH,WAAA,OAAAhB,EAAA,IAYHiB,QArBS,eAAAuC,EAAA7U,OAAA0Q,EAAA,KAAA1Q,CAAA2Q,mBAAAC,KAAA,SAAAkE,IAAA,OAAAnE,mBAAAM,KAAA,SAAA8D,GAAA,eAAAA,EAAA5D,KAAA4D,EAAA3D,MAAA,cAAA2D,EAAA3D,KAAA,EAsBPvG,KAAKwG,kBAtBE,wBAAA0D,EAAA7C,SAAA4C,EAAAjK,SAAA,SAAAyH,IAAA,OAAAuC,EAAArU,MAAAqK,KAAAwH,WAAA,OAAAC,EAAA,IAyBFkC,qDC1Bf,IAAAhB,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BF,EAAAI,MAAAD,GAAwB,OAAAH,EAAAuB,GAAA,IACzFb,EAAA,YAAoC,IAAAV,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,UAAqB,CAAAF,EAAA,UAAeqB,MAAA,CAAOhQ,IAAA,0BAAAiQ,UAAA,OAAAC,GAAA,SAAApI,MAAA,OAAAqI,YAAA,WCoB/JC,aAAA,CACAzO,KAAA,SACAvH,KAFA,WAGA,UAEAwQ,QALA,WASA,SAAAyF,IACA,IAAAC,EAAArR,SAAAsR,eAAA,UAEAC,GADAvR,SAAAwR,KAAAC,YACAzR,SAAAwR,KAAAE,cAEAL,EAAAlI,MAAA2G,OAAA6B,OAAAJ,GAAA,SAGAH,IAEAnN,OAAA2N,SAAA,WACAR,QCzCgVS,EAAA,0BCQhV3B,EAAgBpU,OAAAqU,EAAA,KAAArU,CACd+V,EACAvC,EACAW,GACF,EACA,KACA,WACA,MAIAC,EAAAE,QAAAC,OAAA,aACe/L,EAAA,WAAA4L,ioFCZX4B,EAAO,GAMXC,IAAMC,SAASpQ,QAAU,IAEzB,IAAIqQ,EAAYC,OAChBH,IAAMI,aAAatR,QAAQuR,IACvB,SAAAC,GACI,IAAIC,EAAU,IAAIC,KACdC,EAAa,IAAID,KAAKA,KAAKxG,MAAMkG,EAAUQ,MAAMC,cASrD,OAPIT,EAAUQ,MAAM5H,OAAUyH,EAAUE,GAAcP,EAAUQ,MAAMC,cAElEL,EAAOM,QAAQC,cAAgB,UAAYX,EAAUQ,MAAM5H,OAG/DgI,IAEOR,GAEX,SAAArR,GACI,OAAO8R,EAAAtL,EAAQ5H,OAAOoB,KAK9B+Q,IAAMI,aAAa7G,SAAS8G,IACxB,SAAA9G,GACI,OAAOA,GAEX,SAAAnJ,GACI,IAAI4Q,EAAU,CAAE7H,SAAS,EAAOE,QAAS,MAErC4H,EAAkB7Q,EAAMkQ,OAC5B,GAAiB,gBAAdlQ,EAAM8Q,OAA6D,GAAnC9Q,EAAMiJ,QAAQ8H,QAAQ,YAAmBF,EAAgBG,OAGtF,GAAIhR,EAAMmJ,SACZ,GAA6B,KAAzBnJ,EAAMmJ,SAASvC,OAAe,CAC9B,IAAIuJ,EAAU,IAAIC,KACda,EAAc,IAAIb,KAAKA,KAAKxG,MAAM9H,OAAO6D,aAAasL,cAE1D,GAAInP,OAAO6D,aAAasL,aAAgBd,GAAWc,EAC/C,OAAQC,EAAa,CAACxI,MAAO5G,OAAO6D,aAAawL,QAAQhS,KAAK,SAACiS,GAC3D,GAAIA,EAAIrI,QAAS,CACbsI,aAAIzX,UAAU2P,SAAS,CACnBN,QAAS,wCACT3K,KAAM,YAGVyR,OAAM9J,OAAO,YAAamL,EAAIjI,SAAST,OAEvC,IAAIyH,EAAU,IAAIC,KACdkB,EAAa,IAAIlB,KAAKD,EAAQoB,WAAWpB,EAAQqB,aAAeJ,EAAIjI,SAASsI,aAKjF,OAJA1B,OAAM9J,OAAO,kBAAmBqL,GAEhCtR,EAAMkQ,OAAOwB,kBAAmB,EAChC1R,EAAMkQ,OAAOM,QAAQC,cAAgB,UAAYW,EAAIjI,SAAST,MACvDkH,IAAM5P,EAAMkQ,QAGnByB,MAKRA,SAK0B,KAAzB3R,EAAMmJ,SAASvC,OACrBgK,EAAQ3H,QAAU,YAGa,KAAzBjJ,EAAMmJ,SAASvC,OACpBgK,EAAQ3H,QAAU,kBACa,KAAzBjJ,EAAMmJ,SAASvC,OAErBgK,EAAQ3H,QAAU,aACY,KAAzBjJ,EAAMmJ,SAASvC,OAErBgK,EAAQ3H,QAAU,WACa,KAAzBjJ,EAAMmJ,SAASvC,OAErBgK,EAAQ3H,QAAU,gBACa,KAAzBjJ,EAAMmJ,SAASvC,OAErBgK,EAAQ3H,QAAU,uBAGjB2H,EAAQ3H,QAAU,UAAYjJ,EAAMmJ,SAASvC,YAGjDgK,EAAQ3H,QAAU,gBA3DlB2H,EAAQ3H,QAAU,QAClB4H,EAAgBG,QAAS,EAgE7B,OAJAK,aAAIzX,UAAU2P,SAAS,CACnBN,QAAS2H,EAAQ3H,QACjB3K,KAAM,UAEHsS,IAKR,IAAMgB,EAAYjC,EAGZkC,EAAe,SAAAC,GACxB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,0BAA2C,CAACmC,OAAQA,IAAS3S,KAAK,SAAAiS,GAAG,OAAIA,EAAIpY,QAE3E+Y,EAAmB,SAAAD,GAAY,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,UAA4BmC,GAAQ3S,KAAK,SAAAiS,GAAG,OAAIA,EAAIpY,QAE1FkY,EAAe,SAAAY,GACxB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,2BAA4C,CAACmC,OAAQA,IAAS3S,KAAK,SAAAiS,GAAG,OAAIA,EAAIpY,QAG5E0X,EAAkB,SAAAoB,GAE3B,IAAIG,EAAU,IAAI7B,KACd8B,EAAkBpQ,OAAO6D,aAAasL,YAAc,IAAIb,KAAKtO,OAAO6D,aAAasL,aAAe,IAAIb,MAAM,GAC1GC,EAAa,IAAID,KAAKA,KAAKxG,MAAM9H,OAAO6D,aAAawM,cAErDC,EAAa,EACbF,GAAmBD,GACnBC,EAAgBD,EAAQ5B,EAAa4B,EAAQ5B,EAC7C6B,EAAgBG,WAAWH,EAAgBI,aAAeF,GAC1DtQ,OAAO6D,aAAasL,YAAciB,GAElCpQ,OAAO6D,aAAasL,YAAc,IAAIb,MAAM,IAG7CuB,EAAU,SAAAG,GAEZ/B,OAAM9J,OAAO,YAAa,IAC1B8J,OAAM9J,OAAO,kBAAmB,IAChC8J,OAAM9J,OAAO,eAAgB,IAC7BnE,OAAO6D,aAAaC,WAAW,QAC/B9D,OAAO6D,aAAaC,WAAW,iBAI5BE,EAAOI,QACPgH,OAAuBqF,QAEvBzI,OAAOpF,QAAQ,CACXC,KAAM,SACN6N,MAAO,CAACC,SAAU3I,OAAO4I,aAAatL,aAMrCuL,EAAiB,SAAAb,GAC1B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,4BAA6C,CAACmC,OAAQA,IAAS3S,KAAK,SAAAiS,GAAG,OAAIA,EAAIpY,QAQnF,IAAM4Z,EAAU,SAAAC,GACnBjR,QAAQ2C,IAAI,eAIHuO,EAAkB,SAAAhB,GAC3B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,iBAAkC,CAACmC,OAAQA,KAEzCiB,EAAa,SAAAjB,GACtB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,oBAAwC,CAACmC,OAAQA,KAE/CmB,EAAW,SAAAnB,GACpB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,iBAAkCmC,IAEhCqB,EAAU,SAAArB,GACnB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,kBAAoCmC,IAElCsB,EAAkB,SAAAtB,GAC3B,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,2BAA+C,CAACmC,OAAQA,KAItDuB,EAAkB,SAAAvB,GAC3B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,iBAAkC,CAACmC,OAAQA,KAEzCwB,EAAa,SAAAxB,GACtB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,oBAAwC,CAACmC,OAAQA,KAE/CyB,EAAW,SAAAzB,GACpB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,iBAAkCmC,IAEhC0B,EAAU,SAAA1B,GACnB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,kBAAoCmC,IAIlC2B,EAAoB,SAAA3B,GAC7B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,mBAAoC,CAACmC,OAAQA,KAE3C4B,EAAe,SAAA5B,GACxB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,sBAA0C,CAACmC,OAAQA,KAEjD6B,EAAa,SAAA7B,GACtB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,mBAAoCmC,IAElC8B,EAAY,SAAA9B,GACrB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,oBAAsCmC,IAQpC+B,EAAyB,SAAA/B,GAClC,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,gCAAiD,CAACmC,OAAQA,KAExDgC,EAAmB,SAAAhC,GAC5B,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,0BAA8C,CAACmC,OAAQA,KAErDiC,EAAiB,SAAAjC,GAC1B,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,uBAAwCmC,IAEtCkC,EAAgB,SAAAlC,GACzB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,wBAA0CmC,IAExCmC,EAAoB,SAAAnC,GAC7B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,qCAAsD,CAACmC,OAAQA,KAE7DoC,EAAmB,SAAApC,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,2CAA4D,CAACmC,OAAQA,KAGnEqC,EAAoB,SAAArC,GAC7B,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,0BAA4CmC,IAE1CsC,EAAmB,SAAAtC,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,oCAAqD,CAACmC,OAAQA,IAAS3S,KAAK,SAAAiS,GAAG,OAAIA,EAAIpY,QAIrFqb,EAAiB,SAAAvC,GAC1B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,wBAAyC,CAACmC,OAAQA,KAEhDwC,EAAY,SAAAxC,GACrB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,2BAA+C,CAACmC,OAAQA,KAEtDyC,EAAU,SAAAzC,GACnB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,2BAA4CmC,IAE1C0C,EAAS,SAAA1C,GAClB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,yBAA2CmC,IAKzC2C,EAAkB,SAAA3C,GAC3B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,aAA8B,CAACmC,OAAQA,KAErC4C,EAAgB,SAAA5C,GACzB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,6BAA8C,CAACmC,OAAQA,KAErD6C,EAAW,SAAA7C,GACpB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,oBAAqCmC,IAEnC8C,EAAa,SAAA9C,GACtB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,oBAAwC,CAACmC,OAAQA,KAI/C+C,EAAU,SAAA/C,GACnB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,oBAAqC,CAACmC,OAAQA,KAE5CgD,EAA0B,SAAAhD,GACnC,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,wCAAyD,CAACmC,OAAQA,KAEhEiD,EAAqB,SAAAjD,GAC9B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,mCAAoD,CAACmC,OAAQA,KAE3DkD,EAAqB,SAAAlD,GAC9B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,mCAAoD,CAACmC,OAAQA,KAE3DmD,GAAgB,SAAAnD,GACzB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,uBAAwC,CAACmC,OAAQA,KAQ/CoD,GAAiB,SAAApD,GAC1B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,+BAAgD,CAACmC,OAAQA,KAKvDqD,GAAkB,SAAArD,GAC3B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,oBAAqC,CAACmC,OAAQA,KAE5CsD,GAAa,SAAAtD,GACtB,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,uBAA2C,CAACmC,OAAQA,KAElDuD,GAAW,SAAAvD,GACpB,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,oBAAqCmC,IAEnCwD,GAAU,SAAAxD,GACnB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,qBAAuCmC,IAGrCyD,GAAW,SAAAzD,GACpB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,yBAA0C,CAACmC,OAAQA,KAEjD0D,GAAU,SAAA1D,GACnB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,wBAAyC,CAACmC,OAAQA,KAEhD2D,GAAW,SAAA3D,GACpB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,yBAA0C,CAACmC,OAAQA,KAEjD4D,GAAW,SAAA5D,GACpB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,yBAA0C,CAACmC,OAAQA,KAEjD6D,GAAY,SAAA7D,GACrB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,0BAA2C,CAACmC,OAAQA,KAElD8D,GAAmB,SAAA9D,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,iCAAkD,CAACmC,OAAQA,KAIzD+D,GAAsB,SAAA/D,GAC/B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,2BAA4C,CAACmC,OAAQA,KAKnDgE,GAAmB,SAAAhE,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,yBAA0C,CAAEmC,OAAQA,KAElDiE,GAAsB,SAAAjE,GAC/B,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,4BAAgD,CAAEmC,OAAQA,KAExDkE,GAAyB,SAAAlE,GAClC,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,iCAAqD,CAAEmC,OAAQA,KAE7DmE,GAAmB,SAAAnE,GAC5B,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,0BAA4CmC,IAE1CoE,GAAsB,SAAApE,GAC/B,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,yBAA0CmC,IAExCqE,GAAoB,SAAArE,GAC7B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,2BAA4C,CAAEmC,OAAQA,KAEpDsE,GAAqB,SAAAtE,GAC9B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,4BAA6C,CAAEmC,OAAQA,KAErDuE,GAAgB,SAAAvE,GACzB,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,uBAAwC,CAAEmC,OAAQA,KAEhDwE,GAAmB,SAAAxE,GAC5B,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,0BAA2CmC,IAEzCyE,GAAmB,SAAAzE,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,0BAA2C,CAAEmC,OAAQA,KAEnD0E,GAAsB,SAAA1E,GAC/B,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,6BAAiD,CAAEmC,OAAQA,KAEzD2E,GAA2B,SAAA3E,GACpC,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,kCAAsD,CAAEmC,OAAQA,KAE9D4E,GAAqB,SAAA5E,GAC9B,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,2BAA6CmC,IAE3C6E,GAAsB,SAAA7E,GAC/B,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,0BAA2CmC,IAEzC8E,GAAmB,SAAA9E,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,0BAA2C,CAAEmC,OAAQA,KAEnD+E,GAAmB,SAAA/E,GAC5B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,2BAA4C,CAAEmC,OAAQA,KAEpDgF,GAAoB,SAAAhF,GAC7B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,sBAAuC,CAAEmC,OAAQA,KAE/CiF,GAAc,SAAAjF,GACvB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,0BAA4CmC,IAE1CkF,GAAc,SAAAlF,GACvB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,2BAA6CmC,IAO3CmF,GAAyB,SAAAnF,GAClC,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,gCAAiD,CAACmC,OAAQA,KAGxDoF,GAAmB,SAAApF,GAC5B,OAAOlC,IAAMoD,OAAN,GAAA9J,OAAgByG,EAAhB,0BAA8C,CAACmC,OAAQA,KAErDqF,GAAiB,SAAArF,GAC1B,OAAOlC,IAAMsD,IAAN,GAAAhK,OAAayG,EAAb,uBAAwCmC,IAEtCsF,GAAgB,SAAAtF,GACzB,OAAOlC,IAAMoC,KAAN,GAAA9I,OAAcyG,EAAd,wBAA0CmC,IAExCuF,GAAoB,SAAAvF,GAC7B,OAAOlC,IAAMhP,IAAN,GAAAsI,OAAayG,EAAb,qCAAsD,CAACmC,OAAQA,qJC3H1EwF,EAAA,WAAArH,IAAAsH,EAAAlS,GAWAlD,EAAA,MACA5B,KAAA,UACAvH,KAFA,WAGA,OACAwe,aAAA,EACAC,gBAAA,CACAC,YAAA,GACAC,gBAAA,EACAC,KAAA,GACAC,WAAA,GAEAC,WAAA,GACAC,OAAA,CACAC,OAAA,CACAC,MAAA,CACAC,OAAA,CACAC,MAAA,MAKAC,kBAAA,CACAC,QAAA,GACAC,KAAA,GACAC,MAAA,GAEAC,sBAAA,CACAC,QAAA,UACAC,UAAA,UAEAC,mBAAA,CACA3f,KAAA,CACA,CACAuH,KAAA,MACAjC,KAAA,OAEA,CACAiC,KAAA,MACAjC,KAAA,WAMA8F,QAAA,CACAwU,YADA,SACAC,GACA,OAAAA,GAAA,IAAAA,EAAA,GACA,UAEAA,EAAA,GACA,UAEA,IAEAC,OAVA,SAUAxR,GACA,yBAAA4B,OAAA,MAAA5B,EAAA,MAAA4B,OACA,MAAA5B,EADA,qCAIAyR,OAfA,WAgBAvU,KAAAC,QAAAC,QAAA,CACAC,KAAA,kBAIA6E,QAjEA,WAiEA,IAAA/D,EAAAjB,KACA2L,EAAA,IAAAC,KACA,GAAAtO,OAAA6D,aAAAwM,YAAA,CACA,IAAA9B,EAAA,IAAAD,UAAAxG,MAAA9H,OAAA6D,aAAAwM,cACAhC,GAAAE,IACAvK,EAAAI,QACA8S,EAAA,KAAAzG,QAEA/N,KAAAC,QAAA/K,KAAA,gBAIAoM,EAAAI,QACA8S,EAAA,KAAAzG,QAEA/N,KAAAC,QAAA/K,KAAA,UAIAoM,EAAAI,SACAvM,OAAAsf,EAAA,KAAAtf,CAAA,IAAAwF,KAAA,SAAAiS,GACA3L,EAAA2S,kBAAAhH,EAAApY,KAAAmQ,WAIAxP,OAAAsf,EAAA,KAAAtf,CAAA,IAAAwF,KAAA,SAAAiS,GACA3L,EAAAqS,WAAA1G,EAAApY,KAAAmQ,WAGAxP,OAAAsf,EAAA,KAAAtf,CAAA,IAAAwF,KAAA,SAAAiS,GACA3L,EAAAgS,gBAAArG,EAAApY,KAAAmQ,8HCxZI+P,EAAM,WAAgB,IAAA9L,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBqB,MAAA,CAAOE,GAAA,QAAY,CAAA1B,EAAAjG,OAAAe,KAAAiR,WAA04H5L,EAAA,cAA2DqB,MAAA,CAAOrO,KAAA,OAAAW,KAAA,WAA+B,CAAAqM,EAAA,OAAAA,EAAA,qBAA3+HA,EAAA,cAAiDqB,MAAA,CAAOrO,KAAA,OAAAW,KAAA,WAA+B,CAAAqM,EAAA,UAAeE,YAAA,aAAwB,CAAAF,EAAA,UAAeE,YAAA,SAAAmB,MAAA,CAA4BwK,KAAA,KAAW,CAAA7L,EAAA,UAAeE,YAAA,qBAAA4L,MAAAjM,EAAAtK,UAAA,mCAAA8L,MAAA,CAA+FwK,KAAA,KAAW,CAAA7L,EAAA,OAAY+L,GAAA,CAAIC,MAAAnM,EAAAtI,UAAqB,CAAAsI,EAAAoM,GAAA,IAAApM,EAAAqM,GAAArM,EAAAtK,UAAAsK,EAAAxK,aAAAwK,EAAAzK,cAAA4K,EAAA,UAAkFE,YAAA,UAAAmB,MAAA,CAA6BwK,KAAA,KAAW,CAAA7L,EAAA,OAAY8L,MAAAjM,EAAAtK,UAAA,0BAAAwW,GAAA,CAAkDC,MAAAnM,EAAA5G,WAAsB,CAAA+G,EAAA,KAAUE,YAAA,0BAAkCF,EAAA,iBAAwBE,YAAA,iCAAAmB,MAAA,CAAoD8K,UAAA,MAAiBtM,EAAAuM,GAAAvM,EAAAjG,OAAA,iBAAAK,GAA4C,OAAA+F,EAAA,sBAAgCjM,IAAAkG,EAAA7C,MAAc,CAAA4I,EAAA,UAAc,CAAAH,EAAAoM,GAAA,IAAApM,EAAAqM,GAAAjS,EAAAjH,aAAoC,OAAAgN,EAAA,UAAsBE,YAAA,WAAAmB,MAAA,CAA8BwK,KAAA,IAAU,CAAA7L,EAAA,eAAoBqB,MAAA,CAAOgL,QAAA,UAAmB,CAAArM,EAAA,QAAaE,YAAA,mCAA8C,CAAAL,EAAAoM,GAAA,yBAAApM,EAAAqM,GAAArM,EAAAlK,aAAA,0BAAAqK,EAAA,OAA8FqB,MAAA,CAAOhQ,IAAMjE,EAAQ,QAAmBgT,OAAA,MAAAjH,MAAA,WAAgC6G,EAAA,oBAA2BqB,MAAA,CAAOiL,KAAA,YAAkBA,KAAA,YAAiB,CAAAtM,EAAA,oBAAyBuM,SAAA,CAAUP,MAAA,SAAAQ,GAAyB,OAAA3M,EAAAnI,OAAA8U,MAA4B,CAAAxM,EAAA,YAAiBE,YAAA,OAAAmB,MAAA,CAA0B5N,MAAA,EAAA1C,KAAA,YAA4B,CAAA8O,EAAAoM,GAAA,sFAAAjM,EAAA,oBAAsHuM,SAAA,CAAUP,MAAA,SAAAQ,GAAyB,OAAA3M,EAAA/G,QAAA0T,MAA6B,CAAA3M,EAAAoM,GAAA,QAAAjM,EAAA,oBAAwCuM,SAAA,CAAUP,MAAA,SAAAQ,GAAyB,OAAA3M,EAAA9G,SAAAyT,MAA8B,CAAA3M,EAAAoM,GAAA,YAAAjM,EAAA,oBAA4CqB,MAAA,CAAOoL,QAAA,IAAaF,SAAA,CAAWP,MAAA,SAAAQ,GAAyB,OAAA3M,EAAA7H,OAAAwU,MAA4B,CAAA3M,EAAAoM,GAAA,0BAAAjM,EAAA,UAAgDE,YAAA,OAAAmB,MAAA,CAA0BwK,KAAA,KAAW,CAAA7L,EAAA,SAAc8L,MAAAjM,EAAAnK,gBAAyB,CAAAsK,EAAA,gBAAqBE,YAAA,mBAAAC,YAAA,CAA4CC,OAAA,OAAAsM,WAAA,YAAwC,CAAA1M,EAAA,WAAgBE,YAAA,wBAAAC,YAAA,CAAiDwM,eAAA,QAAsBtL,MAAA,CAAQuL,iBAAA/M,EAAAjG,OAAAxC,KAAAyV,gBAAA,GAAAtQ,OAAA,GAAAtD,SAAA4G,EAAAtK,UAAAuX,mBAAA,UAAAC,aAAA,OAAAC,oBAAA,WAAwKjB,GAAA,CAAK/S,KAAA6G,EAAAlI,WAAAsV,MAAApN,EAAAjI,YAAAsV,OAAArN,EAAAhI,eAAyEgI,EAAAuM,GAAAvM,EAAA,gBAAAsN,EAAApT,GAA0C,OAAAiG,EAAA,WAAqBjM,IAAAgG,EAAAsH,MAAA,CAAiBpH,KAAAkT,OAAe,WAAAnN,EAAA,UAA0BE,YAAA,kBAAA4L,MAAAjM,EAAAtK,UAAA,uCAAA8L,MAAA,CAAgGwK,KAAA,KAAW,CAAAhM,EAAA,SAAAG,EAAA,OAA2BE,YAAA,QAAmB,CAAAF,EAAA,OAAYE,YAAA,sBAAAmB,MAAA,CAAyCE,GAAA,wBAA4B,CAAAvB,EAAA,eAAoBoN,IAAA,aAAAlN,YAAA,qBAAiDL,EAAAuM,GAAAvM,EAAA,kBAAArP,EAAAuJ,GAA2C,OAAAiG,EAAA,eAAyBjM,IAAAvD,EAAA4G,KAAAgW,IAAA,MAAAC,UAAA,EAAAnN,YAAA,iBAAA4L,MAAA,CAAyEwB,OAAAzN,EAAAlG,SAAAnJ,EAAA4G,OAAiCiK,MAAA,CAAQnD,GAAA,CAAM9G,KAAA5G,EAAA4G,KAAA6N,MAAAzU,EAAAyU,MAAApL,SAAArJ,EAAAqJ,UAA2DrJ,IAAA,QAAc+b,SAAA,CAAWgB,QAAA,SAAAf,GAA2B,cAAAA,GAAA,IAAAA,EAAAgB,OAA8C,YAAe3N,EAAA/F,UAAAC,MAAuB,CAAA8F,EAAAoM,GAAA,yCAAApM,EAAAqM,GAAA1b,EAAAuK,OAAA,0CAAAiF,EAAA,QAAyHE,YAAA,gBAAA6L,GAAA,CAAgCC,MAAA,SAAAQ,GAAyBA,EAAAiB,iBAAwBjB,EAAAkB,kBAAyB7N,EAAA/F,UAAAC,WAA2B,OAAAiG,EAAA,OAAmBE,YAAA,kBAA6B,CAAAF,EAAA,eAAoB+L,GAAA,CAAI9Q,QAAA4E,EAAA7E,aAA0B,CAAAgF,EAAA,aAAkBqB,MAAA,CAAOsM,KAAA,SAAe,CAAA3N,EAAA,KAAUE,YAAA,wCAAgDF,EAAA,oBAA2BqB,MAAA,CAAOiL,KAAA,WAAAqB,KAAA,SAAiCrB,KAAA,YAAiB,CAAAtM,EAAA,oBAAyBqB,MAAA,CAAOpG,QAAA,UAAmB,CAAA4E,EAAAoM,GAAA,UAAAjM,EAAA,oBAA0CqB,MAAA,CAAOpG,QAAA,QAAiB,CAAA4E,EAAAoM,GAAA,wBAAApM,EAAAQ,KAAAL,EAAA,cAA2DqB,MAAA,CAAOrO,KAAA,OAAAW,KAAA,WAA+B,CAAAqM,EAAA,OAAYE,YAAA,iCAA4C,CAAAF,EAAA,qCAAiGA,EAAA,aAAoD8L,MAAAjM,EAAAjK,cAAAyL,MAAA,CAA+BtG,MAAA,kBAAAvE,QAAAqJ,EAAAvK,YAAAsY,wBAAA,GAAiF7B,GAAA,CAAK8B,iBAAA,SAAArB,GAAkC3M,EAAAvK,YAAAkX,IAAwBsB,MAAA,CAAQra,MAAAoM,EAAA,YAAAkO,SAAA,SAAAC,GAAiDnO,EAAAvK,YAAA0Y,GAAoBC,WAAA,gBAA2B,CAAAjO,EAAA,MAAAH,EAAAuM,GAAAvM,EAAA,iBAAArP,GAA8C,OAAAwP,EAAA,UAAoBjM,IAAAvD,EAAAwC,KAAAkN,YAAA,UAAAmB,MAAA,CAA0C6M,SAAA,GAAAnd,KAAAP,EAAAO,OAA+B,CAAA8O,EAAAoM,GAAA,qBAAApM,EAAAqM,GAAA1b,EAAAwC,MAAA,sBAAmE,KAAAgN,EAAA,OAAiBmO,WAAA,EAAanb,KAAA,OAAAob,QAAA,SAAA3a,MAAAoM,EAAA,eAAAoO,WAAA,mBAAoF/N,YAAA,WAAAC,YAAA,CAAsCkO,UAAA,QAAiBhN,MAAA,CAAQiN,SAAA,KAAevC,GAAA,CAAKC,MAAAnM,EAAA5G,aAAsB,IAC51JsH,EAAA,eCD8TgO,EAAAC,EAAA,qECY9ThO,EAAgBpU,OAAAqU,EAAA,KAAArU,CACdmiB,EACA5C,EACApL,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,UACe,IAAA8N,EAAAjO,oKCxBAkO,EAAA,CACbhU,MAAO,CACLiU,UAAW,YACXC,aAAc,eACdC,cAAe,gBACfC,MAAO,QACPC,WAAY,aACZC,eAAgB,kBAChBC,eAAgB,kBAChBC,oBAAqB,uBACrBC,MAAO,QACPta,WAAY,aACZua,eAAgB,eAChBC,QAAS,UACTC,SAAU,WACVC,WAAY,cACZC,QAAS,WACTC,UAAW,YACXC,aAAc,gBACdC,SAAU,WACVC,OAAQ,SACRC,QAAS,UACTC,eAAgB,QAChBC,UAAW,YACXC,WAAY,cACZC,WAAY,cACZC,WAAY,cACZC,OAAQ,SACRC,cAAe,iBACfC,UAAW,aACXC,SAAU,YACVC,QAAS,UACTC,OAAQ,gBACRC,MAAO,SACPC,UAAW,WACXC,UAAW,WACXC,YAAa,aACbC,YAAa,aACbC,UAAW,WACXC,MAAO,SACPC,MAAO,QACPC,aAAc,gBACdC,UAAW,aACXC,gBAAiB,cACjBC,aAAc,gBACdC,UAAW,aACXC,gBAAiB,mBACjBC,IAAK,MACLxb,KAAM,OACNyb,cAAe,iBACfC,YAAa,eACbC,YAAa,eACbC,WAAY,cACZC,QAAS,MACTC,QAAS,MACTC,SAAU,YACVC,MAAO,QACPC,YAAa,eACbC,YAAa,kBACbC,YAAa,eACbC,YAAa,eACbC,IAAK,MACLC,IAAK,MACLC,UAAW,aACXC,MAAO,QACPC,cAAe,YACfC,KAAM,OACNC,aAAc,iBAEhBC,OAAQ,CACNC,OAAQ,UACRjE,UAAW,YACXkE,OAAQ,SACRN,MAAO,QACP5E,KAAM,eAER3I,MAAO,CACLjK,MAAO,aACP+X,MAAO,SACPC,SAAU,WACVC,SAAU,WACVC,IAAK,MACLC,WAAY,kBACZC,eAAgB,qFAElBtE,cAAe,CACbA,cAAe,gBACfgE,OAAQ,qBAEV9D,WAAY,CACV9I,QAAS,WACTO,eAAgB,kBAChB4M,MAAO,aACPC,YAAa,eACbC,KAAM,6MACN7N,OAAQ,SACR8N,QAAS,UACTC,OAAQ,UAEV1E,MAAO,CACL2E,YAAa,gKACbjG,OAAQ,cAEV3Y,WAAY,CACVga,cAAe,gBACf6E,YAAa,yWACbC,aAAc,oNACdC,WAAY,8EACZC,eAAgB,gHAChBC,eAAgB,uLAChBC,gBAAiB,6LAEnBC,MAAO,CACLC,aAAc,uCACdC,aAAc,0CACdC,UAAW,oBACXC,UAAW,2BACXrZ,MAAO,QACPsZ,WAAY,MACZtjB,KAAM,OACNujB,OAAQ,SACRC,OAAQ,SACRC,IAAK,MACLC,OAAQ,SACRC,SAAU,WACVnT,GAAI,KACJoT,KAAM,OACNC,OAAQ,SACRC,SAAU,WACVxb,OAAQ,SACRyb,QAAS,UACTC,KAAM,OACNC,QAAS,UACTC,MAAO,QACPxP,OAAQ,SACR+N,OAAQ,SACRD,QAAS,WAEXzB,SAAU,CACRwB,KAAM,sDACNG,YAAa,gUACb5E,cAAe,yBAEjBkD,MAAO,CACL0C,OAAQ,SACRS,eAAgB,wBAChBC,YAAa,kDAEf/C,IAAK,CACHqC,OAAQ,SACRU,YAAa,4CAEf9C,IAAK,CACHiB,KAAM,2EAERf,MAAO,CACL6C,OAAQ,eACRvG,cAAe,sBACfyE,KAAM,iLAER+B,SAAU,CACRC,QAAS,UACTrI,MAAO,QACPsI,YAAa,eACbpb,SAAU,cCpKCqb,EAAA,CACb9a,MAAO,CACLiU,UAAW,KACXC,aAAc,KACdC,cAAe,KACfC,MAAO,MACPC,WAAY,QACZE,eAAgB,OAChBD,eAAgB,OAChBE,oBAAqB,OACrBC,MAAO,KACPta,WAAY,KACZua,eAAgB,KAChBC,QAAS,SACTC,SAAU,WACVC,WAAY,UACZC,QAAS,OACTC,UAAW,YACXC,aAAc,OACdC,SAAU,WACVC,OAAQ,SACRC,QAAS,UACTC,eAAgB,MAChBC,UAAW,OACXC,WAAY,YACZC,WAAY,YACZC,WAAY,QACZC,OAAQ,KACRC,cAAe,OACfC,UAAW,MACXC,SAAU,OACVC,QAAS,OACTC,OAAQ,OACRC,MAAO,MACPC,UAAW,QACXC,UAAW,QACXC,YAAa,UACbC,YAAa,UACbC,UAAW,QACXC,MAAO,MACPC,MAAO,QACPC,aAAc,UACdC,UAAW,UACXC,gBAAiB,WACjBC,aAAc,UACdC,UAAW,OACXC,gBAAiB,QACjBC,IAAK,MACLxb,KAAM,KACNyb,cAAe,OACfC,YAAa,OACbC,YAAa,OACbC,WAAY,OACZC,QAAS,MACTC,QAAS,MACTC,SAAU,OACVC,MAAO,QACPC,YAAa,WACbC,YAAa,UACbC,YAAa,UACbC,YAAa,WACbC,IAAK,MACLC,IAAK,MACLC,UAAW,aACXC,MAAO,KACPC,cAAe,YACfC,KAAM,MACNC,aAAc,MAEhBC,OAAQ,CACNC,OAAQ,OACRjE,UAAW,KACXkE,OAAQ,OACRN,MAAO,KACP5E,KAAM,QAER3I,MAAO,CACLjK,MAAO,OACP+X,MAAO,KACPC,SAAU,KACVC,SAAU,KACVC,IAAK,MACLC,WAAY,QACZC,eAAgB,yBAElBtE,cAAe,CACbA,cAAe,KACfgE,OAAQ,aAEV9D,WAAY,CACV9I,QAAS,OACTO,eAAgB,OAChB4M,MAAO,OACPC,YAAa,OACbC,KAAM,yGACN7N,OAAQ,KACR8N,QAAS,KACTC,OAAQ,MAEV1E,MAAO,CACL2E,YAAa,+CACbjG,OAAQ,QAEV3Y,WAAY,CACVga,cAAe,KACf6E,YAAa,qGACbC,aAAc,gFACdC,WAAY,oBACZC,eAAgB,yBAChBC,eAAgB,oEAChBC,gBAAiB,kEAEnBC,MAAO,CACLC,aAAc,iBACdC,aAAc,kBACdC,UAAW,OACXC,UAAW,QACXrZ,MAAO,KACPsZ,WAAY,MACZtjB,KAAM,KACNujB,OAAQ,KACRC,OAAQ,KACRC,IAAK,KACLC,OAAQ,KACRC,SAAU,MACVnT,GAAI,KACJoT,KAAM,KACNC,OAAQ,KACRC,SAAU,MACVxb,OAAQ,KACRyb,QAAS,KACTC,KAAM,KACNC,QAAS,KACTC,MAAO,KACPxP,OAAQ,KACR+N,OAAQ,MACRD,QAAS,OAEXzB,SAAU,CACRwB,KAAM,eACNG,YAAa,+GACb5E,cAAe,QAEjBkD,MAAO,CACL0C,OAAQ,KACRS,eAAgB,SAChBC,YAAa,wBAEf/C,IAAK,CACHqC,OAAQ,KACRU,YAAa,kBAEf9C,IAAK,CACHiB,KAAM,qCAERf,MAAO,CACL6C,OAAQ,KACRvG,cAAe,OACfyE,KAAM,mEAER+B,SAAU,CACRC,QAAS,KACTrI,MAAO,KACPsI,YAAa,OACbpb,SAAU,SCpKCsb,EAAA,CACb/a,MAAO,CACLiU,UAAW,mBACXC,aAAc,eACdC,cAAe,gBACfC,MAAO,OACPC,WAAY,WACZE,eAAgB,kBAChBD,eAAgB,wBAChBE,oBAAqB,2BACrBC,MAAO,SACPta,WAAY,cACZua,eAAgB,eAChBC,QAAS,UACTC,SAAU,WACVC,WAAY,cACZC,QAAS,YACTC,UAAW,iBACXC,aAAc,eACdC,SAAU,iBACVC,OAAQ,SACRC,QAAS,UACTC,eAAgB,QAChBC,UAAW,YACXC,WAAY,cACZC,WAAY,cACZC,WAAY,cACZC,OAAQ,WACRC,cAAe,iBACfC,UAAW,oBACXC,SAAU,YACVC,QAAS,UACTC,OAAQ,kBACRC,MAAO,SACPC,UAAW,WACXC,UAAW,WACXC,YAAa,aACbC,YAAa,aACbC,UAAW,WACXC,MAAO,SACPC,MAAO,QACPC,aAAc,iBACdC,UAAW,kBACXC,gBAAiB,SACjBC,aAAc,gBACdC,UAAW,aACXC,gBAAiB,mBACjBC,IAAK,UACLxb,KAAM,aACNyb,cAAe,iBACfC,YAAa,kBACbC,YAAa,uBACbC,WAAY,mBACZC,QAAS,MACTC,QAAS,MACTC,SAAU,sBACVC,MAAO,QACPC,YAAa,mBACbC,YAAa,sBACbC,YAAa,eACbC,YAAa,cACbC,IAAK,MACLC,IAAK,MACLC,UAAW,iBACXC,MAAO,OACPC,cAAe,YACfC,KAAM,OACNC,aAAc,kBAEhBC,OAAQ,CACNC,OAAQ,QACRjE,UAAW,mBACXkE,OAAQ,SACRN,MAAO,OACP5E,KAAM,iBAER3I,MAAO,CACLjK,MAAO,uBACP+X,MAAO,SACPC,SAAU,UACVC,SAAU,aACVC,IAAK,OACLC,WAAY,eACZC,eAAgB,uFAElBtE,cAAe,CACbA,cAAe,gBACfgE,OAAQ,sBAEV9D,WAAY,CACV9I,QAAS,YACTO,eAAgB,qBAChB4M,MAAO,eACPC,YAAa,mBACbC,KAAM,6MACN7N,OAAQ,SACR8N,QAAS,YACTC,OAAQ,YAEV1E,MAAO,CACL2E,YAAa,gKACbjG,OAAQ,YAEV3Y,WAAY,CACVga,cAAe,gBACf6E,YAAa,yWACbC,aAAc,oNACdC,WAAY,8EACZC,eAAgB,gHAChBC,eAAgB,uLAChBC,gBAAiB,6LAEnBC,MAAO,CACLC,aAAc,uCACdC,aAAc,0CACdC,UAAW,oBACXC,UAAW,2BACXrZ,MAAO,SACPsZ,WAAY,cACZtjB,KAAM,OACNujB,OAAQ,SACRC,OAAQ,SACRC,IAAK,SACLC,OAAQ,WACRC,SAAU,WACVnT,GAAI,KACJoT,KAAM,QACNC,OAAQ,QACRC,SAAU,SACVxb,OAAQ,SACRyb,QAAS,WACTC,KAAM,SACNC,QAAS,WACTC,MAAO,QACPxP,OAAQ,WACR+N,OAAQ,WACRD,QAAS,aAEXzB,SAAU,CACRwB,KAAM,sDACNG,YAAa,gUACb5E,cAAe,6BAEjBkD,MAAO,CACL0C,OAAQ,WACRS,eAAgB,yBAChBC,YAAa,0CAEf/C,IAAK,CACHqC,OAAQ,WACRU,YAAa,0CAEf9C,IAAK,CACHiB,KAAM,2EAERf,MAAO,CACL6C,OAAQ,eACRvG,cAAe,yBACfyE,KAAM,iLAER+B,SAAU,CACRC,QAAS,aACTrI,MAAO,SACPsI,YAAa,eACbpb,SAAU,iBC1Jd2J,aAAIpB,IAAIgT,QAER,IAAMC,EAAW,CACfC,GAAIxpB,OAAAypB,EAAA,KAAAzpB,CAAA,GACC0pB,EACAC,KAELP,GAAIppB,OAAAypB,EAAA,KAAAzpB,CAAA,GACC4pB,EACAC,KAELC,GAAI9pB,OAAAypB,EAAA,KAAAzpB,CAAA,GACC+pB,EACAC,MAID3D,EAAO,IAAIiD,OAAQ,CAGvBW,OAAQC,IAAQjjB,IAAI,aAAe,KAEnCsiB,aAGalD,6CCtBf3O,aAAIpB,IAAI6T,IAAW,CACf5I,KAAM2I,IAAQjjB,IAAI,SAAW,SAC7Bof,KAAM,SAAC1e,EAAKN,GAAN,OAAgBgf,EAAK/e,EAAEK,EAAKN,MAStC8I,OAAOia,WAAW,SAACtY,EAAIf,EAAMK,GAErBU,EAAGvD,KAAKI,QACRzK,SAASyK,MAAQmD,EAAGvD,KAAKI,OAE7ByC,MAGJsG,aAAInB,OAAO8T,eAAgB,EAE3B,IAAI3S,aAAI,CACJvH,cACAiG,aACAiQ,OACA7S,OAAQ,SAAA8W,GAAC,OAAIA,EAAEjI,MAChBkI,OAAO,6CCvCV,IAAA/W,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAH,EAAA5F,KAAA,cAAA4F,EAAA5F,KAAA7C,MAAA,KAAAyI,EAAA5F,KAAA7C,MAAA,KAAAyI,EAAA5F,KAAA7C,MAAAyI,EAAA5F,KAAA2c,SAA4hC,CAAA/W,EAAA5F,KAAA2c,SAAqW/W,EAAAQ,KAArWL,EAAA,YAA2CjM,IAAA8L,EAAA5F,KAAA7C,KAAA,IAAAiK,MAAA,CAA6BnD,GAAA2B,EAAA5F,KAAA7C,OAAoB,CAAA4I,EAAA,gBAAqBjM,IAAA8L,EAAA5F,KAAA7C,KAAA,IAAAiK,MAAA,CAA6BtH,MAAA8F,EAAAgX,eAAAhX,EAAA5F,KAAA7C,MAAA,GAAAyI,EAAA5F,KAAA7C,OAA6D,CAAA4I,EAAA,KAAUE,YAAA,KAAA4L,MAAAjM,EAAA5F,KAAA6c,UAAwC9W,EAAA,YAAiBsM,KAAA,SAAa,CAAAtM,EAAA,QAAaE,YAAA,aAAAmB,MAAA,CAAgCiL,KAAA,SAAeA,KAAA,SAAc,CAAAzM,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAA5F,KAAAjH,YAAA,QAAj4CgN,EAAA,cAA0IjM,IAAA8L,EAAA5F,KAAA7C,KAAAiK,MAAA,CAAyBtH,MAAA8F,EAAA5F,KAAAsH,GAAA,UAA6B,CAAAvB,EAAA,YAAiBsM,KAAA,SAAa,CAAAzM,EAAA5F,KAAA8c,UAAAlX,EAAA5F,KAAA8c,SAAA9qB,OAAA,GAAA4T,EAAA5F,KAAA6c,UAAAjX,EAAA5F,KAAA2c,SAAA5W,EAAA,KAAgGE,YAAA,KAAA4L,MAAAjM,EAAA5F,KAAA6c,UAAwCjX,EAAAQ,KAAAL,EAAA,QAAsBE,YAAA,aAAAmB,MAAA,CAAgCiL,KAAA,SAAeA,KAAA,SAAc,CAAAzM,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAA5F,KAAAjH,WAAA6M,EAAAuM,GAAAvM,EAAA5F,KAAA,kBAAA+c,GAA+E,OAAAA,EAAAC,QAAApX,EAAA5F,KAAA2c,SAAge/W,EAAAQ,KAAhe,CAAA2W,EAAAD,UAAAC,EAAAD,SAAA9qB,OAAA,EAAA+T,EAAA,WAAqGjM,IAAAijB,EAAA5f,KAAAiK,MAAA,CAAsBpH,KAAA+c,EAAAjd,MAAAid,EAAAzV,MAA+BvB,EAAA,YAAiBjM,IAAAijB,EAAA5f,KAAAiK,MAAA,CAAsBnD,GAAA8Y,EAAA5f,OAAiB,CAAA4I,EAAA,gBAAqBjM,IAAAijB,EAAA5f,KAAAiK,MAAA,CAAsBtH,MAAA8F,EAAAgX,eAAAG,EAAA5f,MAAA,GAAA4f,EAAA5f,MAAsD2U,GAAA,CAAKC,MAAAnM,EAAAqX,MAAiB,CAAAlX,EAAA,KAAUE,YAAA,KAAA4L,MAAAkL,EAAAF,UAAqC9W,EAAA,YAAiBsM,KAAA,SAAa,CAAAtM,EAAA,QAAaE,YAAA,aAAAmB,MAAA,CAAgCiL,KAAA,SAAeA,KAAA,SAAc,CAAAzM,EAAAoM,GAAApM,EAAAqM,GAAA8K,EAAAhkB,YAAA,YAAoD,IAAqW,CAAAgN,EAAA,YAAuEjM,IAAA8L,EAAA5F,KAAA7C,KAAA,IAAAiK,MAAA,CAA6BnD,GAAA2B,EAAA5F,KAAA7C,OAAoB,CAAA4I,EAAA,gBAAqBjM,IAAA8L,EAAA5F,KAAA7C,KAAA,IAAAiK,MAAA,CAA6BtH,MAAA8F,EAAAgX,eAAAhX,EAAA5F,KAAA7C,MAAA,GAAAyI,EAAA5F,KAAA7C,MAA4D2U,GAAA,CAAKC,MAAAnM,EAAAqX,MAAiB,CAAAlX,EAAA,KAAUE,YAAA,KAAA4L,MAAAjM,EAAA5F,KAAA6c,UAAwC9W,EAAA,YAAiBsM,KAAA,SAAa,CAAAtM,EAAA,QAAaE,YAAA,aAAAmB,MAAA,CAAgCiL,KAAA,SAAeA,KAAA,SAAc,CAAAzM,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAA5F,KAAAjH,YAAA,aACh3DuN,EAAA,GCDI4W,iCAAM,WAAgB,IAAAtX,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAAH,EAAA9O,KAAA8O,EAAAuX,GAAA,CAA2B5mB,IAAA,aAAgB,YAAAqP,EAAAwX,UAAAxX,EAAA3B,KAAA,IAAA2B,EAAAyX,GAAA,iBAChIC,EAAe,uBCAZ,SAASC,EAAWpgB,GACzB,MAAO,0BAA0BqgB,KAAKrgB,GCOxC,IAAAsgB,EAAA,CACAC,MAAA,CACAzZ,GAAA,CACAnN,KAAA6mB,OACAC,UAAA,IAGAnb,SAAA,CACA8a,WADA,WAEA,OAAAM,EAAA7gB,KAAAiH,KAEAnN,KAJA,WAKA,OAAAkG,KAAAugB,WACA,IAEA,gBAGA3gB,QAAA,CACAwgB,UADA,SACAnZ,GACA,OAAAjH,KAAAugB,WACA,CACArnB,KAAA+N,EACA9M,OAAA,SACAqI,MAAA,eAGA,CACAyE,SCrCiV6Z,EAAA,cCOjVvX,EAAgBpU,OAAAqU,EAAA,KAAArU,CACd2rB,EACAZ,EACAI,GACF,EACA,KACA,KACA,MAIA/W,EAAAE,QAAAC,OAAA,cACe,IAAAqX,EAAAxX,UCuDfyX,EAAA,CACAjlB,KAAA,UACA6B,WAAA,CAAAmjB,WACAL,MAAA,CACA1d,KAAA,CACAlJ,KAAA3E,OACAyrB,UAAA,IAGAhhB,QAAA,CACAggB,eADA,SACA3Y,GACA,OAAA4Z,EAAA5Z,IAEAgZ,IAAA,WAEAjgB,KAAAihB,MAAA,oBCzFiVC,EAAA,ECO7UC,EAAYhsB,OAAAqU,EAAA,KAAArU,CACd+rB,EACAvY,EACAW,GACF,EACA,KACA,KACA,MAIA6X,EAAS1X,QAAAC,OAAA,cACM/L,EAAA,KAAAwjB,kCCnBf,IAAAC,EAAA,CACAC,kBAAA,CACA,OACA,kBAEAC,kBAAA,CACA,QAEAC,qBAAA,CACA,QAEAC,oBAAA,CACA,OACA,kBAEAC,yBAAA,CACA,OACA,kBAEAC,0BAAA,CACA,OACA,iBACA,kBAEAC,oCAAA,CACA,OACA,kBAEAC,0BAAA,CACA,OACA,kBAEAC,wBAAA,CACA,OACA,kBAEAC,yBAAA,CACA,OACA,kBAEAC,4BAAA,CACA,SAEAC,oBAAA,CACA,QAEAC,gCAAA,CACA,QAEAC,yBAAA,CACA,OACA,iBACA,kBAEAC,gCAAA,CACA,OACA,kBAEAC,gCAAA,CACA,OACA,kBAEAC,oCAAA,CACA,OACA,kBAEAC,mDAAA,CACA,QACA,kBAEAC,mDAAA,CACA,OACA,kBAEAC,wCAAA,CACA,OACA,kBAEAC,wBAAA,CACA,OACA,kBAEAC,6BAAA,CACA,OACA,kBAEAC,+BAAA,CACA,OACA,kBAEAC,+BAAA,CACA,OACA,kBAEAC,yBAAA,CACA,OACA,kBAEAC,yBAAA,CACA,OACA,kBAEAC,yBAAA,CACA,OACA,kBAEAC,8BAAA,CACA,QACA,kBAEAC,6BAAA,CACA,OACA,kBAEAC,6BAAA,CACA,OACA,kBAEAC,0BAAA,CACA,OACA,kBAEAC,6BAAA,CACA,OACA,kBAEAC,iCAAA,CACA,OACA,kBAEAC,6BAAA,CACA,OACA,kBAEAC,8BAAA,CACA,OACA,kBAEAC,sBAAA,CACA,SAGA,SAAAC,EAAAC,GACA,IAAAC,EAAAvC,EAAAsC,GACA,OAAAC,EAOA5qB,QAAA4C,IAAAgoB,EAAAnmB,MAAA,GAAA4jB,IAAAjrB,EAAAyC,IAAA+B,KAAA,WACA,IAAA2P,EAAAqZ,EAAA,GACA,OAAAxtB,EAAAmU,KARAvR,QAAAC,UAAA2B,KAAA,WACA,IAAA/B,EAAA,IAAA0B,MAAA,uBAAAopB,EAAA,KAEA,MADA9qB,EAAA0T,KAAA,mBACA1T,IAQA6qB,EAAAG,KAAA,WACA,OAAAzuB,OAAAyuB,KAAAxC,IAEAqC,EAAAnZ,GAAA,OACA5R,EAAAD,QAAAgrB,8DChKA,IAAAI,EAAA1tB,EAAA,QAAA2tB,EAAA3tB,EAAA6G,EAAA6mB,GAAqbC,EAAG,0BCCxb,IACIprB,EAAOD,QAAU,SAAAsrB,GAAI,OAAI,kBAAM5tB,EAAA,OAAAA,CAAO,UAAY4tB,EAAO,UAC3D,MAAOvoB,GAIL4B,QAAQ4mB,KAAK,uJAAwJ,oDCPzK,IAAAC,EAAA9tB,EAAA,QAAA+tB,EAAA/tB,EAAA6G,EAAAinB,GAAmeC,EAAG,kFCAte5iB,EAAOC,UAAY,GAEnBD,EAAOI,SAAU,qECFjB,IAAAiH,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BF,EAAAI,MAAAD,GAAwB,OAAAH,EAAAuB,GAAA,IACzFb,EAAA,YAAoC,IAAAV,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAA,EAAA,OAA2BqB,MAAA,CAAO+Z,QAAA,KAAAC,OAAA,MAA6B,CAAArb,EAAA,MAAAA,EAAA,MAAAH,EAAAoM,GAAA,sBAAAjM,EAAA,MAAAH,EAAAoM,GAAA,0LCuBlKqP,EAAA,CACAtoB,KAAA,qBACAvH,KAFA,WAGA,UAEAiT,QALA,eAAAuC,EAAA7U,OAAAmvB,EAAA,KAAAnvB,CAAA2Q,mBAAAC,KAAA,SAAAC,IAAA,IAAA/E,EAAA6D,EAAA6G,EAAAmB,EAAA,OAAAhH,mBAAAM,KAAA,SAAAC,GAAA,eAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAC,KAAA,EAAAD,EAAAE,KAAA,EAOAge,EAAA,KAAAC,yBAPA,cAQAvjB,EAAAjB,KARAqG,EAAAE,KAAA,EASAge,EAAA,KAAAhc,UATA,OASAzD,EATAuB,EAAAyD,KAUA7I,EAAAO,OAAAC,OAAA,YAAAqD,EAAA2f,cAEA9Y,EAAA,IAAAC,KACAkB,EAAA,IAAAlB,KACAD,EAAAoB,WAAApB,EAAAqB,aAAAlI,EAAAmI,aAEAhM,EAAAO,OAAAC,OAAA,kBAAAqL,GAEAxP,OAAA6D,aAAAsL,YAAAK,EACAxP,OAAA6D,aAAA8L,WAAAnI,EAAAmI,WAEAhM,EAAAuD,QAAA,CACA1K,KAAA,UACA2K,QAAA,mBACAI,SAAA,MAGAC,EAAAF,UAAAE,EAAAiF,QAAAhO,KACA+I,EAAA4f,WAAA5f,EAAAiF,QAAA4a,mBACA7f,EAAA8f,IAAA9f,EAAAiF,QAAA8a,IACAvnB,OAAA6D,aAAA2D,KAAAggB,IAAAhgB,GACAA,EAAA8f,IAAA,GACA3jB,EAAA8jB,iBAAAjgB,EAAA8f,KAhCAve,EAAAE,KAAA,iBAAAF,EAAAC,KAAA,GAAAD,EAAA2e,GAAA3e,EAAA,YAoCArG,KAAAilB,MAAAhE,MAAA,iBAAAxc,QAAA4B,EAAA2e,KApCA,yBAAA3e,EAAAgB,SAAArB,EAAAhG,KAAA,sBAAAyH,IAAA,OAAAuC,EAAArU,MAAAqK,KAAAwH,WAAA,OAAAC,EAAA,GAuCA7H,QAAA,CAEAmlB,iBAFA,SAEAG,GACA,IAAAjkB,EAAAjB,KACAoE,EAAA,CAAA8gB,MAAAzoB,EAAA,IAAAmP,MAEAzW,OAAAgwB,EAAA,KAAAhwB,CAAAiP,GAAAzJ,KAAA,SAAAnG,GAEA,GAAAA,EAAA+P,QAKA,CAGAtD,EAAA8D,SAAA,CACAN,QAAA,UACA3K,KAAA,YAEA,IAAAsrB,EAAAjgB,KAAAC,MACA9H,OAAA6D,aAAA2D,KAAAxH,OAAA6D,aAAA2D,KAAA,MAEA7D,EAAAuD,QAAA,CACA1K,KAAA,UACA2K,QAAA,iBAAAC,OACA0gB,EAAAxgB,UADA,eAAAF,OAEA2gB,IAAA/nB,OAAA6D,aAAA8L,WAAA,IAFA,WAGApI,SAAA,MAGAvH,OAAA6D,aAAAmE,OAAAwf,IAAAtwB,EAAAmQ,SAAAmb,UAEA,IAAAwF,EAAA9wB,EAAAmQ,SAAAmb,SACAwF,EAAAnwB,OAAAmQ,EAAA,KAAAnQ,CAAAmwB,GACAhgB,EAAA,KAAAigB,WAAAD,GAEArkB,EAAAhB,QAAAC,QACAe,EAAA0B,OAAAqL,MAAAC,SAAAhN,EAAA0B,OAAAqL,MAAAC,SAAA,UA7BAhN,EAAA8D,SAAA,CACAN,QAAAjQ,EAAAiQ,QACA3K,KAAA,eC1E2V0rB,EAAA,cCO3Vjc,EAAgBpU,OAAAqU,EAAA,KAAArU,CACdqwB,EACA7c,EACAW,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,wBACe/L,EAAA,WAAA4L,sDCnBf,IAAAZ,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAA,EAAA,KAAyBE,YAAA,kBAA6B,CAAAL,EAAAoM,GAAA,aAAAjM,EAAA,MAAAH,EAAAoM,GAAA,0BAAAjM,EAAA,eAAkFqB,MAAA,CAAOnD,GAAA,MAAU,CAAA2B,EAAAoM,GAAA,oBAClP1L,EAAA,2BCAAvO,EAAA,GAMAwO,EAAgBpU,OAAAqU,EAAA,KAAArU,CAChB4F,EACE4N,EACAW,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,UACe/L,EAAA,WAAA4L,sECnBf,IAAAkc,EAAAtvB,EAAA,QAAAuvB,EAAAvvB,EAAA6G,EAAAyoB,GAAqbC,EAAG,2QCQlbC,EAAUC,EAAQ,QAIxB/Y,aAAIpB,IAAIoa,QAER,IAAMC,EAAe,kBAAM,IAAID,OAAO,CAClCnpB,KAAM,OACNyO,KAAM4a,OACN1mB,OAAQ,CACJ,CACIc,KAAM,OAAQoJ,UAAWyc,aAAQjqB,KAAM,SACvC2H,KAAM,CACFI,MAAO,SACPmiB,aAAa,EACbtiB,WAAW,EACXgR,YAAY,GAEhBuR,QAAQ,GAEZ,CACI/lB,KAAM,UAAWoJ,UAAW4c,aAAQpqB,KAAM,SAC1C2H,KAAM,CACFI,MAAO,SACPmiB,aAAa,GAEjBC,QAAQ,GAEZ,CACI/lB,KAAM,IACNoJ,UAAW6c,aACXrqB,KAAM,KACN8jB,QAAS,UAETnc,KAAM,CACFI,MAAO,KACPmiB,aAAa,IAGrB,CACI9lB,KAAM,SACNoJ,UAAW8c,aACXtqB,KAAM,QACN8jB,QAAS,kBACTnc,KAAM,CACFI,MAAO,KACPH,WAAW,EACXgR,YAAY,GAEhBuR,QAAQ,GAEZ,CACE/lB,KAAM,YACNpE,KAAM,oBACNwN,UAAW+c,aACX5iB,KAAM,CACFI,MAAO,KACPH,WAAW,GAEfuiB,QAAQ,GAEV,CACI/lB,KAAM,IACN+lB,QAAQ,EACRjY,SAAU,CAAE9N,KAAM,aAKxBmF,EAASwgB,IAER,SAASS,EAAkBC,GAE9B,IAAMC,EAAkBD,EAAeljB,OAAO,SAAAG,GAC1C,GAAIA,EAAMtD,OAASsD,EAAMkc,SACrB,GAAmB,MAAflc,EAAMtD,MAA+B,MAAfsD,EAAMtD,KAC5BsD,EAAM8F,UAAYmd,kBAElB,IACIjjB,EAAM8F,UAAYoc,EAAQliB,EAAMtD,KAAKD,QAAQ,OAAO,KACtD,MAAOtH,GACL,IACI6K,EAAM8F,UAAY,kBAAMpT,EAAA,OAAAA,CAAO,UAAYsN,EAAMtD,KAAKD,QAAQ,OAAO,IAAM,SAC7E,MAAO1E,GACL4B,QAAQ4mB,KAAK,WAAavgB,EAAMtD,KAAKD,QAAQ,OAAO,IAAM,+CAAgD,cAQ1H,OAHIuD,EAAMqc,UAAYrc,EAAMqc,SAAS9qB,SAAWyO,EAAMkc,WAClDlc,EAAMqc,SAAWyG,EAAkB9iB,EAAMqc,YAEtC,IAGX,OAAO2G,EAGJ,SAASE,IACZ,IAAMC,EAAYd,IAClBxgB,EAAOuhB,QAAUD,EAAUC,QAI/BvhB,EAAOigB,WAAa,SAACjY,GAEjB,IAAIwZ,EAAI,SAAJA,EAAI9jB,GAEJ,OAAIA,EAAK,aACLA,EAAK,YAAcA,EAAK,YAAYM,OAAOwjB,IACpC,IACA9jB,EAAK,cACc,IAAnBA,EAAK,aAOhBsK,EAASA,EAAOhK,OAAOwjB,GAE3BxhB,EAAOyhB,UAAUzZ,IAGNhI,mDCpIf,IAAAqD,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,WAAsB,CAAAF,EAAA,MAAWE,YAAA,cAAyB,CAAAL,EAAAuM,GAAA,YAAAnY,GAA0B,OAAA+L,EAAA,MAAgBjM,IAAAE,EAAA,QAAY4L,EAAAuM,GAAA,WAAAvZ,GAAyB,OAAAmN,EAAA,MAAgBjM,IAAAlB,EAAA,SAAY,GAAAmN,EAAA,OAAgBE,YAAA,aAAAC,YAAA,CAAsCzG,QAAA,UAAkBsG,EAAA,OAAYG,YAAA,CAAaC,OAAA,SAAgBJ,EAAA,WAAgBoN,IAAA,YAAAlN,YAAA,gCAAAmB,MAAA,CAAmEyM,MAAAjO,EAAAoe,UAAAC,MAAAre,EAAAse,OAAAC,iBAAA,OAAAC,cAAA,QAAsF,CAAAre,EAAA,MAAWE,YAAA,SAAoB,CAAAL,EAAAoM,GAAA,UAAAjM,EAAA,gBAAsCqB,MAAA,CAAOid,KAAA,YAAkB,CAAAte,EAAA,YAAiBqB,MAAA,CAAOtQ,KAAA,OAAAwtB,gBAAA,MAAApJ,YAAA,MAAuDrH,MAAA,CAAQra,MAAAoM,EAAAoe,UAAA,QAAAlQ,SAAA,SAAAC,GAAuDnO,EAAA2e,KAAA3e,EAAAoe,UAAA,UAAAjQ,IAAwCC,WAAA,wBAAiC,GAAAjO,EAAA,gBAAyBqB,MAAA,CAAOid,KAAA,cAAoB,CAAAte,EAAA,YAAiBqB,MAAA,CAAOkd,gBAAA,MAAAE,gBAAA,GAAAtJ,YAAA,MAA4DrH,MAAA,CAAQra,MAAAoM,EAAAoe,UAAA,UAAAlQ,SAAA,SAAAC,GAAyDnO,EAAA2e,KAAA3e,EAAAoe,UAAA,YAAAjQ,IAA0CC,WAAA,0BAAmC,GAAAjO,EAAA,eAAwBE,YAAA,WAAAmB,MAAA,CAA8Bqd,QAAA,IAAa5Q,MAAA,CAAQra,MAAAoM,EAAA,QAAAkO,SAAA,SAAAC,GAA6CnO,EAAA6e,QAAA1Q,GAAgBC,WAAA,YAAuB,CAAApO,EAAAoM,GAAA,UAAAjM,EAAA,OAA6BE,YAAA,aAAAC,YAAA,CAAsCwe,gBAAA,SAAwB,CAAA3e,EAAA,kBAAuB+L,GAAA,CAAIqJ,OAAAvV,EAAA+e,cAA0B9Q,MAAA,CAAQra,MAAAoM,EAAA,SAAAkO,SAAA,SAAAC,GAA8CnO,EAAAgf,SAAA7Q,GAAiBC,WAAA,aAAwB,CAAAjO,EAAA,mBAAwBqB,MAAA,CAAOqJ,MAAA,WAAiB1K,EAAA,mBAAwBqB,MAAA,CAAOqJ,MAAA,WAAiB1K,EAAA,mBAAwBqB,MAAA,CAAOqJ,MAAA,YAAiB,OAAA1K,EAAA,gBAA6BG,YAAA,CAAahH,MAAA,SAAgB,CAAA6G,EAAA,aAAkBG,YAAA,CAAahH,MAAA,QAAekI,MAAA,CAAQtQ,KAAA,UAAA+tB,QAAAjf,EAAAtE,UAAwCgR,SAAA,CAAWP,MAAA,SAAAQ,GAAiD,OAAxBA,EAAAiB,iBAAwB5N,EAAAkf,cAAAvS,MAAmC,CAAA3M,EAAAoM,GAAA,qBAAApM,EAAAqM,GAAArM,EAAAmf,UAAA,wBAAAhf,EAAA,gBAA8FG,YAAA,CAAahH,MAAA,SAAgB,CAAA6G,EAAA,aAAkBG,YAAA,CAAahH,MAAA,QAAekI,MAAA,CAAQyd,QAAAjf,EAAAof,cAA2B1S,SAAA,CAAWP,MAAA,SAAAQ,GAAiD,OAAxBA,EAAAiB,iBAAwB5N,EAAAqf,iBAAA1S,MAAsC,CAAA3M,EAAAoM,GAAA,uCACvrE1L,EAAA,4FC8CA4e,EAAA,CACA1zB,KADA,WAEA,OACA2zB,SAAA,GACAJ,SAAA,KACAzjB,UAAA,EACA0jB,cAAA,EACAhB,UAAA,CACAoB,QAAA,OACAC,UAAA,QAEAT,SAAA,QACAV,OAAA,CACAkB,QAAA,CACA,CAAAxH,UAAA,EAAAnc,QAAA,QAAA2Q,QAAA,SAGAiT,UAAA,CACA,CAAAzH,UAAA,EAAAnc,QAAA,QAAA2Q,QAAA,UAIAqS,SAAA,IAGA7nB,QAAA,CACA0oB,aADA,WAEAtoB,KAAAqC,MAAA2kB,UAAAuB,eAEAZ,aAJA,WAMA,SAAA3nB,KAAA4nB,UACA5nB,KAAAgnB,UAAAoB,QAAA,OACApoB,KAAAgnB,UAAAqB,UAAA,QACA,SAAAroB,KAAA4nB,UACA5nB,KAAAgnB,UAAAoB,QAAA,QACApoB,KAAAgnB,UAAAqB,UAAA,UAEAroB,KAAAgnB,UAAAoB,QAAA,YACApoB,KAAAgnB,UAAAqB,UAAA,cAIAJ,iBAlBA,SAkBAO,GAAA,IAAAxnB,EAAAhB,KACAiB,EAAAjB,KACAA,KAAAqC,MAAA2kB,UAAAyB,SAAA,SAAAC,GACA,IAAAA,EA0BA,OADAtrB,QAAA2C,IAAA,mBACA,EAxBAiB,EAAAgnB,cAAA,EAEA,IAAA5jB,EAAA,CAAA0X,SAAA9a,EAAAgmB,UAAAoB,QAAArM,SAAA/a,EAAAgmB,UAAAqB,WAEAlzB,OAAAgwB,EAAA,MAAAhwB,CAAAiP,GAAAzJ,KAAA,SAAAnG,GACAwM,EAAAgnB,cAAA,EAEAxzB,KAAA8X,MAAA9X,EAAAm0B,IACA1nB,EAAA8D,SAAA,CACAN,QAAAjQ,EAAA8X,KAAA9X,EAAAm0B,IAAA,qBACA7uB,KAAA,WAIAmH,EAAA8D,SAAA,CACAN,QAAA,0BACA3K,KAAA,UAEAsD,QAAA4mB,KAAA,gDAUA4E,UAnDA,SAmDAD,GACA3oB,KAAAmoB,SAAAnoB,KAAAwE,QAAA,CACAV,MAAA,KACAW,QAAAkkB,EACA9jB,SAAA,EACAgkB,SAAA,cAGAC,WA3DA,WA4DA9oB,KAAAmoB,SAAAnS,SAGA8R,cA/DA,SA+DAU,GAAA,IAAAplB,EAAApD,KACAiB,EAAAjB,KACAA,KAAAqC,MAAA2kB,UAAAyB,SAAA,SAAAC,GACA,IAAAA,EAsDA,OADAtrB,QAAA2C,IAAA,mBACA,EApDAqD,EAAAkB,UAAA,EAGA,IAAAF,EAAA,CAAArI,KAAAqH,EAAA4jB,UAAAoB,QAAAW,KAAA3lB,EAAA4jB,UAAAqB,WAKApnB,EAAA8mB,SAAA,SAEA5yB,OAAAgwB,EAAA,MAAAhwB,CAAAiP,GAAAzJ,KAAA,SAAAnG,GACA,GAAAA,EAAA+P,QAQA,CAEA,IAAAL,EAAA1P,EAAAmQ,SAAAT,MACAjD,EAAAO,OAAAC,OAAA,YAAAyC,GAEA,IAAAyH,EAAA,IAAAC,KACAkB,EAAA,IAAAlB,KAAAD,EAAAoB,WAAApB,EAAAqB,aAAAxY,EAAAmQ,SAAAsI,aACAhM,EAAAO,OAAAC,OAAA,kBAAAqL,GAEAxP,OAAA6D,aAAAsL,YAAAK,EACAxP,OAAA6D,aAAA8L,WAAAzY,EAAAmQ,SAAAsI,WAEAhM,EAAAuD,QAAA,CACA1K,KAAA,UACA2K,QAAA,mBACAI,SAAA,MAMA5D,EAAA8mB,SAAA,2BAEA9mB,EAAAgD,mBAAAC,QA9BAjD,EAAA8D,SAAA,CACAN,QAAAjQ,EAAAm0B,IACA7uB,KAAA,UAEAmH,EAAAqD,UAAA,EACArD,EAAA8mB,SAAA,SA6BAnmB,MAAA,SAAAhJ,GACAqI,EAAAqD,UAAA,EACArD,EAAA8mB,SAAA,YASA9jB,mBA7HA,SA6HAC,GACA,IAAAjD,EAAAjB,KACAoE,EAAA,CAAAF,SACA/O,OAAAgwB,EAAA,KAAAhwB,CAAAiP,GAAAzJ,KAAA,SAAAnG,GAEAA,EAAA+P,SAQAtD,EAAA8mB,SAAA,sBAGAzqB,OAAA6D,aAAA2D,KAAAggB,IAAAtwB,EAAAmQ,UACAnQ,EAAAmQ,SAAAigB,IAAA,GACA3jB,EAAA8jB,iBAAAvwB,EAAAmQ,SAAAigB,MAZA3jB,EAAA8D,SAAA,CACAN,QAAAjQ,EAAAm0B,IACA7uB,KAAA,aAgBAirB,iBArJA,SAqJAG,GACA,IAAAjkB,EAAAjB,KACAoE,EAAA,CAAA8gB,MAAAzoB,EAAA,IAAAmP,MAEAzW,OAAAgwB,EAAA,KAAAhwB,CAAAiP,GAAAzJ,KAAA,SAAAnG,GAIA,GAHAyM,EAAAqD,UAAA,EAGA9P,EAAA+P,QAKA,CAIAtD,EAAA8D,SAAA,CACAN,QAAA,UACA3K,KAAA,YAIA,IAAAsrB,EAAAjgB,KAAAC,MAAA9H,OAAA6D,aAAA2D,KAAAxH,OAAA6D,aAAA2D,KAAA,MACA7D,EAAAuD,QAAA,CACA1K,KAAA,UACA2K,QAAA,iBAAAC,OAAA0gB,EAAAxgB,UAAA,eAAAF,OAAApH,OAAA6D,aAAA8L,WAAA,cACApI,SAAA,MAIAvH,OAAA6D,aAAAmE,OAAAwf,IAAAtwB,EAAAmQ,SAAAmb,UAEA,IAAAwF,EAAA9wB,EAAAmQ,SAAAmb,SACAwF,EAAAnwB,OAAAmQ,EAAA,KAAAnQ,CAAAmwB,GACAhgB,EAAA,KAAAigB,WAAAD,GAEArkB,EAAAhB,QAAAC,QAAAe,EAAA0B,OAAAqL,MAAAC,SAAAhN,EAAA0B,OAAAqL,MAAAC,SAAA,UA5BAhN,EAAA8D,SAAA,CACAN,QAAAjQ,EAAAm0B,IACA7uB,KAAA,cA+BAkL,QAxNA,WA0NA5H,QAAA4mB,KAAA,+BCzQ+UgF,EAAA,0BCQ/Uzf,EAAgBpU,OAAAqU,EAAA,KAAArU,CACd6zB,EACArgB,EACAW,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,YACe/L,EAAA,WAAA4L,oECpBf,IAAA0f,EAAA9yB,EAAA,QAAA+yB,EAAA/yB,EAAA6G,EAAAisB,GAAqbC,EAAG,qCCAxb,IAAAC,EAAAhzB,EAAA,QAAAizB,EAAAjzB,EAAA6G,EAAAmsB,GAAucC,EAAG,4DCA1c,IAAAC,EAAAlzB,EAAA,QAAAmzB,EAAAnzB,EAAA,QAAAozB,EAAApzB,EAAA,QAAAqzB,EAAArzB,EAAA6G,EAAAusB,GAIA1c,aAAIpB,IAAIge,QAEO9rB,EAAA,SAAI8rB,OAAKC,MAAM,CAC5B5d,MAAO,CACH5H,MAAO,KACP6H,YAAa,KACb4d,cAAe,GACfC,SAAUvK,IAAQjjB,IAAI,aAAe,MAGzCytB,UAAW,CACPC,UADO,SACGhe,EAAOtX,GACbsX,EAAM5H,MAAQ1P,EACd8I,OAAO6D,aAAaoC,QAAQ,QAAS/O,IAEzCu1B,gBALO,SAKSje,EAAOtX,GACnBsX,EAAMC,YAAcvX,EACpB8I,OAAO6D,aAAaoC,QAAQ,cAAe/O,IAE/Cw1B,aATO,SASMle,EAAOtX,GAChBsX,EAAM6d,cAAgBn1B,EACtB6M,eAAekC,QAAQ,OAAO/O,IAElCy1B,aAAc,SAACne,EAAO8d,GAClB9d,EAAM8d,SAAWA,EACjBvK,IAAQ6K,IAAI,WAAYN,KAIhC/L,QAAS,CACLsM,YADK,SAAAC,EACmBR,GAAU,IAApBnoB,EAAoB2oB,EAApB3oB,OACVA,EAAO,eAAgBmoB,iECnCjC,IAAAS,EAAAl0B,EAAA,QAAAm0B,EAAAn0B,EAAA6G,EAAAqtB,GAAmeC,EAAG,gGCSlehF,+FACJ,IAAKA,EACD,GAAKiF,EAAU,UAiBXntB,QAAQ4mB,KAAK,mDAAoD,eACjEsB,EAAYiF,EAAU,UACtBjF,EAAYiB,eAAkBjB,GAC9BhgB,OAAOigB,WAAWD,GAClBhkB,EAAOC,UAAY+jB,MArBG,CAEtB,IAAIxgB,EAAOxH,OAAO6D,aAAa2D,KAAOK,KAAKC,MAAM9H,OAAO6D,aAAa2D,MAAQ,KAC7E,GAAIA,GAAQA,EAAK8f,IAAM,EAAG,CACtBxnB,QAAQ4mB,KAAKlf,EAAK8f,KAClB,IAAIxgB,EAAc,CAAE8gB,IAAKpgB,EAAK8f,KAC9BhV,eAAiBxL,GAAazJ,KAAK,SAAAnG,GAC3BA,EAAK+P,UACLnH,QAAQ4mB,KAAK,0CAA2C,aACxDsB,EAAY9wB,EAAKmQ,SAASmb,SAC1B0K,EAAW,SAAUlF,OAgBzC,IAAIha,EAAYC,OAmFhB,SAASkf,EAASxjB,EAAIV,GAGlB+e,EAAYiB,eAAkBjB,GAC9BqB,iBAGArhB,OAAOigB,WAAWD,GAGlBhkB,EAAOC,UAAY+jB,EACnB/e,EAAKpR,OAAAu1B,EAAA,KAAAv1B,CAAA,GAAK8R,EAAN,CAAU/G,SAAS,KAI3B,SAASsqB,EAAWzuB,EAAMvH,GACtB2M,aAAaoC,QAAQxH,EAAM4uB,IAAen2B,IAI9C,SAAS+1B,EAAUxuB,GACf,OAAOoJ,KAAKC,MAAM9H,OAAO6D,aAAa+D,QAAQnJ,IAvGlDuJ,OAAOia,WAAW,SAACtY,EAAIf,EAAMK,GAUrB,GAPK+E,EAAUQ,MAAM5H,OACjBoH,EAAU7J,OAAO,YAAanE,OAAO6D,aAAawL,OAEjDrB,EAAUQ,MAAMC,aACjBT,EAAU7J,OAAO,kBAAmBnE,OAAO6D,aAAawM,aAE5DzB,kBACIjF,EAAGvD,KAAKuiB,YAAa,CAEP,IAAIra,KACD,IAAIA,KAAKA,KAAKxG,MAAM9H,OAAO6D,aAAawM,cACrDrC,EAAUQ,MAAM5H,OAAkC,aAAzBoH,EAAUQ,MAAM5H,OAEzC9G,QAAQ2C,IAAI,GACZwG,MAEAgF,OAAM9J,OAAO,YAAa,IAC1B8J,OAAM9J,OAAO,kBAAmB,IAChC8J,OAAM9J,OAAO,eAAgB,IAC7BnE,OAAO6D,aAAaC,WAAW,QAC/B9D,OAAO6D,aAAaC,WAAW,iBAC/B9D,OAAO6D,aAAaC,WAAW,UAG3BE,EAAOI,QACXgH,OAAuBqF,QAEnBxH,EAAK,CACDpG,KAAM,SACN6N,MAAO,CAAEC,SAAUhH,EAAGrE,kBAQlCxF,QAAQ2C,IAAI,GACZwG,IAOJ,GAAK+e,EAqBGre,EAAGlL,MAAmB,SAAXkL,EAAGlL,OACdupB,EAAYiF,EAAU,UACtBjpB,EAAOC,UAAY+jB,QAtBvB,GAAKiF,EAAU,UAeXjF,EAAYiF,EAAU,UACtBE,EAASxjB,EAAIV,OAhBS,CACtB,IAAIzB,EAAOxH,OAAO6D,aAAa2D,KAAOK,KAAKC,MAAM9H,OAAO6D,aAAa2D,MAAQ,KAC7E,GAAIA,GAAQA,EAAK8f,IAAM,EAAG,CACtB,IAAIxgB,EAAc,CAAE8gB,IAAKpgB,EAAK8f,KAC9BhV,eAAiBxL,GAAazJ,KAAK,SAAAnG,GAC/B4I,QAAQ2C,IAAI,2DACRvL,EAAK+P,UACL+gB,EAAY9wB,EAAKmQ,SAASmb,SAC1B0K,EAAW,SAAUlF,GACrBmF,EAASxjB,EAAIV,UAiDzC,IAAIqkB,EAAa,GAGJC,EAAgB,SAAhBA,EAAiBC,EAAUC,GAYpC,OAXAA,EAAQC,QAAQ,SAAAC,GACZ,GAAIH,GAAWG,EAAQ9qB,KAAM,CACzB,IAAIA,EAAO2qB,EAAUI,cACrB,GAAID,EAAQ9qB,MAAQ8qB,EAAQ9qB,KAAK+qB,gBAAkB/qB,EAE/C,YADAyqB,EAAaK,EAAQnL,UAEdmL,EAAQnL,UACf+K,EAAc1qB,EAAK8qB,EAAQnL,aAIhC8K,8CChKXlyB,EAAAD,QAAA,omECAA,IAAAkQ,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,gBAA0BoN,IAAA,kBAAAlN,YAAA,mBAAAmB,MAAA,CAA4D+gB,UAAA,GAAiB7V,SAAA,CAAW8V,MAAA,SAAA7V,GAAiD,OAAxBA,EAAAiB,iBAAwB5N,EAAAyiB,aAAA9V,MAAkC,CAAA3M,EAAAyX,GAAA,gBAC9R/W,EAAA,GCMAgiB,2CAAA,GAEAC,EAAA,CACAxvB,KAAA,aACAvH,KAFA,WAGA,OACAiL,KAAA,IAGAgG,SAAA,CACA+lB,cADA,WAEA,OAAAxrB,KAAAqC,MAAAopB,gBAAAppB,MAAA+D,OAGAxG,QAAA,CACAyrB,aADA,SACAzyB,GACA,IAAA8yB,EAAA9yB,EAAA+yB,YAAA,IAAA/yB,EAAAgzB,OACAC,EAAA7rB,KAAAwrB,cACAK,EAAAC,WAAAD,EAAAC,WAAAJ,EAAA,GAEAvkB,aANA,SAMA4kB,EAAAC,GAEA,IAAAC,EAAAjsB,KAAAqC,MAAAopB,gBAAAS,IACAC,EAAAF,EAAAG,YACAP,EAAA7rB,KAAAwrB,cAGAa,EAAA,KACAC,EAAA,KAQA,GALAN,EAAAh3B,OAAA,IACAq3B,EAAAL,EAAA,GACAM,EAAAN,IAAAh3B,OAAA,IAGAq3B,IAAAN,EACAF,EAAAC,WAAA,OACA,GAAAQ,IAAAP,EACAF,EAAAC,WAAAD,EAAAU,YAAAJ,MACA,CAEA,IAAAK,EAAAR,EAAAS,UAAA,SAAAzpB,GAAA,OAAAA,IAAA+oB,IACAW,EAAAV,EAAAQ,EAAA,GACAG,EAAAX,EAAAQ,EAAA,GAGAI,EAAAD,EAAAT,IAAAW,WAAAF,EAAAT,IAAAE,YAAAd,EAGAwB,EAAAJ,EAAAR,IAAAW,WAAAvB,EAEAsB,EAAAf,EAAAC,WAAAK,EACAN,EAAAC,WAAAc,EAAAT,EACAW,EAAAjB,EAAAC,aACAD,EAAAC,WAAAgB,OC9DoVC,EAAA,0BCQpVxjB,EAAgBpU,OAAAqU,EAAA,KAAArU,CACd43B,EACApkB,EACAW,GACF,EACA,KACA,KACA,MAIAC,EAAAE,QAAAC,OAAA,iBACe/L,EAAA,KAAA4L,oECpBf,IAAAyjB,EAAA72B,EAAA,QAAA82B,EAAA92B,EAAA6G,EAAAgwB,GAAycC,EAAG,qCCA5c,IAAAC,EAAA/2B,EAAA,QAAAg3B,EAAAh3B,EAAA6G,EAAAkwB,GAA8cC,EAAG,qCCAjd,IAAAC,EAAAj3B,EAAA,QAAAk3B,EAAAl3B,EAAA6G,EAAAowB,GAAqbC,EAAG,4CCAxb,IAAA1kB,EAAA,WAA0B,IAAAC,EAAA5I,KAAa6I,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBG,YAAA,CAAaokB,aAAA,SAAqB,CAAAvkB,EAAA,UAAeE,YAAA,eAA0B,CAAAF,EAAA,UAAeE,YAAA,iBAAAC,YAAA,CAA0CqkB,MAAA,OAAArrB,MAAA,qBAAAsrB,OAAA,MAA0D,CAAAzkB,EAAA,WAAgBE,YAAA,gCAA2C,CAAAF,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,cAAAjM,EAAA,OAA4CE,YAAA,eAAAC,YAAA,CAAwCuM,WAAA,gDAA4D7M,EAAAuM,GAAAvM,EAAAqK,gBAAA,qBAAAjQ,EAAAF,GAA+D,OAAAiG,EAAA,OAAiBjM,IAAAkG,EAAA8B,KAAAhC,EAAAmG,YAAA,mBAAAzG,MAAAoG,EAAA0L,OAAAxR,IAA+E,CAAAiG,EAAA,YAAiBE,YAAA,OAAAmB,MAAA,CAA0B5N,MAAAwG,EAAAqR,MAAA,aAAArR,EAAAqR,MAAAva,KAAA8O,EAAAwL,YAAApR,EAAAqR,SAAqF,CAAAtL,EAAA,SAAcE,YAAA,WAAAmB,MAAA,CAA8BtG,MAAAd,EAAA8B,OAAmB,CAAA8D,EAAAoM,GAAApM,EAAAqM,GAAAjS,EAAA8B,YAAA,KAAoC,SAAAiE,EAAA,OAAqBE,YAAA,mBAA8B,CAAAF,EAAA,UAAeE,YAAA,kBAA6B,CAAAF,EAAA,OAAYE,YAAA,cAAyB,CAAAF,EAAA,OAAYE,YAAA,0BAAqC,CAAAF,EAAA,OAAYE,YAAA,mBAA8B,CAAAL,EAAAoM,GAAA,UAAAjM,EAAA,QAA8BE,YAAA,kBAAAmB,MAAA,CAAqCqjB,kBAAA,KAAsB,CAAA7kB,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAAqK,gBAAAE,gBAAA,EACtvCvK,EAAAqK,gBAAAE,gBACA,IAAAvK,EAAAqK,gBAAAE,0BAAApK,EAAA,UAAmFE,YAAA,kBAA6B,CAAAF,EAAA,OAAYE,YAAA,cAAyB,CAAAF,EAAA,OAAYE,YAAA,0BAAqC,CAAAF,EAAA,OAAYE,YAAA,mBAA8B,CAAAL,EAAAoM,GAAA,UAAAjM,EAAA,QAA8BE,YAAA,kBAAAmB,MAAA,CAAqCqjB,kBAAA,KAAsB,CAAA7kB,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAAgL,kBAAAG,MAAA,EACzUnL,EAAAgL,kBAAAG,MACA,IAAAnL,EAAAgL,kBAAAG,gBAAAhL,EAAA,UAA2EE,YAAA,kBAA6B,CAAAF,EAAA,OAAYE,YAAA,qBAAA6L,GAAA,CAAqCC,MAAAnM,EAAA2L,SAAoB,CAAAxL,EAAA,OAAYE,YAAA,0BAAqC,CAAAF,EAAA,OAAYE,YAAA,mBAA8B,CAAAL,EAAAoM,GAAA,UAAAjM,EAAA,QAA8BE,YAAA,iBAAAmB,MAAA,CAAoCqjB,kBAAA,KAAsB,CAAA7kB,EAAAoM,GAAApM,EAAAqM,GAAArM,EAAAqK,gBAAAI,WAAA,EAChWzK,EAAAqK,gBAAAI,WACA,IAAAzK,EAAAqK,gBAAAI,sBAAA,OAAAtK,EAAA,UAAsFE,YAAA,eAA0B,CAAAF,EAAA,UAAeE,YAAA,iBAAAC,YAAA,CAA0CqkB,MAAA,OAAArrB,MAAA,OAAAsrB,OAAA,MAA4C,CAAAzkB,EAAA,WAAgBE,YAAA,gCAA2C,CAAAF,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,UAAAjM,EAAA,QAAyCG,YAAA,CAAawkB,MAAA,OAAAC,YAAA,SAAmC,CAAA/kB,EAAAoM,GAAA,0BAAAjM,EAAA,OAA6CE,YAAA,eAAAC,YAAA,CAAwCuM,WAAA,gDAA4D7M,EAAAuM,GAAAvM,EAAAqK,gBAAA,qBAAAjQ,EAAAF,GAA+D,OAAAiG,EAAA,OAAiBjM,IAAAkG,EAAA8B,KAAAhC,EAAAmG,YAAA,qBAAA4L,MAAA7R,EAAAqR,MAAA,kBAAA7R,MAAAoG,EAAA0L,OAAAxR,IAAwH,CAAAiG,EAAA,YAAiBE,YAAA,OAAAmB,MAAA,CAA0B5N,MAAAwG,EAAAqR,MAAA,iBAAArR,EAAAqR,MAAAva,KAAA8O,EAAAwL,YAAApR,EAAAqR,SAAyF,CAAAtL,EAAA,SAAcE,YAAA,WAAAmB,MAAA,CAA8BtG,MAAAd,EAAA8B,OAAmB,CAAA8D,EAAAoM,GAAApM,EAAAqM,GAAAjS,EAAA8B,YAAA,KAAoC,aAAAiE,EAAA,WAA6BE,YAAA,2BAAAC,YAAA,CAAoDhH,MAAA,mBAAA0rB,eAAA,SAAkD,CAAA7kB,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,YAAAjM,EAAA,OAA0CE,YAAA,aAAwB,CAAAF,EAAA,KAAUE,YAAA,iBAA2BL,EAAAoM,GAAA,kDAAAjM,EAAA,OAAqEE,YAAA,aAAwB,CAAAF,EAAA,KAAUE,YAAA,iBAA2BL,EAAAoM,GAAA,6CAAAjM,EAAA,OAAgEE,YAAA,aAAwB,CAAAF,EAAA,KAAUE,YAAA,iBAA2BL,EAAAoM,GAAA,kCAAAjM,EAAA,OAAqDE,YAAA,aAAwB,CAAAF,EAAA,KAAUE,YAAA,iBAA2BL,EAAAoM,GAAA,yBAAAjM,EAAA,KAA0CqB,MAAA,CAAOlR,KAAA,oCAAAiB,OAAA,WAA8D,CAAAyO,EAAAoM,GAAA,uCAAApM,EAAAoM,GAAA,eAAAjM,EAAA,WAAoFE,YAAA,sBAAAC,YAAA,CAA+ChH,MAAA,MAAAsrB,OAAA,IAAAG,YAAA,SAA+C,CAAA5kB,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAaG,YAAA,CAAaykB,YAAA,SAAoB,CAAA/kB,EAAAoM,GAAA,WAAAjM,EAAA,OAA8BE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,QAAApM,EAAAqM,GAAArM,EAAA0K,WAAAua,oBAAA9kB,EAAA,OAAqEE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,QAAApM,EAAAqM,GAAArM,EAAA0K,WAAAwa,mBAAA/kB,EAAA,OAAoEE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,2BAAApM,EAAAqM,GAAArM,EAAA0K,WAAAya,iBAAA,YAAAhlB,EAAA,OAAiGE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,eAAApM,EAAAqM,GAAArM,EAAA0K,WAAA0a,gBAAAjlB,EAAA,OAAwEE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,uBAAApM,EAAAqM,GAAArM,EAAA0K,WAAA2a,sBAAA,YAAAllB,EAAA,OAAkGE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,QAAApM,EAAAqM,GAAArM,EAAA0K,WAAA4a,oBAAAnlB,EAAA,OAAqEE,YAAA,aAAwB,CAAAL,EAAAoM,GAAA,QAAApM,EAAAqM,GAAArM,EAAA0K,WAAA6a,gBAAAplB,EAAA,OAAAA,EAAA,UAAAA,EAAA,WAA4FE,YAAA,oBAAAC,YAAA,CAA6ChH,MAAA,MAAAorB,aAAA,SAAmC,CAAAvkB,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,kBAAAjM,EAAA,UAAmDE,YAAA,eAAAmB,MAAA,CAAkCwK,KAAA,KAAW,CAAA7L,EAAA,WAAgBqB,MAAA,CAAO5V,KAAAoU,EAAAgL,kBAAAL,OAAA3K,EAAA2K,OAAA6a,SAAAxlB,EAAAoL,sBAAAqa,aAAAzlB,EAAAuL,uBAA2H,OAAApL,EAAA,WAAwBE,YAAA,eAAAC,YAAA,CAAwCokB,aAAA,OAAAprB,MAAA,QAAmC,CAAA6G,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,4BAAAjM,EAAA,QAA2DG,YAAA,CAAaykB,YAAA,SAAoB,CAAA/kB,EAAAoM,GAAA,uBAAAjM,EAAA,YAA+CmO,WAAA,EAAanb,KAAA,UAAAob,QAAA,YAAA3a,MAAAoM,EAAA,YAAAoO,WAAA,gBAAoF9N,YAAA,CAAehH,MAAA,OAAAyrB,YAAA,QAAkCvjB,MAAA,CAAQ5V,KAAAoU,EAAAqK,gBAAAG,KAAAkb,wBAAA,GAAAC,OAAA,KAAwE,CAAAxlB,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,OAAA5T,MAAA,MAAAvR,MAAA,QAAAssB,SAAA,MAA2DzlB,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,KAAA5T,MAAA,OAAAvR,MAAA,WAA4C6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,YAAA5T,MAAA,OAAAvR,MAAA,WAAmD6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,MAAA5T,MAAA,OAAAvR,MAAA,MAAwC6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,gBAAA5T,MAAA,SAAAvR,MAAA,WAAyD6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,SAAA5T,MAAA,OAAAvR,MAAA,WAAgD6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,cAAA5T,MAAA,KAAAvR,MAAA,MAA8C6G,EAAA,mBAAwBqB,MAAA,CAAOid,KAAA,QAAA5T,MAAA,QAAAvR,MAAA,KAAAusB,wBAAA,IAAuEC,YAAA9lB,EAAA+lB,GAAA,EAAsB7xB,IAAA,UAAA8xB,GAAA,SAAAxmB,GAAiC,OAAAW,EAAA,OAAkBG,YAAA,CAAa2lB,kBAAA,YAAAC,OAAA,YAAkD,CAAAlmB,EAAAoM,GAAA,iBAAApM,EAAAqM,GAAA7M,EAAA2mB,IAAAC,OAAA,0BAAyE,GAAAjmB,EAAA,UAAAA,EAAA,WAAiCE,YAAA,eAAAC,YAAA,CAAwCokB,aAAA,OAAAprB,MAAA,QAAmC,CAAA6G,EAAA,OAAYE,YAAA,WAAAmB,MAAA,CAA8BiL,KAAA,UAAgBA,KAAA,UAAe,CAAAtM,EAAA,QAAAH,EAAAoM,GAAA,YAAAjM,EAAA,YAAAH,EAAAoM,GAAA,oBAAAjM,EAAA,MAAAA,EAAA,OAA6FE,YAAA,aAAwB,CAAAF,EAAA,KAAUE,YAAA,iBAA2BL,EAAAoM,GAAA,0BAAAjM,EAAA,KAA2CqB,MAAA,CAAOlR,KAAA,uEAAAiB,OAAA,WAAiG,CAAAyO,EAAAoM,GAAA,0EAAApM,EAAAoM,GAAA,aAAAjM,EAAA,MAAAA,EAAA,MAAAA,EAAA,MAAAA,EAAA,YAAAH,EAAAoM,GAAA,iBAAAjM,EAAA,MAAAA,EAAA,OAA4LG,YAAA,CAAaC,OAAA,QAAA8lB,aAAA,SAAsC,CAAAlmB,EAAA,YAAiBqB,MAAA,CAAO8kB,UAAA,aAAwB,CAAAnmB,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,8BAAyDzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,gEAA2FzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,yDAAoFzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,iCAA4DzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,SAAA0Y,YAAA,uCAAoEzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,SAAA0Y,YAAA,qCAAkEzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,SAAA0Y,YAAA,yBAAsDzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,8BAAyDzT,EAAA,WAAgBqB,MAAA,CAAOtG,MAAA,OAAA0Y,YAAA,iCAA2D,OAAAzT,EAAA,eAC1lMO,EAAA,eCPiV6lB,EAAAC,EAAA,uCCSjV7lB,EAAgBpU,OAAAqU,EAAA,KAAArU,CACdg6B,EACAxmB,EACAW,GACF,EACA,KACA,WACA,MAIAC,EAAAE,QAAAC,OAAA,cACe/L,EAAA,WAAA4L", "file": "js/app.d6fdef78.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"app\": 0\n \t}\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"js/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-23e41f57\":\"ae5fabaa\",\"chunk-276b085c\":\"39103cdf\",\"chunk-2d0a4854\":\"aee50383\",\"chunk-2d0c0c66\":\"4faa5607\",\"chunk-2d0c4aa3\":\"703d6172\",\"chunk-2d0cf4f3\":\"4034e115\",\"chunk-2d0d2f25\":\"359b78e4\",\"chunk-2d0da5bf\":\"c22ad0ee\",\"chunk-2d213196\":\"5c2d76ce\",\"chunk-2d21f214\":\"ec5ee5a8\",\"chunk-2d229214\":\"cfe33fe9\",\"chunk-2d22d746\":\"bc86ccfd\",\"chunk-40df6ae2\":\"e79ba86d\",\"chunk-47211100\":\"16761898\",\"chunk-479d738e\":\"57cdb42a\",\"chunk-47dd42da\":\"97513c3e\",\"chunk-4b6066be\":\"f63d0f19\",\"chunk-6e83591c\":\"a520c082\",\"chunk-7287e918\":\"7e428c29\",\"chunk-ef28925c\":\"a547d73e\",\"chunk-6f1c3bea\":\"5a9acc22\",\"chunk-735deb8e\":\"2bbe62a6\",\"chunk-770e833a\":\"0890b50d\",\"chunk-77279526\":\"e54ae03e\",\"chunk-789b0e7e\":\"df774071\",\"chunk-bf843d8a\":\"ed731235\",\"chunk-c5ac0cca\":\"605768e7\",\"chunk-c673e236\":\"156eaf15\",\"chunk-c75b8e6e\":\"73103030\",\"chunk-cae4df82\":\"7e75b1da\",\"chunk-d726e0f8\":\"c8fe5894\"}[chunkId] + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"chunk-47211100\":1,\"chunk-4b6066be\":1,\"chunk-7287e918\":1,\"chunk-ef28925c\":1,\"chunk-789b0e7e\":1,\"chunk-c673e236\":1,\"chunk-c75b8e6e\":1,\"chunk-d726e0f8\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"css/\" + ({}[chunkId]||chunkId) + \".\" + {\"chunk-23e41f57\":\"31d6cfe0\",\"chunk-276b085c\":\"31d6cfe0\",\"chunk-2d0a4854\":\"31d6cfe0\",\"chunk-2d0c0c66\":\"31d6cfe0\",\"chunk-2d0c4aa3\":\"31d6cfe0\",\"chunk-2d0cf4f3\":\"31d6cfe0\",\"chunk-2d0d2f25\":\"31d6cfe0\",\"chunk-2d0da5bf\":\"31d6cfe0\",\"chunk-2d213196\":\"31d6cfe0\",\"chunk-2d21f214\":\"31d6cfe0\",\"chunk-2d229214\":\"31d6cfe0\",\"chunk-2d22d746\":\"31d6cfe0\",\"chunk-40df6ae2\":\"31d6cfe0\",\"chunk-47211100\":\"6b9a8428\",\"chunk-479d738e\":\"31d6cfe0\",\"chunk-47dd42da\":\"31d6cfe0\",\"chunk-4b6066be\":\"57cc0d2f\",\"chunk-6e83591c\":\"31d6cfe0\",\"chunk-7287e918\":\"735a054c\",\"chunk-ef28925c\":\"f5aa9d10\",\"chunk-6f1c3bea\":\"31d6cfe0\",\"chunk-735deb8e\":\"31d6cfe0\",\"chunk-770e833a\":\"31d6cfe0\",\"chunk-77279526\":\"31d6cfe0\",\"chunk-789b0e7e\":\"2afc78bb\",\"chunk-bf843d8a\":\"31d6cfe0\",\"chunk-c5ac0cca\":\"31d6cfe0\",\"chunk-c673e236\":\"597cf4d0\",\"chunk-c75b8e6e\":\"40b63f23\",\"chunk-cae4df82\":\"31d6cfe0\",\"chunk-d726e0f8\":\"ac918284\"}[chunkId] + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tlinkTag.onload = resolve;\n \t\t\t\tlinkTag.onerror = function(event) {\n \t\t\t\t\tvar request = event && event.target && event.target.src || fullhref;\n \t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + request + \")\");\n \t\t\t\t\terr.request = request;\n \t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\treject(err);\n \t\t\t\t};\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tvar head = document.getElementsByTagName(\"head\")[0];\n \t\t\t\thead.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\tvar error = new Error('Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')');\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/ui/\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"", "<template>\r\n    <div id=\"app\">\r\n        <transition v-if=\"!$route.meta.NoNeedHome\" name=\"fade\"\r\n                    mode=\"out-in\">\r\n\r\n            <el-row class=\"container\">\r\n                <el-col :span=\"24\" class=\"header\">\r\n                    <el-col :span=\"10\" class=\"logo collapsedLogo\" :class=\"collapsed?'logo-collapse-width':'logo-width'\">\r\n                        <div @click=\"toindex\"> {{collapsed?sysNameShort:sysName}}</div>\r\n                    </el-col>\r\n                    <el-col :span=\"10\" class=\"logoban\">\r\n                        <div :class=\" collapsed?'tools collapsed':'tools'\" @click=\"collapse\">\r\n                            <i class=\"fa fa-align-justify\"></i>\r\n                        </div>\r\n\r\n                        <el-breadcrumb separator=\"/\" class=\"breadcrumb-inner collapsedLogo\">\r\n                            <el-breadcrumb-item v-for=\"item in $route.matched\" :key=\"item.path\">\r\n\r\n                                <span style=\"\"> {{ item.name }}</span>\r\n                            </el-breadcrumb-item>\r\n                        </el-breadcrumb>\r\n\r\n                    </el-col>\r\n                    <el-col :span=\"4\" class=\"userinfo\">\r\n\r\n                        <el-dropdown trigger=\"hover\">\r\n                    <span class=\"el-dropdown-link userinfo-inner\">\r\n                        {{sysUserName}}\r\n                        <img src=\"./assets/logo.png\" height=\"128\" width=\"128\"/>\r\n                    </span>\r\n                            <el-dropdown-menu slot=\"dropdown\">\r\n                                <el-dropdown-item @click.native=\"myNews\">\r\n                                    <el-badge :value=\"2\" class=\"item\" type=\"warning\">\r\n                                        我的消息\r\n                                    </el-badge>\r\n                                </el-dropdown-item>\r\n                                <el-dropdown-item @click.native=\"Setting\">设置</el-dropdown-item>\r\n                                <el-dropdown-item @click.native=\"goGithub\">Github</el-dropdown-item>\r\n                                <el-dropdown-item divided @click.native=\"logout\">退出登录</el-dropdown-item>\r\n                            </el-dropdown-menu>\r\n                        </el-dropdown>\r\n                    </el-col>\r\n                </el-col>\r\n                <el-col :span=\"24\" class=\"main\">\r\n\r\n\r\n                    <aside :class=\"collapsedClass \">\r\n                        <el-scrollbar style=\"height:100%;background: #2f3e52;\"  class=\"scrollbar-handle\">\r\n                            <el-menu \r\n                                     \r\n                                    :default-active=\"$route.path\"\r\n                                     class=\"el-menu-vertical-demo\" @open=\"handleopen\" @close=\"handleclose\"\r\n                                     @select=\"handleselect\"\r\n                                     unique-opened router :collapse=\"collapsed\"\r\n                                     background-color=\"#2f3e52\"\r\n                                     style=\"border-right: none;\"\r\n                                     text-color=\"#fff\"\r\n                                     active-text-color=\"#ffd04b\">\r\n                                <sidebar v-for=\"(menu,index) in routes\" :key=\"index\"\r\n                                         :item=\"menu\"/>\r\n                            </el-menu>\r\n\r\n\r\n                        </el-scrollbar>\r\n                    </aside>\r\n\r\n                    <el-col :span=\"24\" class=\"content-wrapper\"\r\n                            :class=\"collapsed?'content-collapsed':'content-expanded'\">\r\n                        <div class=\"tags\" v-if=\"showTags\">\r\n\r\n                            <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n                                <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\">\r\n                                    <router-link\r\n                                            v-for=\"(tag,index) in tagsList\"\r\n                                            ref=\"tag\"\r\n                                            :key=\"tag.path\"\r\n                                            :class=\"{'active': isActive(tag.path)}\"\r\n                                            :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\r\n                                            tag=\"span\"\r\n                                            @click.middle.native=\"closeTags(index)\"\r\n                                            class=\"tags-view-item\"\r\n                                    >\r\n                                        {{ tag.title }}\r\n                                        <span class=\"el-icon-close\" @click.prevent.stop=\"closeTags(index)\"/>\r\n                                    </router-link>\r\n                                </scroll-pane>\r\n\r\n                            </div>\r\n\r\n                            <!--<ul>-->\r\n                            <!--<li class=\"tags-li\" v-for=\"(item,index) in tagsList\" :class=\"{'active': isActive(item.path)}\" :key=\"index\">-->\r\n                            <!--<span class=\"tag-dot-inner\"></span>-->\r\n                            <!--<router-link :to=\"item.path\" class=\"tags-li-title\">-->\r\n                            <!--{{item.title}}-->\r\n                            <!--</router-link>-->\r\n                            <!--<span class=\"tags-li-icon\" @click=\"closeTags(index)\"><i class=\"el-icon-close\"></i></span>-->\r\n                            <!--</li>-->\r\n                            <!--</ul>-->\r\n\r\n                            <!-- 其他操作按钮 -->\r\n                            <div class=\"tags-close-box\">\r\n                                <el-dropdown @command=\"handleTags\">\r\n                                    <el-button size=\"mini\">\r\n                                        <i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                                    </el-button>\r\n                                    <el-dropdown-menu size=\"small\" slot=\"dropdown\">\r\n                                        <el-dropdown-item command=\"other\">关闭其他</el-dropdown-item>\r\n                                        <el-dropdown-item command=\"all\">关闭所有</el-dropdown-item>\r\n                                    </el-dropdown-menu>\r\n                                </el-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <transition name=\"fade\" mode=\"out-in\">\r\n                            <div class=\"content-az router-view-withly\">\r\n                                <!-- 含有母版的路由页面 -->\r\n                                <router-view></router-view>\r\n                            </div>\r\n                        </transition>\r\n                    </el-col>\r\n\r\n                </el-col>\r\n            </el-row>\r\n\r\n        </transition>\r\n\r\n        <transition v-else name=\"fade\" mode=\"out-in\">\r\n\r\n            <div  >\r\n                <router-view></router-view>\r\n            </div>\r\n        </transition>\r\n\r\n        <el-dialog title=\"Unread Messages\" :class=\"newsDialogCss\" :visible.sync=\"NewsVisible\" v-model=\"NewsVisible\"\r\n                   :close-on-click-modal=\"false\">\r\n            <div>\r\n                <el-tag\r\n                        v-for=\"tag in tagNews\"\r\n                        :key=\"tag.name\"\r\n                        closable\r\n                        class=\"tag-new\"\r\n                        :type=\"tag.type\">\r\n                    {{tag.name}}\r\n                </el-tag>\r\n\r\n            </div>\r\n        </el-dialog> \r\n       \r\n        <div class=\"v-modal \" @click=\"collapse\" v-show=\"SidebarVisible\" tabindex=\"0\" style=\"z-index: 2999;\"></div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import Sidebar from './components/Sidebar'\r\n    import ScrollPane from './components/ScrollPane'\r\n    import {getUserByToken} from './api/api';\r\n\r\n    import applicationUserManager from \"./Auth/applicationusermanager\";\r\n    import userAuth from \"./Auth/UserAuth\";\r\n\r\n    export default {\r\n        components: {Sidebar, ScrollPane},\r\n        mixins: [userAuth],\r\n        data() {\r\n            return {\r\n                sysName: 'BlogAdmin',\r\n                sysNameShort: 'BD',\r\n                NewsVisible: false,\r\n                collapsed: false,\r\n                zModalShadow: false,\r\n                SidebarVisible: false,\r\n                collapsedClass: 'menu-expanded',\r\n                sysUserName: '',\r\n                newsDialogCss: 'news-dialog',\r\n                sysUserAvatar: '',\r\n                tagsList: [],\r\n                form: {\r\n                    name: '',\r\n                    region: '',\r\n                    date1: '',\r\n                    date2: '',\r\n                    delivery: false,\r\n                    type: [],\r\n                    resource: '',\r\n                    desc: ''\r\n                },\r\n                routes: [],\r\n                tagNews: [\r\n                    {name: '前后端分离', type: ''},\r\n                    {name: 'vue.js', type: ''},\r\n                    {name: 'DDD领域驱动设计', type: 'success'},\r\n                    {name: '标签三', type: 'info'},\r\n                    {name: '欠费警告！', type: 'warning'},\r\n                    {name: '异常报告！', type: 'danger'}\r\n                ],\r\n                visible: false,\r\n                top: 0,\r\n                left: 0,\r\n                selectedTag: {},\r\n                affixTags: []\r\n\r\n            }\r\n        },\r\n        methods: {\r\n            gotappath(Path) {\r\n                console.log(Path);\r\n                this.$router.replace({\r\n                    path: Path,\r\n                });\r\n            },\r\n            handleOpen(key, keyPath) {\r\n                console.log(key, keyPath);\r\n            },\r\n            toindex() {\r\n                this.$router.replace({\r\n                    path: \"/\",\r\n                });\r\n            },\r\n            handleClose(key, keyPath) {\r\n                console.log(key, keyPath);\r\n            },\r\n            onSubmit() {\r\n                console.log('submit!');\r\n            },\r\n            myNews() {\r\n\r\n                this.newsDialogCss += ' show ';\r\n                this.NewsVisible = true;\r\n            },\r\n            handleopen() {\r\n                //console.log('handleopen');\r\n            },\r\n            handleclose() {\r\n                //console.log('handleclose');\r\n            },\r\n            handleselect: function (a, b) {\r\n            },\r\n            //退出登录\r\n            logout: function () {\r\n                var _this = this;\r\n                this.$confirm('确认退出吗?', '提示', {\r\n                    //type: 'warning'\r\n                }).then(() => {\r\n                    window.localStorage.removeItem('user');\r\n                    window.localStorage.removeItem('Token');\r\n                    window.localStorage.removeItem('TokenExpire');\r\n                    window.localStorage.removeItem('NavigationBar');\r\n                    window.localStorage.removeItem('refreshtime');\r\n                    window.localStorage.removeItem('router');\r\n                    sessionStorage.removeItem(\"Tags\");\r\n\r\n                    global.antRouter = [];\r\n\r\n                    this.tagsList = [];\r\n                    this.routes = [];\r\n                    this.$store.commit(\"saveTagsData\", \"\");\r\n\r\n                    if (global.IS_IDS4) {\r\n                        applicationUserManager.logout();\r\n                    } else {\r\n                        _this.$router.push('/login');\r\n                        //window.location.reload()\r\n                    }\r\n                    \r\n                }).catch(() => {\r\n\r\n                });\r\n\r\n\r\n            },\r\n            //设置\r\n            Setting: function () {\r\n                var _this = this;\r\n                _this.$router.push('/System/My');\r\n            },\r\n            //源码\r\n            goGithub() {\r\n                window.open(\"https://github.com/anjoy8/Blog.Admin\")\r\n            },\r\n            //折叠导航栏\r\n            collapse: function () {\r\n                this.collapsed = !this.collapsed;\r\n\r\n                if (this.collapsed) {\r\n                    this.collapsedClass = 'menu-collapsed';\r\n                    this.SidebarVisible = false;\r\n                } else {\r\n                    this.collapsedClass = 'menu-expanded';\r\n                    if(window.screen.width < 680){\r\n                        this.SidebarVisible = true;\r\n                    }\r\n                }\r\n                //记录折叠状态\r\n                window.localStorage.collapse = this.collapsed;\r\n            },\r\n            showMenu(i, status) {\r\n                this.$refs.menuCollapsed.getElementsByClassName('submenu-hook-' + i)[0].style.display = status ? 'block' : 'none';\r\n            },\r\n            isActive(path) {\r\n                return path === this.$route.fullPath;\r\n            },\r\n            // 关闭单个标签\r\n            closeTags(index) {\r\n                const delItem = this.tagsList.splice(index, 1)[0];\r\n                const item = this.tagsList[index] ? this.tagsList[index] : this.tagsList[index - 1];\r\n                if (item) {\r\n                    delItem.path === this.$route.fullPath && this.$router.push(item.path);\r\n\r\n                    this.$store.commit(\"saveTagsData\", JSON.stringify(this.tagsList));\r\n                } else {\r\n                    this.$router.push('/');\r\n                }\r\n            },\r\n            // 关闭全部标签\r\n            closeAll() {\r\n                this.tagsList = [];\r\n                this.$router.push('/');\r\n                sessionStorage.removeItem(\"Tags\");\r\n            },\r\n            // 关闭其他标签\r\n            closeOther() {\r\n                const curItem = this.tagsList.filter(item => {\r\n                    return item.path === this.$route.fullPath;\r\n                })\r\n                this.tagsList = curItem;\r\n\r\n\r\n                sessionStorage.setItem(\"Tags\", JSON.stringify(this.tagsList))\r\n            },\r\n            // 设置标签\r\n            setTags(route) {\r\n                if (!route.meta.NoTabPage) {\r\n                    const isExist = this.tagsList.some(item => {\r\n                        return item.path === route.fullPath;\r\n                    })\r\n                    !isExist && this.tagsList.push({\r\n                        title: route.meta.title,\r\n                        path: route.fullPath,\r\n                        // name: route.matched[1].components.default.name\r\n                    })\r\n                }\r\n            },\r\n            // 当关闭所有页面时隐藏\r\n            handleTags(command) {\r\n                command === 'other' ? this.closeOther() : this.closeAll();\r\n            },\r\n            getUserInfoByToken(token) {\r\n                var _this = this;\r\n                var loginParams = {token: token};\r\n                getUserByToken(loginParams).then(data => {\r\n                    this.logining = false;\r\n                    if (!data.success) {\r\n                        _this.$message({\r\n                            message: data.message,\r\n                            type: 'error'\r\n                        });\r\n                    } else {\r\n\r\n                        _this.$notify({\r\n                            type: \"success\",\r\n                            message: `欢迎管理员：${data.response.uRealName} ！`,\r\n                            duration: 3000\r\n                        });\r\n\r\n                        _this.sysUserName = data.response.uRealName;\r\n                        window.localStorage.user = JSON.stringify(data.response)\r\n                    }\r\n                });\r\n            },\r\n        },\r\n        mounted() {\r\n            console.log(this.$route)\r\n\r\n            var tags = sessionStorage.getItem('Tags') ? JSON.parse(sessionStorage.getItem('Tags')) : [];\r\n\r\n            if (tags && tags.length > 0) {\r\n                this.tagsList = tags;\r\n            }\r\n            \r\n\r\n            var NavigationBar = JSON.parse(window.localStorage.router ? window.localStorage.router : null);\r\n            // var NavigationBar = global.antRouter;\r\n\r\n            if (this.routes.length <= 0 && NavigationBar && NavigationBar.length >= 0) {\r\n                this.routes = NavigationBar;\r\n            }\r\n\r\n            // 折叠菜单栏\r\n            \r\n            \r\n        },\r\n        updated() {\r\n            var user = JSON.parse(window.localStorage.user ? window.localStorage.user : null);\r\n            if (user) {\r\n                this.sysUserName = user.uRealName || '老张的哲学';\r\n                this.sysUserAvatar = user.avatar || '../assets/user.png';\r\n            }\r\n\r\n\r\n            var NavigationBar = JSON.parse(window.localStorage.router ? window.localStorage.router : null);\r\n\r\n            if (NavigationBar && NavigationBar.length >= 0) {\r\n                if (this.routes.length <= 0 || (JSON.stringify(this.routes) != JSON.stringify((NavigationBar)))) {\r\n                    this.routes = NavigationBar;\r\n                }\r\n            }\r\n\r\n        },\r\n        computed: {\r\n            showTags() {\r\n                if (this.tagsList.length > 1) {\r\n                    this.$store.commit(\"saveTagsData\", JSON.stringify(this.tagsList));\r\n                }\r\n                return this.tagsList.length > 0;\r\n            }\r\n        },\r\n        watch: {\r\n            // 对router进行监听，每当访问router时，对tags的进行修改\r\n            $route: async function(newValue, from) {\r\n                \r\n                if (global.IS_IDS4) {\r\n                    await this.refreshUserInfo();\r\n                }\r\n\r\n                this.setTags(newValue);\r\n\r\n                const tags = this.$refs.tag\r\n                this.$nextTick(() => {\r\n                    if (tags) {\r\n                        for (const tag of tags) {\r\n                            if (tag.to.path === this.$route.path) {\r\n                                this.$refs.scrollPane.moveToTarget(tag, tags)\r\n                                // when query is different then update\r\n                                // if (tag.to.fullPath !== this.$route.fullPath) {\r\n                                //     this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n                                // }\r\n                                break\r\n                            }\r\n                        }\r\n                    }\r\n                })\r\n\r\n            },\r\n            \r\n        },\r\n        created() {\r\n            // 第一次进入页面时，修改tag的值\r\n            this.setTags(this.$route);\r\n            //读取折叠状态\r\n            if(window.localStorage.collapse == 'true'){\r\n                this.collapsed = false;\r\n                this.collapse();\r\n            }else{\r\n                this.collapsed = true;\r\n                this.collapse();\r\n            }\r\n        },\r\n    }\r\n\r\n</script>\r\n\r\n<style lang=\"css\">\r\n    @import \"./style/home.css\";\r\n\r\n    .el-menu-vertical-demo {\r\n        /*width: 230px;*/\r\n    }\r\n\r\n    .el-breadcrumb {\r\n        line-height: 60px !important;\r\n    }\r\n</style>\r\n\r\n<style>\r\n    .menu-collapsed .el-icon-arrow-right:before {\r\n        display: none;\r\n    }\r\n\r\n    .tags {\r\n        position: relative;\r\n        overflow: hidden;\r\n        border: 1px solid #f0f0f0;\r\n        background: #f0f0f0;\r\n    }\r\n\r\n    .tags ul {\r\n        box-sizing: border-box;\r\n        width: 100%;\r\n        height: 100%;\r\n        padding: 0;\r\n        margin: 0;\r\n        display: none;\r\n    }\r\n\r\n    .tags-li {\r\n        float: left;\r\n        margin: 3px 5px 2px 3px;\r\n        border-radius: 3px;\r\n        font-size: 13px;\r\n        overflow: hidden;\r\n        height: 23px;\r\n        line-height: 23px;\r\n        border: 1px solid #e9eaec;\r\n        background: #fff;\r\n        padding: 3px 5px 4px 12px;\r\n        vertical-align: middle;\r\n        color: #666;\r\n        -webkit-transition: all .3s ease-in;\r\n        transition: all .3s ease-in;\r\n    }\r\n\r\n    .tags-li-icon {\r\n        cursor: pointer;\r\n    }\r\n\r\n    .tags-li:not(.active):hover {\r\n        background: #f8f8f8;\r\n    }\r\n\r\n    .tags-li-title {\r\n        float: left;\r\n        max-width: 80px;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        text-overflow: ellipsis;\r\n        margin-right: 5px;\r\n        color: #666;\r\n        text-decoration: none;\r\n    }\r\n\r\n    .tags-li.active {\r\n        /*color: #fff;*/\r\n        /*border: 1px solid #10B9D3;*/\r\n        /*background-color: #10B9D3;*/\r\n    }\r\n\r\n    .tags-li.active .tags-li-title {\r\n        /*color: #fff;*/\r\n    }\r\n\r\n    .tags-close-box {\r\n        box-sizing: border-box;\r\n        text-align: center;\r\n        z-index: 10;\r\n        float: right;\r\n        margin-right: 1px;\r\n        line-height: 2;\r\n    }\r\n\r\n</style>\r\n<style>\r\n    /*.logoban{*/\r\n    /*width: auto !important;*/\r\n    /*}*/\r\n    .news-dialog {\r\n\r\n        background: #fff;\r\n        z-index: 3000 !important;\r\n        position: fixed;\r\n        height: 100vh;\r\n        width: 100%;\r\n        max-width: 260px;\r\n        top: 60px !important;\r\n        left: 0 !important;;\r\n        -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);\r\n        box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);\r\n        -webkit-transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n        transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n        -webkit-transform: translate(100%);\r\n        z-index: 40000;\r\n        left: auto !important;;\r\n        right: 0 !important;;\r\n        transform: translate(0);\r\n    }\r\n\r\n    .news-dialog .el-dialog {\r\n        margin: auto !important;\r\n        -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .3);\r\n        box-shadow: none;\r\n        width: 100%;\r\n    }\r\n\r\n    .news-dialog.show {\r\n        transform: translate(0);\r\n    }\r\n\r\n    .tag-new {\r\n        width: 100%;\r\n        margin: 10px 0;\r\n    }\r\n\r\n    @media screen and (max-width: 680px) {\r\n\r\n        .collapsedLogo {\r\n            display: none;\r\n        }\r\n\r\n        .el-dialog {\r\n            width: 90% !important;\r\n        }\r\n\r\n        .content-expanded {\r\n            max-width: 100% !important;\r\n            width: 100% !important;\r\n            /* max-height: calc(100% - 60px); */\r\n        }\r\n        .content-collapsed{\r\n            max-width: 100% !important;\r\n            width: 100% !important;\r\n        }\r\n        .menu-collapsed{\r\n            display: none;\r\n        }\r\n        .menu-expanded{\r\n            position: fixed;\r\n            z-index: 3000;\r\n            height: 100vh !important;\r\n            width: 210px !important;\r\n            overflow-y:scroll !important;\r\n            overflow-x:scroll !important;\r\n        }\r\n        .scrollbar-handle{\r\n            margin-bottom: 60px;\r\n        }\r\n        .mobile-ex {\r\n            background: #fff;\r\n            z-index: 3000;\r\n            position: fixed;\r\n            height: 100vh;\r\n            width: 100%;\r\n            max-width: 260px;\r\n            top: 0;\r\n            left: 0;\r\n            -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);\r\n            box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);\r\n            -webkit-transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n            transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n            -webkit-transform: translate(100%);\r\n            z-index: 40000;\r\n            left: auto;\r\n            right: 0;\r\n            transform: translate(100%);\r\n        }\r\n\r\n        .mobile-ex.menu-collapsed-mobile {\r\n            transform: translate(0);\r\n        }\r\n\r\n        .el-menu--collapse {\r\n            width: 100% !important;\r\n        }\r\n\r\n        .el-date-editor.el-input, .el-date-editor.el-input__inner, .el-cascader.el-cascader--medium {\r\n            width: 100% !important;\r\n        }\r\n\r\n        .toolbar.roles {\r\n            width: 100% !important;\r\n        }\r\n\r\n        .toolbar.perms {\r\n            width: 800px !important;\r\n        }\r\n\r\n        .toolbar.perms .box-card {\r\n            width: 100% !important;\r\n        }\r\n\r\n        .login-container {\r\n            width: 300px !important;\r\n        }\r\n\r\n        .count-test label {\r\n\r\n        }\r\n\r\n        .content-wrapper .tags {\r\n            margin: 0px;\r\n            padding: 0px;\r\n        }\r\n\r\n        .activeuser{\r\n            display: none !important;\r\n        }\r\n\r\n    }\r\n</style>\r\n\r\n<style>\r\n\r\n    .tags-view-container {\r\n        height: 34px;\r\n        width: calc(100% - 60px);\r\n        /*background: #fff;*/\r\n        /*border-bottom: 1px solid #d8dce5;*/\r\n        /*box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);*/\r\n        float: left;\r\n    }\r\n\r\n    .tags-view-container .tags-view-wrapper .tags-view-item {\r\n        display: inline-block;\r\n        position: relative;\r\n        cursor: pointer;\r\n        height: 26px;\r\n        line-height: 26px;\r\n        border: 1px solid #d8dce5;\r\n        color: #495060;\r\n        background: #fff;\r\n        padding: 0 8px;\r\n        font-size: 12px;\r\n        margin-left: 5px;\r\n        margin-top: 4px;\r\n    }\r\n\r\n    .tags-view-container .tags-view-wrapper .tags-view-item:first-of-type {\r\n        margin-left: 15px;\r\n    }\r\n\r\n    .tags-view-container .tags-view-wrapper .tags-view-item:last-of-type {\r\n        margin-right: 15px;\r\n    }\r\n\r\n    .tags-view-container .tags-view-wrapper .tags-view-item.active {\r\n        /*background-color: #42b983;*/\r\n        /*color: #fff;*/\r\n        /*border-color: #42b983;*/\r\n    }\r\n\r\n    .tags-view-container .tags-view-wrapper .tags-view-item.active::before {\r\n        content: \"\";\r\n        background: #2d8cf0;\r\n        display: inline-block;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 50%;\r\n        position: relative;\r\n        margin-right: 2px;\r\n    }\r\n\r\n    .tags-view-container .contextmenu {\r\n        margin: 0;\r\n        background: #fff;\r\n        z-index: 3000;\r\n        position: absolute;\r\n        list-style-type: none;\r\n        padding: 5px 0;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n        font-weight: 400;\r\n        color: #333;\r\n        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);\r\n    }\r\n\r\n    .tags-view-container .contextmenu li {\r\n        margin: 0;\r\n        padding: 7px 16px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    .tags-view-container .contextmenu li:hover {\r\n        background: #eee;\r\n    }\r\n</style>\r\n\r\n<style>\r\n    .tags-view-wrapper .tags-view-item .el-icon-close {\r\n        width: 16px;\r\n        height: 16px;\r\n        vertical-align: 2px;\r\n        border-radius: 50%;\r\n        text-align: center;\r\n        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n        transform-origin: 100% 50%;\r\n    }\r\n\r\n    .tags-view-wrapper .tags-view-item .el-icon-close:before {\r\n        transform: scale(0.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n    }\r\n\r\n    .tags-view-wrapper .tags-view-item .el-icon-close:hover {\r\n        background-color: #ef2b74;\r\n        color: #fff;\r\n    }\r\n</style>\r\n", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./APIDoc.vue?vue&type=style&index=0&id=1759f995&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./APIDoc.vue?vue&type=style&index=0&id=1759f995&scoped=true&lang=css&\"", "import { UserManager } from 'oidc-client'\r\n\r\n// 使用id4更改这里1\r\nclass ApplicationUserManager extends UserManager {\r\n  constructor () {\r\n    super({\r\n      authority: 'https://ids.neters.club',\r\n      client_id: 'blogadminjs',\r\n      redirect_uri: 'https://vueadmin.neters.club/callback',\r\n      response_type: 'id_token token',\r\n      scope: 'openid profile roles blog.core.api',\r\n      post_logout_redirect_uri: 'https://vueadmin.neters.club'\r\n    })\r\n  }\r\n\r\n  async login () {\r\n    await this.signinRedirect()\r\n    return this.getUser()\r\n  }\r\n\r\n  async logout () {\r\n    return this.signoutRedirect()\r\n  }\r\n}\r\n\r\nconst applicationUserManager = new ApplicationUserManager()\r\nexport { applicationUserManager as default }\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-scrollbar',{staticClass:\"scrollbar-handle\",staticStyle:{\"height\":\"100%\"}},[_c('keep-alive',[(_vm.$route.meta.keepAlive)?_c('router-view'):_vm._e()],1),(!_vm.$route.meta.keepAlive)?_c('router-view'):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=3deda5b2&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"Layout.vue\"\nexport default component.exports", "import applicationUserManager from \"./applicationusermanager\";\r\nconst userAuth = {\r\n  data() {\r\n    return {\r\n      user: {\r\n        name: \"\",\r\n        isAuthenticated: false\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    async refreshUserInfo() {\r\n      const user = await applicationUserManager.getUser();\r\n      if (user) {\r\n        this.user.name = user.profile.name;\r\n        this.user.isAuthenticated = true;\r\n      } else {\r\n        this.user.name = \"\";\r\n        this.user.isAuthenticated = false;\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.refreshUserInfo();\r\n  }\r\n};\r\nexport default userAuth;\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"thanks\"},[_c('iframe',{attrs:{\"src\":\"https://apk.neters.club\",\"scrolling\":\"auto\",\"id\":\"apidoc\",\"width\":\"100%\",\"frameborder\":\"0\"}})])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"thanks\">\r\n    <!-- <iframe\r\n      src=\"https://apk.neters.club\"\r\n      id=\"apidoc\"\r\n      scrolling=\"no\"\r\n      frameborder=\"0\"\r\n      style=\"width:100%;\"\r\n    ></iframe> -->\r\n\r\n    <iframe\r\n      src=\"https://apk.neters.club\"\r\n      scrolling=\"auto\"\r\n      id=\"apidoc\"\r\n      width=\"100%\"\r\n      frameborder=\"0\"\r\n    ></iframe>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"APIDoc\",\r\n  data() {\r\n    return {};\r\n  },\r\n  mounted() {\r\n    /**\r\n     * iframe-宽高自适应显示\r\n     */\r\n    function changeMobsfIframe() {\r\n      const apidoc = document.getElementById(\"apidoc\");\r\n      const deviceWidth = document.body.clientWidth;\r\n      const deviceHeight = document.body.clientHeight;\r\n      //   apidoc.style.width = Number(deviceWidth) - 120 + \"px\"; //数字是页面布局宽度差值\r\n      apidoc.style.height = Number(deviceHeight) - 128 + \"px\"; //数字是页面布局高度差\r\n    }\r\n\r\n    changeMobsfIframe();\r\n\r\n    window.onresize = function() {\r\n      changeMobsfIframe();\r\n    };\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.thanks {\r\n  text-align: center;\r\n  font-size: 30px;\r\n  color: darkgray;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./APIDoc.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./APIDoc.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./APIDoc.vue?vue&type=template&id=1759f995&scoped=true&\"\nimport script from \"./APIDoc.vue?vue&type=script&lang=js&\"\nexport * from \"./APIDoc.vue?vue&type=script&lang=js&\"\nimport style0 from \"./APIDoc.vue?vue&type=style&index=0&id=1759f995&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1759f995\",\n  null\n  \n)\n\ncomponent.options.__file = \"APIDoc.vue\"\nexport default component.exports", "import axios from 'axios';\r\n// import router from '../routerManuaConfig'\r\nimport router from '../router/index'\r\nimport store from \"../store\";\r\nimport Vue from 'vue';\r\n\r\nimport applicationUserManager from \"../Auth/applicationusermanager\";\r\n\r\nlet base = '';\r\n// 如果是IIS部署，用这个，因为 IIS 只能是 CORS 跨域，不能代理\r\n// let base = process.env.NODE_ENV==\"production\"? 'http://localhost:8081':'';\r\n\r\n\r\n// 请求延时\r\naxios.defaults.timeout = 20000\r\n\r\nvar storeTemp = store;\r\naxios.interceptors.request.use(\r\n    config => {\r\n        var curTime = new Date()\r\n        var expiretime = new Date(Date.parse(storeTemp.state.tokenExpire))\r\n\r\n        if (storeTemp.state.token && (curTime < expiretime && storeTemp.state.tokenExpire)) {\r\n            // 判断是否存在token，如果存在的话，则每个http header都加上token\r\n            config.headers.Authorization = \"Bearer \" + storeTemp.state.token;\r\n        }\r\n\r\n        saveRefreshtime();\r\n\r\n        return config;\r\n    },\r\n    err => {\r\n        return Promise.reject(err);\r\n    }\r\n);\r\n\r\n// http response 拦截器\r\naxios.interceptors.response.use(\r\n    response => { \r\n        return response; \r\n    },\r\n    error => {\r\n        let errInfo = { success: false, message: \"错误\" }\r\n        // 超时请求处理\r\n        var originalRequest = error.config;\r\n        if(error.code == 'ECONNABORTED' && error.message.indexOf('timeout')!=-1 && !originalRequest._retry){\r\n            errInfo.message = \"请求超时！\"; \r\n            originalRequest._retry = true \r\n        }else if (error.response) {\r\n            if (error.response.status == 401) {\r\n                var curTime = new Date()\r\n                var refreshtime = new Date(Date.parse(window.localStorage.refreshtime))\r\n                // 在用户操作的活跃期内\r\n                if (window.localStorage.refreshtime && (curTime <= refreshtime)) {\r\n                    return  refreshToken({token: window.localStorage.Token}).then((res) => {\r\n                        if (res.success) {\r\n                            Vue.prototype.$message({\r\n                                message: 'refreshToken success! loading data...',\r\n                                type: 'success'\r\n                            });\r\n\r\n                            store.commit(\"saveToken\", res.response.token);\r\n\r\n                            var curTime = new Date();\r\n                            var expiredate = new Date(curTime.setSeconds(curTime.getSeconds() + res.response.expires_in));\r\n                            store.commit(\"saveTokenExpire\", expiredate);\r\n\r\n                            error.config.__isRetryRequest = true;\r\n                            error.config.headers.Authorization = 'Bearer ' + res.response.token;\r\n                            return axios(error.config);\r\n                        } else {\r\n                            // 刷新token失败 清除token信息并跳转到登录页面\r\n                            ToLogin()\r\n                        }\r\n                    });\r\n                } else {\r\n                    // 返回 401，并且不知用户操作活跃期内 清除token信息并跳转到登录页面\r\n                    ToLogin()\r\n                }\r\n\r\n            }\r\n            // 403 无权限\r\n            else if (error.response.status == 403) {\r\n               errInfo.message = \"失败！该操作无权限\"; \r\n            }\r\n            // 429 ip限流\r\n            else if (error.response.status == 429) {\r\n                errInfo.message = \"刷新次数过多，请稍事休息重试！\";\r\n            }else if (error.response.status == 404) {\r\n                // 404 不存在\r\n                errInfo.message = \"失败！访问接口不存在\";\r\n           }else if (error.response.status == 500) {\r\n               // 500 服务器异常\r\n               errInfo.message = \"失败！服务器异常\"; \r\n           }else if (error.response.status == 405) {\r\n               // 405 请求http方法错误\r\n               errInfo.message = \"失败！请求http方法错误\";  \r\n           }else if (error.response.status == 415) {\r\n               // 415 参数没有指定Body还是Query\r\n               errInfo.message = \"失败！参数没有指定Body还是Query\";   \r\n           }else {\r\n               //其他错误参数\r\n                errInfo.message = '失败！请求错误' + error.response.status;    \r\n           }\r\n        }else{\r\n            errInfo.message = \"失败！服务器断开\";  \r\n        }\r\n        Vue.prototype.$message({\r\n            message: errInfo.message,\r\n            type: 'error'\r\n        });\r\n        return errInfo; // 返回接口返回的错误信息\r\n    }\r\n);\r\n\r\n\r\nexport const BaseApiUrl =base;\r\n\r\n// 登录\r\nexport const requestLogin = params => {\r\n    return axios.get(`${base}/api/login/jwttoken3.0`, {params: params}).then(res => res.data);\r\n};\r\nexport const requestLoginMock = params => { return axios.post(`${base}/login`, params).then(res => res.data); };\r\n\r\nexport const refreshToken = params => {\r\n    return axios.get(`${base}/api/login/RefreshToken`, {params: params}).then(res => res.data);\r\n};\r\n\r\nexport const saveRefreshtime = params => {\r\n\r\n    let nowtime = new Date();\r\n    let lastRefreshtime = window.localStorage.refreshtime ? new Date(window.localStorage.refreshtime) : new Date(-1);\r\n    let expiretime = new Date(Date.parse(window.localStorage.TokenExpire))\r\n\r\n    let refreshCount=1;//滑动系数\r\n    if (lastRefreshtime >= nowtime) {\r\n        lastRefreshtime=nowtime>expiretime ? nowtime:expiretime;\r\n        lastRefreshtime.setMinutes(lastRefreshtime.getMinutes() + refreshCount);\r\n        window.localStorage.refreshtime = lastRefreshtime;\r\n    }else {\r\n        window.localStorage.refreshtime = new Date(-1);\r\n    }\r\n};\r\n const ToLogin = params => {\r\n     \r\n     store.commit(\"saveToken\", \"\");\r\n     store.commit(\"saveTokenExpire\", \"\");\r\n     store.commit(\"saveTagsData\", \"\");\r\n     window.localStorage.removeItem('user');\r\n     window.localStorage.removeItem('NavigationBar');\r\n\r\n     \r\n                \r\n    if (global.IS_IDS4) {\r\n        applicationUserManager.login();\r\n    } else {\r\n        router.replace({\r\n            path: \"/login\",\r\n            query: {redirect: router.currentRoute.fullPath}\r\n        }); \r\n        //window.location.reload()\r\n    }\r\n};\r\n\r\nexport const getUserByToken = params => {\r\n    return axios.get(`${base}/api/user/getInfoByToken`, {params: params}).then(res => res.data);\r\n};\r\n\r\n\r\nexport function testapi2() {\r\n    console.log('api is ok.')\r\n}\r\n\r\nexport const testapi = pa => {\r\n    console.log('api is ok.')\r\n}\r\n\r\n// 用户管理\r\nexport const getUserListPage = params => {\r\n    return axios.get(`${base}/api/user/get`, {params: params});\r\n};\r\nexport const removeUser = params => {\r\n    return axios.delete(`${base}/api/user/delete`, {params: params});\r\n};\r\nexport const editUser = params => {\r\n    return axios.put(`${base}/api/user/put`, params);\r\n};\r\nexport const addUser = params => {\r\n    return axios.post(`${base}/api/user/post`, params);\r\n};\r\nexport const batchRemoveUser = params => {\r\n    return axios.delete(`${base}/api/Claims/BatchDelete`, {params: params});//没做\r\n};\r\n\r\n// 角色管理\r\nexport const getRoleListPage = params => {\r\n    return axios.get(`${base}/api/role/get`, {params: params});\r\n};\r\nexport const removeRole = params => {\r\n    return axios.delete(`${base}/api/role/delete`, {params: params});\r\n};\r\nexport const editRole = params => {\r\n    return axios.put(`${base}/api/role/put`, params);\r\n};\r\nexport const addRole = params => {\r\n    return axios.post(`${base}/api/role/post`, params);\r\n};\r\n\r\n// 接口模块管理\r\nexport const getModuleListPage = params => {\r\n    return axios.get(`${base}/api/module/get`, {params: params});\r\n};\r\nexport const removeModule = params => {\r\n    return axios.delete(`${base}/api/module/delete`, {params: params});\r\n};\r\nexport const editModule = params => {\r\n    return axios.put(`${base}/api/module/put`, params);\r\n};\r\nexport const addModule = params => {\r\n    return axios.post(`${base}/api/module/post`, params);\r\n};\r\n\r\n\r\n// 菜单模块管理\r\nexport const getPermissionListPage = params => {\r\n    return axios.get(`${base}/api/permission/get`, {params: params});\r\n};\r\nexport const getPermissionTreeTable = params => {\r\n    return axios.get(`${base}/api/permission/GetTreeTable`, {params: params});\r\n};\r\nexport const removePermission = params => {\r\n    return axios.delete(`${base}/api/permission/delete`, {params: params});\r\n};\r\nexport const editPermission = params => {\r\n    return axios.put(`${base}/api/permission/put`, params);\r\n};\r\nexport const addPermission = params => {\r\n    return axios.post(`${base}/api/permission/post`, params);\r\n};\r\nexport const getPermissionTree = params => {\r\n    return axios.get(`${base}/api/permission/getpermissiontree`, {params: params});\r\n};\r\nexport const getPermissionIds = params => {\r\n    return axios.get(`${base}/api/permission/GetPermissionIdByRoleId`, {params: params});\r\n};\r\n\r\nexport const addRolePermission = params => {\r\n    return axios.post(`${base}/api/permission/Assign`, params);\r\n};\r\nexport const getNavigationBar = params => {\r\n    return axios.get(`${base}/api/permission/GetNavigationBar`, {params: params}).then(res => res.data);\r\n};\r\n\r\n// Bug模块管理\r\nexport const getBugListPage = params => {\r\n    return axios.get(`${base}/api/TopicDetail/get`, {params: params});\r\n};\r\nexport const removeBug = params => {\r\n    return axios.delete(`${base}/api/TopicDetail/delete`, {params: params});\r\n};\r\nexport const editBug = params => {\r\n    return axios.put(`${base}/api/TopicDetail/update`, params);\r\n};\r\nexport const addBug = params => {\r\n    return axios.post(`${base}/api/TopicDetail/post`, params);\r\n};\r\n\r\n\r\n// 博客模块管理\r\nexport const getBlogListPage = params => {\r\n    return axios.get(`${base}/api/Blog`, {params: params});\r\n};\r\nexport const getBlogDeatil = params => {\r\n    return axios.get(`${base}/api/Blog/DetailNuxtNoPer`, {params: params});\r\n};\r\nexport const editBlog = params => {\r\n    return axios.put(`${base}/api/Blog/update`, params);\r\n};\r\nexport const removeBlog = params => {\r\n    return axios.delete(`${base}/api/Blog/delete`, {params: params});\r\n};\r\n\r\n// 日志\r\nexport const getLogs = params => {\r\n    return axios.get(`${base}/api/Monitor/get`, {params: params});\r\n};\r\nexport const getRequestApiinfoByWeek = params => {\r\n    return axios.get(`${base}/api/Monitor/GetRequestApiinfoByWeek`, {params: params});\r\n};\r\nexport const getAccessApiByDate = params => {\r\n    return axios.get(`${base}/api/Monitor/GetAccessApiByDate`, {params: params});\r\n};\r\nexport const getAccessApiByHour = params => {\r\n    return axios.get(`${base}/api/Monitor/GetAccessApiByHour`, {params: params});\r\n};\r\nexport const getServerInfo = params => {\r\n    return axios.get(`${base}/api/Monitor/Server`, {params: params});\r\n};\r\nexport const getAccessLogs = params => {\r\n    return axios.get(`${base}/api/Monitor/GetAccessLogs`, {params: params});\r\n};\r\nexport const getIds4UsersGrow = params => {\r\n    return axios.get(`${base}/api/Monitor/GetIds4Users`, {params: params});\r\n};\r\nexport const getActiveUsers = params => {\r\n    return axios.get(`${base}/api/Monitor/GetActiveUsers`, {params: params});\r\n};\r\n\r\n\r\n// Task管理\r\nexport const getTaskListPage = params => {\r\n    return axios.get(`${base}/api/TasksQz/get`, {params: params});\r\n};\r\nexport const removeTask = params => {\r\n    return axios.delete(`${base}/api/TasksQz/delete`, {params: params});\r\n};\r\nexport const editTask = params => {\r\n    return axios.put(`${base}/api/TasksQz/put`, params);\r\n};\r\nexport const addTask = params => {\r\n    return axios.post(`${base}/api/TasksQz/post`, params);\r\n};\r\n\r\nexport const startJob = params => {\r\n    return axios.get(`${base}/api/TasksQz/StartJob`, {params: params});\r\n};\r\nexport const stopJob = params => {\r\n    return axios.get(`${base}/api/TasksQz/StopJob`, {params: params});\r\n};\r\nexport const reCovery = params => {\r\n    return axios.get(`${base}/api/TasksQz/ReCovery`, {params: params});\r\n};\r\nexport const pauseJob = params => {\r\n    return axios.get(`${base}/api/TasksQz/PauseJob`, {params: params});\r\n};\r\nexport const resumeJob = params => {\r\n    return axios.get(`${base}/api/TasksQz/ResumeJob`, {params: params});\r\n};\r\nexport const getTaskNameSpace = params => {\r\n    return axios.get(`${base}/api/TasksQz/GetTaskNameSpace`, {params: params});\r\n};\r\n\r\n// ids4\r\nexport const getAchieveUsers_IS4 = params => {\r\n    return axios.get(`${base}/is4api/GetAchieveUsers`, {params: params});\r\n};\r\n\r\n\r\n//微信公众号\r\nexport const getWeChatAccount = params => {\r\n    return axios.get(`${base}/api/WeChatConfig/get`, { params: params });\r\n};\r\nexport const removeWeChatAccount = params => {\r\n    return axios.delete(`${base}/api/WeChatConfig/delete`, { params: params });\r\n};\r\nexport const batchDeleteChatAccount = params => {\r\n    return axios.delete(`${base}/api/WeChatConfig/BatchDelete`, { params: params });\r\n};\r\nexport const addWeChatAccount = params => {\r\n    return axios.post(`${base}/api/WeChatConfig/post`, params);\r\n};\r\nexport const updateWeChatAccount = params => {\r\n    return axios.put(`${base}/api/WeChatConfig/put`, params);\r\n};\r\nexport const getWeChatTemplate = params => {\r\n    return axios.get(`${base}/api/WeChat/GetTemplate`, { params: params });//获取模板\r\n};\r\nexport const refreshWeChatToken = params => {\r\n    return axios.get(`${base}/api/WeChat/RefreshToken`, { params: params });//更新Token\r\n};\r\nexport const getWeChatMenu = params => {\r\n    return axios.get(`${base}/api/WeChat/GetMenu`, { params: params });//获取微信菜单\r\n};\r\nexport const updateWeChatMenu = params => {\r\n    return axios.put(`${base}/api/WeChat/updateMenu`, params);//更新微信菜单\r\n};\r\nexport const getWeChatCompany = params => {\r\n    return axios.get(`${base}/api/WeChatCompany/get`, { params: params });\r\n};\r\nexport const removeWeChatCompany = params => {\r\n    return axios.delete(`${base}/api/WeChatCompany/delete`, { params: params });\r\n};\r\nexport const batchDeleteWeChatCompany = params => {\r\n    return axios.delete(`${base}/api/WeChatCompany/BatchDelete`, { params: params });\r\n};\r\nexport const addWeWeChatCompany = params => {\r\n    return axios.post(`${base}/api/WeChatCompany/post`, params);\r\n};\r\nexport const updateWeChatCompany = params => {\r\n    return axios.put(`${base}/api/WeChatCompany/put`, params);\r\n};\r\nexport const getWeChatPushLog = params => {\r\n    return axios.get(`${base}/api/WeChatPushLog/get`, { params: params });\r\n}; \r\nexport const getWeChatSubUser = params => {\r\n    return axios.get(`${base}/api/WeChat/GetSubUsers`, { params: params });\r\n};\r\nexport const getWeChatBindUser = params => {\r\n    return axios.get(`${base}/api/WeChatSub/get`, { params: params });\r\n};\r\nexport const pushTestMsg = params => {\r\n    return axios.post(`${base}/api/WeChat/PushTxtMsg`, params);\r\n};\r\nexport const pushCardMsg = params => {\r\n    return axios.post(`${base}/api/WeChat/PushCardMsg`, params);\r\n};\r\n\r\n// 部门模块管理\r\nexport const getDepartmentListPage = params => {\r\n    return axios.get(`${base}/api/department/get`, {params: params});\r\n};\r\nexport const getDepartmentTreeTable = params => {\r\n    return axios.get(`${base}/api/department/getTreeTable`, {params: params});\r\n};\r\n\r\nexport const removeDepartment = params => {\r\n    return axios.delete(`${base}/api/department/delete`, {params: params});\r\n};\r\nexport const editDepartment = params => {\r\n    return axios.put(`${base}/api/department/put`, params);\r\n};\r\nexport const addDepartment = params => {\r\n    return axios.post(`${base}/api/department/post`, params);\r\n};\r\nexport const getDepartmentTree = params => {\r\n    return axios.get(`${base}/api/department/getDepartmentTree`, {params: params});\r\n};", "<template>\r\n  <div style=\"margin-top: 30px\">\r\n    <el-row class=\"panel-group\">\r\n      <el-col\r\n        class=\"card-panel-col\"\r\n        style=\"float: left; width: calc(100% - 405px); margin: 0\"\r\n      >\r\n        <el-card class=\"welcome-card activeuser note\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>今日活跃用户</span>\r\n          </div>\r\n\r\n          <div\r\n            class=\"bg-color-sub\"\r\n            style=\"background: rgb(236, 245, 255) none repeat scroll 0% 0%\"\r\n          >\r\n            <div\r\n              v-for=\"(item, index) in welcomeInitData.activeUsers\"\r\n              :key=\"item.user + index\"\r\n              class=\"bg-blue-sub-item\"\r\n              :style=\"getBck(index)\"\r\n            >\r\n              <el-badge\r\n                :value=\"item.count > 9999 ? '9999+' : item.count\"\r\n                class=\"item\"\r\n                :type=\"getTypeName(item.count)\"\r\n              >\r\n                <label :title=\"item.user\" class=\"acc-user\">{{ item.user }}</label>\r\n              </el-badge>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <div class=\"statistical-cus\">\r\n        <el-col class=\"card-panel-col\">\r\n          <div class=\"card-panel\">\r\n            <div class=\"card-panel-description\">\r\n              <div class=\"card-panel-text\">今日活跃</div>\r\n              <span data-v-6723c96e class=\"card-acuser-num\">{{\r\n                welcomeInitData.activeUserCount > 9\r\n                  ? welcomeInitData.activeUserCount\r\n                  : \"0\" + welcomeInitData.activeUserCount\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col class=\"card-panel-col\">\r\n          <div class=\"card-panel\">\r\n            <div class=\"card-panel-description\">\r\n              <div class=\"card-panel-text\">今日新增</div>\r\n              <span data-v-6723c96e class=\"card-acuser-num\">{{\r\n                lineChartDataIDS4.today > 9\r\n                  ? lineChartDataIDS4.today\r\n                  : \"0\" + lineChartDataIDS4.today\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n        <el-col class=\"card-panel-col\">\r\n          <div class=\"card-panel extoday\" @click=\"toLogs\">\r\n            <div class=\"card-panel-description\">\r\n              <div class=\"card-panel-text\">今日异常</div>\r\n              <span data-v-6723c96e class=\"card-panel-num\">{{\r\n                welcomeInitData.errorCount > 9\r\n                  ? welcomeInitData.errorCount\r\n                  : \"0\" + welcomeInitData.errorCount\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </div>\r\n    </el-row>\r\n    <el-row class=\"panel-group\">\r\n      <el-col\r\n        class=\"card-panel-col\"\r\n        style=\"float: left; width: 100%; margin: 0\"\r\n      >\r\n        <el-card class=\"welcome-card activeuser note\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>本月活跃用户<span style=\"color: #ccc;font-size: 14px;\">（使用任务调度，1分钟统计一次）</span></span>\r\n          </div>\r\n\r\n          <div\r\n            class=\"bg-color-sub\"\r\n            style=\"background: rgb(236, 245, 255) none repeat scroll 0% 0%\"\r\n          >\r\n            <div\r\n              v-for=\"(item, index) in welcomeInitData.activeCount\"\r\n              :key=\"item.user + index\"\r\n              class=\"bg-blue-sub-item-m\"\r\n              :class=\"item.count > 9999 ? 'amazing':''\"\r\n              :style=\"getBck(index)\"\r\n            >\r\n              <el-badge\r\n                :value=\"item.count > 999999 ? '999999+' : item.count\"\r\n                class=\"item\"\r\n                :type=\"getTypeName(item.count)\"\r\n              >\r\n                <label :title=\"item.user\" class=\"acc-user\">{{ item.user }}</label>\r\n              </el-badge>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <el-card\r\n      class=\"welcome-card note note50\"\r\n      style=\"width: calc(49% - 10px); margin-right: 10px\"\r\n    >\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>操作指南</span>\r\n      </div>\r\n      <div class=\"text item\">\r\n        <i class=\"el-icon-edit\"></i\r\n        >、在vue.config.js中配置项目端口号，以及代理后端API项目域名。\r\n      </div>\r\n      <div class=\"text item\">\r\n        <i class=\"el-icon-edit\"></i>、在global.js中配置授权方案global.IS_IDS4。\r\n      </div>\r\n      <div class=\"text item\">\r\n        <i class=\"el-icon-edit\"></i>、动态添加页面以及权限配置，看右侧两个动图。\r\n      </div>\r\n      <div class=\"text item\">\r\n        <i class=\"el-icon-edit\"></i>、更多内容，查看官方文档：\r\n        <a href=\"http://vueadmin.neters.club/.doc/\" target=\"_blank\"\r\n          >http://vueadmin.neters.club/.doc/</a\r\n        >。\r\n      </div>\r\n    </el-card>\r\n    <el-card\r\n      class=\"welcome-card note50\"\r\n      style=\"width: 49%; margin: 0; font-size: 14px\"\r\n    >\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"font-size: 16px\">服务器</span>\r\n      </div>\r\n      <div class=\"text item\">环境变量：{{ serverInfo.EnvironmentName }}</div>\r\n      <div class=\"text item\">系统架构：{{ serverInfo.OSArchitecture }}</div>\r\n      <div class=\"text item\">\r\n        ContentRootPath：{{ serverInfo.ContentRootPath }}\r\n      </div>\r\n      <div class=\"text item\">WebRootPath：{{ serverInfo.WebRootPath }}</div>\r\n      <div class=\"text item\">\r\n        .NET Core版本：{{ serverInfo.FrameworkDescription }}\r\n      </div>\r\n      <div class=\"text item\">内存占用：{{ serverInfo.MemoryFootprint }}</div>\r\n      <div class=\"text item\">启动时间：{{ serverInfo.WorkingTime }}</div>\r\n      <div>\r\n        <br />\r\n      </div>\r\n    </el-card>\r\n    <el-card class=\"welcome-card note\" style=\"width: 98%; margin-top: 20px\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>30天用户注册曲线图</span>\r\n      </div>\r\n\r\n      <el-col :span=\"24\" class=\"echarts-item\">\r\n        <ve-line\r\n          :data=\"lineChartDataIDS4\"\r\n          :extend=\"extend\"\r\n          :settings=\"lineChartSettings7Day\"\r\n          :mark-point=\"lineChartMarkPoint\"\r\n        ></ve-line>\r\n      </el-col>\r\n    </el-card>\r\n    <el-card class=\"welcome-card\" style=\"margin-top: 20px; width: 98%\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>\r\n          访问日志\r\n          <span style=\"font-size: 12px\">(Top 50 desc)</span>\r\n        </span>\r\n      </div>\r\n      <el-table\r\n        :data=\"welcomeInitData.logs\"\r\n        highlight-current-row\r\n        border\r\n        v-loading=\"listLoading\"\r\n        style=\"width: 100%; font-size: 12px\"\r\n      >\r\n        <el-table-column\r\n          prop=\"User\"\r\n          label=\"访问者\"\r\n          width=\"150px\"\r\n          sortable\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"IP\"\r\n          label=\"请求地址\"\r\n          width=\"150px\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"BeginTime\"\r\n          label=\"请求时间\"\r\n          width=\"150px\"\r\n        ></el-table-column>\r\n        <el-table-column prop=\"API\" label=\"访问接口\" width></el-table-column>\r\n        <el-table-column\r\n          prop=\"RequestMethod\"\r\n          label=\"Method\"\r\n          width=\"100px\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"OPTime\"\r\n          label=\"响应时长\"\r\n          width=\"100px\"\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"RequestData\"\r\n          label=\"参数\"\r\n          width\r\n        ></el-table-column>\r\n        <el-table-column\r\n          prop=\"Agent\"\r\n          label=\"Agent\"\r\n          width=\"80\"\r\n          show-overflow-tooltip\r\n        >\r\n          <template scope=\"scope\">\r\n            <div style=\"text-decoration: underline; cursor: pointer\">\r\n              {{ scope.row.Agent }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <br />\r\n    </el-card>\r\n\r\n    <el-card class=\"welcome-card\" style=\"margin-top: 20px; width: 98%\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>相关配置</span>\r\n      </div>\r\n\r\n      <el-aside>1、动态添加一个vue页面：</el-aside>\r\n\r\n      <br />\r\n\r\n      <div class=\"text item\">\r\n        <i class=\"el-icon-edit\"></i>、更多内容，查看博客园文档：\r\n        <a\r\n          href=\"https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0\"\r\n          target=\"_blank\"\r\n          >https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0</a\r\n        >。\r\n      </div>\r\n\r\n      <br />\r\n      <hr />\r\n      <br />\r\n\r\n      <el-aside>2、快速配置接口权限：</el-aside>\r\n\r\n      <br />\r\n      <div style=\"height: 300px; overflow-y: auto\">\r\n        <el-steps direction=\"vertical\">\r\n          <el-step\r\n            title=\"步骤 1\"\r\n            description=\"创建一个测试控制器 DemoController\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 2\"\r\n            description=\"修改接口路由地址，带上 [action] ，比如，/api/[controller]/[action]，编译是否正常\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 3\"\r\n            description=\"给需要加权限的路由api上，增加授权特性[[Authorize(Permissions.Name)]]\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 4\"\r\n            description=\"测试 /api/demo/get 接口，是否已经被保护\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 5.1\"\r\n            description=\"vueadmin 后台 配置权限：第一步：登录后台，新建api接口\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 5.2\"\r\n            description=\"第二步：添加一个菜单，可以是一个查询按钮，也可以是一个路由页面\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 5.3\"\r\n            description=\"第三步：权限分配！勾选角色和刚刚的菜单\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 6\"\r\n            description=\"如果后端netcore资源服务器有缓存，记得清理\"\r\n          ></el-step>\r\n          <el-step\r\n            title=\"步骤 7\"\r\n            description=\"重新登录Admin管理后台，访问接口，查看是否有权限\"\r\n          ></el-step>\r\n        </el-steps>\r\n      </div>\r\n\r\n      <br />\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from \"vue\";\r\nimport VCharts from \"v-charts\";\r\nVue.use(VCharts);\r\n\r\nimport applicationUserManager from \"../Auth/applicationusermanager\";\r\nimport {\r\n  getServerInfo,\r\n  getAccessLogs,\r\n  getIds4UsersGrow,\r\n  getActiveUsers,\r\n  getAchieveUsers_IS4,\r\n} from \"../api/api\";\r\n\r\nexport default {\r\n  name: \"Welcome\",\r\n  data() {\r\n    return {\r\n      listLoading: false,\r\n      welcomeInitData: {\r\n        activeUsers: [],\r\n        activeUserCount: 0,\r\n        logs: [],\r\n        errorCount: 0,\r\n      },\r\n      serverInfo: {},\r\n      extend: {\r\n        series: {\r\n          label: {\r\n            normal: {\r\n              show: true,\r\n            },\r\n          },\r\n        },\r\n      },\r\n      lineChartDataIDS4: {\r\n        columns: [],\r\n        rows: [],\r\n        today: 0,\r\n      },\r\n      lineChartSettings7Day: {\r\n        metrics: [\"count\"],\r\n        dimension: [\"date\"],\r\n      },\r\n      lineChartMarkPoint: {\r\n        data: [\r\n          {\r\n            name: \"最大值\",\r\n            type: \"max\",\r\n          },\r\n          {\r\n            name: \"最小值\",\r\n            type: \"min\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    getTypeName(count) {\r\n      if (count >= 10 && count < 50) {\r\n        return \"warning\";\r\n      }\r\n      if (count < 10) {\r\n        return \"primary\";\r\n      }\r\n      return \"\";\r\n    },\r\n    getBck(index) {\r\n      return `background: rgb(${43 + index * 14}, ${\r\n        148 + index * 7\r\n      }, 255) none repeat scroll 0% 0%;`;\r\n    },\r\n    toLogs() {\r\n      this.$router.replace({\r\n        path: \"/Logs/Index\",\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    var curTime = new Date();\r\n    if (window.localStorage.TokenExpire) {\r\n      var expiretime = new Date(Date.parse(window.localStorage.TokenExpire));\r\n      if (curTime >= expiretime) {\r\n        if (global.IS_IDS4) {\r\n          applicationUserManager.login();\r\n        } else {\r\n          this.$router.push(\"/login\");\r\n        }\r\n      }\r\n    } else {\r\n      if (global.IS_IDS4) {\r\n        applicationUserManager.login();\r\n      } else {\r\n        this.$router.push(\"/login\");\r\n      }\r\n    }\r\n\r\n    if (global.IS_IDS4) {\r\n      getAchieveUsers_IS4({}).then((res) => {\r\n        this.lineChartDataIDS4 = res.data.response;\r\n      });\r\n    }\r\n\r\n    getServerInfo({}).then((res) => {\r\n      this.serverInfo = res.data.response;\r\n    });\r\n\r\n    getActiveUsers({}).then((res) => {\r\n      this.welcomeInitData = res.data.response;\r\n    });\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.amazing /deep/ .el-badge__content{\r\n  background-color: purple !important;\r\n}\r\n.bg-blue-sub-item {\r\n  max-width: 120px !important;\r\n  height: 50px;\r\n  float: left;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  line-height: 50px;\r\n  padding: 0 10px;\r\n}\r\n.bg-blue-sub-item .acc-user {\r\n  max-width: 115px;\r\n  display: block;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.bg-blue-sub-item-m {\r\n  height: 50px;\r\n  float: left;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  line-height: 50px;\r\n  padding: 0 10px;\r\n}\r\n.bg-blue-sub-item-m .acc-user {\r\n  display: block;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.note .text {\r\n  font-size: 14px;\r\n}\r\n\r\n.note .item {\r\n  margin-bottom: 18px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.panel-group {\r\n  margin-bottom: 18px;\r\n  margin-right: 2%;\r\n}\r\n.card-panel-col {\r\n  /* margin-bottom: 32px; */\r\n  width: 113px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-left: 15px;\r\n  float: right;\r\n}\r\n\r\n.card-panel {\r\n  height: 108px;\r\n  font-size: 12px;\r\n  position: relative;\r\n  overflow: hidden;\r\n  color: #666;\r\n  background: #fff;\r\n  box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);\r\n  border-color: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-panel .card-panel-icon-wrapper {\r\n  color: #fff;\r\n}\r\n\r\n.card-panel .card-panel .icon-people {\r\n  background: #40c9c6;\r\n}\r\n\r\n.card-panel .card-panel .icon-message {\r\n  background: #36a3f7;\r\n}\r\n\r\n.card-panel .card-panel .icon-money {\r\n  background: #f4516c;\r\n}\r\n\r\n.card-panel .card-panel .icon-shopping {\r\n  background: #34bfa3;\r\n}\r\n\r\n.card-panel .icon-people {\r\n  color: #40c9c6;\r\n}\r\n\r\n.card-panel .icon-message {\r\n  color: #36a3f7;\r\n}\r\n\r\n.card-panel .icon-money {\r\n  color: #f4516c;\r\n}\r\n\r\n.card-panel .icon-shopping {\r\n  color: #34bfa3;\r\n}\r\n\r\n.card-panel .card-panel-icon-wrapper {\r\n  float: left;\r\n  margin: 14px 0 0 14px;\r\n  padding: 16px;\r\n  transition: all 0.38s ease-out;\r\n  border-radius: 6px;\r\n}\r\n\r\n.card-panel .card-panel-icon {\r\n  float: left;\r\n  font-size: 48px;\r\n}\r\n\r\n.card-panel .card-panel-description {\r\n  float: left;\r\n  font-weight: bold;\r\n  margin-left: 30px;\r\n  margin-top: 20px;\r\n}\r\n.card-panel .card-panel-description .card-panel-text {\r\n  line-height: 18px;\r\n  color: rgba(0, 0, 0, 0.45);\r\n  font-size: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.card-panel .card-panel-description .card-panel-num {\r\n  font-size: 36px;\r\n  color: #f4516c;\r\n}\r\n.extoday {\r\n  cursor: pointer;\r\n}\r\n.card-acuser-num {\r\n  font-size: 36px;\r\n  color: #40c9c6;\r\n}\r\n.bg-blue-sub-item /deep/ .el-badge__content.is-fixed {\r\n  top: 5px !important;\r\n}\r\n.bg-blue-sub-item-m /deep/ .el-badge__content.is-fixed {\r\n  top: 5px !important;\r\n}\r\n\r\n@media (max-width: 550px) {\r\n  .note50 {\r\n    width: 100% !important;\r\n  }\r\n  .statistical-cus {\r\n    width: 100%;\r\n  }\r\n\r\n  .card-panel-col {\r\n    margin-bottom: 5px !important;\r\n    width: 100% !important;\r\n  }\r\n  .card-panel-icon-wrapper {\r\n    float: none !important;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin: 0 !important;\r\n  }\r\n  .card-panel-icon-wrapper .svg-icon {\r\n    display: block;\r\n    margin: 14px auto !important;\r\n    float: none !important;\r\n  }\r\n}\r\n</style>\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[(!_vm.$route.meta.NoNeedHome)?_c('transition',{attrs:{\"name\":\"fade\",\"mode\":\"out-in\"}},[_c('el-row',{staticClass:\"container\"},[_c('el-col',{staticClass:\"header\",attrs:{\"span\":24}},[_c('el-col',{staticClass:\"logo collapsedLogo\",class:_vm.collapsed?'logo-collapse-width':'logo-width',attrs:{\"span\":10}},[_c('div',{on:{\"click\":_vm.toindex}},[_vm._v(\" \"+_vm._s(_vm.collapsed?_vm.sysNameShort:_vm.sysName))])]),_c('el-col',{staticClass:\"logoban\",attrs:{\"span\":10}},[_c('div',{class:_vm.collapsed?'tools collapsed':'tools',on:{\"click\":_vm.collapse}},[_c('i',{staticClass:\"fa fa-align-justify\"})]),_c('el-breadcrumb',{staticClass:\"breadcrumb-inner collapsedLogo\",attrs:{\"separator\":\"/\"}},_vm._l((_vm.$route.matched),function(item){return _c('el-breadcrumb-item',{key:item.path},[_c('span',{},[_vm._v(\" \"+_vm._s(item.name))])])}),1)],1),_c('el-col',{staticClass:\"userinfo\",attrs:{\"span\":4}},[_c('el-dropdown',{attrs:{\"trigger\":\"hover\"}},[_c('span',{staticClass:\"el-dropdown-link userinfo-inner\"},[_vm._v(\"\\n                    \"+_vm._s(_vm.sysUserName)+\"\\n                    \"),_c('img',{attrs:{\"src\":require(\"./assets/logo.png\"),\"height\":\"128\",\"width\":\"128\"}})]),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\"},slot:\"dropdown\"},[_c('el-dropdown-item',{nativeOn:{\"click\":function($event){return _vm.myNews($event)}}},[_c('el-badge',{staticClass:\"item\",attrs:{\"value\":2,\"type\":\"warning\"}},[_vm._v(\"\\n                                    我的消息\\n                                \")])],1),_c('el-dropdown-item',{nativeOn:{\"click\":function($event){return _vm.Setting($event)}}},[_vm._v(\"设置\")]),_c('el-dropdown-item',{nativeOn:{\"click\":function($event){return _vm.goGithub($event)}}},[_vm._v(\"Github\")]),_c('el-dropdown-item',{attrs:{\"divided\":\"\"},nativeOn:{\"click\":function($event){return _vm.logout($event)}}},[_vm._v(\"退出登录\")])],1)],1)],1)],1),_c('el-col',{staticClass:\"main\",attrs:{\"span\":24}},[_c('aside',{class:_vm.collapsedClass},[_c('el-scrollbar',{staticClass:\"scrollbar-handle\",staticStyle:{\"height\":\"100%\",\"background\":\"#2f3e52\"}},[_c('el-menu',{staticClass:\"el-menu-vertical-demo\",staticStyle:{\"border-right\":\"none\"},attrs:{\"default-active\":_vm.$route.path,\"unique-opened\":\"\",\"router\":\"\",\"collapse\":_vm.collapsed,\"background-color\":\"#2f3e52\",\"text-color\":\"#fff\",\"active-text-color\":\"#ffd04b\"},on:{\"open\":_vm.handleopen,\"close\":_vm.handleclose,\"select\":_vm.handleselect}},_vm._l((_vm.routes),function(menu,index){return _c('sidebar',{key:index,attrs:{\"item\":menu}})}),1)],1)],1),_c('el-col',{staticClass:\"content-wrapper\",class:_vm.collapsed?'content-collapsed':'content-expanded',attrs:{\"span\":24}},[(_vm.showTags)?_c('div',{staticClass:\"tags\"},[_c('div',{staticClass:\"tags-view-container\",attrs:{\"id\":\"tags-view-container\"}},[_c('scroll-pane',{ref:\"scrollPane\",staticClass:\"tags-view-wrapper\"},_vm._l((_vm.tagsList),function(tag,index){return _c('router-link',{key:tag.path,ref:\"tag\",refInFor:true,staticClass:\"tags-view-item\",class:{'active': _vm.isActive(tag.path)},attrs:{\"to\":{ path: tag.path, query: tag.query, fullPath: tag.fullPath },\"tag\":\"span\"},nativeOn:{\"mouseup\":function($event){if('button' in $event && $event.button !== 1){ return null; }_vm.closeTags(index)}}},[_vm._v(\"\\n                                    \"+_vm._s(tag.title)+\"\\n                                    \"),_c('span',{staticClass:\"el-icon-close\",on:{\"click\":function($event){$event.preventDefault();$event.stopPropagation();_vm.closeTags(index)}}})])}),1)],1),_c('div',{staticClass:\"tags-close-box\"},[_c('el-dropdown',{on:{\"command\":_vm.handleTags}},[_c('el-button',{attrs:{\"size\":\"mini\"}},[_c('i',{staticClass:\"el-icon-arrow-down el-icon--right\"})]),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\",\"size\":\"small\"},slot:\"dropdown\"},[_c('el-dropdown-item',{attrs:{\"command\":\"other\"}},[_vm._v(\"关闭其他\")]),_c('el-dropdown-item',{attrs:{\"command\":\"all\"}},[_vm._v(\"关闭所有\")])],1)],1)],1)]):_vm._e(),_c('transition',{attrs:{\"name\":\"fade\",\"mode\":\"out-in\"}},[_c('div',{staticClass:\"content-az router-view-withly\"},[_c('router-view')],1)])],1)],1)],1)],1):_c('transition',{attrs:{\"name\":\"fade\",\"mode\":\"out-in\"}},[_c('div',[_c('router-view')],1)]),_c('el-dialog',{class:_vm.newsDialogCss,attrs:{\"title\":\"Unread Messages\",\"visible\":_vm.NewsVisible,\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.NewsVisible=$event}},model:{value:(_vm.NewsVisible),callback:function ($$v) {_vm.NewsVisible=$$v},expression:\"NewsVisible\"}},[_c('div',_vm._l((_vm.tagNews),function(tag){return _c('el-tag',{key:tag.name,staticClass:\"tag-new\",attrs:{\"closable\":\"\",\"type\":tag.type}},[_vm._v(\"\\n                \"+_vm._s(tag.name)+\"\\n            \")])}),1)]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.SidebarVisible),expression:\"SidebarVisible\"}],staticClass:\"v-modal \",staticStyle:{\"z-index\":\"2999\"},attrs:{\"tabindex\":\"0\"},on:{\"click\":_vm.collapse}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=40132ba9&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./App.vue?vue&type=style&index=1&lang=css&\"\nimport style2 from \"./App.vue?vue&type=style&index=2&lang=css&\"\nimport style3 from \"./App.vue?vue&type=style&index=3&lang=css&\"\nimport style4 from \"./App.vue?vue&type=style&index=4&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "export default {\r\n  route: {\r\n    dashboard: 'Dashboard',\r\n    introduction: 'Introduction',\r\n    documentation: 'Documentation',\r\n    guide: 'Guide',\r\n    permission: 'Permission',\r\n    pagePermission: 'Page Permission',\r\n    rolePermission: 'Role Permission',\r\n    directivePermission: 'Directive Permission',\r\n    icons: 'Icons',\r\n    components: 'Components',\r\n    componentIndex: 'Introduction',\r\n    tinymce: 'Tinymce',\r\n    markdown: 'Markdown',\r\n    jsonEditor: 'JSON Editor',\r\n    dndList: 'Dnd List',\r\n    splitPane: 'SplitPane',\r\n    avatarUpload: 'Avatar Upload',\r\n    dropzone: 'Dropzone',\r\n    sticky: 'Sticky',\r\n    countTo: 'CountTo',\r\n    componentMixin: 'Mixin',\r\n    backToTop: 'BackToTop',\r\n    dragDialog: 'Drag Dialog',\r\n    dragSelect: 'Drag Select',\r\n    dragKanban: 'Drag Kanban',\r\n    charts: 'Charts',\r\n    keyboardChart: 'Keyboard Chart',\r\n    lineChart: 'Line Chart',\r\n    mixChart: 'Mix Chart',\r\n    example: 'Example',\r\n    nested: 'Nested Routes',\r\n    menu1: 'Menu 1',\r\n    'menu1-1': 'Menu 1-1',\r\n    'menu1-2': 'Menu 1-2',\r\n    'menu1-2-1': 'Menu 1-2-1',\r\n    'menu1-2-2': 'Menu 1-2-2',\r\n    'menu1-3': 'Menu 1-3',\r\n    menu2: 'Menu 2',\r\n    Table: 'Table',\r\n    dynamicTable: 'Dynamic Table',\r\n    dragTable: 'Drag Table',\r\n    inlineEditTable: 'Inline Edit',\r\n    complexTable: 'Complex Table',\r\n    treeTable: 'Tree Table',\r\n    customTreeTable: 'Custom TreeTable',\r\n    tab: 'Tab',\r\n    form: 'Form',\r\n    createArticle: 'Create Article',\r\n    editArticle: 'Edit Article',\r\n    articleList: 'Article List',\r\n    errorPages: 'Error Pages',\r\n    page401: '401',\r\n    page404: '404',\r\n    errorLog: 'Error Log',\r\n    excel: 'Excel',\r\n    exportExcel: 'Export Excel',\r\n    selectExcel: 'Export Selected',\r\n    mergeHeader: 'Merge Header',\r\n    uploadExcel: 'Upload Excel',\r\n    zip: 'Zip',\r\n    pdf: 'PDF',\r\n    exportZip: 'Export Zip',\r\n    theme: 'Theme',\r\n    clipboardDemo: 'Clipboard',\r\n    i18n: 'I18n',\r\n    externalLink: 'External Link'\r\n  },\r\n  navbar: {\r\n    logOut: 'Log Out',\r\n    dashboard: 'Dashboard',\r\n    github: 'Github',\r\n    theme: 'Theme',\r\n    size: 'Global Size'\r\n  },\r\n  login: {\r\n    title: 'Login Form',\r\n    logIn: 'Log in',\r\n    username: 'Username',\r\n    password: 'Password',\r\n    any: 'any',\r\n    thirdparty: 'Or connect with',\r\n    thirdpartyTips: 'Can not be simulated on local, so please combine you own business simulation! ! !'\r\n  },\r\n  documentation: {\r\n    documentation: 'Documentation',\r\n    github: 'Github Repository'\r\n  },\r\n  permission: {\r\n    addRole: 'New Role',\r\n    editPermission: 'Edit Permission',\r\n    roles: 'Your roles',\r\n    switchRoles: 'Switch roles',\r\n    tips: 'In some cases it is not suitable to use v-permission, such as element Tab component or el-table-column and other asynchronous rendering dom cases which can only be achieved by manually setting the v-if.',\r\n    delete: 'Delete',\r\n    confirm: 'Confirm',\r\n    cancel: 'Cancel'\r\n  },\r\n  guide: {\r\n    description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',\r\n    button: 'Show Guide'\r\n  },\r\n  components: {\r\n    documentation: 'Documentation',\r\n    tinymceTips: 'Rich text editor is a core part of management system, but at the same time is a place with lots of problems. In the process of selecting rich texts, I also walked a lot of detours. The common rich text editors in the market are basically used, and the finally chose Tinymce. See documentation for more detailed rich text editor comparisons and introductions.',\r\n    dropzoneTips: 'Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.',\r\n    stickyTips: 'when the page is scrolled to the preset position will be sticky on the top.',\r\n    backToTopTips1: 'When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner',\r\n    backToTopTips2: 'You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally',\r\n    imageUploadTips: 'Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.'\r\n  },\r\n  table: {\r\n    dynamicTips1: 'Fixed header, sorted by header order',\r\n    dynamicTips2: 'Not fixed header, sorted by click order',\r\n    dragTips1: 'The default order',\r\n    dragTips2: 'The after dragging order',\r\n    title: 'Title',\r\n    importance: 'Imp',\r\n    type: 'Type',\r\n    remark: 'Remark',\r\n    search: 'Search',\r\n    add: 'Add',\r\n    export: 'Export',\r\n    reviewer: 'reviewer',\r\n    id: 'ID',\r\n    date: 'Date',\r\n    author: 'Author',\r\n    readings: 'Readings',\r\n    status: 'Status',\r\n    actions: 'Actions',\r\n    edit: 'Edit',\r\n    publish: 'Publish',\r\n    draft: 'Draft',\r\n    delete: 'Delete',\r\n    cancel: 'Cancel',\r\n    confirm: 'Confirm'\r\n  },\r\n  errorLog: {\r\n    tips: 'Please click the bug icon in the upper right corner',\r\n    description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',\r\n    documentation: 'Document introduction'\r\n  },\r\n  excel: {\r\n    export: 'Export',\r\n    selectedExport: 'Export Selected Items',\r\n    placeholder: 'Please enter the file name(default excel-list)'\r\n  },\r\n  zip: {\r\n    export: 'Export',\r\n    placeholder: 'Please enter the file name(default file)'\r\n  },\r\n  pdf: {\r\n    tips: 'Here we use window.print() to implement the feature of downloading pdf.'\r\n  },\r\n  theme: {\r\n    change: 'Change Theme',\r\n    documentation: 'Theme documentation',\r\n    tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.'\r\n  },\r\n  tagsView: {\r\n    refresh: 'Refresh',\r\n    close: 'Close',\r\n    closeOthers: 'Close Others',\r\n    closeAll: 'Close All'\r\n  }\r\n}\r\n", "export default {\r\n  route: {\r\n    dashboard: '首页',\r\n    introduction: '简述',\r\n    documentation: '文档',\r\n    guide: '引导页',\r\n    permission: '权限测试页',\r\n    rolePermission: '角色权限',\r\n    pagePermission: '页面权限',\r\n    directivePermission: '指令权限',\r\n    icons: '图标',\r\n    components: '组件',\r\n    componentIndex: '介绍',\r\n    tinymce: '富文本编辑器',\r\n    markdown: 'Markdown',\r\n    jsonEditor: 'JSON编辑器',\r\n    dndList: '列表拖拽',\r\n    splitPane: 'Splitpane',\r\n    avatarUpload: '头像上传',\r\n    dropzone: 'Dropzone',\r\n    sticky: 'Sticky',\r\n    countTo: 'CountTo',\r\n    componentMixin: '小组件',\r\n    backToTop: '返回顶部',\r\n    dragDialog: '拖拽 Dialog',\r\n    dragSelect: '拖拽 Select',\r\n    dragKanban: '可拖拽看板',\r\n    charts: '图表',\r\n    keyboardChart: '键盘图表',\r\n    lineChart: '折线图',\r\n    mixChart: '混合图表',\r\n    example: '综合实例',\r\n    nested: '路由嵌套',\r\n    menu1: '菜单1',\r\n    'menu1-1': '菜单1-1',\r\n    'menu1-2': '菜单1-2',\r\n    'menu1-2-1': '菜单1-2-1',\r\n    'menu1-2-2': '菜单1-2-2',\r\n    'menu1-3': '菜单1-3',\r\n    menu2: '菜单2',\r\n    Table: 'Table',\r\n    dynamicTable: '动态Table',\r\n    dragTable: '拖拽Table',\r\n    inlineEditTable: 'Table内编辑',\r\n    complexTable: '综合Table',\r\n    treeTable: '树形表格',\r\n    customTreeTable: '自定义树表',\r\n    tab: 'Tab',\r\n    form: '表单',\r\n    createArticle: '创建文章',\r\n    editArticle: '编辑文章',\r\n    articleList: '文章列表',\r\n    errorPages: '错误页面',\r\n    page401: '401',\r\n    page404: '404',\r\n    errorLog: '错误日志',\r\n    excel: 'Excel',\r\n    exportExcel: '导出 Excel',\r\n    selectExcel: '导出 已选择项',\r\n    mergeHeader: '导出 多级表头',\r\n    uploadExcel: '上传 Excel',\r\n    zip: 'Zip',\r\n    pdf: 'PDF',\r\n    exportZip: 'Export Zip',\r\n    theme: '换肤',\r\n    clipboardDemo: 'Clipboard',\r\n    i18n: '国际化',\r\n    externalLink: '外链'\r\n  },\r\n  navbar: {\r\n    logOut: '退出登录',\r\n    dashboard: '首页',\r\n    github: '项目地址',\r\n    theme: '换肤',\r\n    size: '布局大小'\r\n  },\r\n  login: {\r\n    title: '系统登录',\r\n    logIn: '登录',\r\n    username: '账号',\r\n    password: '密码',\r\n    any: '随便填',\r\n    thirdparty: '第三方登录',\r\n    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'\r\n  },\r\n  documentation: {\r\n    documentation: '文档',\r\n    github: 'Github 地址'\r\n  },\r\n  permission: {\r\n    addRole: '新增角色',\r\n    editPermission: '编辑权限',\r\n    roles: '你的权限',\r\n    switchRoles: '切换权限',\r\n    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 Tab 组件或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',\r\n    delete: '删除',\r\n    confirm: '确定',\r\n    cancel: '取消'\r\n  },\r\n  guide: {\r\n    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',\r\n    button: '打开引导'\r\n  },\r\n  components: {\r\n    documentation: '文档',\r\n    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',\r\n    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',\r\n    stickyTips: '当页面滚动到预设的位置会吸附在顶部',\r\n    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',\r\n    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',\r\n    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。'\r\n  },\r\n  table: {\r\n    dynamicTips1: '固定表头, 按照表头顺序排序',\r\n    dynamicTips2: '不固定表头, 按照点击顺序排序',\r\n    dragTips1: '默认顺序',\r\n    dragTips2: '拖拽后顺序',\r\n    title: '标题',\r\n    importance: '重要性',\r\n    type: '类型',\r\n    remark: '点评',\r\n    search: '搜索',\r\n    add: '添加',\r\n    export: '导出',\r\n    reviewer: '审核人',\r\n    id: '序号',\r\n    date: '时间',\r\n    author: '作者',\r\n    readings: '阅读数',\r\n    status: '状态',\r\n    actions: '操作',\r\n    edit: '编辑',\r\n    publish: '发布',\r\n    draft: '草稿',\r\n    delete: '删除',\r\n    cancel: '取 消',\r\n    confirm: '确 定'\r\n  },\r\n  errorLog: {\r\n    tips: '请点击右上角bug小图标',\r\n    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',\r\n    documentation: '文档介绍'\r\n  },\r\n  excel: {\r\n    export: '导出',\r\n    selectedExport: '导出已选择项',\r\n    placeholder: '请输入文件名(默认excel-list)'\r\n  },\r\n  zip: {\r\n    export: '导出',\r\n    placeholder: '请输入文件名(默认file)'\r\n  },\r\n  pdf: {\r\n    tips: '这里使用   window.print() 来实现下载pdf的功能'\r\n  },\r\n  theme: {\r\n    change: '换肤',\r\n    documentation: '换肤文档',\r\n    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。'\r\n  },\r\n  tagsView: {\r\n    refresh: '刷新',\r\n    close: '关闭',\r\n    closeOthers: '关闭其它',\r\n    closeAll: '关闭所有'\r\n  }\r\n}\r\n", "export default {\r\n  route: {\r\n    dashboard: 'Panel de control',\r\n    introduction: 'Introducción',\r\n    documentation: 'Documentación',\r\n    guide: 'Guía',\r\n    permission: 'Permisos',\r\n    rolePermission: 'Permisos de rol',\r\n    pagePermission: 'Permisos de la página',\r\n    directivePermission: 'Permisos de la directiva',\r\n    icons: 'Iconos',\r\n    components: 'Componentes',\r\n    componentIndex: 'Introducción',\r\n    tinymce: 'Tinymce',\r\n    markdown: 'Markdown',\r\n    jsonEditor: 'Editor JSON',\r\n    dndList: 'Lista Dnd',\r\n    splitPane: 'Panel dividido',\r\n    avatarUpload: 'Subir avatar',\r\n    dropzone: 'Subir ficheros',\r\n    sticky: 'Sticky',\r\n    countTo: 'CountTo',\r\n    componentMixin: 'Mixin',\r\n    backToTop: 'Ir arriba',\r\n    dragDialog: 'Drag Dialog',\r\n    dragSelect: 'Drag Select',\r\n    dragKanban: 'Drag Kanban',\r\n    charts: 'Gráficos',\r\n    keyboardChart: 'Keyboard Chart',\r\n    lineChart: 'Gráfico de líneas',\r\n    mixChart: 'Mix Chart',\r\n    example: 'Ejemplo',\r\n    nested: '<PERSON><PERSON><PERSON> anidadas<PERSON>',\r\n    menu1: 'Menu 1',\r\n    'menu1-1': 'Menu 1-1',\r\n    'menu1-2': 'Menu 1-2',\r\n    'menu1-2-1': 'Menu 1-2-1',\r\n    'menu1-2-2': 'Menu 1-2-2',\r\n    'menu1-3': 'Menu 1-3',\r\n    menu2: 'Menu 2',\r\n    Table: 'Tabla',\r\n    dynamicTable: 'Tabla dinámica',\r\n    dragTable: 'Arrastrar tabla',\r\n    inlineEditTable: 'Editor',\r\n    complexTable: 'Complex Table',\r\n    treeTable: 'Tree Table',\r\n    customTreeTable: 'Custom TreeTable',\r\n    tab: 'Pestaña',\r\n    form: 'Formulario',\r\n    createArticle: 'Crear artículo',\r\n    editArticle: 'Editar artículo',\r\n    articleList: 'Listado de artículos',\r\n    errorPages: 'Páginas de error',\r\n    page401: '401',\r\n    page404: '404',\r\n    errorLog: 'Registro de errores',\r\n    excel: 'Excel',\r\n    exportExcel: 'Exportar a Excel',\r\n    selectExcel: 'Export seleccionado',\r\n    mergeHeader: 'Merge Header',\r\n    uploadExcel: 'Subir Excel',\r\n    zip: 'Zip',\r\n    pdf: 'PDF',\r\n    exportZip: 'Exportar a Zip',\r\n    theme: 'Tema',\r\n    clipboardDemo: 'Clipboard',\r\n    i18n: 'I18n',\r\n    externalLink: 'Enlace externo'\r\n  },\r\n  navbar: {\r\n    logOut: 'Salir',\r\n    dashboard: 'Panel de control',\r\n    github: 'Github',\r\n    theme: 'Tema',\r\n    size: 'Tamaño global'\r\n  },\r\n  login: {\r\n    title: 'Formulario de acceso',\r\n    logIn: 'Acceso',\r\n    username: 'Usuario',\r\n    password: 'Contraseña',\r\n    any: 'nada',\r\n    thirdparty: 'Conectar con',\r\n    thirdpartyTips: 'No se puede simular en local, así que combine su propia simulación de negocios. ! !'\r\n  },\r\n  documentation: {\r\n    documentation: 'Documentación',\r\n    github: 'Repositorio Github'\r\n  },\r\n  permission: {\r\n    addRole: 'Nuevo rol',\r\n    editPermission: 'Permiso de edición',\r\n    roles: 'Tus permisos',\r\n    switchRoles: 'Cambiar permisos',\r\n    tips: 'In some cases it is not suitable to use v-permission, such as element Tab component or el-table-column and other asynchronous rendering dom cases which can only be achieved by manually setting the v-if.',\r\n    delete: 'Borrar',\r\n    confirm: 'Confirmar',\r\n    cancel: 'Cancelar'\r\n  },\r\n  guide: {\r\n    description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',\r\n    button: 'Ver guía'\r\n  },\r\n  components: {\r\n    documentation: 'Documentación',\r\n    tinymceTips: 'Rich text editor is a core part of management system, but at the same time is a place with lots of problems. In the process of selecting rich texts, I also walked a lot of detours. The common rich text editors in the market are basically used, and the finally chose Tinymce. See documentation for more detailed rich text editor comparisons and introductions.',\r\n    dropzoneTips: 'Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.',\r\n    stickyTips: 'when the page is scrolled to the preset position will be sticky on the top.',\r\n    backToTopTips1: 'When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner',\r\n    backToTopTips2: 'You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally',\r\n    imageUploadTips: 'Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.'\r\n  },\r\n  table: {\r\n    dynamicTips1: 'Fixed header, sorted by header order',\r\n    dynamicTips2: 'Not fixed header, sorted by click order',\r\n    dragTips1: 'Orden por defecto',\r\n    dragTips2: 'The after dragging order',\r\n    title: 'Título',\r\n    importance: 'Importancia',\r\n    type: 'Tipo',\r\n    remark: 'Remark',\r\n    search: 'Buscar',\r\n    add: 'Añadir',\r\n    export: 'Exportar',\r\n    reviewer: 'reviewer',\r\n    id: 'ID',\r\n    date: 'Fecha',\r\n    author: 'Autor',\r\n    readings: 'Lector',\r\n    status: 'Estado',\r\n    actions: 'Acciones',\r\n    edit: 'Editar',\r\n    publish: 'Publicar',\r\n    draft: 'Draft',\r\n    delete: 'Eliminar',\r\n    cancel: 'Cancelar',\r\n    confirm: 'Confirmar'\r\n  },\r\n  errorLog: {\r\n    tips: 'Please click the bug icon in the upper right corner',\r\n    description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',\r\n    documentation: 'Documento de introducción'\r\n  },\r\n  excel: {\r\n    export: 'Exportar',\r\n    selectedExport: 'Exportar seleccionados',\r\n    placeholder: 'Por favor escribe un nombre de fichero'\r\n  },\r\n  zip: {\r\n    export: 'Exportar',\r\n    placeholder: 'Por favor escribe un nombre de fichero'\r\n  },\r\n  pdf: {\r\n    tips: 'Here we use window.print() to implement the feature of downloading pdf.'\r\n  },\r\n  theme: {\r\n    change: 'Cambiar tema',\r\n    documentation: 'Documentación del tema',\r\n    tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.'\r\n  },\r\n  tagsView: {\r\n    refresh: 'Actualizar',\r\n    close: 'Cerrar',\r\n    closeOthers: 'Cerrar otros',\r\n    closeAll: 'Cerrar todos'\r\n  }\r\n}\r\n", "import Vue from 'vue'\r\nimport VueI18n from 'vue-i18n'\r\nimport Cookies from 'js-cookie'\r\nimport elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang\r\nimport elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang\r\nimport elementEsLocale from 'element-ui/lib/locale/lang/es'// element-ui lang\r\nimport enLocale from './en'\r\nimport zhLocale from './zh'\r\nimport esLocale from './es'\r\n\r\nVue.use(VueI18n)\r\n\r\nconst messages = {\r\n  en: {\r\n    ...enLocale,\r\n    ...elementEnLocale\r\n  },\r\n  zh: {\r\n    ...zhLocale,\r\n    ...elementZhLocale\r\n  },\r\n  es: {\r\n    ...esLocale,\r\n    ...elementEsLocale\r\n  }\r\n}\r\n\r\nconst i18n = new VueI18n({\r\n  // set locale\r\n  // options: en | zh | es\r\n  locale: Cookies.get('language') || 'zh',\r\n  // set locale messages\r\n  messages\r\n})\r\n\r\nexport default i18n\r\n", "import Vue from 'vue'\r\nimport App from './App.vue'\r\n// import router from './routerManuaConfig'\r\nimport store from './store'\r\nimport router from './router/index'\r\nimport '../util/global'//全局\r\nimport './promissionRouter'//这里进行路由后台获取的模拟\r\nimport i18n from './lang' // Internationalization\r\nimport Cookies from 'js-cookie'\r\n\r\nimport ElementUI from \"element-ui\";\r\nimport \"element-ui/lib/theme-chalk/index.css\";\r\n\r\nVue.use(ElementUI, {\r\n    size: Cookies.get('size') || 'medium', // set element-ui default size\r\n    i18n: (key, value) => i18n.t(key, value)\r\n});\r\n\r\n//如果想使用 mock ，开启这两行即可，想看效果，看登录页的 mock登录功能\r\n// import Mock from './mock'\r\n// Mock.bootstrap();\r\n\r\nimport 'font-awesome/css/font-awesome.min.css'\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n    /* 路由发生变化修改页面title */\r\n    if (to.meta.title) {\r\n        document.title = to.meta.title\r\n    }\r\n    next()\r\n})\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n    router,\r\n    store,\r\n    i18n,\r\n    render: h => h(App)\r\n}).$mount('#app')\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[(_vm.item.children)?[(!(_vm.item.path!=''&&_vm.item.path!=' '&&_vm.item.path!='-')&&!_vm.item.IsButton)?_c('el-submenu',{key:_vm.item.path,attrs:{\"index\":_vm.item.id+'index'}},[_c('template',{slot:\"title\"},[(_vm.item.children&&_vm.item.children.length>0&&_vm.item.iconCls&&!_vm.item.IsButton)?_c('i',{staticClass:\"fa\",class:_vm.item.iconCls}):_vm._e(),_c('span',{staticClass:\"title-name\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(_vm._s(_vm.item.name))])]),_vm._l((_vm.item.children),function(child){return [(!child.IsHide&&!_vm.item.IsButton)?[(child.children&&child.children.length>0)?_c('sidebar',{key:child.path,attrs:{\"item\":child,\"index\":child.id}}):_c('app-link',{key:child.path,attrs:{\"to\":child.path}},[_c('el-menu-item',{key:child.path,attrs:{\"index\":_vm.isExternalLink(child.path)? '':child.path},on:{\"click\":_vm.cop}},[_c('i',{staticClass:\"fa\",class:child.iconCls}),_c('template',{slot:\"title\"},[_c('span',{staticClass:\"title-name\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(_vm._s(child.name))])])],2)],1)]:_vm._e()]})],2):[(!_vm.item.IsButton)?_c('app-link',{key:_vm.item.path+'d',attrs:{\"to\":_vm.item.path}},[_c('el-menu-item',{key:_vm.item.path+'d',attrs:{\"index\":_vm.isExternalLink(_vm.item.path)? '':_vm.item.path}},[_c('i',{staticClass:\"fa\",class:_vm.item.iconCls}),_c('template',{slot:\"title\"},[_c('span',{staticClass:\"title-name\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(_vm._s(_vm.item.name))])])],2)],1):_vm._e()]]:[_c('app-link',{key:_vm.item.path+'d',attrs:{\"to\":_vm.item.path}},[_c('el-menu-item',{key:_vm.item.path+'d',attrs:{\"index\":_vm.isExternalLink(_vm.item.path)? '':_vm.item.path},on:{\"click\":_vm.cop}},[_c('i',{staticClass:\"fa\",class:_vm.item.iconCls}),_c('template',{slot:\"title\"},[_c('span',{staticClass:\"title-name\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(_vm._s(_vm.item.name))])])],2)],1)]],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c(_vm.type,_vm._b({tag:\"component\"},'component',_vm.linkProps(_vm.to),false),[_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\r\nexport function isExternal(path) {\r\n  return /^(https?:|mailto:|tel:)/.test(path)\r\n}\r\n\r\nexport function validURL(url) {\r\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\r\n  return reg.test(url)\r\n}\r\n\r\nexport function validEmail(email) {\r\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\r\n  return reg.test(email)\r\n}\r\n\r\nexport function isString(str) {\r\n  if (typeof str === 'string' || str instanceof String) {\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nexport function isArray(arg) {\r\n  if (typeof Array.isArray === 'undefined') {\r\n    return Object.prototype.toString.call(arg) === '[object Array]'\r\n  }\r\n  return Array.isArray(arg)\r\n}\r\n", "<template>\r\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '../../util/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.to)\r\n    },\r\n    type() {\r\n      if (this.isExternal) {\r\n        return 'a'\r\n      }\r\n      return 'router-link'\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(to) {\r\n      if (this.isExternal) {\r\n        return {\r\n          href: to,\r\n          target: '_blank',\r\n          style:'color:#fff;'\r\n        }\r\n      }\r\n      return {\r\n        to: to\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AppLink.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AppLink.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./AppLink.vue?vue&type=template&id=212f2a8a&\"\nimport script from \"./AppLink.vue?vue&type=script&lang=js&\"\nexport * from \"./AppLink.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"AppLink.vue\"\nexport default component.exports", "<template>\r\n  <div>\r\n    <!-- if 有子节点，渲染节点递归 -->\r\n    <template v-if=\"item.children\">\r\n      <el-submenu\r\n        v-if=\"!(item.path!=''&&item.path!=' '&&item.path!='-')&&!item.IsButton\"\r\n        :index=\"item.id+'index'\"\r\n        :key=\"item.path\"\r\n      >\r\n        <template slot=\"title\">\r\n          <i\r\n            v-if=\"item.children&&item.children.length>0&&item.iconCls&&!item.IsButton\"\r\n            class=\"fa\"\r\n            :class=\"item.iconCls\"\r\n          ></i>\r\n          <span class=\"title-name\" slot=\"title\">{{item.name}}</span>\r\n        </template>\r\n        <template v-for=\"child in item.children\">\r\n          <!-- 这里实现自己递归嵌套 -->\r\n          <template v-if=\"!child.IsHide&&!item.IsButton\">\r\n            <sidebar\r\n              v-if=\"child.children&&child.children.length>0\"\r\n              :item=\"child\"\r\n              :index=\"child.id\"\r\n              :key=\"child.path\"\r\n            />\r\n            <app-link :to=\"child.path\" v-else :key=\"child.path\">\r\n              <el-menu-item :key=\"child.path\" \r\n          :index=\"isExternalLink(child.path)? '':child.path\"\r\n               @click=\"cop\">\r\n                <i class=\"fa\" :class=\"child.iconCls\"></i>\r\n                <template slot=\"title\">\r\n                  <span class=\"title-name\" slot=\"title\">{{child.name}}</span>\r\n                </template>\r\n              </el-menu-item>\r\n            </app-link>\r\n          </template>\r\n        </template>\r\n      </el-submenu>\r\n      <template v-else>\r\n        <app-link :to=\"item.path\" v-if=\"!item.IsButton\" :key=\"item.path+'d'\">\r\n          <el-menu-item\r\n          :index=\"isExternalLink(item.path)? '':item.path\"\r\n           :key=\"item.path+'d'\">\r\n            <i class=\"fa\" :class=\"item.iconCls\"></i>\r\n            <template slot=\"title\">\r\n              <span class=\"title-name\" slot=\"title\">{{item.name}}</span>\r\n            </template>\r\n          </el-menu-item>\r\n        </app-link>\r\n      </template>\r\n    </template>\r\n    <!-- else 没有子节点，直接输出 -->\r\n    <template v-else>\r\n      <app-link :to=\"item.path\" :key=\"item.path+'d'\">\r\n        <el-menu-item\r\n          :index=\"isExternalLink(item.path)? '':item.path\"\r\n          :key=\"item.path+'d'\"\r\n          @click=\"cop\"\r\n        >\r\n          <i class=\"fa\" :class=\"item.iconCls\"></i>\r\n          <template slot=\"title\">\r\n            <span class=\"title-name\" slot=\"title\">{{item.name}}</span>\r\n          </template>\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport AppLink from \"./AppLink\";\r\nimport { isExternal } from \"../../util/validate\";\r\n\r\nexport default {\r\n  name: \"Sidebar\",\r\n  components: { AppLink },\r\n  props: {\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  methods: {\r\n    isExternalLink(to) {\r\n      return isExternal(to);\r\n    },\r\n    cop: function() {\r\n      // 子组件中触发父组件方法collaFa并传值123\r\n      this.$emit(\"collaFa\", \"123\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Sidebar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Sidebar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Sidebar.vue?vue&type=template&id=736799a0&\"\nimport script from \"./Sidebar.vue?vue&type=script&lang=js&\"\nexport * from \"./Sidebar.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"Sidebar.vue\"\nexport default component.exports", "var map = {\n\t\"./views/403.vue\": [\n\t\t\"00a5\",\n\t\t\"chunk-c673e236\"\n\t],\n\t\"./views/404.vue\": [\n\t\t\"8cdb\"\n\t],\n\t\"./views/APIDoc.vue\": [\n\t\t\"3e70\"\n\t],\n\t\"./views/About.vue\": [\n\t\t\"f820\",\n\t\t\"chunk-2d22d746\"\n\t],\n\t\"./views/Blog/Blogs.vue\": [\n\t\t\"7b19\",\n\t\t\"chunk-40df6ae2\"\n\t],\n\t\"./views/Blog/Detail.vue\": [\n\t\t\"ccf9\",\n\t\t\"chunk-6e83591c\",\n\t\t\"chunk-7287e918\"\n\t],\n\t\"./views/Department/Department.vue\": [\n\t\t\"4ac3\",\n\t\t\"chunk-735deb8e\"\n\t],\n\t\"./views/Form/Charts.vue\": [\n\t\t\"8bd6\",\n\t\t\"chunk-d726e0f8\"\n\t],\n\t\"./views/Form/Form.vue\": [\n\t\t\"62bc\",\n\t\t\"chunk-2d0cf4f3\"\n\t],\n\t\"./views/I18n/index.vue\": [\n\t\t\"51f7\",\n\t\t\"chunk-c75b8e6e\"\n\t],\n\t\"./views/Layout/Layout.vue\": [\n\t\t\"32e93\"\n\t],\n\t\"./views/Login.vue\": [\n\t\t\"a55b\"\n\t],\n\t\"./views/LoginCallbackView.vue\": [\n\t\t\"86d7\"\n\t],\n\t\"./views/Logs/Index.vue\": [\n\t\t\"9877\",\n\t\t\"chunk-6e83591c\",\n\t\t\"chunk-ef28925c\"\n\t],\n\t\"./views/Permission/Assign.vue\": [\n\t\t\"5f67\",\n\t\t\"chunk-47211100\"\n\t],\n\t\"./views/Permission/Module.vue\": [\n\t\t\"1be3\",\n\t\t\"chunk-c5ac0cca\"\n\t],\n\t\"./views/Permission/Permission.vue\": [\n\t\t\"3c42\",\n\t\t\"chunk-6f1c3bea\"\n\t],\n\t\"./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_1.vue\": [\n\t\t\"e1fce\",\n\t\t\"chunk-77279526\"\n\t],\n\t\"./views/Recursion/Menu_1/Menu_1_1/Menu_1_1_2.vue\": [\n\t\t\"3c96\",\n\t\t\"chunk-2d0c4aa3\"\n\t],\n\t\"./views/Recursion/Menu_1/Menu_1_2.vue\": [\n\t\t\"0790\",\n\t\t\"chunk-2d0a4854\"\n\t],\n\t\"./views/System/My.vue\": [\n\t\t\"c9a6\",\n\t\t\"chunk-789b0e7e\"\n\t],\n\t\"./views/Task/QuartzJob.vue\": [\n\t\t\"fa19\",\n\t\t\"chunk-cae4df82\"\n\t],\n\t\"./views/TestShow/TestOne.vue\": [\n\t\t\"dbaa\",\n\t\t\"chunk-2d229214\"\n\t],\n\t\"./views/TestShow/TestTwo.vue\": [\n\t\t\"d909\",\n\t\t\"chunk-2d21f214\"\n\t],\n\t\"./views/Tibug/Bugs.vue\": [\n\t\t\"9fa2\",\n\t\t\"chunk-47dd42da\"\n\t],\n\t\"./views/User/Roles.vue\": [\n\t\t\"dd68\",\n\t\t\"chunk-bf843d8a\"\n\t],\n\t\"./views/User/Users.vue\": [\n\t\t\"bfe3\",\n\t\t\"chunk-479d738e\"\n\t],\n\t\"./views/WeChat/BindUser.vue\": [\n\t\t\"c8c13\",\n\t\t\"chunk-770e833a\"\n\t],\n\t\"./views/WeChat/Company.vue\": [\n\t\t\"8a55\",\n\t\t\"chunk-23e41f57\"\n\t],\n\t\"./views/WeChat/Manager.vue\": [\n\t\t\"9c04\",\n\t\t\"chunk-276b085c\"\n\t],\n\t\"./views/WeChat/Menu.vue\": [\n\t\t\"0a92\",\n\t\t\"chunk-4b6066be\"\n\t],\n\t\"./views/WeChat/PushLog.vue\": [\n\t\t\"42e7\",\n\t\t\"chunk-2d0c0c66\"\n\t],\n\t\"./views/WeChat/SendMessage.vue\": [\n\t\t\"5b57\",\n\t\t\"chunk-2d0d2f25\"\n\t],\n\t\"./views/WeChat/SubUser.vue\": [\n\t\t\"aadd\",\n\t\t\"chunk-2d213196\"\n\t],\n\t\"./views/WeChat/Template.vue\": [\n\t\t\"6aec\",\n\t\t\"chunk-2d0da5bf\"\n\t],\n\t\"./views/Welcome.vue\": [\n\t\t\"eec5\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tvar ids = map[req];\n\tif(!ids) {\n\t\treturn Promise.resolve().then(function() {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\treturn Promise.all(ids.slice(1).map(__webpack_require__.e)).then(function() {\n\t\tvar id = ids[0];\n\t\treturn __webpack_require__(id);\n\t});\n}\nwebpackAsyncContext.keys = function webpackAsyncContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackAsyncContext.id = \"627e\";\nmodule.exports = webpackAsyncContext;", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=2&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=2&lang=css&\"", "// 生产环境导入组件\r\ntry {\r\n    module.exports = file => () => import('@/views' + file + '.vue')\r\n} catch (error) {\r\n    // IIS 发布模式下，使用下边这个方法\r\n    // export default  file => () => import('@/views' + file + '.vue')\r\n\r\n    console.info('%c 如果使用 IIS 部署，请\\n 1：修改api.js的base为绝对路径 \\n 2：在根目录创建web.config文件，内容查看https://router.vuejs.org/zh/guide/essentials/history-mode.html \\n 3：配置CORS跨域 \\n ', \"color:blue\")\r\n}\r\n\r\n// web.config ，主要解决IIS 部署，刷新 404 问题，官方文章地址：https://router.vuejs.org/zh/guide/essentials/history-mode.html#%E5%90%8E%E7%AB%AF%E9%85%8D%E7%BD%AE%E4%BE%8B%E5%AD%90\r\n// <?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n// <configuration>\r\n//   <system.webServer>\r\n//     <rewrite>\r\n//       <rules>\r\n//         <rule name=\"Handle History Mode and custom 404/500\" stopProcessing=\"true\">\r\n//           <match url=\"(.*)\" />\r\n//           <conditions logicalGrouping=\"MatchAll\">\r\n//             <add input=\"{REQUEST_FILENAME}\" matchType=\"IsFile\" negate=\"true\" />\r\n//             <add input=\"{REQUEST_FILENAME}\" matchType=\"IsDirectory\" negate=\"true\" />\r\n//           </conditions>\r\n//           <action type=\"Rewrite\" url=\"/\" />\r\n//         </rule>\r\n//       </rules>\r\n//     </rewrite>\r\n//   </system.webServer>\r\n// </configuration>\r\n", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=style&index=1&id=11b2dad1&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=style&index=1&id=11b2dad1&scoped=true&lang=css&\"", "global.antRouter = ''//全局的路由\r\n// 使用id4更改这里2\r\nglobal.IS_IDS4 = false // 默认false，表示使用JWT模式，如果true，表示使用ids4", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('div',{attrs:{\"desktop\":\"12\",\"tablet\":\"8\"}},[_c('dl',[_c('dt',[_vm._v(\"Login successful\")]),_c('dt',[_vm._v(\"Your browser should be redirected soon\")])])])])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <div desktop=\"12\" tablet=\"8\">\r\n      <dl>\r\n        <dt>Login successful</dt>\r\n        <dt>Your browser should be redirected soon</dt>\r\n      </dl>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport applicationUserManager from \"../Auth/applicationusermanager\";\r\n\r\nimport {\r\n  requestLogin,\r\n  requestLoginMock,\r\n  getUserByToken,\r\n  getNavigationBar\r\n} from \"../api/api\";\r\n\r\nimport router from \"@/router\";\r\nimport { resetRouter, filterAsyncRouter } from \"@/router/index\";\r\n\r\nexport default {\r\n  name: \"logincallback-view\",\r\n  data() {\r\n    return {};\r\n  },\r\n  async created() {\r\n    try {\r\n      await applicationUserManager.signinRedirectCallback();\r\n      let _this = this;\r\n      let user = await applicationUserManager.getUser();\r\n      _this.$store.commit(\"saveToken\", user.access_token);\r\n\r\n      var curTime = new Date();\r\n      var expiredate = new Date(\r\n        curTime.setSeconds(curTime.getSeconds() + user.expires_in)\r\n      );\r\n      _this.$store.commit(\"saveTokenExpire\", expiredate);\r\n\r\n      window.localStorage.refreshtime = expiredate;\r\n      window.localStorage.expires_in = user.expires_in;\r\n\r\n      _this.$notify({\r\n        type: \"success\",\r\n        message: `成功获取令牌，项目初始化中...`,\r\n        duration: 3000\r\n      });\r\n      \r\n      user.uRealName=user.profile.name;\r\n      user.uLoginName=user.profile.preferred_username;\r\n      user.uID=user.profile.sub;\r\n      window.localStorage.user = JSON.stringify(user);\r\n      if (user.uID > 0) {\r\n        _this.GetNavigationBar(user.uID);\r\n      }\r\n\r\n    } catch (e) {\r\n      this.$root.$emit(\"show-snackbar\", { message: e });\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取路由树\r\n    GetNavigationBar(uid) {\r\n      var _this = this;\r\n      var loginParams = { uid: uid, t: new Date() };\r\n\r\n      getNavigationBar(loginParams).then(data => {\r\n\r\n        if (!data.success) {\r\n          _this.$message({\r\n            message: data.message,\r\n            type: \"error\"\r\n          });\r\n        } else {\r\n          // _this.closeAlert()\r\n\r\n          _this.$message({\r\n            message: \"后台初始化成功\",\r\n            type: \"success\"\r\n          });\r\n          let userinfo = JSON.parse(\r\n            window.localStorage.user ? window.localStorage.user : null\r\n          );\r\n          _this.$notify({\r\n            type: \"success\",\r\n            message: `登录成功 \\n 欢迎管理员：${\r\n              userinfo.uRealName\r\n            } ！Token 将在 ${parseInt(window.localStorage.expires_in / 60)} 分钟后过期！`,\r\n            duration: 6000\r\n          });\r\n\r\n          window.localStorage.router = JSON.stringify(data.response.children);\r\n\r\n          let getRouter = data.response.children; //后台拿到路由\r\n          getRouter = filterAsyncRouter(getRouter); //过滤路由\r\n          router.$addRoutes(getRouter); //动态添加路由\r\n\r\n          _this.$router.replace(\r\n            _this.$route.query.redirect ? _this.$route.query.redirect : \"/\"\r\n          );\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginCallbackView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginCallbackView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LoginCallbackView.vue?vue&type=template&id=0b3de1b8&\"\nimport script from \"./LoginCallbackView.vue?vue&type=script&lang=js&\"\nexport * from \"./LoginCallbackView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"LoginCallbackView.vue\"\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('p',{staticClass:\"page-container\"},[_vm._v(\"没有找到你要的页面\"),_c('br'),_vm._v(\"你是不是迷路了?\\n\\n\\n        \"),_c('router-link',{attrs:{\"to\":\"/\"}},[_vm._v(\"点击返回首页 \")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./404.vue?vue&type=template&id=fa5bc354&\"\nvar script = {}\nimport style0 from \"./404.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"404.vue\"\nexport default component.exports", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=4&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=4&lang=css&\"", "import Vue from 'vue'\r\nimport Router from 'vue-router'\r\nimport Login from '../views/Login.vue'\r\nimport Welcome from '../views/Welcome'\r\nimport APIDoc from '../views/APIDoc'\r\nimport NoPage from '../views/404'\r\n\r\nimport Layout from \"../views/Layout/Layout\";\r\nconst _import = require('@/router/_import_' + process.env.NODE_ENV)//获取组件的方法\r\n\r\nimport LoginCallbackView from \"../views/LoginCallbackView\";\r\n\r\nVue.use(Router)\r\n\r\nconst createRouter = () => new Router({\r\n    mode: 'hash',\r\n    base: process.env.BASE_URL,\r\n    routes: [\r\n        {\r\n            path: '/404', component: NoPage, name: 'NoPage',\r\n            meta: {\r\n                title: 'NoPage',\r\n                requireAuth: false,\r\n                NoTabPage: true,\r\n                NoNeedHome: true // 添加该字段，表示不需要home模板\r\n            },\r\n            hidden: true\r\n        },\r\n        {\r\n            path: '/APIDoc', component: APIDoc, name: 'APIDoc',\r\n            meta: {\r\n                title: 'APIDoc',\r\n                requireAuth: false\r\n            },\r\n            hidden: true\r\n        },\r\n        {\r\n            path: '/',\r\n            component: Welcome,\r\n            name: '首页',\r\n            iconCls: 'fa-home',//图标样式class\r\n            // hidden: true,\r\n            meta: {\r\n                title: '首页',\r\n                requireAuth: true // 添加该字段，表示进入这个路由是需要登录的\r\n            }\r\n        },\r\n        {\r\n            path: '/login',\r\n            component: Login,\r\n            name: 'login',\r\n            iconCls: 'fa-address-card',//图标样式class\r\n            meta: {\r\n                title: '登录',\r\n                NoTabPage: true,\r\n                NoNeedHome: true // 添加该字段，表示不需要home模板\r\n            },\r\n            hidden: true\r\n        },\r\n        {\r\n          path: \"/callback\",\r\n          name: \"LoginCallbackView\",\r\n          component: LoginCallbackView,\r\n          meta: {\r\n              title: '登出',\r\n              NoTabPage: true,\r\n          },\r\n          hidden: true\r\n        },\r\n        {\r\n            path: '*',\r\n            hidden: true,\r\n            redirect: { path: '/404' }\r\n        }\r\n    ]\r\n})\r\n\r\nconst router = createRouter()\r\n\r\nexport function filterAsyncRouter(asyncRouterMap) {\r\n    //注意这里的 asyncRouterMap 是一个数组\r\n    const accessedRouters = asyncRouterMap.filter(route => {\r\n        if (route.path && !route.IsButton) {\r\n            if (route.path === '/' || route.path === '-') {//Layout组件特殊处理\r\n                route.component = Layout\r\n            } else {\r\n                try {\r\n                    route.component = _import(route.path.replace('/:id',''))\r\n                } catch (e) {\r\n                    try {\r\n                        route.component = () => import('@/views' + route.path.replace('/:id','') + '.vue');\r\n                    } catch (error) {\r\n                        console.info('%c 当前路由 ' + route.path.replace('/:id','') + '.vue 不存在，因此如法导入组件，请检查接口数据和组件是否匹配，并重新登录，清空缓存!', \"color:red\")\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (route.children && route.children.length && !route.IsButton) {\r\n            route.children = filterAsyncRouter(route.children)\r\n        }\r\n        return true\r\n    })\r\n\r\n    return accessedRouters\r\n}\r\n\r\nexport function resetRouter() {\r\n    const newRouter = createRouter()\r\n    router.matcher = newRouter.matcher // the relevant part\r\n}\r\n\r\n\r\nrouter.$addRoutes = (params) => {\r\n\r\n    var f = item => {\r\n        \r\n        if (item['children']) {\r\n            item['children'] = item['children'].filter(f);\r\n            return true;\r\n        } else if (item['IsButton']) {\r\n            return item['IsButton']===false;\r\n        }  else {\r\n            return true;\r\n        }\r\n        \r\n    }\r\n    \r\n    var params = params.filter(f);\r\n\r\n    router.addRoutes(params)\r\n}\r\n\r\nexport default router;\r\n\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"wrapper\"},[_c('ul',{staticClass:\"bg-bubbles\"},[_vm._l((10),function(n){return _c('li',{key:n+'n'})}),_vm._l((5),function(m){return _c('ol',{key:m+'m'})})],2),_c('div',{staticClass:\"bg bg-blur\",staticStyle:{\"display\":\"none\"}}),_c('div',{staticStyle:{\"height\":\"10%\"}}),_c('el-form',{ref:\"ruleForm2\",staticClass:\"demo-ruleForm login-container\",attrs:{\"model\":_vm.ruleForm2,\"rules\":_vm.rules2,\"label-position\":\"left\",\"label-width\":\"0px\"}},[_c('h3',{staticClass:\"title\"},[_vm._v(\"系统登录\")]),_c('el-form-item',{attrs:{\"prop\":\"account\"}},[_c('el-input',{attrs:{\"type\":\"text\",\"auto-complete\":\"off\",\"placeholder\":\"账号\"},model:{value:(_vm.ruleForm2.account),callback:function ($$v) {_vm.$set(_vm.ruleForm2, \"account\", $$v)},expression:\"ruleForm2.account\"}})],1),_c('el-form-item',{attrs:{\"prop\":\"checkPass\"}},[_c('el-input',{attrs:{\"auto-complete\":\"off\",\"show-password\":\"\",\"placeholder\":\"密码\"},model:{value:(_vm.ruleForm2.checkPass),callback:function ($$v) {_vm.$set(_vm.ruleForm2, \"checkPass\", $$v)},expression:\"ruleForm2.checkPass\"}})],1),_c('el-checkbox',{staticClass:\"remember\",attrs:{\"checked\":\"\"},model:{value:(_vm.checked),callback:function ($$v) {_vm.checked=$$v},expression:\"checked\"}},[_vm._v(\"记住密码\")]),_c('div',{staticClass:\"count-test\",staticStyle:{\"margin-bottom\":\"20px\"}},[_c('el-radio-group',{on:{\"change\":_vm.loginAccount},model:{value:(_vm.account3),callback:function ($$v) {_vm.account3=$$v},expression:\"account3\"}},[_c('el-radio-button',{attrs:{\"label\":\"测试账号1\"}}),_c('el-radio-button',{attrs:{\"label\":\"测试账号2\"}}),_c('el-radio-button',{attrs:{\"label\":\"超级管理员\"}})],1)],1),_c('el-form-item',{staticStyle:{\"width\":\"100%\"}},[_c('el-button',{staticStyle:{\"width\":\"100%\"},attrs:{\"type\":\"primary\",\"loading\":_vm.logining},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.handleSubmit2($event)}}},[_vm._v(\"\\n                \"+_vm._s(_vm.loginStr)+\"\\n            \")])],1),_c('el-form-item',{staticStyle:{\"width\":\"100%\"}},[_c('el-button',{staticStyle:{\"width\":\"100%\"},attrs:{\"loading\":_vm.loginingMock},nativeOn:{\"click\":function($event){$event.preventDefault();return _vm.handleSubmitMock($event)}}},[_vm._v(\"Mock登录\\n            \")])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div class=\"wrapper\">\r\n        <ul class=\"bg-bubbles\">\r\n            <li v-for=\"n in 10\" :key=\"n+'n'\"></li>\r\n            <ol v-for=\"m in 5\"  :key=\"m+'m'\"></ol>\r\n        </ul>\r\n        <div class=\"bg bg-blur\" style=\"display: none;\"></div>\r\n        <div style=\"height: 10%;\"></div>\r\n        <el-form :model=\"ruleForm2\" :rules=\"rules2\" ref=\"ruleForm2\" label-position=\"left\" label-width=\"0px\"\r\n                 class=\"demo-ruleForm login-container\">\r\n            <h3 class=\"title\">系统登录</h3>\r\n            <el-form-item prop=\"account\">\r\n                <el-input type=\"text\" v-model=\"ruleForm2.account\" auto-complete=\"off\" placeholder=\"账号\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item prop=\"checkPass\">\r\n                <el-input v-model=\"ruleForm2.checkPass\" auto-complete=\"off\" show-password placeholder=\"密码\"></el-input>\r\n            </el-form-item>\r\n            <el-checkbox v-model=\"checked\" checked class=\"remember\">记住密码</el-checkbox>\r\n\r\n            <div style=\"margin-bottom: 20px;\" class=\"count-test\">\r\n                <el-radio-group @change=\"loginAccount\" v-model=\"account3\">\r\n                    <el-radio-button label=\"测试账号1\"></el-radio-button>\r\n                    <el-radio-button label=\"测试账号2\"></el-radio-button>\r\n                    <el-radio-button label=\"超级管理员\"></el-radio-button>\r\n                </el-radio-group>\r\n            </div>\r\n            <el-form-item style=\"width:100%;\">\r\n                <el-button type=\"primary\" style=\"width:100%;\" @click.native.prevent=\"handleSubmit2\" :loading=\"logining\">\r\n                    {{loginStr}}\r\n                </el-button>\r\n\r\n            </el-form-item>\r\n            <el-form-item style=\"width:100%;\">\r\n\r\n                <el-button :loading=\"loginingMock\" style=\"width:100%;\" @click.native.prevent=\"handleSubmitMock\">Mock登录\r\n                </el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {requestLogin, requestLoginMock, getUserByToken, getNavigationBar} from '../api/api';\r\n\r\n    import router from '@/router'\r\n    import {resetRouter, filterAsyncRouter} from '@/router/index'\r\n\r\n    export default {\r\n        data() {\r\n            return {\r\n                instance: \"\",\r\n                loginStr: '登录',\r\n                logining: false,\r\n                loginingMock: false,\r\n                ruleForm2: {\r\n                    account: 'test',\r\n                    checkPass: 'test'\r\n                },\r\n                account3: '测试账号1',\r\n                rules2: {\r\n                    account: [\r\n                        {required: true, message: '请输入账号', trigger: 'blur'},\r\n                        //{ validator: validaePass }\r\n                    ],\r\n                    checkPass: [\r\n                        {required: true, message: '请输入密码', trigger: 'blur'},\r\n                        //{ validator: validaePass2 }\r\n                    ]\r\n                },\r\n                checked: true\r\n            };\r\n        },\r\n        methods: {\r\n            handleReset2() {\r\n                this.$refs.ruleForm2.resetFields();\r\n            },\r\n            loginAccount() {\r\n\r\n                if (this.account3 == \"测试账号1\") {\r\n                    this.ruleForm2.account = \"test\";\r\n                    this.ruleForm2.checkPass = \"test\";\r\n                } else if (this.account3 == \"测试账号2\") {\r\n                    this.ruleForm2.account = \"test2\";\r\n                    this.ruleForm2.checkPass = \"test2\";\r\n                } else {\r\n                    this.ruleForm2.account = \"blogadmin\";\r\n                    this.ruleForm2.checkPass = \"blogadmin\";\r\n                }\r\n            },\r\n            //这个是用来测试 mock 的，很简单，只需要在 main.js 中开启服务即可\r\n            handleSubmitMock(ev) {\r\n                var _this = this;\r\n                this.$refs.ruleForm2.validate((valid) => {\r\n                    if (valid) {\r\n                        //_this.$router.replace('/table');\r\n                        this.loginingMock = true;\r\n                        //NProgress.start();\r\n                        var loginParams = {username: this.ruleForm2.account, password: this.ruleForm2.checkPass};\r\n\r\n                        requestLoginMock(loginParams).then(data => {\r\n                            this.loginingMock = false;\r\n\r\n                            if (data && data.code && data.msg) {\r\n                                _this.$message({\r\n                                    message: data.code + data.msg + \"，用户名admin,密码123456\",\r\n                                    type: 'error'\r\n                                });\r\n                            } else {\r\n\r\n                                _this.$message({\r\n                                    message: \"测试mock，请在main.js 中开启服务!\",\r\n                                    type: 'error'\r\n                                });\r\n                                console.info('%c 测试mock，请在main.js 中开启服务!', \"color:red\")\r\n                            }\r\n\r\n                        });\r\n                    } else {\r\n                        console.log('error submit!!');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            openAlert(msg) {\r\n                this.instance = this.$notify({\r\n                    title: '提示',\r\n                    message: msg,\r\n                    duration: 0,\r\n                    position: 'top-left'\r\n                });\r\n            },\r\n            closeAlert() {\r\n                this.instance.close()\r\n            },\r\n            // 获取 Token\r\n            handleSubmit2(ev) {\r\n                var _this = this;\r\n                this.$refs.ruleForm2.validate((valid) => {\r\n                    if (valid) {\r\n                        //_this.$router.replace('/table');\r\n                        this.logining = true;\r\n\r\n                        //NProgress.start();\r\n                        var loginParams = {name: this.ruleForm2.account, pass: this.ruleForm2.checkPass};\r\n\r\n\r\n                        // _this.openAlert(\"登录中...\")\r\n\r\n                        _this.loginStr = \"登录中...\";\r\n\r\n                        requestLogin(loginParams).then(data => {\r\n                            if (!data.success) {\r\n                                _this.$message({\r\n                                    message: data.msg,\r\n                                    type: 'error'\r\n                                });\r\n                                _this.logining = false;\r\n                                _this.loginStr = \"重新登录\";\r\n                                // _this.closeAlert()\r\n                            } else {\r\n\r\n                                var token = data.response.token;\r\n                                _this.$store.commit(\"saveToken\", token);\r\n\r\n                                var curTime = new Date();\r\n                                var expiredate = new Date(curTime.setSeconds(curTime.getSeconds() + data.response.expires_in));\r\n                                _this.$store.commit(\"saveTokenExpire\", expiredate);\r\n\r\n                                window.localStorage.refreshtime = expiredate;\r\n                                window.localStorage.expires_in = data.response.expires_in;\r\n\r\n                                _this.$notify({\r\n                                    type: \"success\",\r\n                                    message: `成功获取令牌，项目初始化中...`,\r\n                                    duration: 3000\r\n                                });\r\n\r\n\r\n                                // _this.closeAlert()\r\n                                // _this.openAlert(\"成功获取Token，等待服务器返回用户信息...\")\r\n                                _this.loginStr = \"成功获取Token，等待服务器返回用户信息...\";\r\n\r\n                                _this.getUserInfoByToken(token)\r\n\r\n\r\n                            }\r\n                        }).catch(e => {\r\n                            _this.logining = false;\r\n                            _this.loginStr = \"重新登录\"; \r\n                        });\r\n                    } else {\r\n                        console.log('error submit!!');\r\n                        return false;\r\n                    }\r\n                });\r\n            },\r\n            // 获取用户数据\r\n            getUserInfoByToken(token) {\r\n                var _this = this;\r\n                var loginParams = {token: token};\r\n                getUserByToken(loginParams).then(data => {\r\n\r\n                    if (!data.success) {\r\n                        _this.$message({\r\n                            message: data.msg,\r\n                            type: 'error'\r\n                        });\r\n                    } else {\r\n                        // _this.closeAlert()\r\n                        // _this.openAlert(\"接收到用户数据，开始初始化路由树...\")\r\n                        _this.loginStr = \"接收到用户数据，开始初始化路由树...\";\r\n\r\n\r\n                        window.localStorage.user = JSON.stringify(data.response)\r\n                        if (data.response.uID > 0) {\r\n                            _this.GetNavigationBar(data.response.uID)\r\n                        }\r\n                    }\r\n                });\r\n            },\r\n            // 获取路由树\r\n            GetNavigationBar(uid) {\r\n                var _this = this;\r\n                var loginParams = {uid: uid, t: new Date()};\r\n\r\n                getNavigationBar(loginParams).then(data => {\r\n                    _this.logining = false;\r\n\r\n\r\n                    if (!data.success) {\r\n                        _this.$message({\r\n                            message: data.msg,\r\n                            type: 'error'\r\n                        });\r\n                    } else {\r\n\r\n                        // _this.closeAlert()\r\n\r\n                        _this.$message({\r\n                            message: \"后台初始化成功\",\r\n                            type: 'success'\r\n                        });\r\n\r\n\r\n                        let userinfo = JSON.parse(window.localStorage.user ? window.localStorage.user : null);\r\n                        _this.$notify({\r\n                            type: \"success\",\r\n                            message: `登录成功 \\n 欢迎管理员：${userinfo.uRealName} ！Token 将在 ${window.localStorage.expires_in / 60} 分钟后过期！`,\r\n                            duration: 6000\r\n                        });\r\n\r\n\r\n                        window.localStorage.router = (JSON.stringify(data.response.children));\r\n\r\n                        let getRouter = data.response.children//后台拿到路由\r\n                        getRouter = filterAsyncRouter(getRouter) //过滤路由\r\n                        router.$addRoutes(getRouter) //动态添加路由\r\n\r\n                        _this.$router.replace(_this.$route.query.redirect ? _this.$route.query.redirect : \"/\");\r\n                    }\r\n                });\r\n            }\r\n        },\r\n        mounted() {\r\n            // window.localStorage.clear()\r\n            console.info('%c 本地缓存已清空!', \"color:green\")\r\n\r\n        },\r\n    }\r\n\r\n</script>\r\n\r\n<style>\r\n    .bg {\r\n        margin: 0px;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: url(../assets/loginbck.png) no-repeat top left;\r\n        background-repeat: no-repeat;\r\n        background-size: cover;\r\n        width: 100%;\r\n        height: 100%;\r\n    }\r\n\r\n    .login-container {\r\n        -webkit-border-radius: 5px;\r\n        border-radius: 5px;\r\n        -moz-border-radius: 5px;\r\n        background-clip: padding-box;\r\n        margin: auto;\r\n        width: 350px;\r\n        padding: 35px 35px 15px 35px;\r\n        background: #fff;\r\n        border: 1px solid #eaeaea;\r\n        box-shadow: 0 0 25px #cac6c6;\r\n        z-index: 9999;\r\n        position: relative;\r\n    }\r\n\r\n    .login-container .title {\r\n        margin: 0px auto 40px auto;\r\n        text-align: center;\r\n        color: #505458;\r\n    }\r\n\r\n    .login-container .remember {\r\n        margin: 0px 0px 25px 0px;\r\n    }\r\n    \r\n    .wrapper {\r\n        background: #50a3a2;\r\n        background: -webkit-linear-gradient(top left, #50a3a2 0%, #53e3a6 100%);\r\n        background: linear-gradient(to bottom right, #127c7b 0, #50a3a2);\r\n        opacity: 0.8;\r\n        position: absolute;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        overflow: hidden;\r\n    }\r\n\r\n    .wrapper.form-success .containerLogin h1 {\r\n        -webkit-transform: translateY(85px);\r\n        -ms-transform: translateY(85px);\r\n        transform: translateY(85px);\r\n    }\r\n\r\n    .containerLogin {\r\n        max-width: 600px;\r\n        margin: 0 auto;\r\n        padding: 80px 0;\r\n        height: 400px;\r\n        text-align: center;\r\n    }\r\n\r\n    .containerLogin h1 {\r\n        font-size: 40px;\r\n        -webkit-transition-duration: 1s;\r\n        transition-duration: 1s;\r\n        -webkit-transition-timing-function: ease-in-put;\r\n        transition-timing-function: ease-in-put;\r\n        font-weight: 200;\r\n    }\r\n\r\n    .bg-bubbles {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: 1;\r\n    }\r\n\r\n    .bg-bubbles li, .bg-bubbles ol {\r\n        position: absolute;\r\n        list-style: none;\r\n        display: block;\r\n        width: 40px;\r\n        height: 40px;\r\n        background-color: rgba(255, 255, 255, 0.15);\r\n        bottom: -160px;\r\n        -webkit-animation: square 25s infinite;\r\n        animation: square 25s infinite;\r\n        -webkit-transition-timing-function: linear;\r\n        transition-timing-function: linear;\r\n    }\r\n\r\n    ol {\r\n        padding: 0 !important;\r\n    }\r\n\r\n    .bg-bubbles ol:nth-child(11) {\r\n        left: 10%;\r\n        top: 10%;\r\n        width: 20px;\r\n        height: 20px;\r\n    }\r\n\r\n    .bg-bubbles ol:nth-child(12) {\r\n        left: 20%;\r\n        top: 40%;\r\n\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .bg-bubbles ol:nth-child(13) {\r\n        left: 65%;\r\n        top: 30%;\r\n\r\n        width: 100px;\r\n        height: 60px;\r\n    }\r\n\r\n    .bg-bubbles ol:nth-child(14) {\r\n        left: 70%;\r\n        top: 30%;\r\n        width: 100px;\r\n        height: 150px;\r\n    }\r\n\r\n    .bg-bubbles ol:nth-child(15) {\r\n        left: 50%;\r\n        top: 70%;\r\n\r\n        width: 40px;\r\n        height: 60px;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(1) {\r\n        left: 10%;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(2) {\r\n        left: 20%;\r\n        width: 80px;\r\n        height: 80px;\r\n        -webkit-animation-delay: 2s;\r\n        animation-delay: 2s;\r\n        -webkit-animation-duration: 17s;\r\n        animation-duration: 17s;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(3) {\r\n        left: 25%;\r\n        -webkit-animation-delay: 4s;\r\n        animation-delay: 4s;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(4) {\r\n        left: 40%;\r\n        width: 60px;\r\n        height: 60px;\r\n        -webkit-animation-duration: 22s;\r\n        animation-duration: 22s;\r\n        background-color: rgba(255, 255, 255, 0.25);\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(5) {\r\n        left: 70%;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(6) {\r\n        left: 80%;\r\n        width: 120px;\r\n        height: 120px;\r\n        -webkit-animation-delay: 3s;\r\n        animation-delay: 3s;\r\n        background-color: rgba(255, 255, 255, 0.2);\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(7) {\r\n        left: 32%;\r\n        width: 160px;\r\n        height: 160px;\r\n        -webkit-animation-delay: 7s;\r\n        animation-delay: 7s;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(8) {\r\n        left: 55%;\r\n        width: 20px;\r\n        height: 20px;\r\n        -webkit-animation-delay: 15s;\r\n        animation-delay: 15s;\r\n        -webkit-animation-duration: 40s;\r\n        animation-duration: 40s;\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(9) {\r\n        left: 25%;\r\n        width: 10px;\r\n        height: 10px;\r\n        -webkit-animation-delay: 2s;\r\n        animation-delay: 2s;\r\n        -webkit-animation-duration: 40s;\r\n        animation-duration: 40s;\r\n        background-color: rgba(255, 255, 255, 0.3);\r\n    }\r\n\r\n    .bg-bubbles li:nth-child(10) {\r\n        left: 90%;\r\n        width: 160px;\r\n        height: 160px;\r\n        -webkit-animation-delay: 11s;\r\n        animation-delay: 11s;\r\n    }\r\n\r\n    @-webkit-keyframes square {\r\n        0% {\r\n            -webkit-transform: translateY(0);\r\n            transform: translateY(0);\r\n        }\r\n\r\n        100% {\r\n            -webkit-transform: translateY(-700px) rotate(600deg);\r\n            transform: translateY(-700px) rotate(600deg);\r\n        }\r\n    }\r\n\r\n    @keyframes square {\r\n        0% {\r\n            -webkit-transform: translateY(0);\r\n            transform: translateY(0);\r\n        }\r\n\r\n        100% {\r\n            -webkit-transform: translateY(-700px) rotate(600deg);\r\n            transform: translateY(-700px) rotate(600deg);\r\n        }\r\n    }\r\n\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Login.vue?vue&type=template&id=63ee2f62&\"\nimport script from \"./Login.vue?vue&type=script&lang=js&\"\nexport * from \"./Login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"Login.vue\"\nexport default component.exports", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=1&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./404.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./404.vue?vue&type=style&index=0&lang=css&\"", "import Vue from 'vue'\r\nimport Vuex from 'vuex'\r\nimport Cookies from 'js-cookie'\r\n\r\nVue.use(Vuex)\r\n\r\nexport default new Vuex.Store({\r\n  state: {\r\n      token: null,\r\n      tokenExpire: null,\r\n      tagsStoreList: [],\r\n      language: Cookies.get('language') || 'en',\r\n\r\n  },\r\n  mutations: {\r\n      saveToken(state, data) {\r\n          state.token = data;\r\n          window.localStorage.setItem(\"Token\", data);\r\n      },\r\n      saveTokenExpire(state, data) {\r\n          state.tokenExpire = data;\r\n          window.localStorage.setItem(\"TokenExpire\", data);\r\n      },\r\n      saveTagsData(state, data) {\r\n          state.tagsStoreList = data;\r\n          sessionStorage.setItem(\"Tags\",data)\r\n      },\r\n      SET_LANGUAGE: (state, language) => {\r\n          state.language = language\r\n          Cookies.set('language', language)\r\n      },\r\n\r\n  },\r\n  actions: {\r\n      setLanguage({ commit }, language) {\r\n          commit('SET_LANGUAGE', language)\r\n      },\r\n\r\n  }\r\n})\r\n", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=style&index=0&id=11b2dad1&scoped=true&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=style&index=0&id=11b2dad1&scoped=true&lang=css&\"", "import router from '@/router'\r\nimport { resetRouter, filterAsyncRouter } from '@/router/index'\r\n\r\nimport { getNavigationBar, saveRefreshtime } from '@/api/api';\r\nimport store from \"@/store\";\r\n\r\nimport applicationUserManager from \"./Auth/applicationusermanager\";\r\n\r\n//用来获取后台拿到的路由\r\nvar getRouter\r\nif (!getRouter) {//不加这个判断，路由会陷入死循环\r\n    if (!getObjArr('router')) {\r\n        //本地没有，则从数据库获取\r\n        var user = window.localStorage.user ? JSON.parse(window.localStorage.user) : null;\r\n        if (user && user.uID > 0) {\r\n            console.info(user.uID)\r\n            var loginParams = { uid: user.uID };\r\n            getNavigationBar(loginParams).then(data => {\r\n                if (data.success) {\r\n                    console.info('%c get navigation bar from api succeed!', \"color:red\")\r\n                    getRouter = data.response.children//后台拿到路由\r\n                    saveObjArr('router', getRouter) //存储路由到localStorage\r\n                    // routerGo(to, next)//执行路由跳转方法\r\n                }\r\n            });\r\n        }\r\n    } else {\r\n        //从localStorage拿到了路由\r\n        console.info('%c get navigation bar from localStorage succeed!', \"color:green\")\r\n        getRouter = getObjArr('router')//拿到路由\r\n        getRouter = filterAsyncRouter(getRouter) //过滤路由\r\n        router.$addRoutes(getRouter) //动态添加路由\r\n        global.antRouter = getRouter //将路由数据传递给全局变量，做侧边栏菜单渲染工作\r\n    }\r\n\r\n}\r\n\r\nvar storeTemp = store;\r\nrouter.beforeEach((to, from, next) => {\r\n    //验证Token\r\n    {\r\n        if (!storeTemp.state.token) {\r\n            storeTemp.commit(\"saveToken\", window.localStorage.Token)\r\n        }\r\n        if (!storeTemp.state.tokenExpire) {\r\n            storeTemp.commit(\"saveTokenExpire\", window.localStorage.TokenExpire)\r\n        }\r\n        saveRefreshtime();\r\n        if (to.meta.requireAuth) {\r\n            // 判断该路由是否需要登录权限\r\n            var curTime = new Date()\r\n            var expiretime = new Date(Date.parse(window.localStorage.TokenExpire))\r\n            if (storeTemp.state.token && storeTemp.state.token != \"undefined\") {\r\n                // 通过vuex state获取当前的token是否存在\r\n                console.log(1)\r\n                next();\r\n            } else {\r\n                store.commit(\"saveToken\", \"\");\r\n                store.commit(\"saveTokenExpire\", \"\");\r\n                store.commit(\"saveTagsData\", \"\");\r\n                window.localStorage.removeItem('user');\r\n                window.localStorage.removeItem('NavigationBar');\r\n                window.localStorage.removeItem('router');\r\n\r\n                \r\n                if (global.IS_IDS4) {\r\n                applicationUserManager.login();\r\n                }else{\r\n                    next({\r\n                        path: \"/login\",\r\n                        query: { redirect: to.fullPath } // 将跳转的路由path作为参数，登录成功后跳转到该路由\r\n                    });\r\n\r\n                    //window.location.reload()\r\n                }\r\n\r\n            }\r\n        } else {          \r\n            console.log(2)\r\n            next();\r\n        }\r\n    }\r\n\r\n    //动态添加路由\r\n    {\r\n        //不加这个判断，路由会陷入死循环\r\n        if (!getRouter) {\r\n            if (!getObjArr('router')) {\r\n                var user = window.localStorage.user ? JSON.parse(window.localStorage.user) : null;\r\n                if (user && user.uID > 0) {\r\n                    var loginParams = { uid: user.uID };\r\n                    getNavigationBar(loginParams).then(data => {\r\n                        console.log('router before each get navigation bar from api succeed!')\r\n                        if (data.success) {\r\n                            getRouter = data.response.children//后台拿到路由\r\n                            saveObjArr('router', getRouter) //存储路由到localStorage\r\n                            routerGo(to, next)//执行路由跳转方法\r\n                        }\r\n                    });\r\n                }\r\n            } else {\r\n                //从localStorage拿到了路由\r\n                getRouter = getObjArr('router')//拿到路由\r\n                routerGo(to, next)\r\n            }\r\n        } else {\r\n\r\n            if (to.name && to.name != 'login') {\r\n                getRouter = getObjArr('router')//拿到路由\r\n                global.antRouter = getRouter\r\n                // routerGo(to, next)//执行路由跳转方法\r\n            }\r\n            // console.log(3)\r\n            // next()\r\n\r\n        }\r\n    }\r\n});\r\n\r\n\r\nfunction routerGo(to, next) {\r\n\r\n    //过滤路由\r\n    getRouter = filterAsyncRouter(getRouter)\r\n    resetRouter()\r\n\r\n    //动态添加路由\r\n    router.$addRoutes(getRouter)\r\n\r\n    //将路由数据传递给全局变量，做侧边栏菜单渲染工作\r\n    global.antRouter = getRouter\r\n    next({ ...to, replace: true })\r\n}\r\n\r\n//localStorage 存储数组对象的方法\r\nfunction saveObjArr(name, data) {\r\n    localStorage.setItem(name, JSON.stringify(data))\r\n}\r\n\r\n//localStorage 获取数组对象的方法\r\nfunction getObjArr(name) {\r\n    return JSON.parse(window.localStorage.getItem(name));\r\n}\r\n\r\n\r\nvar buttonList = [];\r\n\r\n\r\nexport const getButtonList = (routePath,routers) => {\r\n    routers.forEach(element => {\r\n        if (routePath&&element.path) {\r\n            let path = routePath.toLowerCase();\r\n            if (element.path && element.path.toLowerCase() === path) {\r\n                buttonList = element.children;\r\n                return;\r\n            } else if (element.children) {\r\n                getButtonList(path,element.children);\r\n            }\r\n        }\r\n    });\r\n    return buttonList;\r\n};\r\n\r\n\r\n", "module.exports = \"data:image/png;base64,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\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-scrollbar',{ref:\"scrollContainer\",staticClass:\"scroll-container\",attrs:{\"vertical\":false},nativeOn:{\"wheel\":function($event){$event.preventDefault();return _vm.handleScroll($event)}}},[_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <el-scrollbar ref=\"scrollContainer\" :vertical=\"false\" class=\"scroll-container\" @wheel.native.prevent=\"handleScroll\">\r\n    <slot />\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nconst tagAndTagSpacing = 4 // tagAndTagSpacing\r\n\r\nexport default {\r\n  name: 'ScrollPane',\r\n  data() {\r\n    return {\r\n      left: 0\r\n    }\r\n  },\r\n  computed: {\r\n    scrollWrapper() {\r\n      return this.$refs.scrollContainer.$refs.wrap\r\n    }\r\n  },\r\n  methods: {\r\n    handleScroll(e) {\r\n      const eventDelta = e.wheelDelta || -e.deltaY * 40\r\n      const $scrollWrapper = this.scrollWrapper\r\n      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4\r\n    },\r\n    moveToTarget(currentTag,tagList) {\r\n\r\n      const $container = this.$refs.scrollContainer.$el\r\n      const $containerWidth = $container.offsetWidth\r\n      const $scrollWrapper = this.scrollWrapper\r\n      // const tagList = this.$parent.$refs.tag\r\n\r\n      let firstTag = null\r\n      let lastTag = null\r\n\r\n      // find first tag and last tag\r\n      if (tagList.length > 0) {\r\n        firstTag = tagList[0]\r\n        lastTag = tagList[tagList.length - 1]\r\n      }\r\n\r\n      if (firstTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = 0\r\n      } else if (lastTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth\r\n      } else {\r\n        // find preTag and nextTag\r\n        const currentIndex = tagList.findIndex(item => item === currentTag)\r\n        const prevTag = tagList[currentIndex - 1]\r\n        const nextTag = tagList[currentIndex + 1]\r\n\r\n        // the tag's offsetLeft after of nextTag\r\n        const afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing\r\n\r\n        // the tag's offsetLeft before of prevTag\r\n        const beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing\r\n\r\n        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {\r\n          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth\r\n        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {\r\n          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style >\r\n  .scroll-container {\r\n    white-space: nowrap;\r\n    position: relative !important;\r\n    overflow: hidden !important;\r\n    width: 100%;\r\n  }\r\n  .scroll-container  .el-scrollbar__bar {\r\n    bottom: 0px;\r\n  }\r\n  .scroll-container  .el-scrollbar__wrap {\r\n    height: 49px;\r\n  }\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScrollPane.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScrollPane.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ScrollPane.vue?vue&type=template&id=22288d51&\"\nimport script from \"./ScrollPane.vue?vue&type=script&lang=js&\"\nexport * from \"./ScrollPane.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ScrollPane.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\ncomponent.options.__file = \"ScrollPane.vue\"\nexport default component.exports", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Login.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScrollPane.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScrollPane.vue?vue&type=style&index=0&lang=css&\"", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=3&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=3&lang=css&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"margin-top\":\"30px\"}},[_c('el-row',{staticClass:\"panel-group\"},[_c('el-col',{staticClass:\"card-panel-col\",staticStyle:{\"float\":\"left\",\"width\":\"calc(100% - 405px)\",\"margin\":\"0\"}},[_c('el-card',{staticClass:\"welcome-card activeuser note\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"今日活跃用户\")])]),_c('div',{staticClass:\"bg-color-sub\",staticStyle:{\"background\":\"rgb(236, 245, 255) none repeat scroll 0% 0%\"}},_vm._l((_vm.welcomeInitData.activeUsers),function(item,index){return _c('div',{key:item.user + index,staticClass:\"bg-blue-sub-item\",style:(_vm.getBck(index))},[_c('el-badge',{staticClass:\"item\",attrs:{\"value\":item.count > 9999 ? '9999+' : item.count,\"type\":_vm.getTypeName(item.count)}},[_c('label',{staticClass:\"acc-user\",attrs:{\"title\":item.user}},[_vm._v(_vm._s(item.user))])])],1)}),0)])],1),_c('div',{staticClass:\"statistical-cus\"},[_c('el-col',{staticClass:\"card-panel-col\"},[_c('div',{staticClass:\"card-panel\"},[_c('div',{staticClass:\"card-panel-description\"},[_c('div',{staticClass:\"card-panel-text\"},[_vm._v(\"今日活跃\")]),_c('span',{staticClass:\"card-acuser-num\",attrs:{\"data-v-6723c96e\":\"\"}},[_vm._v(_vm._s(_vm.welcomeInitData.activeUserCount > 9\n                ? _vm.welcomeInitData.activeUserCount\n                : \"0\" + _vm.welcomeInitData.activeUserCount))])])])]),_c('el-col',{staticClass:\"card-panel-col\"},[_c('div',{staticClass:\"card-panel\"},[_c('div',{staticClass:\"card-panel-description\"},[_c('div',{staticClass:\"card-panel-text\"},[_vm._v(\"今日新增\")]),_c('span',{staticClass:\"card-acuser-num\",attrs:{\"data-v-6723c96e\":\"\"}},[_vm._v(_vm._s(_vm.lineChartDataIDS4.today > 9\n                ? _vm.lineChartDataIDS4.today\n                : \"0\" + _vm.lineChartDataIDS4.today))])])])]),_c('el-col',{staticClass:\"card-panel-col\"},[_c('div',{staticClass:\"card-panel extoday\",on:{\"click\":_vm.toLogs}},[_c('div',{staticClass:\"card-panel-description\"},[_c('div',{staticClass:\"card-panel-text\"},[_vm._v(\"今日异常\")]),_c('span',{staticClass:\"card-panel-num\",attrs:{\"data-v-6723c96e\":\"\"}},[_vm._v(_vm._s(_vm.welcomeInitData.errorCount > 9\n                ? _vm.welcomeInitData.errorCount\n                : \"0\" + _vm.welcomeInitData.errorCount))])])])])],1)],1),_c('el-row',{staticClass:\"panel-group\"},[_c('el-col',{staticClass:\"card-panel-col\",staticStyle:{\"float\":\"left\",\"width\":\"100%\",\"margin\":\"0\"}},[_c('el-card',{staticClass:\"welcome-card activeuser note\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"本月活跃用户\"),_c('span',{staticStyle:{\"color\":\"#ccc\",\"font-size\":\"14px\"}},[_vm._v(\"（使用任务调度，1分钟统计一次）\")])])]),_c('div',{staticClass:\"bg-color-sub\",staticStyle:{\"background\":\"rgb(236, 245, 255) none repeat scroll 0% 0%\"}},_vm._l((_vm.welcomeInitData.activeCount),function(item,index){return _c('div',{key:item.user + index,staticClass:\"bg-blue-sub-item-m\",class:item.count > 9999 ? 'amazing':'',style:(_vm.getBck(index))},[_c('el-badge',{staticClass:\"item\",attrs:{\"value\":item.count > 999999 ? '999999+' : item.count,\"type\":_vm.getTypeName(item.count)}},[_c('label',{staticClass:\"acc-user\",attrs:{\"title\":item.user}},[_vm._v(_vm._s(item.user))])])],1)}),0)])],1)],1),_c('el-card',{staticClass:\"welcome-card note note50\",staticStyle:{\"width\":\"calc(49% - 10px)\",\"margin-right\":\"10px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"操作指南\")])]),_c('div',{staticClass:\"text item\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\"、在vue.config.js中配置项目端口号，以及代理后端API项目域名。\\n    \")]),_c('div',{staticClass:\"text item\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\"、在global.js中配置授权方案global.IS_IDS4。\\n    \")]),_c('div',{staticClass:\"text item\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\"、动态添加页面以及权限配置，看右侧两个动图。\\n    \")]),_c('div',{staticClass:\"text item\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\"、更多内容，查看官方文档：\\n      \"),_c('a',{attrs:{\"href\":\"http://vueadmin.neters.club/.doc/\",\"target\":\"_blank\"}},[_vm._v(\"http://vueadmin.neters.club/.doc/\")]),_vm._v(\"。\\n    \")])]),_c('el-card',{staticClass:\"welcome-card note50\",staticStyle:{\"width\":\"49%\",\"margin\":\"0\",\"font-size\":\"14px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\"服务器\")])]),_c('div',{staticClass:\"text item\"},[_vm._v(\"环境变量：\"+_vm._s(_vm.serverInfo.EnvironmentName))]),_c('div',{staticClass:\"text item\"},[_vm._v(\"系统架构：\"+_vm._s(_vm.serverInfo.OSArchitecture))]),_c('div',{staticClass:\"text item\"},[_vm._v(\"\\n      ContentRootPath：\"+_vm._s(_vm.serverInfo.ContentRootPath)+\"\\n    \")]),_c('div',{staticClass:\"text item\"},[_vm._v(\"WebRootPath：\"+_vm._s(_vm.serverInfo.WebRootPath))]),_c('div',{staticClass:\"text item\"},[_vm._v(\"\\n      .NET Core版本：\"+_vm._s(_vm.serverInfo.FrameworkDescription)+\"\\n    \")]),_c('div',{staticClass:\"text item\"},[_vm._v(\"内存占用：\"+_vm._s(_vm.serverInfo.MemoryFootprint))]),_c('div',{staticClass:\"text item\"},[_vm._v(\"启动时间：\"+_vm._s(_vm.serverInfo.WorkingTime))]),_c('div',[_c('br')])]),_c('el-card',{staticClass:\"welcome-card note\",staticStyle:{\"width\":\"98%\",\"margin-top\":\"20px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"30天用户注册曲线图\")])]),_c('el-col',{staticClass:\"echarts-item\",attrs:{\"span\":24}},[_c('ve-line',{attrs:{\"data\":_vm.lineChartDataIDS4,\"extend\":_vm.extend,\"settings\":_vm.lineChartSettings7Day,\"mark-point\":_vm.lineChartMarkPoint}})],1)],1),_c('el-card',{staticClass:\"welcome-card\",staticStyle:{\"margin-top\":\"20px\",\"width\":\"98%\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"\\n        访问日志\\n        \"),_c('span',{staticStyle:{\"font-size\":\"12px\"}},[_vm._v(\"(Top 50 desc)\")])])]),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\",\"font-size\":\"12px\"},attrs:{\"data\":_vm.welcomeInitData.logs,\"highlight-current-row\":\"\",\"border\":\"\"}},[_c('el-table-column',{attrs:{\"prop\":\"User\",\"label\":\"访问者\",\"width\":\"150px\",\"sortable\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"IP\",\"label\":\"请求地址\",\"width\":\"150px\"}}),_c('el-table-column',{attrs:{\"prop\":\"BeginTime\",\"label\":\"请求时间\",\"width\":\"150px\"}}),_c('el-table-column',{attrs:{\"prop\":\"API\",\"label\":\"访问接口\",\"width\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"RequestMethod\",\"label\":\"Method\",\"width\":\"100px\"}}),_c('el-table-column',{attrs:{\"prop\":\"OPTime\",\"label\":\"响应时长\",\"width\":\"100px\"}}),_c('el-table-column',{attrs:{\"prop\":\"RequestData\",\"label\":\"参数\",\"width\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"Agent\",\"label\":\"Agent\",\"width\":\"80\",\"show-overflow-tooltip\":\"\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticStyle:{\"text-decoration\":\"underline\",\"cursor\":\"pointer\"}},[_vm._v(\"\\n            \"+_vm._s(scope.row.Agent)+\"\\n          \")])]}}])})],1),_c('br')],1),_c('el-card',{staticClass:\"welcome-card\",staticStyle:{\"margin-top\":\"20px\",\"width\":\"98%\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(\"相关配置\")])]),_c('el-aside',[_vm._v(\"1、动态添加一个vue页面：\")]),_c('br'),_c('div',{staticClass:\"text item\"},[_c('i',{staticClass:\"el-icon-edit\"}),_vm._v(\"、更多内容，查看博客园文档：\\n      \"),_c('a',{attrs:{\"href\":\"https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0\",\"target\":\"_blank\"}},[_vm._v(\"https://www.cnblogs.com/laozhang-is-phi/p/10643993.html#autoid-2-6-0\")]),_vm._v(\"。\\n    \")]),_c('br'),_c('hr'),_c('br'),_c('el-aside',[_vm._v(\"2、快速配置接口权限：\")]),_c('br'),_c('div',{staticStyle:{\"height\":\"300px\",\"overflow-y\":\"auto\"}},[_c('el-steps',{attrs:{\"direction\":\"vertical\"}},[_c('el-step',{attrs:{\"title\":\"步骤 1\",\"description\":\"创建一个测试控制器 DemoController\"}}),_c('el-step',{attrs:{\"title\":\"步骤 2\",\"description\":\"修改接口路由地址，带上 [action] ，比如，/api/[controller]/[action]，编译是否正常\"}}),_c('el-step',{attrs:{\"title\":\"步骤 3\",\"description\":\"给需要加权限的路由api上，增加授权特性[[Authorize(Permissions.Name)]]\"}}),_c('el-step',{attrs:{\"title\":\"步骤 4\",\"description\":\"测试 /api/demo/get 接口，是否已经被保护\"}}),_c('el-step',{attrs:{\"title\":\"步骤 5.1\",\"description\":\"vueadmin 后台 配置权限：第一步：登录后台，新建api接口\"}}),_c('el-step',{attrs:{\"title\":\"步骤 5.2\",\"description\":\"第二步：添加一个菜单，可以是一个查询按钮，也可以是一个路由页面\"}}),_c('el-step',{attrs:{\"title\":\"步骤 5.3\",\"description\":\"第三步：权限分配！勾选角色和刚刚的菜单\"}}),_c('el-step',{attrs:{\"title\":\"步骤 6\",\"description\":\"如果后端netcore资源服务器有缓存，记得清理\"}}),_c('el-step',{attrs:{\"title\":\"步骤 7\",\"description\":\"重新登录Admin管理后台，访问接口，查看是否有权限\"}})],1)],1),_c('br')],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Welcome.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Welcome.vue?vue&type=template&id=11b2dad1&scoped=true&\"\nimport script from \"./Welcome.vue?vue&type=script&lang=js&\"\nexport * from \"./Welcome.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Welcome.vue?vue&type=style&index=0&id=11b2dad1&scoped=true&lang=css&\"\nimport style1 from \"./Welcome.vue?vue&type=style&index=1&id=11b2dad1&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"11b2dad1\",\n  null\n  \n)\n\ncomponent.options.__file = \"Welcome.vue\"\nexport default component.exports"], "sourceRoot": ""}