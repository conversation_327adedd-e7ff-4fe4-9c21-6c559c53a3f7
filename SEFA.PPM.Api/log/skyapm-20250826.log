2025-08-26 14:10:00.552 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Initializing ...
2025-08-26 14:10:00.577 +08:00 [FA_DFM] [Information] SkyApm.Service.RegisterService : Loaded instrument service [SkyApm.Service.RegisterService].
2025-08-26 14:10:00.579 +08:00 [FA_DFM] [Information] SkyApm.Service.PingService : Loaded instrument service [SkyApm.Service.PingService].
2025-08-26 14:10:00.580 +08:00 [FA_DFM] [Information] SkyApm.Service.SegmentReportService : Loaded instrument service [SkyApm.Service.SegmentReportService].
2025-08-26 14:10:00.580 +08:00 [FA_DFM] [Information] SkyApm.Service.CLRStatsService : Loaded instrument service [SkyApm.Service.CLRStatsService].
2025-08-26 14:10:00.625 +08:00 [FA_DFM] [Information] SkyApm.Sampling.SimpleCountSamplingInterceptor : Loaded instrument service [SkyApm.Sampling.SimpleCountSamplingInterceptor].
2025-08-26 14:10:00.626 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectService : Loaded instrument service [SkyApm.Transport.Grpc.ConnectService].
2025-08-26 14:10:00.688 +08:00 [FA_DFM] [Information] SkyApm.Diagnostics.TracingDiagnosticProcessorObserver : Loaded diagnostic listener [Microsoft.AspNetCore].
2025-08-26 14:10:00.688 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Started SkyAPM .NET Core Agent.
2025-08-26 14:39:43.865 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Initializing ...
2025-08-26 14:39:43.882 +08:00 [FA_DFM] [Information] SkyApm.Service.RegisterService : Loaded instrument service [SkyApm.Service.RegisterService].
2025-08-26 14:39:43.884 +08:00 [FA_DFM] [Information] SkyApm.Service.PingService : Loaded instrument service [SkyApm.Service.PingService].
2025-08-26 14:39:43.884 +08:00 [FA_DFM] [Information] SkyApm.Service.SegmentReportService : Loaded instrument service [SkyApm.Service.SegmentReportService].
2025-08-26 14:39:43.884 +08:00 [FA_DFM] [Information] SkyApm.Service.CLRStatsService : Loaded instrument service [SkyApm.Service.CLRStatsService].
2025-08-26 14:39:43.885 +08:00 [FA_DFM] [Information] SkyApm.Sampling.SimpleCountSamplingInterceptor : Loaded instrument service [SkyApm.Sampling.SimpleCountSamplingInterceptor].
2025-08-26 14:39:43.885 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectService : Loaded instrument service [SkyApm.Transport.Grpc.ConnectService].
2025-08-26 14:39:43.974 +08:00 [FA_DFM] [Information] SkyApm.Diagnostics.TracingDiagnosticProcessorObserver : Loaded diagnostic listener [Microsoft.AspNetCore].
2025-08-26 14:39:43.974 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Started SkyAPM .NET Core Agent.
2025-08-26 17:23:13.405 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Initializing ...
2025-08-26 17:23:13.432 +08:00 [FA_DFM] [Information] SkyApm.Service.RegisterService : Loaded instrument service [SkyApm.Service.RegisterService].
2025-08-26 17:23:13.435 +08:00 [FA_DFM] [Information] SkyApm.Service.PingService : Loaded instrument service [SkyApm.Service.PingService].
2025-08-26 17:23:13.435 +08:00 [FA_DFM] [Information] SkyApm.Service.SegmentReportService : Loaded instrument service [SkyApm.Service.SegmentReportService].
2025-08-26 17:23:13.435 +08:00 [FA_DFM] [Information] SkyApm.Service.CLRStatsService : Loaded instrument service [SkyApm.Service.CLRStatsService].
2025-08-26 17:23:13.436 +08:00 [FA_DFM] [Information] SkyApm.Sampling.SimpleCountSamplingInterceptor : Loaded instrument service [SkyApm.Sampling.SimpleCountSamplingInterceptor].
2025-08-26 17:23:13.438 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectService : Loaded instrument service [SkyApm.Transport.Grpc.ConnectService].
2025-08-26 17:23:13.515 +08:00 [FA_DFM] [Information] SkyApm.Diagnostics.TracingDiagnosticProcessorObserver : Loaded diagnostic listener [Microsoft.AspNetCore].
2025-08-26 17:23:13.515 +08:00 [FA_DFM] [Information] SkyApm.InstrumentStartup : Started SkyAPM .NET Core Agent.
2025-08-26 17:23:23.654 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:23:28.472 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:23:38.492 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:23:43.469 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:23:53.494 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:23:58.453 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:24:08.476 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:24:13.467 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:24:23.488 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:24:28.457 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:24:38.487 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:24:43.458 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:24:53.477 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:24:58.454 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
2025-08-26 17:25:08.472 +08:00 [FA_DFM] [Error] SkyApm.Transport.Grpc.ConnectionManager : Connect server timeout.
System.Threading.Tasks.TaskCanceledException: Reached deadline.
   at Grpc.Core.Channel.WaitForStateChangedAsync(ChannelState lastObservedState, Nullable`1 deadline)
   at Grpc.Core.Channel.ConnectAsync(Nullable`1 deadline)
   at SkyApm.Transport.Grpc.ConnectionManager.ConnectAsync()
2025-08-26 17:25:13.459 +08:00 [FA_DFM] [Information] SkyApm.Transport.Grpc.ConnectionManager : Shutdown connection[elasticsearch:11800].
