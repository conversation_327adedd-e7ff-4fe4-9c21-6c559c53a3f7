using SEFA.PTM.IServices;
using SEFA.Base.Model;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.PTM.Services;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PTMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcessOrderViewController : BaseApiController
    {
        /// <summary>
        /// ProcessOrderView
        /// </summary>
        private readonly IProcessOrderViewServices _processOrderViewServices;


        public ProcessOrderViewController(IProcessOrderViewServices ProcessOrderViewServices)
        {
            _processOrderViewServices = ProcessOrderViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcessOrderViewEntity>>> GetList([FromBody] ProcessOrderViewRequestModel reqModel)
        {
            var data = await _processOrderViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcessOrderViewEntity>>> GetPageList([FromBody] ProcessOrderViewRequestModel reqModel)
        {
            var data = await _processOrderViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

		[HttpPost]
		public async Task<MessageModel<List<ProcessOrderViewEntity>>> GetList2([FromBody] ProcessOrderViewRequestModel reqModel)
		{
			var data = await _processOrderViewServices.GetList2(reqModel);
			return Success(data, "获取成功");
		}

		[HttpPost]
		public async Task<MessageModel<PageModel<ProcessOrderViewEntity>>> GetPageList2([FromBody] ProcessOrderViewRequestModel reqModel)
		{
			var data = await _processOrderViewServices.GetPageList2(reqModel);
			return Success(data, "获取成功");
		}

        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetSegments([FromBody] ProcessOrderViewRequestModel reqModel)
        {
            return await _processOrderViewServices.GetSegments(reqModel);
        }

        [HttpGet("{segment}")]
        public async Task<MessageModel<List<EquipmentEntity>>> GetSegmentUnits(string segment)
        {
            return await _processOrderViewServices.GetSegmentUnits(segment);
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcessOrderViewEntity>> GetEntity(string id)
        {
            var data = await _processOrderViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //public async Task<MessageModel<string>> SaveForm([FromBody] ProcessOrderViewEntity request)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _processOrderViewServices.SaveForm(request);
        //    if (data.success)
        //    {
        //        return Success("", "添加成功");
        //    }
        //    else
        //    {
        //        return Failed("添加失败");
        //    }
        //}


        //[HttpPost]
        //public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        //{
        //    var data = new MessageModel<string>();
        //    data.success = await _processOrderViewServices.DeleteByIds(ids);
        //    if (data.success)
        //    {
        //        return Success("", "删除成功");
        //    }
        //    else
        //    {
        //        return Failed( "删除失败");
        //    }
        //}
    }
    //public class ProcessOrderViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}