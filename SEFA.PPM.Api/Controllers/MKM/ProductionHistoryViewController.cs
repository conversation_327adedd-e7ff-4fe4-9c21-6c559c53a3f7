using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class ProductionHistoryViewController : BaseApiController
    {
        /// <summary>
        /// ProductionHistoryView
        /// </summary>
        private readonly IProductionHistoryViewServices _productionHistoryViewServices;
        private readonly IProduceViewServices _produceViewServices;


        public ProductionHistoryViewController(IProductionHistoryViewServices ProductionHistoryViewServices, IProduceViewServices produceViewServices)
        {
            _productionHistoryViewServices = ProductionHistoryViewServices;
            _produceViewServices = produceViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductionHistoryViewEntity>>> GetList([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<GroupData>>> GetProduceSumList([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetProduceSumList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetProductionMachineGBZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "GBZ";
            var data = await _productionHistoryViewServices.GetProductionMachine(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetProductionMachineZZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "ZZ";
            var data = await _productionHistoryViewServices.GetProductionMachine(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionHistoryViewEntity>>> GetPageList([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionHistoryViewEntityModel>>> GetPageLists([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetPageLists(reqModel);
            return Success(data, "获取成功");
        }


        #region 产出历史制造导出

        /// <summary>
        /// 导出库存数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<IActionResult> ExportProductionHistoryZZ([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            try
            {
                var data = await _productionHistoryViewServices.GetPHisExport(reqModel);
                //  var exportData = _mapper.Map<List<ConsumExports>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(data);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"产出历史制造-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"产出历史制造出现错误:{ex.Message}");
            }
        }

        #endregion


        #region 罐包装产出导出

        /// <summary>
        /// 导出库存数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<IActionResult> ExportProductionHistoryGBZ([FromBody] ProductionHistoryViewRequestModel reqModel)
        {
            try
            {
                var data = await _productionHistoryViewServices.GetPHisExport(reqModel);
                //  var exportData = _mapper.Map<List<ConsumExports>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(data);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"产出历史罐包装-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"产出历史罐包装导出出现错误:{ex.Message}");
            }
        }

        #endregion

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionHistoryViewEntity>> GetEntity(string id)
        {
            var data = await _productionHistoryViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProductionHistoryViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _productionHistoryViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReverseAsync(ReverseModel reqModel)
        {
            var data = await _productionHistoryViewServices.Reverse(reqModel);
            return data;
        }


        [HttpPost]
        public async Task<MessageModel<string>> GetReverseQty(ReverseModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetReverseQty(reqModel);
            return Success(data.msg, "获取成功");
        }



        /// <summary>
        ///  根据节点插叙是否管理库存（返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetEquipmentStorege(ReverseModel reqModel)
        {
            var data = await _productionHistoryViewServices.GetEquipmentStorege(reqModel.EquipmentID);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 重发
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> RepeatPoProducedActual(SendModel reqModel)
        {
            var list = reqModel.id != null ? reqModel.id.ToList() : new List<string>();
            if (reqModel.isReverse)
            {
                return await _produceViewServices.ProduceReverseReport(list);
            }
            return await _produceViewServices.ProduceReport(list);
            //var data = await _productionHistoryViewServices.RepeatPoProducedActual(id);
            //return data;
        }

        /// <summary>
        /// 消耗WMS标签数据
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ScanPoProducedActualWMS(string sscc)
        {
            var data = await _productionHistoryViewServices.ScanWMSPrint(sscc);
            return data;
        }

        /// <summary>
        /// 消耗WMS标签数据灌包装
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ScanPoProducedActualWMSGBZ(string sscc)
        {
            var data = await _productionHistoryViewServices.ScanWMSPrintGBZ(sscc);
            return data;
        }

        [HttpPost]
        public async Task<string> GetproductionEqupmentID(string equpmentID)
        {
            var data = await _productionHistoryViewServices.GetEqupmentID(equpmentID);
            return data;
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _productionHistoryViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class ProductionHistoryViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}