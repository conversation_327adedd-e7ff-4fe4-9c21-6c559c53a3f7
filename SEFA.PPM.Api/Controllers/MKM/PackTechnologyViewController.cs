using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PackTechnologyViewController : BaseApiController
    {
        /// <summary>
        /// PackTechnologyView
        /// </summary>
        private readonly IPackTechnologyViewServices _packTechnologyViewServices;

        public PackTechnologyViewController(IPackTechnologyViewServices PackTechnologyViewServices)
        {
            _packTechnologyViewServices = PackTechnologyViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PackTechnologyViewEntity>>> GetList([FromBody] PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PackTechnologyViewEntity>>> GetPageList([FromBody] PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PackTechnologyViewEntity>> GetEntity(string id)
        {
            var data = await _packTechnologyViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PackTechnologyViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _packTechnologyViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _packTechnologyViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] PackTechnologyViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _packTechnologyViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _packTechnologyViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        /// <summary>
        /// 包装看板-JIT物料状态指示灯
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PackJitModel>>> GetJITMaterial(PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.GetJITMaterial(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 包装看板-获取PAC-ONPK工艺提醒
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PackTechnologyViewEntity>>> GetPackTechnology(PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.GetPackTechnology(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 包装看板-停机时间
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DowntimeModel>>> GetDowntime(PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.GetDowntime(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 包装看板-当前灌装工单信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<CurrentOrderInfoMode>>> CurrentWorkOrderInfo(PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.CurrentWorkOrderInfo(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 包装看板-下一张灌装工单信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<NextOrderInfoModel>>> NextOrderInfo(PackTechnologyViewRequestModel reqModel)
        {
            var data = await _packTechnologyViewServices.NextOrderInfo(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 包装看板-关键设备指示灯
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentStatusModel>>> GetProductivity(PackTechnologyViewRequestModel reqModel)
        {
            return await _packTechnologyViewServices.GetProductivity(reqModel);
            // return Success(data, "获取成功");
        }
    }
}