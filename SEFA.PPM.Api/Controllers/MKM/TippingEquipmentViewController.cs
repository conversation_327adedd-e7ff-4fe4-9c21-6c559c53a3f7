using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TippingEquipmentViewController : BaseApiController
    {
        /// <summary>
        /// TippingEquipmentView
        /// </summary>
        private readonly ITippingEquipmentViewServices _tippingEquipmentViewServices;

        public TippingEquipmentViewController(ITippingEquipmentViewServices TippingEquipmentViewServices)
        {
            _tippingEquipmentViewServices = TippingEquipmentViewServices;
        }
        /// <summary>
        /// 投料看板获取投料信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TippingInfo>>> GetFeedingMes(TippingEquipmentViewRequestModel reqModel)
        {
            var data = await _tippingEquipmentViewServices.GetFeedingMes(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 投料看板-获取当日产线投料进度
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<LineTippingModel>> GetLineTipping(LineTippingViewRequestModel reqModel)
        {
            var data = await _tippingEquipmentViewServices.GetLineTipping(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 投料看板-获取当前配方生产进度
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>

        [HttpPost]
        public async Task<MessageModel<TippingVersionModel>> GetTippingVersion(TippingVersionPlayViewRequestModel reqModel)
        {
            var data = await _tippingEquipmentViewServices.GetTippingVersion(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<TippingEquipmentViewEntity>>> GetPageList([FromBody] TippingEquipmentViewRequestModel reqModel)
        {
            var data = await _tippingEquipmentViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<TippingEquipmentViewEntity>> GetEntity(string id)
        {
            var data = await _tippingEquipmentViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] TippingEquipmentViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _tippingEquipmentViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _tippingEquipmentViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] TippingEquipmentViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _tippingEquipmentViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _tippingEquipmentViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class TippingEquipmentViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}