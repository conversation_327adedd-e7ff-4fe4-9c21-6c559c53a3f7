using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PackIViewController : BaseApiController
    {
        /// <summary>
        /// PackIView
        /// </summary>
        private readonly IPackIViewServices _packIViewServices;
        private readonly IPackSpecIiViewServices _packSpecIiViewServices;

        public PackIViewController(IPackIViewServices PackIViewServices, IPackSpecIiViewServices PackSpecIiViewServices)
        {
            _packIViewServices = PackIViewServices;
            _packSpecIiViewServices = PackSpecIiViewServices;
        }
        /// <summary>
        /// 获取全年产量累加
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AnnualOutModel>> AnnualOutPut(PackIViewRequestModel reqModel)
        {
            var data = await _packIViewServices.AnnualOutPut(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取某年某月产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AnnualOutModel>> CertainYearMonth(PackFebruaryViewRequestModel reqModel)
        {
            var data = await _packIViewServices.CertainYearMonth(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取某年各月产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<EveryMonthModel>> GetEveryMonth(PackEveryMonthViewRequestModel reqModel)
        {
            var data = await _packIViewServices.GetEveryMonth(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 包装车间各线情况（吨/箱）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<LineOutPutTonModel>> LineOutPutTon(PackFebruaryViewRequestModel reqModel)
        {
            var data = await _packIViewServices.LineOutPutTon(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 查规格类别查产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<SpecOutPutModel>> GetSpecOutPut(PackSpecIiViewRequestModel reqModel)
        {
            var data = await _packIViewServices.GetSpecOutPut(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 查询每个规格分类下的规格产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PackSpecModels>> PackSpecOutPut(PackSpecIiViewRequestModel reqModel)
        {
            var data = await _packIViewServices.PackSpecOutPut(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 规格大类下拉
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PackSpecIiViewEntity>>> GetSpeType(PackSpecIiViewRequestModel reqModel)
        {
            var data = await _packSpecIiViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// X月酱料类别产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<FormulaTypeModel>> FormulaType(PackFormulaViewRequestModel reqModel)
        {
            var data = await _packIViewServices.FormulaType(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 按不同方式统计各月产量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DifferentWaysModel>>> DifferentWays(PackRecipeViewRequestModel reqModel)
        {
            var data = await _packIViewServices.DifferentWays(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<PackIViewEntity>>> GetList([FromBody] PackIViewRequestModel reqModel)
        {
            var data = await _packIViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PackIViewEntity>>> GetPageList([FromBody] PackIViewRequestModel reqModel)
        {
            var data = await _packIViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PackIViewEntity>> GetEntity(string id)
        {
            var data = await _packIViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PackIViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _packIViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _packIViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] PackIViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _packIViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _packIViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PackIViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}