using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProductionSummaryViewController : BaseApiController
    {
        /// <summary>
        /// ProductionSummaryView
        /// </summary>
        private readonly IProductionSummaryViewServices _productionSummaryViewServices;

        public ProductionSummaryViewController(IProductionSummaryViewServices ProductionSummaryViewServices)
        {
            _productionSummaryViewServices = ProductionSummaryViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductionSummaryViewEntity>>> GetList([FromBody] ProductionSummaryViewRequestModel reqModel)
        {
            var data = await _productionSummaryViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 产出数据汇总查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionSummaryViewEntity>>> GetPageList([FromBody] ProductionSummaryViewRequestModel reqModel)
        {
            var data = await _productionSummaryViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionSummaryViewEntity>> GetEntity(string id)
        {
            var data = await _productionSummaryViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProductionSummaryViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _productionSummaryViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _productionSummaryViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class ProductionSummaryViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}