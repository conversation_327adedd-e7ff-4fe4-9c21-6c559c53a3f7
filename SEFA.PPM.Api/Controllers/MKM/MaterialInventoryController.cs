using AutoMapper;
using CommunityToolkit.HighPerformance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using MongoDB.Driver;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.MKM.IRepository;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SEFA.PPM.Model.ViewModels.MKM.PrintView;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SqlSugar;
using System.Globalization;
using System.Linq.Expressions;
using SEFA.PPM.Model.Models.MKM;


namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialInventoryController : BaseApiController
    {

        private readonly IMaterialInventoryServices _materialInventoryServices;
        /// <summary>
        /// 注册打印类
        /// </summary>
        private readonly IPrintSelectViewServices _IPrintSelectViewServices;
        private readonly IMaterialSubLotServices _materialSubLotServices;
        private readonly IUser _user;
        private readonly IMaterialLotRepository _materialLotServices;
        private readonly IMaterialSubLotRepository _IMaterialSubLotRepository;
        private readonly IMaterialTransferServices _materialTransferServices;
        private readonly IInventorylistingViewServices _inventorylistingViewServices;
        private readonly IMaterialServices _materialServices;
        private readonly IContainerServices _containerServices;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDicEquipmentSapViewServices _dicEquipmentSapViewServices;
        private readonly IMMaterialPropertyViewServices _IMMaterialPropertyViewServices;
        private readonly IMapper _mapper;
        private readonly IRequestInventoryViewServices _requestInventoryViewServices;
        private readonly ISelectViewServices _iSelectViewServices;


        public MaterialInventoryController(IMaterialInventoryServices MaterialInventoryServices, IMaterialSubLotServices MaterialSubLotServices, IUser user, IMaterialLotRepository materialLotServices, IMaterialTransferServices materialTransferServices, IInventorylistingViewServices inventorylistingViewServices,
            IMaterialServices materialServices, IUnitOfWork unitOfWork, IContainerServices containerServices,
            IDicEquipmentSapViewServices dicEquipmentSapViewServices, IMMaterialPropertyViewServices IMMaterialPropertyViewServices,
            IMapper mapper, IRequestInventoryViewServices requestInventoryViewServices, ISelectViewServices iSelectViewServices, IMaterialSubLotRepository IMaterialSubLotRepository, IPrintSelectViewServices iPrintSelectViewServices, IUnitOfWork iUnitOfWork)
        {
            _materialInventoryServices = MaterialInventoryServices;
            _materialSubLotServices = MaterialSubLotServices;
            _user = user;
            _materialLotServices = materialLotServices;
            _materialTransferServices = materialTransferServices;
            _inventorylistingViewServices = inventorylistingViewServices;
            _materialServices = materialServices;
            _unitOfWork = unitOfWork;
            _containerServices = containerServices;
            _dicEquipmentSapViewServices = dicEquipmentSapViewServices;
            _IMMaterialPropertyViewServices = IMMaterialPropertyViewServices;
            _mapper = mapper;
            _requestInventoryViewServices = requestInventoryViewServices;
            _iSelectViewServices = iSelectViewServices;
            _IMaterialSubLotRepository = IMaterialSubLotRepository;
            _IPrintSelectViewServices = iPrintSelectViewServices;

        }



        [HttpPost]
        public async Task<MessageModel<List<InventorylistingViewEntity>>> GetInvnetGroup([FromBody] TransferModel reqModel)
        {
            var data = await _inventorylistingViewServices.GetInvnetGroup(reqModel);
            return Success(data, "获取成功");
        }
        #region 功能 Inventory Listing

        #region 创建WMS标签

        [HttpPost]
        public async Task<MessageModel<string>> CreateWMSLable([FromBody] InventoryMergesModel models)//(string[] sscsArray, string sscc, string quantity, string isPrint, string selectPrint)
        {
            try
            {
                string[] inventIDS = models.IDS;
                if (inventIDS == null || inventIDS.Length == 0)
                {
                    return Failed("未找到库存信息");
                }
                //获取对应的库存视图及信息
                var model = await _inventorylistingViewServices.GetInventoryListByID(inventIDS);
                if (model == null)
                {
                    return Failed("库存信息不存在");
                }
                List<MaterialInventoryEntity> upInventList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> tranList = new List<MaterialTransferEntity>();
                List<MaterialSubLotEntity> subList = new List<MaterialSubLotEntity>();
                //循环获取需要操作数据(打印并创建标签，写入历史记录)

                string mCode = string.Empty;
                string uName = string.Empty;
                string unitName = string.Empty;
                string lotCode = string.Empty;
                decimal qty = 0;
                // 2(正常)、3(冻结)、4(质检)
                int invstatus = 2;
                //1新增、2删除
                int type = 1;
                // retwarTCItem（退仓打印）、semiP（原物料、半成品入仓打印、 mesPrint mes打印
                string printtype = "mesPrint";
                string sscc = string.Empty;
                DateTime expirationDate = DateTime.Now; string inventID = string.Empty;
                for (int i = 0; i < model.Count; i++)
                {
                    mCode = model[i].MaterialCode;
                    uName = model[i].MaxUnit;
                    inventID = model[i].InventoryId;
                    lotCode = model[i].BatchId;
                    qty = Math.Round(model[i].Quantity == null ? 0 : model[i].Quantity.Value, 3);
                    sscc = model[i].Sscc;
                    expirationDate = model[i].ExpirationDate == null ? DateTime.Now : model[i].ExpirationDate;

                    #region 打印WMS标签，获取子批次号

                    //构造实体
                    PrintSendDataItems wmsPrinit = new PrintSendDataItems
                    {
                        itemcode = mCode,
                        itemunitcode = uName,
                        batch = lotCode,
                        printtype = printtype,
                        qty = qty,
                        type = type,
                        invstatus = invstatus
                    };
                    List<PrintSendDataItems> wmsList = new List<PrintSendDataItems>();
                    wmsList.Add(wmsPrinit);
                    var response = _requestInventoryViewServices.PrintLabelSynchro(wmsList);

                    if (response.Result.successed == true)
                    {
                        PrintResult scanModel = response.Result.Response;
                        //获取批次号和有效期
                        if (scanModel.flag == true)
                        {
                            if (scanModel.data != null && scanModel.data.Count > 0)
                            {
                                //这里只会有一条数据
                                for (int j = 0; j < scanModel.data.Count; j++)
                                {
                                    if (j == 0)
                                    {
                                        string stringDate = scanModel.data[j].validity_date;
                                        string newSSCC = scanModel.data[j].itembarcode;
                                        stringDate = stringDate.Replace('.', '-');
                                        string newSubID = string.Empty;
                                        try
                                        {
                                            DateTime date = DateTime.ParseExact(stringDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                                            stringDate = date.ToString("yyyy-MM-dd") + " 00:00:00";
                                        }
                                        catch
                                        {
                                            stringDate = DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00";
                                        }

                                        #region 创建子批次

                                        if (string.IsNullOrEmpty(newSSCC))
                                        {
                                            return Failed("不存在子批次信息");
                                        }
                                        //新增子批次（判断是否存在批次信息，无创建，有,无需操作）
                                        List<MaterialSubLotEntity> subLotList = await _materialSubLotServices.FindList(x => x.SubLotId == newSSCC);

                                        if (subLotList != null && subLotList.Count > 0)
                                        {
                                            return Failed("子批次已经存在" + newSSCC);
                                        }
                                        else
                                        {
                                            MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                                            submodel.Create(_user.Name.ToString());
                                            newSubID = submodel.ID;
                                            submodel.SubLotId = newSSCC;
                                            submodel.ExternalStatus = model[i].StatusS;
                                            submodel.Type = "0";
                                            subList.Add(submodel);
                                        }

                                        #endregion

                                        #region 写入转移历史

                                        //写入历史记录
                                        MaterialTransferEntity trans1 = new MaterialTransferEntity();
                                        trans1.Create(_user.Name.ToString());
                                        //trans1.OldStorageLocation = model.LocationF;
                                        //trans1.NewStorageLocation = model.LocationF;
                                        trans1.OldLotId = model[i].Lid;
                                        trans1.NewLotId = model[i].Lid;
                                        trans1.OldSublotId = model[i].SlotId;
                                        trans1.NewSublotId = newSubID;
                                        trans1.OldExpirationDate = model[i].ExpirationDate;
                                        trans1.NewExpirationDate = expirationDate;
                                        trans1.Quantity = Math.Round(Convert.ToDecimal(model[i].Quantity), 3);
                                        trans1.QuantityUomId = model[i].UId;
                                        trans1.ProductionExecutionId = model[i].EquipmentRequirementId;
                                        trans1.Type = "Transfer Inventory";
                                        trans1.Comment = "WMS库存标签变更";
                                        trans1.NewEquipmentRequirementId = model[i].EquipmentRequirementId;
                                        trans1.OldEquipmentRequirementId = model[i].EquipmentRequirementId;
                                        //trans.TransferGroupId
                                        trans1.OldEquipmentId = model[i].EquipmentId;
                                        trans1.NewEquipmentId = model[i].EquipmentId;
                                        trans1.OldContainerId = model[i].ContainerId;
                                        trans1.NewContainerId = model[i].ContainerId;
                                        //status
                                        trans1.OldMaterialId = model[i].MaterialId;
                                        trans1.NewMaterialId = model[i].MaterialId;
                                        trans1.OldLotExternalStatus = model[i].StatusF;
                                        trans1.OldSublotExternalStatus = model[i].StatusS;
                                        trans1.NewLotExternalStatus = model[i].StatusF;
                                        trans1.NewSublotExternalStatus = model[i].StatusS;

                                        trans1.PhysicalQuantity = ""; //物理数量
                                        trans1.TareQuantity = 0;  //皮数量

                                        //bool tranHis = await _materialTransferServices.Add(trans1) > 0;
                                        tranList.Add(trans1);
                                        #endregion

                                        #region 库存变更

                                        MaterialInventoryEntity inventModel = await _materialInventoryServices.FindEntity(p => p.ID == inventID);
                                        inventModel.Modify(inventModel.ID, _user.Name.ToString());
                                        inventModel.SublotId = newSubID;
                                        upInventList.Add(inventModel);

                                        #endregion

                                    }
                                }
                            }
                        }
                    }

                    #endregion
                }

                if (upInventList.Count <= 0)
                {
                    return Failed("暂无更新数据");
                }

                _unitOfWork.BeginTran();
                bool bInvent = true;
                bool bSublot = true;
                bool bTran = true;

                if (upInventList.Count > 0)
                {
                    bInvent = await _materialInventoryServices.Update(upInventList);
                }
                if (subList.Count > 0) { bInvent = await _materialSubLotServices.Add(subList) > 0; }
                if (tranList.Count > 0) { bInvent = await _materialTransferServices.Add(tranList) > 0; }

                if (bInvent == true && bSublot == true && bTran == true)
                {
                    _unitOfWork.CommitTran();
                    return Success("", "生成WMS标签成功");
                }
                _unitOfWork.RollbackTran();
                return Failed("生成WMS标签失败");
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("生成WMS标签失败" + ex.Message);
            }

        }


        #endregion



        // <summary>
        /// 获取实体
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<MMaterialPropertyViewEntity>> GetWeightEntityByID(MMaterialPropertyViewRequestModel model)
        {
            model.PropertyCode = "FullBagWeight";
            var data = await _IMMaterialPropertyViewServices.GetList(model);
            if (data.Count <= 0)
            {
                return Success(new MMaterialPropertyViewEntity(), "获取成功");
            }
            return Success(data[0], "获取成功");
        }

        #region 下拉查询

        /// <summary>
        /// 根据下拉的设备code查询是否需要填写子批次(有关系让填写子批次)，判断false 和 true
        /// </summary>
        /// <param name="equipmentCode">选中下拉框</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> GetEquipmentSap(string equipmentCode)
        {
            List<DicEquipmentSapViewEntity> data = await _dicEquipmentSapViewServices.FindList(p => p.EquipmentCode == equipmentCode);
            var datas = new MessageModel<string>();

            if (data == null || data.Count <= 0)
            {
                datas.status = 200;
                datas.success = true;
                datas.response = "false";

                return datas;
            }
            else
            {
                datas.status = 200;
                datas.success = true;
                datas.response = "true";

                return datas;
            }
        }

        #endregion

        #region 查询

        [HttpPost]
        public async Task<MessageModel<PageModel<InventorylistingViewEntity>>> GetPageInVentList([FromBody] InventorylistingViewRequestModel reqModel)
        {
            var data = await _inventorylistingViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<InventorylistingViewEntity>>> GetPageInVentList2([FromBody] InventorylistingViewRequestModel reqModel)
        {
            var data = await _inventorylistingViewServices.GetPageList2(reqModel);
            return Success(data, "获取成功");
        }



        /// <summary>
        /// 获取实体
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<InventorylistingViewEntity>> GetEntityByID(string id)
        {
            var data = await _inventorylistingViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取可选容器下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<ContainerEntity>>> GetContainerSelectList()
        {

            var data = await _containerServices.FindList(p => p.Status != "1" || p.Status != "2" || p.Status != "3");
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取物料下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<MaterialEntity>>> GetMaterialSelectList()
        {

            var data = await _materialServices.FindList(p => p.ID != null);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取物料下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<SelectViewEntity>>> GetMaterialSelectListClass(string keyword)
        {
            //if (string.IsNullOrEmpty(type))
            //{
            //    var datas = await _materialServices.FindList(p => p.ID != null);
            //    return Success(datas, "获取成功");
            //}

            //var data = await _materialServices.FindList(p => p.ID != null && p.Type == type);
            //return Success(data, "获取成功");
            if (string.IsNullOrEmpty(keyword))
            {
                var data = await _iSelectViewServices.FindList(p => p.ID != null);
                return Success(data.Take(10).ToList(), "获取成功");
            }

            var data1 = await _iSelectViewServices.FindList(p => p.ID != null && p.SelectName.Contains(keyword));
            return Success(data1.Take(30).ToList(), "获取成功");
        }
        /// <summary>
        /// 获取物料下拉框
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<SelectViewEntity>>> GetMSelectListByClass(string type, string keyword)
        {
            if (string.IsNullOrEmpty(type))
            {
                var data = await _iSelectViewServices.FindList(p => p.ID != null && p.SelectName.Contains(keyword));
                return Success(data.Take(30).ToList(), "获取成功");
            }

            var data1 = await _iSelectViewServices.FindList(p => p.ID != null && p.SelectName.Contains(keyword));
            return Success(data1.Take(30).ToList(), "获取成功");
        }


        /// <summary>
        /// 获取物料下拉框(成品和半成品)FERT成品、HALB半成品ZSFG 新
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<MaterialEntity>>> GetMaterialSelectList2()
        {
            var data = await _materialServices.FindList(p => p.ID != null && (p.Type == "FERT" || p.Type == "ZSFG"));
            return Success(data, "获取成功");
        }

        #endregion

        #region 导出

        /// <summary>
		/// 导出库存数据
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		[HttpPost]
        public async Task<IActionResult> ExportInventData([FromBody] InventorylistingViewRequestModel reqModel)
        {
            try
            {
                var data = await _inventorylistingViewServices.GetExportList(reqModel);
                var exportData = _mapper.Map<List<VerifiyExport>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(exportData);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"库存导出-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"OpcTag导出出现错误:{ex.Message}");
            }
        }

        #endregion

        #region 操作



        /// <summary>
        /// 新增库存信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddInventory([FromBody] MaterialInventoryModel request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialInventoryServices.AddInventory(request);
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialInventoryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        /// <summary>
        /// New新增库存信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> NewAddInventory([FromBody] MaterialInventoryModel request)
        {

            var data = await _materialInventoryServices.NewAddInventory(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 根据总量来创建数量（零头袋子需要拆分到最后一袋子）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> NewAddInventoryByTotal([FromBody] MaterialInventoryModel request)
        {

            var data = await _materialInventoryServices.NewAddInventoryByTotal(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 根据总量来创建数量（零头袋子需要拆分到最后一袋子）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> NewAddInvenCYC([FromBody] MaterialInventoryModel request)
        {

            var data = await _materialInventoryServices.NewAddInvenCYC(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 删除库存数据string[]
        /// </summary>
        /// <param name="ids">string[]</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteInventory(string[] ids)
        {
            var data = new MessageModel<string>();
            try
            {
                #region 判断当前批次和子批次数据
                ////查询批次状态是否为锁定
                ////查询子批次状态是否为锁定
                //string msg = await _inventorylistingViewServices.GetStateByInventID(ids);

                //if (!string.IsNullOrEmpty(msg))
                //{
                //    return Failed("删除失败" + msg);
                //}

                #endregion

                _unitOfWork.BeginTran();

                for (int i = 0; i < ids.Length; i++)
                {
                    string id = ids[i];
                    if (id != string.Empty)
                    {
                        var model = await _inventorylistingViewServices.QueryById(id);

                        if (model == null)
                        {
                            continue;
                        }
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = model.LocationF;
                        trans.NewStorageLocation = model.LocationF;
                        trans.OldLotId = model.LotId;
                        trans.NewLotId = model.LotId;
                        trans.OldSublotId = model.SlotId;
                        trans.NewSublotId = model.SlotId;
                        trans.OldExpirationDate = model.ExpirationDate;
                        trans.NewExpirationDate = model.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3); //Convert.ToInt32(model.Quantity);
                        trans.QuantityUomId = model.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans.Type = "Delete";
                        trans.Comment = "删除库存";
                        trans.NewEquipmentRequirementId = model.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = model.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = model.EquipmentId;
                        trans.NewEquipmentId = model.EquipmentId;
                        trans.OldContainerId = model.ContainerId;
                        trans.NewContainerId = model.ContainerId;
                        //status
                        trans.OldMaterialId = model.MaterialId;
                        trans.NewMaterialId = model.MaterialId;
                        trans.OldLotExternalStatus = model.StatusF;
                        trans.OldSublotExternalStatus = model.StatusS;
                        trans.NewLotExternalStatus = model.StatusF;
                        trans.NewSublotExternalStatus = model.StatusS;

                        trans.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                        bool tranHis = await _materialTransferServices.Add(trans) > 0;

                        #endregion

                        #region 删除数据                     

                        bool result = await _materialInventoryServices.DeleteById(id);

                        if (!tranHis || !result)
                        {
                            _unitOfWork.RollbackTran();
                            return Failed("删除失败");
                        }

                        #endregion
                    }
                }

                _unitOfWork.CommitTran();
                return Success("", "删除成功");
            }
            catch
            {
                _unitOfWork.RollbackTran();
                return Failed("删除失败");
            }

        }


        /// <summary>
        /// 删除库存数据string[]
        /// </summary>
        /// <param name="ids">string[]</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UnbindInventory(string[] ids)
        {
            var data = new MessageModel<string>();
            try
            {
                #region 判断当前批次和子批次数据
                ////查询批次状态是否为锁定
                ////查询子批次状态是否为锁定
                //string msg = await _inventorylistingViewServices.GetStateByInventID(ids);

                //if (!string.IsNullOrEmpty(msg))
                //{
                //    return Failed("删除失败" + msg);
                //}

                #endregion


                if (ids == null || ids.Length <= 0)
                {

                    return Failed("请选择需要解绑的数据");
                }

                _unitOfWork.BeginTran();

                List<MaterialInventoryEntity> upInventList = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> insertTransfor = new List<MaterialTransferEntity>();
                for (int i = 0; i < ids.Length; i++)
                {
                    string id = ids[i];
                    if (id != string.Empty)
                    {
                        var model = await _inventorylistingViewServices.QueryById(id);

                        if (model == null)
                        {
                            continue;
                        }
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = model.LocationF;
                        trans.NewStorageLocation = model.LocationF;
                        trans.OldLotId = model.LotId;
                        trans.NewLotId = model.LotId;
                        trans.OldSublotId = model.SlotId;
                        trans.NewSublotId = model.SlotId;
                        trans.OldExpirationDate = model.ExpirationDate;
                        trans.NewExpirationDate = model.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3); //Convert.ToInt32(model.Quantity);
                        trans.QuantityUomId = model.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans.Type = "Unbind";
                        trans.Comment = "解绑零头工单库存";
                        trans.NewEquipmentRequirementId = "";
                        trans.OldEquipmentRequirementId = model.ProductionRequestId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = model.EquipmentId;
                        trans.NewEquipmentId = model.EquipmentId;
                        trans.OldContainerId = model.ContainerId;
                        trans.NewContainerId = model.ContainerId;

                        trans.OldBatchId = model.BatchId2;
                        trans.NewBatchId = "";
                        //status
                        trans.OldMaterialId = model.MaterialId;
                        trans.NewMaterialId = model.MaterialId;
                        trans.OldLotExternalStatus = model.StatusF;
                        trans.OldSublotExternalStatus = model.StatusS;
                        trans.NewLotExternalStatus = model.StatusF;
                        trans.NewSublotExternalStatus = model.StatusS;
                        trans.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                        insertTransfor.Add(trans);

                        #endregion

                        #region 更新数据          

                        var models = await _materialInventoryServices.FindEntity(p => p.ID == id);
                        MaterialInventoryEntity upModel = models;
                        upModel.Modify(id, _user.Name);
                        upModel.BatchId = "";
                        upModel.ProductionRequestId = "";
                        upModel.InventoryType = "";
                        upInventList.Add(upModel);

                        #endregion
                    }
                }
                bool tranHis = true;
                bool result = true;

                if (insertTransfor.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(insertTransfor) > 0;
                }
                if (upInventList.Count > 0)
                {
                    result = await _materialInventoryServices.Update(upInventList);
                }

                if (!tranHis || !result)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("解绑失败");
                }

                _unitOfWork.CommitTran();
                return Success("", "解绑成功");
            }
            catch
            {
                _unitOfWork.RollbackTran();
                return Failed("解绑失败");
            }

        }


        /// <summary>
        /// 上锁
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="comment">备注</param>

        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> BlockInventory(string id, string comment)
        {
            try
            {
                if (id == null)
                {
                    return Failed("锁定失败，不存在库存信息");
                }
                var data = new MessageModel<string>();
                //查询当前库存信息

                var model = await _inventorylistingViewServices.QueryById(id);
                if (model == null)
                {
                    return Failed("锁定失败，不存在库存信息");
                }
                else
                {
                    string subID = model.SlotId;
                    string lotID = model.LotId;
                    if (subID == string.Empty)
                    {
                        return Failed("锁定失败，不存在子批次信息");
                    }
                    if (lotID == string.Empty)
                    {
                        return Failed("锁定失败，不存在批次信息");
                    }
                    MaterialLotEntity models = _materialLotServices.FindList(p => p.ID == model.LotId).Result.FirstOrDefault();
                    if (models == null)
                    {
                        return Failed("锁定失败，不存在批次信息");
                    }

                    List<MaterialSubLotEntity> upSubLot = new List<MaterialSubLotEntity>();
                    List<MaterialTransferEntity> itranList = new List<MaterialTransferEntity>();

                    //获取当前批次下所有库存数据
                    var inventDataList = await _inventorylistingViewServices.FindList(p => p.LotId == model.LotId);

                    string subLotID = string.Empty;
                    for (int i = 0; i < inventDataList.Count; i++)
                    {
                        subLotID = inventDataList[i].SlotId;
                        //找子批次
                        var subLostData = await _materialSubLotServices.FindEntity(p => p.ID == subLotID);
                        if (subLostData == null || subLostData.ID == string.Empty || subLostData.ExternalStatus == "1")
                        {
                            continue;
                        }

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());

                        trans.OldStorageLocation = inventDataList[i].LocationF;
                        trans.NewStorageLocation = inventDataList[i].LocationF;
                        trans.OldLotId = inventDataList[i].LotId;
                        trans.NewLotId = inventDataList[i].LotId;
                        trans.OldSublotId = inventDataList[i].SlotId;
                        trans.NewSublotId = inventDataList[i].SlotId;
                        trans.OldExpirationDate = inventDataList[i].ExpirationDate;
                        trans.NewExpirationDate = inventDataList[i].ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventDataList[i].Quantity), 3);  // Convert.ToInt32(inventDataList[i].Quantity);
                        trans.QuantityUomId = inventDataList[i].QuantityUomId;
                        trans.ProductionExecutionId = inventDataList[i].ProductionRequestId;
                        trans.NewProductionExecutionId = inventDataList[i].ProductionRequestId;

                        trans.Type = "BLOCK";
                        trans.Comment = "批次锁定";
                        trans.NewEquipmentRequirementId = inventDataList[i].EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventDataList[i].EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventDataList[i].EquipmentId;
                        trans.NewEquipmentId = inventDataList[i].EquipmentId;
                        trans.OldContainerId = inventDataList[i].ContainerId;
                        trans.NewContainerId = inventDataList[i].ContainerId;
                        //status
                        trans.OldMaterialId = inventDataList[i].MaterialId;
                        trans.NewMaterialId = inventDataList[i].MaterialId;
                        //批次状态（上锁）
                        trans.OldLotExternalStatus = inventDataList[i].StatusF;
                        trans.NewLotExternalStatus = "1";
                        //子批次状态(上锁)
                        trans.OldSublotExternalStatus = inventDataList[i].StatusS;
                        trans.NewSublotExternalStatus = "1";

                        trans.PhysicalQuantity = inventDataList[i].MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventDataList[i].TareWeight == null ? 0 : Convert.ToDecimal(inventDataList[i].TareWeight); //皮数量                

                        itranList.Add(trans);

                        #endregion

                        #region 更新子批次

                        subLostData.Modify(subLostData.ID, _user.Name.ToString());
                        subLostData.ExternalStatus = "1";
                        //subLostData.Comment = "子批次上锁";//(1:B 上锁 2:Q 3:U 解锁)
                        upSubLot.Add(subLostData);
                        #endregion

                    }

                    //更新批次状态，同时需要更改批次下所有子批次状态
                    models.Modify(models.ID, _user.Name.ToString());
                    models.ExternalStatus = "1";
                    //models.Comment = comment;//(1:B 上锁 2:Q 3:U 解锁)

                    _unitOfWork.BeginTran();

                    bool subLotUp = true;
                    if (upSubLot.Count > 0)
                    {
                        subLotUp = await _materialSubLotServices.Update(upSubLot);
                    }

                    if (!subLotUp)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("锁定失败，修改子批次状态失败");
                    }

                    bool upData = await _materialLotServices.Update(models);

                    if (!upData)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("锁定失败，锁定当前数据失败");
                    }

                    bool tranHis = true;
                    if (itranList.Count > 0)
                    {
                        tranHis = await _materialTransferServices.Add(itranList) > 0;
                    }
                    if (!tranHis)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("锁定失败，写入转移历史失败");
                    }
                    _unitOfWork.CommitTran();

                    return Success("", "锁定成功");
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("锁定失败,", ex.Message);
            }
        }


        /// <summary>
        /// 解锁
        /// </summary>
        /// <param name="id"></param>
        /// <param name="comment">备注</param>
        /// <param name="subLotState">状态</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UBlockInventory(string id, string comment, string subLotState)
        {
            if (id == null)
            {
                return Failed("锁定失败，不存在库存信息");
            }
            if (subLotState == null)
            {
                subLotState = "3";
            }
            try
            {
                var data = new MessageModel<string>();

                var model = await _inventorylistingViewServices.QueryById(id);
                if (model == null)
                {
                    return Failed("解锁失败，不存在库存信息");
                }
                else
                {
                    string subID = model.SlotId;
                    string lotID = model.LotId;
                    if (subID == string.Empty)
                    {
                        return Failed("解锁失败，不存在子批次信息");
                    }
                    if (lotID == string.Empty)
                    {
                        return Failed("解锁失败，不存在批次信息");
                    }

                    MaterialSubLotEntity models = _materialSubLotServices.FindList(p => p.ID == subID).Result.FirstOrDefault();
                    if (models == null)
                    {
                        return Failed("解锁失败，不存在子批次信息");
                    }

                    _unitOfWork.BeginTran();

                    #region 判断批次是否为解锁状态

                    MaterialLotEntity lotModel = _materialLotServices.FindList(p => p.ID == lotID).Result.FirstOrDefault();
                    if (lotModel == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("解锁失败，不存在批次信息");
                    }
                    string lotState = lotModel.ExternalStatus;
                    //如果状态为解锁状态无需解锁
                    if (lotState != "2")
                    {
                        lotModel.Modify(lotModel.ID, _user.Name.ToString());
                        lotModel.ExternalStatus = "2";
                        bool upLotData = await _materialLotServices.Update(lotModel);
                        if (upLotData == false)
                        {
                            _unitOfWork.RollbackTran();
                            return Failed("解锁失败，解锁批次失败");
                        }
                    }

                    #endregion


                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = model.LocationF;
                    trans.NewStorageLocation = model.LocationF;
                    trans.OldLotId = model.LotId;
                    trans.NewLotId = model.LotId;
                    trans.OldSublotId = model.SlotId;
                    trans.NewSublotId = model.SlotId;
                    trans.OldExpirationDate = model.ExpirationDate;
                    trans.NewExpirationDate = model.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3);  //Convert.ToInt32(model.Quantity);
                    trans.QuantityUomId = model.QuantityUomId;
                    // trans.ProductionExecutionId = model.ProductionRequestId;
                    trans.Type = "UNBLOCK";
                    trans.Comment = "批次解锁";
                    trans.NewEquipmentRequirementId = model.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = model.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = model.EquipmentId;
                    trans.NewEquipmentId = model.EquipmentId;
                    trans.OldContainerId = model.ContainerId;
                    trans.NewContainerId = model.ContainerId;
                    //status
                    trans.OldMaterialId = model.MaterialId;
                    trans.NewMaterialId = model.MaterialId;

                    trans.OldLotExternalStatus = lotState;
                    trans.NewLotExternalStatus = "2";

                    trans.OldSublotExternalStatus = model.StatusS;
                    trans.NewSublotExternalStatus = subLotState;

                    trans.PhysicalQuantity = model.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                    bool tranHis = await _materialTransferServices.Add(trans) > 0;

                    if (tranHis == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("解锁失败,写入转移历史失败");
                    }
                    #endregion

                    models.Modify(models.ID, _user.Name.ToString());
                    models.Comment = comment;

                    models.ExternalStatus = subLotState;//(1:B 上锁 2:Q 3:U 解锁)
                    bool upData = await _materialSubLotServices.Update(models);

                    if (upData == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("解锁失败，解锁当前数据失败");
                    }

                    _unitOfWork.CommitTran();

                    return Success("", "解锁成功");
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("解锁失败," + ex.Message);
            }

        }



        /// <summary>
        /// 批量打印标签
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="PrintId"></param>
        /// <param name="IS_YS"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintInventLableALL(MaterialInventoryModel model)
        {
            //string[] ids,
            string PrintId = model.PrintId;
            string IS_YS = model.IS_YS;
            var data = new MessageModel<string>();
            if (!string.IsNullOrEmpty(PrintId))
            {
                for (int i = 0; i < model.ids.Length; i++)
                {
                    string id = model.ids[i];
                    if (IS_YS == "YL")
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");
                        //执行打印
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                    }
                    else if (IS_YS == "YLJG")
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                        //执行打印
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                    }
                    else if (IS_YS == "CY")
                    {

                        var inventViewData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                        //执行打印
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");

                        //var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "三厂");
                        //List<object> objs = new List<object>();
                        //objs.Add(inventViewData);

                        ////执行打印
                        //data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                    }
                    else if (IS_YS == "SC")
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //执行打印
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                    }
                    else
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //执行打印
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                    }
                }


                if (data.success == false)
                {
                    return Failed("打印失败");
                }
                else
                {
                    return Success("", "打印成功");
                }
            }

            return Failed("请选择打印机");
        }




        /// <summary>
        /// 批量打印标签
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="PrintId"></param>
        /// <param name="IS_YS"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintMinLableALL(MaterialInventoryModel model)
        {
            //string[] ids,
            string PrintId = model.PrintId;
            string IS_YS = model.IS_YS;
            var data = new MessageModel<string>();
            if (!string.IsNullOrEmpty(PrintId))
            {
                for (int i = 0; i < model.ids.Length; i++)
                {
                    string id = model.ids[i];
                    var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(id);
                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");

                }


                if (data.success == false)
                {
                    return Failed("打印失败");
                }
                else
                {
                    return Success("", "打印成功");
                }
            }

            return Failed("请选择打印机");
        }


        /// <summary>
        /// 更新生产和打印(打印库存标签)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="times"></param>
        /// <param name="remark"></param>
        /// <param name="isPrint"></param>
        /// <param name="PrintId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintInventLable(string id, string PrintId, string IS_YS)
        {
            var data = new MessageModel<string>();
            if (!string.IsNullOrEmpty(PrintId))
            {

                if (IS_YS == "YL")
                {
                    var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");
                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                }
                else if (IS_YS == "YLJG")
                {
                    var inventViewData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                }
                else if (IS_YS == "CY")
                {

                    var inventViewData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");

                    //var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "三厂");
                    //List<object> objs = new List<object>();
                    //objs.Add(inventViewData);

                    ////执行打印
                    //data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                }
                else if (IS_YS == "SC")
                {
                    var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                    List<object> objs = new List<object>();
                    objs.Add(inventViewData);

                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                }
                else
                {
                    var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                    List<object> objs = new List<object>();
                    objs.Add(inventViewData);

                    //执行打印
                    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                }
                if (data.success == false)
                {
                    return Failed("打印失败");
                }
                else
                {
                    return Success("", "打印成功");
                }
            }

            return Failed("请选择打印机");
        }

        /// <summary>
        /// 更新生产和打印(打印小标签)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="times"></param>
        /// <param name="remark"></param>
        /// <param name="isPrint"></param>
        /// <param name="PrintId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintMinLabel(string id, string printId)
        {
            var data = new MessageModel<string>();
            if (!string.IsNullOrEmpty(printId))
            {
                var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(id);
                if (inventViewData == null || inventViewData.Count <= 0)
                {
                    return Failed("打印失败,工单信息不存在");
                }

                //执行打印
                data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(printId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                if (data.success == false)
                {
                    return Failed("打印失败");
                }
                else
                {
                    return Success("", "打印成功");
                }
            }

            return Failed("请选择打印机");
        }

        /// <summary>
        /// 更新生产和打印(打印小标签)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="times"></param>
        /// <param name="remark"></param>
        /// <param name="isPrint"></param>
        /// <param name="PrintId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintPreparaLabel(MaterialInventoryModel model)
        {
            var data = new MessageModel<string>();

            if (model.ids == null)
            {
                return Failed("打印失败，暂无可用库存");
            }
            if (string.IsNullOrEmpty(model.printId))
            {
                return Failed("请选择打印机");
            }

            if (string.IsNullOrEmpty(model.EquipmentId))
            {
                return Failed("请选择房间");
            }
            if (!string.IsNullOrEmpty(model.printId))
            {
                for (int i = 0; i < model.ids.Count(); i++)
                {
                    string id = model.ids[i];
                    var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                    if (inventViewData == null)
                    {
                        continue;
                    }

                    //获取库存数据
                    string qty = inventViewData.NUM;
                    string mCode = inventViewData.Material_Code;
                    if (mCode == model.MCode && Convert.ToDecimal(qty) < Convert.ToDecimal(model.BagSiZe))
                    {
                        List<object> obj = new List<object>();
                        obj.Add(inventViewData);

                        var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_CLBL(model.EquipmentId);//_IPrintSelectViewServices.GetSelectPrinit_Move();
                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;
                            //执行打印                                                                        
                            data = await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(model.printId, teampID, teampClassID, obj, obj[0], 1);
                        }

                        //    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(model.printId, model.EquipmentId, obj, obj[0], 1, "RemainingBagTemplate");

                        //if (data.success == false)
                        //{
                        //    return Failed("打印失败");
                        //}
                    }
                }

                return Success("", "打印成功");
            }

            return Failed("请选择打印机");
        }



        /// <summary>
        /// 更新生产和打印(打印小标签)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="times"></param>
        /// <param name="remark"></param>
        /// <param name="isPrint"></param>
        /// <param name="PrintId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> PrintPreparaLabelKY(MaterialInventoryModel model)
        {
            var data = new MessageModel<string>();

            if (model.ids == null)
            {
                return Failed("打印失败，暂无可用库存");
            }
            if (string.IsNullOrEmpty(model.printId))
            {
                return Failed("请选择打印机");
            }

            if (string.IsNullOrEmpty(model.EquipmentId))
            {
                return Failed("请选择房间");
            }
            if (!string.IsNullOrEmpty(model.printId))
            {
                for (int i = 0; i < model.ids.Count(); i++)
                {
                    string id = model.ids[i];
                    var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                    if (inventViewData == null)
                    {
                        continue;
                    }

                    //获取库存数据
                    string qty = inventViewData.NUM;
                    string mCode = inventViewData.Material_Code;
                    //if (mCode == model.MCode && Convert.ToDecimal(qty) < Convert.ToDecimal(model.BagSiZe))
                    //{
                    List<object> obj = new List<object>();
                    obj.Add(inventViewData);

                    var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_CLBL(model.EquipmentId);//_IPrintSelectViewServices.GetSelectPrinit_Move();
                    if (printIDData != null && printIDData.Count >= 0)
                    {
                        string teampID = printIDData[0].TemplateId;
                        string teampClassID = printIDData[0].TemplateClassId;
                        string printID = printIDData[0].ID;
                        //执行打印                                                                        
                        data = await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(model.printId, teampID, teampClassID, obj, obj[0], 1);
                    }

                    //    data = await _IPrintSelectViewServices.PrintCodeByEquipmentId(model.printId, model.EquipmentId, obj, obj[0], 1, "RemainingBagTemplate");

                    //if (data.success == false)
                    //{
                    //    return Failed("打印失败");
                    //}
                    //}
                }

                return Success("", "打印成功");
            }

            return Failed("请选择打印机");
        }

        /// <summary>
        /// 更新生产和打印
        /// </summary>
        /// <param name="id"></param>
        /// <param name="times"></param>
        /// <param name="remark"></param>
        /// <param name="isPrint"></param>
        /// <param name="PrintId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeTimesInventory(string id, string times, string remark, string isPrint, string PrintId, string IS_YS)
        {
            try
            {
                remark = remark == null ? "" : remark;
                var data = new MessageModel<string>();
                DateTime exTime = DateTime.Now;
                if (!string.IsNullOrEmpty(times))
                {
                    try
                    {
                        exTime = Convert.ToDateTime(times);
                    }
                    catch
                    {
                        return Failed("更新时间失败，时间转换失败");
                    }

                }
                if (isPrint == "true")
                {
                    //判断是否选择打印机
                    if (string.IsNullOrEmpty(PrintId))
                    {
                        return Failed("请选择打印机");
                    }

                }
                if (string.IsNullOrEmpty(id))
                {
                    return Failed("更新时间失败，不存在批次数据");
                }
                else
                {
                    var dataOld = await _materialInventoryServices.QueryById(id);

                    if (dataOld == null)
                    {
                        return Failed("更新时间失败，不存在库存信息");
                    }

                    //这里判断时间是否更改、备注是否更改
                    //查询库存信息(视图)
                    var inventoryModel = await _inventorylistingViewServices.QueryById(id);

                    string remarks = inventoryModel.Remark;

                    if (remarks == remark && exTime == inventoryModel.ExpirationDate)
                    {
                        if (isPrint == "true")
                        {

                            if (IS_YS == "YL")
                            {

                                var lableData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", lableData, lableData[0], 1, "PrintProcessPlantTemplete");
                                return Success("", "更新成功");
                            }
                            else if (IS_YS == "YLJG")
                            {

                                var lableData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", lableData, lableData[0], 1, "PrintProcessPlantTemplete");

                                return Success("", "更新成功");
                            }
                            else if (IS_YS == "CY") //这里和原料一样
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");

                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                                return Success("", "更新成功");
                            }
                            else if (IS_YS == "SC") //这里和原料一样
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");

                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                                return Success("", "更新成功");
                            }
                            else
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                                List<object> objs = new List<object>();
                                objs.Add(inventViewData);
                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                                return Success("", "更新成功");
                            }
                            return Success("", "更新成功");
                        }
                    }

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;

                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

                    #endregion

                    List<MaterialTransferEntity> insertList = new List<MaterialTransferEntity>();
                    _unitOfWork.BeginTran();

                    if (remarks != remark)
                    {
                        //trans.Create(_user.Name.ToString());
                        ////更新备注
                        //trans.Type = "Inventory Update Remark";
                        //trans.Comment = "库存-数量更新";
                        //insertList.Add(trans);
                    }
                    if (exTime != inventoryModel.ExpirationDate)
                    {
                        trans.Create(_user.Name.ToString());
                        //新增日期
                        trans.Type = "Inventory Update ExpirationDate";
                        trans.Comment = "库存-更新日期";
                        insertList.Add(trans);
                    }

                    string lotID = dataOld.LotId;
                    string sblot = dataOld.SublotId;
                    if (lotID == string.Empty)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新时间失败，不存在批次信息");
                    }

                    var lotList = await _materialLotServices.FindEntity(p => p.ID == lotID);
                    if (lotList == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新时间失败，不存在批次信息");
                    }


                    bool resultLot = true;
                    bool resultSubLot = true;
                    bool insertTran = true;
                    bool upInvet = true;

                    List<MaterialSubLotEntity> subList = new List<MaterialSubLotEntity>();
                    if (exTime < DateTime.Now)
                    {
                        lotList.ExternalStatus = "1";
                        lotList.Modify(lotList.ID, _user.Name.ToString());
                        //锁批次
                        resultLot = await _materialLotServices.Update(lotList);
                        //锁子批次
                        var lotLists = await _inventorylistingViewServices.FindList(p => p.LotId == lotList.ID);

                        string[] subLotIDS = lotLists.Select(l => l.SlotId).ToArray();
                        var sLotData = await _IMaterialSubLotRepository.Db.Queryable<MaterialSubLotEntity>().In(P => P.ID, subLotIDS).ToListAsync();
                        for (int j = 0; j < sLotData.Count; j++)
                        {
                            sLotData[j].Modify(sLotData[j].ID, _user.Name.ToString());
                            sLotData[j].ExternalStatus = "1";
                            subList.Add(sLotData[j]);

                            #region MyRegion

                            #region 写入转移历史

                            var lockInventModel = await _inventorylistingViewServices.FindEntity(p => p.SlotId == sLotData[j].ID);

                            //写入历史记录
                            MaterialTransferEntity locktrans = new MaterialTransferEntity();
                            locktrans.Create(_user.Name.ToString());

                            locktrans.OldStorageLocation = lockInventModel.LocationF;
                            locktrans.NewStorageLocation = lockInventModel.LocationF;
                            locktrans.OldLotId = lockInventModel.LotId;
                            locktrans.NewLotId = lockInventModel.LotId;
                            locktrans.OldSublotId = lockInventModel.SlotId;
                            locktrans.NewSublotId = lockInventModel.SlotId;
                            locktrans.OldExpirationDate = lockInventModel.ExpirationDate;
                            locktrans.NewExpirationDate = lockInventModel.ExpirationDate;
                            locktrans.Quantity = Math.Round(Convert.ToDecimal(lockInventModel.Quantity), 3);  // Convert.ToInt32(inventDataList[i].Quantity);
                            locktrans.QuantityUomId = lockInventModel.QuantityUomId;
                            locktrans.ProductionExecutionId = lockInventModel.ProductionRequestId;
                            locktrans.NewProductionExecutionId = lockInventModel.ProductionRequestId;

                            locktrans.Type = "BLOCK";
                            locktrans.Comment = "批次锁定";
                            locktrans.NewEquipmentRequirementId = lockInventModel.EquipmentRequirementId;
                            locktrans.OldEquipmentRequirementId = lockInventModel.EquipmentRequirementId;
                            //trans.TransferGroupId
                            locktrans.OldEquipmentId = lockInventModel.EquipmentId;
                            locktrans.NewEquipmentId = lockInventModel.EquipmentId;
                            locktrans.OldContainerId = lockInventModel.ContainerId;
                            locktrans.NewContainerId = lockInventModel.ContainerId;
                            //status
                            locktrans.OldMaterialId = lockInventModel.MaterialId;
                            locktrans.NewMaterialId = lockInventModel.MaterialId;
                            //批次状态（上锁）
                            locktrans.OldLotExternalStatus = lockInventModel.StatusF;
                            locktrans.NewLotExternalStatus = "1";
                            //子批次状态(上锁)
                            locktrans.OldSublotExternalStatus = lockInventModel.StatusS;
                            locktrans.NewSublotExternalStatus = "1";

                            //locktrans.PhysicalQuantity = lockInventModel.MaxVolume.ToString(); //物理数量
                            //locktrans.TareQuantity = lockInventModel.TareWeight == null ? 0 : Convert.ToDecimal(lockInventModel.TareWeight); //皮数量                

                            insertList.Add(locktrans);

                            #endregion

                            #endregion
                        }
                        resultSubLot = await _materialSubLotServices.Update(subList);
                    }

                    MaterialLotEntity models = _materialLotServices.FindList(p => p.ID == lotID).Result.FirstOrDefault();
                    if (models == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新失败，不存在批次数据");

                    }

                    #region 这里查询子批次

                    MaterialSubLotEntity model = await _materialSubLotServices.FindEntity(p => p.ID == sblot);
                    if (model == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新失败，不存在子批次数据");

                    }

                    model.Modify(model.ID, _user.Name.ToString());
                    model.Comment = remark;
                    if (exTime < DateTime.Now)
                    {
                        model.ExternalStatus = "1";
                    }
                    bool result = await _materialSubLotServices.Update(model);
                    if (result == false || resultLot == false || resultSubLot == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新失败");
                    }
                    #endregion

                    models.Modify(models.ID, _user.Name.ToString());
                    models.ExpirationDate = exTime;

                    upInvet = await _materialLotServices.Update(models);
                    if (insertList.Count > 0)
                    {
                        insertTran = await _materialTransferServices.Add(insertList) > 0;
                    }
                    if (upInvet == true && insertTran == true)
                    {
                        _unitOfWork.CommitTran();

                        //if (isPrint == "true")
                        //{
                        //    var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "三厂");
                        //    List<object> objs = new List<object>();
                        //    objs.Add(inventViewData);
                        //    //执行打印
                        //    await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        //}


                        if (isPrint == "true")
                        {

                            if (IS_YS == "YL")
                            {

                                var lableData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", lableData, lableData[0], 1, "PrintProcessPlantTemplete");


                                return Success("", "更新成功");
                            }
                            if (IS_YS == "YLJG")
                            {

                                var lableData = await _IPrintSelectViewServices.GetPrepareLableDataByTime(id, "");
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", lableData, lableData[0], 1, "PrintProcessPlantTemplete");

                                return Success("", "更新成功");
                            }
                            else if (IS_YS == "CY") //这里和原料一样
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetPrepareLableData(id, "");

                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintProcessPlantTemplete");
                                return Success("", "更新成功");
                            }
                            else
                            {
                                var inventViewData = await _IPrintSelectViewServices.GetInventLabel(id, "");
                                List<object> objs = new List<object>();
                                objs.Add(inventViewData);
                                //执行打印
                                await _IPrintSelectViewServices.PrintCodeByEquipmentId(PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                                return Success("", "更新成功");
                            }
                        }
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新失败");
                    }
                    return Success("", "更新成功");
                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("更新失败" + ex.Message);
            }

        }

        /// <summary>
        /// 更新物料库存数量
        /// </summary>
        /// <param name="id"></param>
        /// <param name="quantity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeQuantityInventory(string id, string quantity)
        {
            try
            {
                var data = new MessageModel<string>();
                if (string.IsNullOrEmpty(id))
                {
                    return Failed("请选中需要修改的库存数据");
                }
                else if (string.IsNullOrEmpty(quantity))
                {
                    return Failed("请确认变更的库存信息是否存在");
                }
                else if (quantity.Length > 19)
                {
                    return Failed("库存数量超长度");
                }
                else
                {
                    MaterialInventoryEntity model = await _materialInventoryServices.QueryById(id);

                    if (!string.IsNullOrEmpty(model.BatchId) || !string.IsNullOrEmpty(model.ProductionRequestId))
                    {
                        return Success("", "更新成功");

                        //      return Failed("无法修改数量该库存已经绑定工单，请先解绑");
                    }


                    #region 判断是否更改，并写入历史记录

                    decimal newQty = Math.Round(Convert.ToDecimal(quantity), 3);
                    decimal oldQty = Math.Round(Convert.ToDecimal(model.Quantity), 3);

                    //如果数量没变无需更新
                    if (newQty == oldQty)   ///if (Convert.ToDecimal(quantity) == model.Quantity)
                    {
                        return Success("", "更新成功");
                    }

                    #region 记录差值

                    string stringQty = string.Empty;
                    decimal cha = 0;

                    if (newQty > oldQty)
                    {
                        cha = newQty - oldQty;
                        stringQty = oldQty + "+" + cha;
                    }
                    else if (newQty < oldQty)
                    {
                        cha = oldQty - newQty;
                        stringQty = oldQty + "-" + cha;
                    }

                    #endregion

                    #region 写入转移历史 

                    //查询库存信息(视图)
                    var inventoryModel = await _inventorylistingViewServices.QueryById(id);

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(quantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Inventory Update Quantity";
                    trans.Comment = "库存-数量更新" + stringQty;
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = inventoryModel.EquipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量


                    #endregion

                    #endregion

                    model.Modify(model.ID, _user.Name.ToString());
                    model.Quantity = Math.Round(Convert.ToDecimal(quantity), 3);

                    _unitOfWork.BeginTran();
                    bool inTran = true;
                    bool upInvent = true;
                    upInvent = await _materialInventoryServices.Update(model);
                    //新增转移历史
                    inTran = await _materialTransferServices.Add(trans) > 0;


                    if (upInvent == true && upInvent == true)
                    {
                        _unitOfWork.CommitTran();
                        return Success("", "更新成功");
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("更新失败");
                    }

                }
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("更新失败" + ex.Message);
            }

        }

        /// <summary>
        /// 校验子批次是否存在
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<MessageModel<string>> Validata(string sscc)
        {
            List<MaterialSubLotEntity> result = await _materialSubLotServices.FindList(p => p.SubLotId == sscc);
            if (result != null && result.Count > 0)
            {
                return Failed("已经存在SSCC");
            }
            return Success("", "不存在SSCC，可新增");

        }

        #region 更新备注信息

        /// <summary>
        /// 更新生产日期操作
        /// </summary>
        /// <param name="id">ID</param>
        /// <param name="times">时间字符串（更新时间）</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeRemarkInventory(InventoryModel models)
        {
            var data = new MessageModel<string>();
            DateTime exTime = DateTime.Now;

            try
            {
                if (!string.IsNullOrEmpty(models.Remark))
                {
                    try
                    {
                        exTime = Convert.ToDateTime(models.Remark);
                    }
                    catch
                    {
                        return Failed("添加备注失败，时间转换失败");
                    }

                }
                if (models.IDS.Length <= 0)
                {
                    return Failed("添加备注失败，不存在批次数据");
                }
                else
                {
                    //获取所有库存信息
                    var datas = await _inventorylistingViewServices.QueryByIDs(models.IDS);


                    if (datas == null)
                    {
                        return Failed("添加备注失败，不存在库存信息");
                    }
                    string[] subID = datas.GroupBy(p => p.SlotId).Select(P => P.Key).ToArray();

                    List<MaterialSubLotEntity> list = new List<MaterialSubLotEntity>();
                    for (int i = 0; i < subID.Length; i++)
                    {
                        string ids = subID[i];
                        if (!string.IsNullOrEmpty(ids))
                        {
                            MaterialSubLotEntity model = await _materialSubLotServices.FindEntity(p => p.ID == ids);
                            model.Modify(model.ID, _user.Name.ToString());
                            model.Comment = models.Remark;
                            list.Add(model);
                        }

                    }

                    if (list.Count <= 0)
                    {
                        return Failed("添加备注失败.不存在信息");
                    }

                    _unitOfWork.BeginTran();

                    bool up = await _materialSubLotServices.Update(list);
                    if (up == true)
                    {
                        _unitOfWork.CommitTran();
                        return Success("", "添加备注成功");
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("添加备注失败");
                    }
                }

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("添加备注失败" + ex.Message);
            }

        }


        #endregion

        #region 合并

        /// <summary>
        /// 根据选中的库存ID集合获取需要合并的列（这里需要考虑只能同批次才能合并，前端需要）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<InventorylistingViewEntity>>> GetInventoryMergeList(string[] ids)
        {
            var data = await _inventorylistingViewServices.GetInventoryListByID(ids);

            if (data.GroupBy(p => p.LotId).Count() != 1)
            {
                return Failed(data, "获取失败,非同批次数据");
            }

            return Success(data, "获取成功");

        }

        /// <summary>
        /// 根据选中的库存ID集合获取需要合并的列（这里需要考虑只能同批次才能合并，前端需要）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetInventoryMergeOK(string[] ids)
        {
            var data = await _inventorylistingViewServices.GetInventoryListByID(ids);

            if (data.GroupBy(p => p.LotId).Count() != 1)
            {
                return Failed("False", "获取失败,非同批次数据");
            }
            return Success("True", "获取成功");

        }
        /// <summary>
        /// 根据设备获取打印机
        /// </summary>
        /// <param name="equmentID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DFM.Model.Models.LabelPrinterEntity>>> GetSelectPrinit(string equmentID)
        {
            var data = await _inventorylistingViewServices.GetSelectPrinit(equmentID);
            return Success(data, "获取成功");

        }

        /// <summary>
        ///获取下拉打印机-备料包库存标签模板/复称(小标签)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_Move()
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_Move(); // (equipmentId, propertyCode);
            return Success(data, "获取成功");

        }


        /// <summary>
        ///获取下拉打印机-备料包库存标签模板/复称(小标签)通过设备
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetPrinit_MoveByEqumentID(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(equipmentId); // (equipmentId, propertyCode);
            return Success(data, "获取成功");
        }


        /// <summary>
        ///  获取下拉打印机-库存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_Bag()
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_Bag();//(equipmentId, propertyCode);
            return Success(data, "获取成功");

        }


        /// <summary>
        /// 获取下拉打印机-托盘和拼锅最后一个
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_Pallet()
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_Pallet();
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> SelectPrinit_PG(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_PG(equipmentId);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> SelectPrinit_KC(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_KC(equipmentId);
            return Success(data, "获取成功");
        }


        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_CY(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_CY(equipmentId);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_SC(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_SC(equipmentId);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GSelectPrinit_CLBL(string equipmentId)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_CLBL(equipmentId);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取拼锅区域打印机
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_CLBLPDA(string equipmentId, string code)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_CLBLPDA(equipmentId, code);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取下拉打印机-原料加工厂备料标签模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_Process()
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_Process();
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 获取下拉打印机-原料加工厂备料标签模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintSelectViewEntity>>> GetSelectPrinit_ProcessFour(PrintSelectViewRequestModel model)
        {
            var data = await _IPrintSelectViewServices.GetSelectPrinit_ProcessFour(model.EquipmentId);
            return Success(data, "获取成功");

        }

        ///// <summary>
        /////库存合并并打印条码
        ///// </summary>
        ///// <param name="sscsArray">删除的子批次（这里如果和列表重复返回时候需要给删掉）</param>
        ///// <param name="sscc">输入框的批次</param>
        ///// <param name="quantity">合并总量</param>
        ///// <param name="isPrint">是否打印</param>
        ///// <param name="selectPrint">打印地址</param>
        ///// <returns></returns>
        /// <summary>
        /// 库存合并并打印条码
        /// </summary>
        /// <param name="models">models</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> InventoryMerges([FromBody] InventoryMergesModel models)//(string[] sscsArray, string sscc, string quantity, string isPrint, string selectPrint)
        {
            string sscc = models.sscc.Trim();
            // string quantity = models.quantity;
            decimal quantity = Math.Round(Convert.ToDecimal(models.quantity), 3);
            bool isPrint = models.isPrint;
            string selectPrint = models.selectPrint;
            string[] idArray = models.sscsArray;

            if (idArray == null || idArray.Length == 0)
            {
                return Failed("未找到库存信息");
            }
            string upID = sscc;
            var data = new MessageModel<string>();
            List<string> list = new List<string>(idArray);
            if (string.IsNullOrEmpty(sscc))
            {
                return Failed("请输入子批次信息");
            }

            #region 查询子批次

            //这里需要判断是否存在子批次信息如果存在需要返回提示信息
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.SubLotId == upID)
                .ToExpression();
            var model = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

            if (model == null)
            {
                return Failed("不存在该子批次信息请重新扫描");
            }

            #region 判断当前批次和子批次数据(有效期)

            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            List<string> inventIDs = new List<string>();

            for (int i = 0; i < idArray.Length; i++)
            {
                inventIDs.Add(idArray[i]);
            }
            inventIDs.Add(model.ID);

            string msg = await _inventorylistingViewServices.GetStateByInventID(inventIDs.ToArray());
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed(msg);
            }

            msg = await _inventorylistingViewServices.GetProductionByInventID(inventIDs.ToArray());
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed(msg);
            }

            #endregion

            if (model != null && model.ID != string.Empty)
            {
                upID = model.ID;
                //不存在移除相同元素,重置需要删除的数据
                list.Remove(upID);
                idArray = list.ToArray();
            }
            else
            {
                return Failed("不存在该子批次信息请重新扫描");
            }

            #endregion

            #region 业务处理

            try
            {
                _unitOfWork.BeginTran();

                #region 更新库存

                //获取需要更新的库存信息
                MaterialInventoryEntity upmodel = new MaterialInventoryEntity();
                upmodel = await _materialInventoryServices.QueryById(upID);
                upmodel.Modify(upID, _user.Name.ToString());

                //if (isSSCC == false)
                //{
                //    //新增子批次
                //    MaterialSubLotEntity materialsub = new MaterialSubLotEntity();
                //    materialsub.Create(_user.Name.ToString());
                //    materialsub.SubLotId = sscc;
                //    materialsub.Type = "0";
                //    materialsub.ExternalStatus = "3";
                //    await _materialSubLotServices.Add(materialsub);
                //    upmodel.SublotId
                //}
                //变更数量和子批次号
                upmodel.SublotId = model.SlotId;
                upmodel.Quantity = Math.Round(Convert.ToDecimal(quantity), 3);// Convert.ToDecimal(quantity);
                bool updateResult = await _materialInventoryServices.Update(upmodel);

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans1 = new MaterialTransferEntity();
                trans1.Create(_user.Name.ToString());
                //trans.ID = Guid.NewGuid().ToString();
                trans1.OldStorageLocation = model.LocationF;
                trans1.NewStorageLocation = model.LocationF;
                trans1.OldLotId = model.LotId;
                trans1.NewLotId = model.LotId;
                trans1.OldSublotId = model.SlotId;
                trans1.NewSublotId = model.SlotId;
                trans1.OldExpirationDate = model.ExpirationDate;
                trans1.NewExpirationDate = model.ExpirationDate;
                trans1.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3);  // Convert.ToInt32(model.Quantity);
                trans1.QuantityUomId = model.QuantityUomId;
                // trans.ProductionExecutionId = model.ProductionRequestId;
                trans1.Type = "Batch Merge";
                trans1.Comment = "库存合并";
                trans1.NewEquipmentRequirementId = model.EquipmentRequirementId;
                trans1.OldEquipmentRequirementId = model.EquipmentRequirementId;
                //trans.TransferGroupId
                trans1.OldEquipmentId = model.EquipmentId;
                trans1.NewEquipmentId = model.EquipmentId;
                trans1.OldContainerId = model.ContainerId;
                trans1.NewContainerId = model.ContainerId;
                //status
                trans1.OldMaterialId = model.MaterialId;
                trans1.NewMaterialId = model.MaterialId;
                trans1.OldLotExternalStatus = model.StatusF;
                trans1.OldSublotExternalStatus = model.StatusS;
                trans1.NewLotExternalStatus = model.StatusF;
                trans1.NewSublotExternalStatus = model.StatusS;

                trans1.PhysicalQuantity = model.MaxVolume.ToString(); //物理数量
                trans1.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                bool tranHis = await _materialTransferServices.Add(trans1) > 0;

                #endregion

                #endregion

                #region 删除多余的物料库存信息

                for (int i = 0; i < idArray.Length; i++)
                {
                    string id = idArray[i];
                    if (id != string.Empty)
                    {
                        string deleteSScc = idArray[i];
                        var modelDelete = await _inventorylistingViewServices.QueryById(deleteSScc);
                        string deleteID = modelDelete.ID;
                        // var modelDelete = await _inventorylistingViewServices.f(id);

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_user.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = modelDelete.LocationF;
                        trans.NewStorageLocation = modelDelete.LocationF;
                        trans.OldLotId = modelDelete.LotId;
                        trans.NewLotId = modelDelete.LotId;
                        trans.OldSublotId = modelDelete.SlotId;
                        trans.NewSublotId = modelDelete.SlotId;
                        trans.OldExpirationDate = modelDelete.ExpirationDate;
                        trans.NewExpirationDate = modelDelete.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(model.Quantity), 3);  // Convert.ToInt32(model.Quantity);
                        trans.QuantityUomId = modelDelete.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans.Type = "Batch Merge";
                        trans.Comment = "库存合并";
                        trans.NewEquipmentRequirementId = modelDelete.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = modelDelete.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = modelDelete.EquipmentId;
                        trans.NewEquipmentId = modelDelete.EquipmentId;
                        trans.OldContainerId = modelDelete.ContainerId;
                        trans.NewContainerId = modelDelete.ContainerId;
                        //status
                        trans.OldMaterialId = modelDelete.MaterialId;
                        trans.NewMaterialId = modelDelete.MaterialId;
                        trans.OldLotExternalStatus = modelDelete.StatusF;
                        trans.OldSublotExternalStatus = modelDelete.StatusS;
                        trans.NewLotExternalStatus = modelDelete.StatusF;
                        trans.NewSublotExternalStatus = modelDelete.StatusS;

                        trans.PhysicalQuantity = modelDelete.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = modelDelete.TareWeight == null ? 0 : modelDelete.TareWeight.Value;  //皮数量

                        bool tranHisDelele = await _materialTransferServices.Add(trans) > 0;

                        #endregion

                        #region 删除数据                     

                        await _materialInventoryServices.DeleteById(deleteID);

                        #endregion
                    }
                }

                #endregion

                _unitOfWork.CommitTran();

                var resultMsg = new MessageModel<string>();

                if (isPrint == true)
                {
                    var inventLable = await _IPrintSelectViewServices.GetInventLabel(model.ID, "");
                    //更新数据的数量
                    inventLable.NUM = quantity.ToString();
                    List<object> objs = new List<object>();
                    objs.Add(inventLable);
                    resultMsg = await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.PrintId, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");

                    //执行打印
                    //selectPrint 打印机
                }

                return Success("", "合并成功" + resultMsg.msg);
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("合并失败");
            }

            #endregion

        }

        #endregion



        #region 拆分

        /// <summary>
        /// 根据id获取其属性，SPEC （这里可能需要拆分字段）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialEntity>> GetMaterial(string id)
        {
            var data = await _materialServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 库存拆分（拆分数据，前端完成按包数和数量的方式进行显示，传入后端需要用实际数量）
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> InventorySplit([FromBody] InventorySplitModel models)
        {
            if (models.isPrint == true)
            {
                string p = models.PrintId;
                if (string.IsNullOrEmpty(p))
                {
                    return Failed("请选择打印机");
                }
            }
            #region 构造实体
            //if (models.isPrint == true)
            //{
            //    var pResult = await _IPrintSelectViewServices.FindEntity(p => p.ID == models.selectPrint);
            //    if (pResult == null)
            //    {
            //        return Failed("请配置打印机");
            //    }

            //    models.TemplateClassId = pResult.TemplateClassId;
            //    models.TemplateId = pResult.TemplateId;

            //}



            if (string.IsNullOrEmpty(models.quantity))
            {
                return Failed("拆分失败，请输入拆分数量");
            }
            SSCCModel model = new SSCCModel();
            model.Type = "";
            model.NextCode = "";
            model.MaxCode = "";
            model.MinCode = "";
            model.Prefix = "";
            model.TableName = "";
            model.TableId = "";
            model.SequenceType = "";
            model.ResetType = "";
            model.FeatureId = "";
            model.pageIndex = 1;
            model.pageSize = 10;
            model.orderByFileds = "";
            string token = _user.GetToken();

            #endregion

            var ssccStrings1 = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);

            //这里不是整袋
            string splitID = models.splitID;
            string equipmentId = models.equipmentId;
            string quantity = models.quantity;
            bool isPrint = models.isPrint;
            string selectPrint = models.selectPrint;
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(splitID))
            {
                return Failed("请确认拆分信息是否存在");
            }

            //打印实体
            List<Object> objList = new List<Object>();

            //查询更新之前的数据
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.ID == splitID)
                 .ToExpression();
            var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);
            if (inventoryModel == null)
            {
                return Failed("拆分失败，未能找到拆分数据");
            }
            List<string> splitIDS = new List<string>();
            splitIDS.Add(splitID);
            string msg = await _inventorylistingViewServices.GetProductionByInventID(splitIDS.ToArray());
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed(msg);
            }

            //这里只拆分两袋
            if (models.offullbags == null || Convert.ToInt32(models.offullbags) <= 0)
            {
                try
                {
                    decimal oldQty = inventoryModel.Quantity.Value;
                    string newSubLot = string.Empty;
                    decimal newQty = 0;

                    if (Convert.ToDecimal(oldQty) - Convert.ToDecimal(quantity) <= 0)
                    {
                        return Failed("拆分失败，拆分数量必须小于当前数量");
                    }

                    #region 原有一袋需要写转移记录

                    ////写入历史记录
                    //MaterialTransferEntity transold = new MaterialTransferEntity();
                    //transold.Create(_user.Name.ToString());
                    //transold.OldStorageLocation = inventoryModel.LocationF;
                    //transold.NewStorageLocation = inventoryModel.LocationF;
                    //transold.OldLotId = inventoryModel.LotId;
                    //transold.NewLotId = inventoryModel.LotId;
                    //transold.OldSublotId = inventoryModel.SubLotId;
                    //transold.NewSublotId = inventoryModel.SubLotId;
                    //transold.OldExpirationDate = inventoryModel.ExpirationDate;
                    //transold.NewExpirationDate = inventoryModel.ExpirationDate;
                    //transold.Quantity = Convert.ToInt32(quantity);
                    //transold.QuantityUomId = inventoryModel.QuantityUomId;
                    //transold.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    //transold.Type = "Split";
                    //transold.Comment = "拆分";
                    //transold.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //transold.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    ////trans.TransferGroupId
                    //transold.OldEquipmentId = inventoryModel.EquipmentId;
                    //transold.NewEquipmentId = equipmentId;
                    //transold.OldContainerId = inventoryModel.ContainerId;
                    //transold.NewContainerId = inventoryModel.ContainerId;
                    ////status
                    //transold.OldMaterialId = inventoryModel.MaterialId;
                    //transold.NewMaterialId = inventoryModel.MaterialId;
                    //transold.OldLotExternalStatus = inventoryModel.StatusF;
                    //transold.OldSublotExternalStatus = inventoryModel.StatusS;
                    //transold.NewLotExternalStatus = inventoryModel.StatusF;
                    //transold.NewSublotExternalStatus = inventoryModel.StatusS;
                    ////trans.PhysicalQuantity 物理数量
                    ////trans.TareQuantity 皮数量
                    //transold.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    //transold.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                    //await _materialTransferServices.Add(transold);

                    #endregion

                    #region 新建子批次

                    string ssccString = models.Sscc;
                    var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                    if (ssccStrings.success == true)
                    {
                        ssccString = ssccStrings.response;
                    }
                    newSubLot = ssccString;
                    string subLOTID = string.Empty;
                    MaterialSubLotEntity materialsub = new MaterialSubLotEntity();
                    materialsub.Create(_user.Name.ToString());
                    materialsub.SubLotId = ssccString;
                    materialsub.Type = "0";
                    materialsub.ExternalStatus = "3";
                    subLOTID = materialsub.ID.ToString();

                    #endregion

                    #region 写入历史记录

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = subLOTID;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(quantity), 3); //Convert.ToDecimal(quantity);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Split";
                    trans.Comment = "拆分";
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    //trans.PhysicalQuantity 物理数量
                    //trans.TareQuantity 皮数量
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;   //皮数量



                    #endregion

                    #region 新增拆分的库存实体和修改原有数据

                    //查询原数据
                    var modelInven = await _materialInventoryServices.QueryById(splitID);
                    decimal splitQuantity = Convert.ToDecimal(inventoryModel.Quantity - Convert.ToDecimal(quantity));
                    if (splitQuantity == 0)
                    { return Failed("库存未改变无需拆分"); }
                    if (splitQuantity < 0)
                    { return Failed("拆分数量过大"); }
                    //更新
                    modelInven.Modify(splitID, _user.Name.ToString());
                    modelInven.Quantity = Math.Round(Convert.ToDecimal(splitQuantity), 3);

                    newQty = Math.Round(Convert.ToDecimal(splitQuantity), 3);

                    //新增
                    MaterialInventoryEntity inserInvent = new MaterialInventoryEntity();
                    inserInvent.Create(_user.Name.ToString());
                    inserInvent.LotId = modelInven.LotId;
                    //喉头
                    inserInvent.IsThorat = modelInven.IsThorat;
                    inserInvent.SublotId = subLOTID;
                    inserInvent.Quantity = Math.Round(Convert.ToDecimal(quantity), 3); // Convert.ToDecimal(quantity);
                    inserInvent.QuantityUomId = modelInven.QuantityUomId;
                    inserInvent.StorageLocation = modelInven.StorageLocation;
                    inserInvent.EquipmentId = equipmentId;//modelInven.EquipmentId;
                    inserInvent.IsPrechecked = modelInven.IsPrechecked;
                    inserInvent.ContainerId = modelInven.ContainerId;
                    inserInvent.ProductionRequestId = modelInven.ProductionRequestId;
                    inserInvent.SortOrder = modelInven.SortOrder;

                    #endregion

                    #region 逻辑
                    _unitOfWork.BeginTran();

                    List<MaterialTransferEntity> mode11 = new List<MaterialTransferEntity>();
                    mode11.Add(trans);
                    List<MaterialInventoryEntity> mode12 = new List<MaterialInventoryEntity>();
                    mode12.Add(modelInven);
                    List<MaterialSubLotEntity> mode13 = new List<MaterialSubLotEntity>();
                    mode13.Add(materialsub);
                    List<MaterialInventoryEntity> mode14 = new List<MaterialInventoryEntity>();
                    mode14.Add(inserInvent);

                    List<string> inventIDS = new List<string>();
                    // inventIDS.Add(modelInven.ID);
                    inventIDS.Add(inserInvent.ID);
                    bool r1, r2, r3, r4 = true;
                    r1 = await _materialTransferServices.Add(mode11) > 0;
                    r2 = await _materialInventoryServices.Update(mode12);
                    r3 = await _materialSubLotServices.Add(mode13) > 0;
                    r4 = await _materialInventoryServices.Add(mode14) > 0;

                    #endregion

                    if (r1 == false || r2 == false || r3 == false || r4 == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("拆分失败");
                    }

                    _unitOfWork.CommitTran();

                    var resultMsg = new MessageModel<string>();

                    if (isPrint == true)
                    {

                        #region 构造数据

                        //这里需求进行数据绑定(默认第一条数据)
                        VerifiyDetailTotalData addobj = await GetInventLabel(splitID, "");
                        addobj.NUM = Math.Round(oldQty - newQty, 3).ToString();
                        objList.Add(addobj);

                        //第二条数据
                        addobj.NUM = newQty.ToString();
                        addobj.SSCC = newSubLot;
                        objList.Add(addobj);

                        #endregion

                        #region 打印

                        for (int i = 0; i < inventIDS.Count; i++)
                        {
                            //List<object> print = new List<object> { objList[i] };
                            var result = await _IPrintSelectViewServices.GetInventLabel(inventIDS[i], "");
                            List<object> print = new List<object> { result };
                            resultMsg = await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.PrintId, "02308281-5121-6559-163e-0370f6000000", print, print[0], 1, "PrintTemplete");
                        }

                        //     PrintByCode(printID, templateID, templateclassID, objList, objList[i]);

                        #endregion

                    }
                    return Success("", "拆分成功" + resultMsg.msg);
                }
                catch (Exception ex)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("拆分失败" + ex.Message);
                }
            }
            else
            {
                decimal oldQty = inventoryModel.Quantity.Value;
                string newSubLot = string.Empty;

                //获取袋数
                int bags = Convert.ToInt32(models.offullbags);
                try
                {
                    if (Convert.ToDecimal(bags) * Convert.ToDecimal(models.bagsSize) > oldQty)
                    {
                        return Failed("拆分失败，拆分袋数过大");
                    }

                    if (Convert.ToDecimal(bags) * Convert.ToDecimal(models.bagsSize) == oldQty)
                    {
                        return Failed("拆分失败，拆分数量必须小于当前数量");
                    }

                    _unitOfWork.BeginTran();

                    List<MaterialTransferEntity> inserTrans = new List<MaterialTransferEntity>();
                    List<MaterialInventoryEntity> inserInvens = new List<MaterialInventoryEntity>();
                    List<MaterialSubLotEntity> inserSubLots = new List<MaterialSubLotEntity>();

                    //查询原数据
                    var modelInven = await _materialInventoryServices.QueryById(splitID);

                    for (int i = 0; i < bags; i++)
                    {
                        string newSScc = string.Empty;

                        var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                        if (ssccStrings.success == true)
                        {
                            newSScc = ssccStrings.response;
                        }
                        #region 新建子批次

                        string subID = string.Empty;
                        MaterialSubLotEntity materialsub = new MaterialSubLotEntity();
                        materialsub.Create(_user.Name.ToString());
                        materialsub.SubLotId = newSScc;
                        materialsub.Type = "0";
                        materialsub.ExternalStatus = "3";
                        subID = materialsub.ID.ToString();

                        inserSubLots.Add(materialsub);
                        #endregion

                        #region 写入历史记录

                        //写入历史记录
                        MaterialTransferEntity trans1 = new MaterialTransferEntity();
                        trans1.Create(_user.Name.ToString());
                        trans1.OldStorageLocation = inventoryModel.LocationF;
                        trans1.NewStorageLocation = inventoryModel.LocationF;
                        trans1.OldLotId = inventoryModel.LotId;
                        trans1.NewLotId = inventoryModel.LotId;
                        trans1.OldSublotId = inventoryModel.SlotId;
                        trans1.NewSublotId = subID;
                        trans1.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans1.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans1.Quantity = Math.Round(Convert.ToDecimal(models.bagsSize), 3); // Convert.ToDecimal(models.bagsSize);
                        trans1.QuantityUomId = inventoryModel.QuantityUomId;
                        trans1.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans1.Type = "Split";
                        trans1.Comment = "拆分";
                        trans1.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans1.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans1.OldEquipmentId = inventoryModel.EquipmentId;
                        trans1.NewEquipmentId = equipmentId;
                        trans1.OldContainerId = inventoryModel.ContainerId;
                        trans1.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans1.OldMaterialId = inventoryModel.MaterialId;
                        trans1.NewMaterialId = inventoryModel.MaterialId;
                        trans1.OldLotExternalStatus = inventoryModel.StatusF;
                        trans1.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans1.NewLotExternalStatus = inventoryModel.StatusF;
                        trans1.NewSublotExternalStatus = inventoryModel.StatusS;
                        //trans.PhysicalQuantity 物理数量
                        //trans.TareQuantity 皮数量
                        trans1.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                        trans1.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;   //皮数量
                        inserTrans.Add(trans1);


                        #endregion

                        #region 新增

                        //新增
                        MaterialInventoryEntity inserInvent = new MaterialInventoryEntity();
                        inserInvent.Create(_user.Name.ToString());
                        //喉头
                        inserInvent.IsThorat = modelInven.IsThorat;
                        inserInvent.LotId = modelInven.LotId;
                        inserInvent.SublotId = subID;
                        inserInvent.Quantity = Math.Round(Convert.ToDecimal(models.bagsSize), 3); // Convert.ToDecimal(models.bagsSize);
                        inserInvent.QuantityUomId = modelInven.QuantityUomId;
                        inserInvent.StorageLocation = modelInven.StorageLocation;
                        inserInvent.EquipmentId = models.equipmentId;
                        inserInvent.IsPrechecked = modelInven.IsPrechecked;
                        inserInvent.ContainerId = modelInven.ContainerId;
                        inserInvent.ProductionRequestId = modelInven.ProductionRequestId;
                        inserInvent.SortOrder = modelInven.SortOrder;
                        inserInvens.Add(inserInvent);

                        #endregion

                        //这里打印需要考虑数据
                        if (isPrint == true)
                        {
                            //执行打印
                            //selectPrint 打印机
                        }
                    }

                    #region 处理余量

                    #region 写入历史记录

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(_user.Name.ToString());
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(models.bagsSurplus), 3); // Convert.ToDecimal(models.bagsSurplus); 
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Split";
                    trans.Comment = "拆分";
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    //trans.PhysicalQuantity 物理数量
                    //trans.TareQuantity 皮数量
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;   //皮数量
                    inserTrans.Add(trans);


                    #endregion

                    //更新
                    modelInven.Modify(splitID, _user.Name.ToString());
                    //余量
                    modelInven.Quantity = Math.Round(Convert.ToDecimal(models.bagsSurplus), 3); //Convert.ToDecimal(models.bagsSurplus);
                    #endregion

                    bool r1, r2, r3, r4 = true;
                    List<string> dis = new List<string>();
                    for (int i = 0; i < inserInvens.Count; i++)
                    {
                        dis.Add(inserInvens[i].ID);
                    }
                    if (modelInven != null)
                    {
                        //   dis.Add(modelInven.ID);
                    }
                    r1 = await _materialTransferServices.Add(inserTrans) > 0;
                    r2 = await _materialInventoryServices.Add(inserInvens) > 0;
                    r3 = await _materialSubLotServices.Add(inserSubLots) > 0;

                    r4 = await _materialInventoryServices.Update(modelInven);

                    if (r1 == false || r2 == false || r3 == false || r4 == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("拆分失败");
                    }
                    var resultMsg = new MessageModel<string>();

                    _unitOfWork.CommitTran();

                    //这里打印需要考虑数据
                    if (isPrint == true)
                    {
                        #region 构造数据

                        //这里需求进行数据绑定(默认第一条数据)
                        VerifiyDetailTotalData addobj = await _IPrintSelectViewServices.GetInventLabel(splitID, "");
                        addobj.NUM = Convert.ToDecimal(models.bagsSurplus).ToString();
                        objList.Add(addobj);

                        //循环加其他数据
                        for (int i = 0; i < inserInvens.Count; i++)
                        {
                            addobj.SSCC = inserInvens[i].SublotId;
                            addobj.NUM = Convert.ToDecimal(models.bagsSize).ToString();
                            objList.Add(addobj);
                        }

                        #endregion

                        #region 打印

                        for (int i = 0; i < dis.Count; i++)
                        {
                            VerifiyDetailTotalData getInventData = await _IPrintSelectViewServices.GetInventLabel(dis[i], "");
                            List<object> print = new List<object> { getInventData };
                            resultMsg = await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.PrintId, "02308281-5121-6559-163e-0370f6000000", print, print[0], 1, "PrintTemplete");
                        }

                        //for (int i = 0; i < dis.Count; i++)
                        //{
                        //    List<object> print = new List<object> { objList[i] };
                        //    resultMsg = await _IPrintSelectViewServices.PrintCodeByEquipmentId(models.PrintId, "02308281-5121-6559-163e-0370f6000000", print, objList[0], 1, "PrintTemplete");
                        //}
                        #endregion
                    }


                    return Success("", "拆分成功" + resultMsg.msg);
                }
                catch (Exception ex)
                {

                    _unitOfWork.RollbackTran();
                    return Failed("拆分失败" + ex.Message);
                }

            }

        }


        #endregion


        #endregion


        #region 扫码收货

        [HttpPost]
        public async Task<MessageModel<PageModel<TransferHistoryViewEntity>>> GetScanDataList(InventorylistingViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.GetScanDataList(reqModel);
            return Success(data, "获取成功");

        }

        #endregion
        #region 白糖预处理
        /// <summary>
        /// 白糖预/查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<InventorylistingViewEntity>>> GetSugarPre(InventorylistingViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.GetSugarPre(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 白糖预/查询（网页专用）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<InventorylistingViewEntityModel>>> GetSugarPreAll(InventorylistingViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.GetSugarPreAll(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> GetSugarPreCount(InventorylistingViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.GetSugarPreCount(reqModel);
            if (data.success)
            {

                return Success(data.msg, "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 白糖预/合并
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPreMerges([FromBody] InventoryMergesModel reqModel)
        {
            var data = await _materialInventoryServices.SugarPreMerges(reqModel);
            if (data.success)
            {
                return Success("", "合并成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }
        /// <summary>
        /// 白糖预/转移
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> TransferEquipment(TransferEquipment reqModel)
        {
            var data = await _materialInventoryServices.TransferEquipment(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 根据物料追溯码判断执行收料或收货操作
        /// 该方法用于决定对特定物料是执行"收料"还是"扫码收货"操作
        /// </summary>
        /// <param name="reqModel">请求参数实体，包含以下字段：
        /// - sscc: 物料追溯码(子批次号)
        /// - equipmentId: 目标仓库ID
        /// - name: 目标仓库名称
        /// </param>
        /// <returns>
        /// 返回操作类型判断结果：
        /// - 成功时返回具体操作类型("收料"表示已有库存需要收料处理，"扫码收货"表示需要进行收货处理)
        /// - 失败时返回错误信息
        /// </returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPutOrPrePutMtrDP(SugarPrePutMtr reqModel)
        {
            var data = await _materialInventoryServices.SugarPutOrPrePutMtrDP(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 扫码收货和收料合并
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPutOrPrePutMtr(SugarPrePutMtr reqModel)
        {
            var data = await _materialInventoryServices.SugarPutOrPrePutMtr(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 白糖预扫码/收货（收货按钮）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPrePut(SugarPrePutModel reqModel)
        {
            var data = await _materialInventoryServices.SugarPrePut(reqModel);
            if (data.success)
            {
                return Success("", "扫码收货成功");
            }
            else
            {
                return Success("", data.msg);
                //  return Failed(data.msg);
            }
        }

        /// <summary>
        /// 是否开启节点功能
        /// </summary>
        /// <param name="eqpID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> StorageOpen(string eqpID)
        {


            var data = new MessageModel<string>();
            var result = await _materialInventoryServices.ISStorage(eqpID);
            if (result == true)
            {
                data.status = 200;
                data.response = "true";
                return Success("", "开启成功");
            }
            else
            {
                data.response = "false";
                data.status = 500;
                return Failed("", "开启失败");
            }

        }

        /// <summary>
        /// 白糖预/收料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPrePutMtr(SugarPrePutMtr reqModel)
        {
            var data = await _materialInventoryServices.SugarPrePutMtr(reqModel);
            if (data.success)
            {
                return Success("", "收料成功");
            }
            else
            {
                return Success("", data.msg);
            }
        }



        /// <summary>
        /// 白糖预处理/拆分
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarPreSplit(SugarPreSplitModel reqModel)
        {
            var data = await _materialInventoryServices.SugarPreSplit(reqModel);
            if (data.success)
            {
                return Success("", "拆分成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        #endregion
        #endregion

        [HttpPost]
        public async Task<MessageModel<List<MaterialInventoryEntity>>> GetList(string key = "")
        {
            Expression<Func<MaterialInventoryEntity, bool>> whereExpression = a => true;
            var data = await _materialInventoryServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialInventoryEntity>>> GetPageList([FromBody] MaterialInventoryRequestModel reqModel)
        {

            Expression<Func<MaterialInventoryEntity, bool>> whereExpression = a => true;
            var data = await _materialInventoryServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialInventoryEntity>> GetEntity(string id)
        {
            var data = await _materialInventoryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 修改和新增，修改需要传入ID和修改数量的数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialInventoryEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialInventoryServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialInventoryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        #region server

        /// <summary>
        /// 进行合并库存信息
        /// </summary>
        /// <param name="upID"></param>
        /// <param name="deleteID"></param>
        /// <param name="sscc"></param>
        /// <param name="totalQuantity"></param>
        /// <returns></returns>
        private async Task<bool> SaveMergeServer(string upID, string[] deleteID, string sscc, string totalQuantity)
        {

            bool result = true;

            _unitOfWork.BeginTran();

            MaterialInventoryEntity upmodel = new MaterialInventoryEntity();
            upmodel.ID = upID;
            upmodel.SublotId = sscc;
            upmodel.Quantity = Math.Round(Convert.ToDecimal(totalQuantity), 3);// Convert.ToDecimal(totalQuantity);
            //更新
            bool updateResult = await _materialInventoryServices.Update(upmodel);
            //删除数据
            bool deleteResult = await _materialInventoryServices.DeleteById(deleteID);

            if (!updateResult || !deleteResult)
            {
                _unitOfWork.RollbackTran();
                return false;
            }
            _unitOfWork.CommitTran();
            return result;
        }

        /// <summary>
        /// 进行数据拆分
        /// </summary>
        /// <param name="splitID"></param>
        /// <param name="oldTotalQuantity"></param>
        /// <param name="insertModel"></param>
        /// <returns></returns>
        private async Task<bool> SaveSplitServer(string splitID, string oldTotalQuantity, MaterialInventoryEntity insertModel)
        {
            bool result = true;
            //更新
            _unitOfWork.BeginTran();
            MaterialInventoryEntity upmodel = new MaterialInventoryEntity();
            upmodel.ID = splitID;
            upmodel.Quantity = Math.Round(Convert.ToDecimal(oldTotalQuantity), 3); //Convert.ToDecimal(oldTotalQuantity);
            bool updateResult = await _materialInventoryServices.Update(upmodel);
            //新增
            bool addResult = await _materialInventoryServices.Add(insertModel) > 0;

            if (!updateResult || !addResult)
            {
                _unitOfWork.RollbackTran();
                return false;
            }
            _unitOfWork.CommitTran();
            return result;
        }

        /// <summary>
        /// 转移
        /// </summary>
        /// <param name="id">转移数据id(库存表ID)</param>
        /// <param name="equipmentId"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private async Task<bool> TransferServer(string id, string equipmentId, string name)
        {
            bool result = true;
            //获取视图
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
             .ToExpression();
            var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);
            _unitOfWork.BeginTran();

            //更新库房信息
            //var whereExpression = Expressionable.Create<MaterialInventoryEntity>().And(c => c.ID == id).ToExpression();
            //bool upResult = await _materialInventoryServices.Update(new MaterialInventoryEntity(), whereExpression, new List<string> { "EquipmentId" });
            //new MaterialInventoryEntity() { EquipmentId = equipmentId }, whereExpression);
            //更新库房信息
            MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == id).Result.FirstOrDefault();
            models.EquipmentId = equipmentId;
            bool upResult = await _materialInventoryServices.Update(models);

            //写入历史记录
            MaterialTransferEntity trans = new MaterialTransferEntity();
            trans.ID = Guid.NewGuid().ToString();
            trans.OldStorageLocation = inventoryModel.LocationF;
            trans.NewStorageLocation = name;
            trans.OldLotId = inventoryModel.LotId;
            trans.NewLotId = inventoryModel.LotId;
            trans.OldSublotId = inventoryModel.SlotId;
            trans.NewSublotId = inventoryModel.SlotId;
            trans.OldExpirationDate = inventoryModel.ExpirationDate;
            trans.NewExpirationDate = inventoryModel.ExpirationDate;
            trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3); // Convert.ToInt32(inventoryModel.Quantity);
            trans.QuantityUomId = inventoryModel.QuantityUomId;
            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
            trans.Type = "Transfer Inventory";
            trans.Comment = "转移";
            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
            //trans.TransferGroupId
            trans.OldEquipmentId = inventoryModel.EquipmentId;
            trans.NewEquipmentId = equipmentId;
            trans.OldContainerId = inventoryModel.ContainerId;
            trans.NewContainerId = inventoryModel.ContainerId;
            //status
            trans.OldMaterialId = inventoryModel.MaterialId;
            trans.NewMaterialId = inventoryModel.MaterialId;
            trans.OldLotExternalStatus = inventoryModel.StatusF;
            trans.OldSublotExternalStatus = inventoryModel.StatusS;
            trans.NewLotExternalStatus = inventoryModel.StatusF;
            trans.NewSublotExternalStatus = inventoryModel.StatusS;

            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

            bool tranHis = await _materialTransferServices.Add(trans) > 0;

            if (!upResult || !tranHis)//|| !addResult)
            {
                _unitOfWork.RollbackTran();
                return false;
            }
            _unitOfWork.CommitTran();

            return result;
        }

        #endregion

        /// <summary>
        /// 转移历史反冲用
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> TranferHisReverse(TranferReverse reqModel)
        {
            var data = await _materialInventoryServices.TranferHisReverse(reqModel);
            if (data.success)
            {
                return Success("", "反冲成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 转移历史退货返还给SAP
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReturnWMS(TranferReverse reqModel)
        {
            var data = await _materialInventoryServices.ReturnWMS(reqModel);
            if (data.success)
            {
                return Success("", "退货返还成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> DeliveryInventory(string[] reqModel1)
        {
            TransferModel reqModel = new TransferModel();
            reqModel.ID = reqModel1;
            var data = await _materialInventoryServices.DeliveryInventory(reqModel);
            if (data.success)
            {
                return Success("", "交仓成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// C
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> InterFACesSC()
        {
            var data = await _materialInventoryServices.InterFACesSC();
            if (data.success)
            {
                return Success("", "拆分成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        #region 物料标签

        #region 查询（）

        #endregion

        #endregion

        #region 读取电子称

        //读取电子称
        [HttpPost]
        public async Task<MessageModel<CallModel>> ReadCallData(string printer, string ip, string port, string isMTL)
        {
            if (isMTL == "有")
            {
                var data = await _materialInventoryServices.ReadCallDataMetiler(printer, ip, port);
                return Success(data, "获取成功");
            }
            else
            {
                var data = await _materialInventoryServices.ReadCallData(printer, ip, port);
                return Success(data, "获取成功");
            }

        }

        //读取电子称
        [HttpPost]
        public async Task<MessageModel<CallModel>> ReadCallDataMetiler(string printer, string ip, string port)
        {
            var data = await _materialInventoryServices.ReadCallDataMetiler(printer, ip, port);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 查询电子称
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ScaleSelectViewEntity>>> GetScaleList(string equipmentID)
        {
            var data = await _materialInventoryServices.GetScaleList(equipmentID);
            return Success(data, "获取成功");
        }

        #endregion


        #region 库存标签补打

        /// <summary>
        /// 根据库存ID获取备料标签（库存）这里可能会有部分数据缺失
        /// </summary>
        /// <param name="inventID">库存ID</param>
        /// <param name="factoryName">表头使用功能组织(默认三厂)</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<VerifiyDetailTotalData> GetInventLabel(string inventID, string factoryNam)
        {

            //查询视图
            var result = await _inventorylistingViewServices.FindEntity(p => p.ID == inventID);
            if (result != null)
            {
                string supplier_Name = string.Empty;
                string material_Name = string.Empty;
                string material_Code = string.Empty;
                string lot_Code = string.Empty;
                string material_Inventory_Qty = string.Empty;
                string unit_Code = string.Empty;
                string sublot_Code = string.Empty;
                string fNumber = string.IsNullOrEmpty(result.Sapformula) ? "" : result.Sapformula;
                VerifiyDetailTotalData objs = new PPM.Model.ViewModels.MKM.PrintView.VerifiyDetailTotalData
                {
                    Plant = factoryNam,
                    Material_Name = result.MaterialName,
                    Material_Code = result.MaterialCode + " " + fNumber,
                    Batch_Code = result.BatchId,
                    NUM = result.Quantity.ToString(),
                    PO_NUM = result.ProductionOrderNo == null ? "" : result.ProductionOrderNo,
                    Unit = result.MinUnit,
                    Receive_Date = result.CreateDate.ToString("yyyy-MM-dd"),
                    SSCC = result.Sscc
                };
                return objs;

            }
            return new VerifiyDetailTotalData();

        }

        #endregion

        #region 喉头PDA

        #region 查询

        /// <summary>
        /// 查询喉头（状态1是已出仓，0是未出仓） PAD扫码后判断状态 已出仓就有撤回按钮
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ColdAWarehouseViewEntity>>> GetThroatPDAList(PPM.Model.ViewModels.ColdAWarehouseViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.GetThroat_PDAList(reqModel);
            return Success(data, "获取成功");
        }





        /// <summary>
        /// 喉头出仓
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetDataStatus(PPM.Model.ViewModels.ColdAWarehouseViewRequestModel reqModel)
        {
            //获取计划时间
            DateTime star = Convert.ToDateTime(reqModel.PlanStar);
            DateTime end = Convert.ToDateTime(reqModel.PlanEnd).AddDays(1).AddSeconds(-1);

            var data = await _materialInventoryServices.GetDataStatus(reqModel.SSCC, reqModel.TYPE, star, end);
            return data;
        }

        #endregion

        #region 出仓

        /// <summary>
        /// 喉头出仓
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> OutWarehouse(PPM.Model.ViewModels.ColdAWarehouseViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.OutWarehouse(reqModel);
            return data;
        }


        #endregion

        #region 撤回

        /// <summary>
        /// 喉头撤回
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReturnWarehouse(PPM.Model.ViewModels.ColdAWarehouseViewRequestModel reqModel)
        {
            var data = await _materialInventoryServices.ReturnWarehouse(reqModel);
            return data;
        }

        #endregion

        #endregion


    }
    public class MaterialInventoryRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}