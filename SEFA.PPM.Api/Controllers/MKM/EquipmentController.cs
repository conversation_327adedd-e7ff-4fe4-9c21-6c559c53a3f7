using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentController : BaseApiController
    {
        /// <summary>
        /// Equipment
        /// </summary>
        private readonly IEquipmentServices _equipmentServices;

        public EquipmentController(IEquipmentServices EquipmentServices)
        {
            _equipmentServices = EquipmentServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<EquipmentEntity>>> GetList([FromBody] EquipmentRequestModel reqModel)
        {
            var data = await _equipmentServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentEntity>>> GetPageList([FromBody] EquipmentRequestModel reqModel)
        {
            var data = await _equipmentServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentEntity>> GetEntity(string id)
        {
            var data = await _equipmentServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        /// <summary>
        /// 获取LEVEL='machine'的设备
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<EquipmentEntity>>> GetMachine()
        {
            var data = await _equipmentServices.GetMachine();
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class EquipmentRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}