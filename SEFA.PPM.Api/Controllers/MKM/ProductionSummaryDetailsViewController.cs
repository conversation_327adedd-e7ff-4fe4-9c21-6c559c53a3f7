using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProductionSummaryDetailsViewController : BaseApiController
    {
        /// <summary>
        /// ProductionSummaryDetailsView
        /// </summary>
        private readonly IProductionSummaryDetailsViewServices _productionSummaryDetailsViewServices;

        public ProductionSummaryDetailsViewController(IProductionSummaryDetailsViewServices ProductionSummaryDetailsViewServices)
        {
            _productionSummaryDetailsViewServices = ProductionSummaryDetailsViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductionSummaryDetailsViewEntity>>> GetList(string id)
        {
            var data = await _productionSummaryDetailsViewServices.GetList(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionSummaryDetailsViewEntity>>> GetPageList([FromBody] ProductionSummaryDetailsViewRequestModel reqModel)
        {
            var data = await _productionSummaryDetailsViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionSummaryDetailsViewEntity>> GetEntity(string id)
        {
            var data = await _productionSummaryDetailsViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProductionSummaryDetailsViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _productionSummaryDetailsViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _productionSummaryDetailsViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReverseAsync(ReverseModel reqModel)
        {
            var data = await _productionSummaryDetailsViewServices.Reverse(reqModel);
            return data;
        }

    }
    //public class ProductionSummaryDetailsViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}