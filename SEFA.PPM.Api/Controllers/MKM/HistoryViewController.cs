using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.Model.Models.MKM;
using SEFA.PTM.IServices;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class HistoryViewController : BaseApiController
    {
        /// <summary>
        /// HistoryView
        /// </summary>
        private readonly IHistoryViewServices _historyViewServices;
        private readonly IConsumeViewServices _consumeViewServices;
        private readonly IMapper _mapper;
        public HistoryViewController(IHistoryViewServices HistoryViewServices, IConsumeViewServices consumeViewServices, IMapper mapper)
        {
            _historyViewServices = HistoryViewServices;
            _consumeViewServices = consumeViewServices;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<MessageModel<List<HistoryViewEntity>>> GetList([FromBody] HistoryViewRequestModel reqModel)
        {
            var data = await _historyViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<GroupData>>> GetConsumeSumList([FromBody] HistoryViewRequestModel reqModel)
        {
            var data = await _historyViewServices.GetConsumeSumList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetConsumMachineGBZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "GBZ";
            var data = await _historyViewServices.GetConsumMachine(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetConsumMachineZZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "ZZ";
            var data = await _historyViewServices.GetConsumMachine(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetConsumMachineSourceGBZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "GBZ";
            var data = await _historyViewServices.GetConsumMachineSource(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Machine
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetConsumMachineSourceZZ([FromBody] BatchPalletModel reqModel)
        {
            reqModel.typeSerch = "ZZ";
            var data = await _historyViewServices.GetConsumMachineSource(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 查询产出历史（使用）
        /// </summary>
        /// <param name="reqModel">实体</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<HistoryViewEntityModel>>> GetPageList([FromBody] HistoryViewRequestModel reqModel)
        {

            var data = await _historyViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }


        #region 导出

        /// <summary>
        /// 导出库存数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<IActionResult> ExportConsumptionData([FromBody] HistoryViewRequestModel reqModel)
        {
            try
            {
                var data = await _historyViewServices.GetExportList(reqModel);
                //  var exportData = _mapper.Map<List<ConsumExports>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(data);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"消耗历史制造-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"OpcTag导出出现错误:{ex.Message}");
            }
        }



        /// <summary>
        /// 导出库存数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<IActionResult> ExportConsumptionDataGBZ([FromBody] HistoryViewRequestModel reqModel)
        {
            try
            {
                var data = await _historyViewServices.GetExportList(reqModel);
                //  var exportData = _mapper.Map<List<ConsumExports>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(data);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"消耗历史罐包装-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"OpcTag导出出现错误:{ex.Message}");
            }
        }

        #endregion


        /// <summary>
        /// 查询编辑框数据并显示（consumed qty）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<HistoryViewEntity>> GetEntityByQTY(string id)
        {
            var data = await _historyViewServices.QueryById(id);
            if (data != null)
            {
                decimal quality = await _historyViewServices.GetConsumedQTY(data.ProcessOrder, data.MCode, data.BatchCode, data.SubSscc, data.MachineCode, data.SourceCode);
                data.IQuantity = quality;
            }
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> GetGetConsumedQTY(ReverseModel reqModel)
        {
            var data = await _historyViewServices.GetGetConsumedQTY(reqModel);
            return Success(data.msg, "获取成功");
        }
        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="id">主表ID</param>
        /// <param name="quantity">反冲数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Recoil(string id, decimal quantity)
        {
            return await _historyViewServices.Recoil(id, quantity);
        }

        /// <summary>
        /// 重发
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> RepeatPlan([FromBody] SendModel reqModel)
        {
            var list = reqModel.id != null ? reqModel.id.ToList() : new List<string>();
            if (reqModel.isReverse)
            {
                return await _consumeViewServices.ConsumeReverseReport(list);
            }
            return await _consumeViewServices.ConsumeReport(list);

            //var data = await _historyViewServices.RepeatPlan(reqModel.id);
            //if (data.success)
            //{
            //	return Success("", "重发成功");
            //}
            //else
            //{
            //	return Failed(data.msg);
            //}
        }


        [HttpGet("{id}")]
        public async Task<MessageModel<HistoryViewEntity>> GetEntity(string id)
        {
            var data = await _historyViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] HistoryViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _historyViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _historyViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class HistoryViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}