using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PalletlistViewController : BaseApiController
    {
        /// <summary>
        /// PalletlistView
        /// </summary>
        private readonly IPalletlistViewServices _palletlistViewServices;

        public PalletlistViewController(IPalletlistViewServices PalletlistViewServices)
        {
            _palletlistViewServices = PalletlistViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PalletlistViewEntity>>> GetList([FromBody] PalletlistViewRequestModel reqModel)
        {
            var data = await _palletlistViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        #region 有用数据

        #region 查询

        /// <summary>
        /// Desinations下拉选 第二个Area下拉选用api/Equipment/GetListByLevel?key=Line
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetDesinations([FromBody] PalletDesinationModel reqModel)
        {
            var data = await _palletlistViewServices.GetDesinations(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取Machine(这里和主表传入数据一致)
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<Select>>> GetDesination([FromBody] PalletModel reqModel)
        {
            var data = await _palletlistViewServices.GetDesination(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取栈板信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<PalletlistViewEntity>>> GetPageList([FromBody] PalletModel reqModel)
        {
            var data = await _palletlistViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据ID查询栈板明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<PalletlistViewEntity>> GetEntity(string id)
        {
            var data = await _palletlistViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        #endregion

        #region 事件

        /// <summary>
        /// Scan toVerify（校验）
        /// </summary>
        /// <param name="sscc">子批次号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Verify(string sscc)
        {
            var data = await _palletlistViewServices.Verify(sscc);
            return data;
        }

        /// <summary>
        /// 反冲
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Reverse([FromBody] ReverseModel reqModel)
        {
            var data = await _palletlistViewServices.Reverse(reqModel);
            if (data.success == false)
            {
                return Failed("反冲失败:" + data.msg);
            }
            else
            {
                return Success("", "反冲成功");
            }

        }

        [HttpPost]
        public async Task<MessageModel<string>> GetQTY(ReverseModel reqModel)
        {
            var data = await _palletlistViewServices.GetQTY(reqModel);
            return Success(data.msg, "获取成功");
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Print(string key = "")
        {
            var data = await _palletlistViewServices.Print(key);


            return data;
        }

        #endregion

        #endregion

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _palletlistViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
}