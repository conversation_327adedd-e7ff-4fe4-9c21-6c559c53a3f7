using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SqlSugar;
using System.Linq.Expressions;
using static SEFA.PPM.Services.TippingMlistViewServices;
using MaterialEntity = SEFA.PPM.Model.Models.MKM.MaterialEntity;


namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ContainerController : BaseApiController
    {

        /// <summary>
        /// 
        /// </summary>
        private readonly IContainerServices _containerServices;
        private readonly IInventorylistingViewServices _inventorylistingViewServices;
        private readonly IMaterialInventoryServices _materialInventoryServices;
        private readonly IMContainerViewServices _mContainerViewServices;
        private readonly Base.IRepository.UnitOfWork.IUnitOfWork _unitOfWork;
        private readonly IMaterialTransferServices _materialTransferServices;
        private readonly IContainerHistoryServices _containerHistoryServices;
        private readonly IContainerHistoryViewServices _containerHistoryViewServices;
        private readonly IMaterialServices _materialServices;
        private readonly IUser _uIser;
        private readonly IRequestInventoryViewServices _requestInventoryViewServices;
        private readonly IEquipmentServices _equipmentServices;
        private readonly IBaseRepository<DFM.Model.Models.UnitConvertEntity> _dalUnitConvertEntity;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _dalUnitmanageEntity;
        private readonly IBaseRepository<PPM.Model.Models.VerifiyDetailEntity> _dalVerifiyDetailEntity;
        private readonly IBaseRepository<InventorylistingViewEntity> _dalInventorylistingViewEntity;

        //private readonly IUnitOfWork _mContainerViewServices
        public ContainerController(IContainerServices ContainerServices, IMContainerViewServices mContainerViewServices, IInventorylistingViewServices inventorylistingViewServices,
            IMaterialInventoryServices materialInventoryServices, IUnitOfWork unitOfWork, IContainerHistoryViewServices containerHistoryViewServices, IMaterialServices materialServices, IUser user
            , IMaterialTransferServices materialTransferServices, IContainerHistoryServices containerHistoryServices, IRequestInventoryViewServices requestInventoryViewServices, IEquipmentServices equipmentServices, IBaseRepository<UnitConvertEntity> dalUnitConvertEntity, IBaseRepository<UnitmanageEntity> dalUnitmanageEntity, IBaseRepository<PPM.Model.Models.VerifiyDetailEntity> dalVerifiyDetailEntity, IBaseRepository<InventorylistingViewEntity> dalInventorylistingViewEntity)
        {
            _containerServices = ContainerServices;
            _mContainerViewServices = mContainerViewServices;
            _inventorylistingViewServices = inventorylistingViewServices;
            _materialInventoryServices = materialInventoryServices;
            _unitOfWork = unitOfWork;
            _containerHistoryViewServices = containerHistoryViewServices;
            _materialServices = materialServices;
            _materialTransferServices = materialTransferServices;
            _containerHistoryServices = containerHistoryServices;
            _uIser = user;
            _requestInventoryViewServices = requestInventoryViewServices;
            _equipmentServices = equipmentServices;
            _dalUnitConvertEntity = dalUnitConvertEntity;
            _dalUnitmanageEntity = dalUnitmanageEntity;
            _dalVerifiyDetailEntity = dalVerifiyDetailEntity;
            _dalInventorylistingViewEntity = dalInventorylistingViewEntity;
        }

        [HttpPost]
        public async Task<MessageModel<List<ContainerEntity>>> GetList(string key = "")
        {
            Expression<Func<ContainerEntity, bool>> whereExpression = a => true;
            var data = await _containerServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        //[HttpPost]
        //public async Task<MessageModel<PageModel<ContainerEntity>>> GetPageList([FromBody] ContainerRequestModel reqModel)
        //{

        //    Expression<Func<ContainerEntity, bool>> whereExpression = a => true;
        //    var data = await _containerServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
        //    return Success(data, "获取成功");

        //}

        [HttpGet("{id}")]
        public async Task<MessageModel<ContainerEntity>> GetEntity(string id)
        {
            var data = await _containerServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ContainerEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _containerServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _containerServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _containerServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }


        #region 函数

        /// <summary>
        /// 获取托盘信息 传入参数(BatchPallet)
        /// </summary>
        /// <param name="classType"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ContainerEntity>>> GetConBatchPallet_List(string classType = "BatchPallet")
        {
            Expression<Func<ContainerEntity, bool>> whereExpression = a => true;
            var data = await _containerServices.FindList(p => p.Class == classType);
            return Success(data, "获取成功");
        }




        /// <summary>
        /// 容器管理视图分页查询（刷新）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MContainerViewEntity>>> GetPageViewList([FromBody] MContainerViewRequestModel reqModel)
        {

            var data = await _mContainerViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取Destination
        /// </summary>
        /// <returns></returns>
        /// <summary>
        /// 获取容器class BatchPallet
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DicDestinationEntity>>> GetDestinationList()
        {

            var data = await _mContainerViewServices.GetDestinationList();
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 获取Destination
        /// </summary>
        /// <returns></returns>
        /// <summary>
        /// 获取容器class BatchPallet
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PPM.Model.Models.SelectCycViewEntity>>> GetDestinationList_CYC()
        {
            var data = await _mContainerViewServices.GetDestinationList_CYC();
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取Destination
        /// </summary>
        /// <returns></returns>
        /// <summary>
        /// 获取容器class BatchPallet
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DicDestinationEntity>>> GetDestinationList_YL()
        {
            var data = await _mContainerViewServices.GetDestinationList_YL();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取容器class BatchPallet
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DicContainerBpalletViewEntity>>> GetConClassList()
        {

            var data = await _mContainerViewServices.GetConClassList();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取容器状态
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DicContainerstateViewEntity>>> GetConStatusList()
        {
            var data = await _mContainerViewServices.GetConStatusList();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据ID查询容器明细
        ///Container_State 状态:
        //0:Unavailable
        //1:Pending
        //2:Commlete
        //3:Inactive
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<MContainerViewEntity>> GetViewEntity(string id)
        {
            //Task<MContainerViewEntity> GetViewList(string id);
            if (id == string.Empty)
            {
                return Success(new MContainerViewEntity(), "获取失败");
            }
            var data = await _mContainerViewServices.GetViewList(id);
            //这里需要重新绑定总数
            //获取当前容器下所有的库存信息(并绑定)
            List<MaterialInventoryEntity> inventEntity = await _materialInventoryServices.FindList(p => p.ContainerId == id);
            if (inventEntity != null && inventEntity.Count > 0)
            {
                data.Quantity = inventEntity.Sum(p => p.Quantity);
            }
            else
            {
                data.Quantity = 0;
            }
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 清线
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ClearLineByEquipment([FromBody] TransferModel tranModel)
        {
            /// </summary>
            /// <param name="id">物料库存ID(这里再编辑界面能拿到)</param>      
            /// <param name="equipmentId">存储新区域id</param>
            /// <param name="name">存储区域名称</param>
            /// <returns></returns>
            /// 
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;
            string equipmentId = tranModel.EquipmentId;



            #region 这里获取数据

            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _uIser.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData == null)
            {
                return Failed("请配置清线转移地");
            }

            var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Storage");
            var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "LineCleaningDestination");

            string eCode = item2?.ActualValue ?? item2?.DefaultValue;

            if (string.IsNullOrEmpty(eCode))
            {
                return Failed("请配置清线转移地");
            }

            //查询对应的eid
            var eModel = await _equipmentServices.FindEntity(p => p.EquipmentCode.Contains(eCode));

            if (eModel == null)
            {
                return Failed("不存在对应的节点");
            }
            equipmentId = eModel.ID;
            #endregion


            //if (string.IsNullOrEmpty(id))
            //{
            //    return Failed("转移失败，不存在ID");
            //}
            if (string.IsNullOrEmpty(equipmentId))
            {
                return Failed("转移失败，不存在equipmentId");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            List<ContainerEntity> upCon = new List<ContainerEntity>();
            List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();

            #endregion

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("清线失败" + msg);
            }

            msg = await _inventorylistingViewServices.GetProductionByInventID(tranModel.ID);
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("清线失败" + msg);
            }


            #endregion            


            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();
                if (id != string.Empty)
                {
                    //判断当前批次和子批次数据


                    #region 进行转移操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("清线失败，不存在转移视图实体类");
                    }

                    #region 这里执行判断数据并控制删除或新增

                    string sapNo = string.Empty;


                    #endregion

                    #region 业务处理                   

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                    models.EquipmentId = equipmentId;
                    models.Modify(models.ID, userID);
                    upInvent.Add(models);


                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    //trans.NewStorageLocation = name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;

                    trans.Type = "Transfer Inventory";
                    trans.Comment = "清线-转移";

                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;

                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    trans.SapPrintno = sapNo;

                    addTransfer.Add(trans);


                    #endregion

                    #region 修改容器位置


                    if (inventoryModel.ContainerId != null && inventoryModel.ContainerId != "")
                    {
                        //查询之前的位置
                        var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                        var containerModel = await _containerServices.FindEntity(whereLocation);

                        //更新容器新的位置
                        ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                        model.ID = inventoryModel.ContainerId;
                        model.EquipmentId = equipmentId;
                        model.Modify(model.ID, userID);
                        upCon.Add(model);

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);

                        //hisModel.ID = Guid.NewGuid().ToString();
                        //   hisModel
                        hisModel.ContainerId = inventoryModel.ContainerId;
                        hisModel.Type = "";
                        hisModel.EquipmentId = equipmentId;
                        hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        hisModel.State = containerModel.Status;
                        hisModel.Comment = containerModel.Comment;
                        //hisModel.ProductOrderId 工单ID
                        //hisModel.BatchId 工单批次ID
                        hisModel.MaterialId = inventoryModel.MaterialId;
                        hisModel.SublotId = inventoryModel.SubLotId;
                        hisModel.Quantity = inventoryModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                        //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                        //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                        //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                        // hisModel.Status 批次执行状态
                        //hisModel.ContainerCode= containerModel. 容器编号
                        //hisModel.MaterialProducedActualId 物料产出记录ID
                        //hisModel.MaterialConsumedActualId 物料消耗记录ID
                        hisModel.LotId = inventoryModel.LotId;
                        hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                        conHis.Add(hisModel);
                    }

                    #endregion

                    #endregion                   

                    #endregion
                }
            }

            try
            {


                _unitOfWork.BeginTran();

                bool upResult = true;
                if (upInvent.Count > 0)
                {
                    upResult = await _materialInventoryServices.Update(upInvent);
                }

                #region 调用SAP接口(调用失败直接跳过)
                string sapNo = string.Empty;


                bool tranSapHis = true;
                if (addTransferSap.Count > 0)
                {
                    tranSapHis = await _materialTransferServices.Add(addTransferSap) > 0;
                }

                #endregion

                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }

                bool rqResult = true;
                if (upCon.Count > 0)
                {
                    rqResult = await _containerServices.Update(upCon);
                }

                bool hisUp = true;
                if (conHis.Count > 0)
                {
                    hisUp = await _containerHistoryServices.Add(conHis) > 0;
                }

                if (!upResult || !tranHis || !rqResult || !hisUp || !tranSapHis)//|| !addResult)
                {
                    _unitOfWork.RollbackTran();

                    return Failed("转移失败");
                }
                _unitOfWork.CommitTran();
                return Success("", "转移成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("转移失败");
            }

        }


        [HttpPost]
        /// <summary>
        /// 单位转换
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<ConvertDataModel>> ChangeUnit(TransferModel tranModel)
        {
            string materialCode = tranModel.MCODE;
            string unitID = tranModel.UnitID;
            string uCode = tranModel.UnitCode;
            string tatolQty = tranModel.TotalNumber;
            string pageSize = tranModel.BagSiZe;
            ConvertDataModel model = new ConvertDataModel();
            var data = new MessageModel<string>();
            //如果等于kg直接计算包数
            if (uCode == "KG")
            {
                int bags = Convert.ToInt32(Convert.ToDecimal(tatolQty) / Convert.ToDecimal(pageSize));
                if (Convert.ToDouble(tatolQty) % Convert.ToDouble(pageSize) != 0.0)
                {
                    bags++;
                }

                //返回结果
                model.UnitID = unitID;
                model.OldUCode = uCode;
                model.UnitCode = uCode;
                model.BagSiZe = pageSize;
                model.TotalNumber = bags.ToString();
                model.TotalSum = tatolQty.ToString();

                return Success(model, "获取成功");

            }
            else
            {
                //这里进行查询并转换(查询当前转换规格)
                var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == materialCode && P.FormUnitName == uCode && P.ToUnitName == "KG");
                if (uConventList != null && uConventList.Count > 0)
                {
                    var uConvertModel = uConventList.FirstOrDefault();
                    //标准值
                    decimal fValue = uConvertModel.ConvertFormQty.Value;
                    //目标单位
                    decimal toValue = uConvertModel.ConvertToQty.Value;
                    //这里处理数量
                    decimal inventQty = Convert.ToDecimal(tatolQty) / fValue * toValue;

                    tatolQty = inventQty.ToString();//更改数量

                    int bags = Convert.ToInt32(Convert.ToDecimal(tatolQty) / Convert.ToDecimal(pageSize));
                    if (Convert.ToDouble(tatolQty) % Convert.ToDouble(pageSize) != 0.0)
                    {
                        bags++;
                    }

                    model.UnitID = uConvertModel.ToUnitId;
                    model.OldUCode = uCode;
                    model.UnitCode = "KG";
                    model.BagSiZe = pageSize;
                    model.TotalNumber = bags.ToString();
                    model.TotalSum = tatolQty.ToString();

                    return Success(model, "获取成功");
                }
            }
            return Success(model, "获取成功");

        }

        /// <summary>
        /// 转移按钮（非容器转移，根据物料库存进行转移）
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> TransferContainer([FromBody] TransferModel tranModel)
        {
            /// </summary>
            /// <param name="id">物料库存ID(这里再编辑界面能拿到)</param>      
            /// <param name="equipmentId">存储新区域id</param>
            /// <param name="name">存储区域名称</param>
            /// <returns></returns>
            /// 
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;
            string equipmentId = tranModel.EquipmentId;
            string name = tranModel.Name;
            //if (string.IsNullOrEmpty(id))
            //{
            //    return Failed("转移失败，不存在ID");
            //}
            if (string.IsNullOrEmpty(equipmentId))
            {
                return Failed("转移失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("转移失败，不存在name");
            }

            #region 查询属性

            bool isConvert = false;
            string kgUnitID = string.Empty;

            //查询属性表
            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _uIser.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData != null)
            {
                var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                if (item != null)
                {
                    isConvert = true;
                    var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                    if (umanagerModel != null)
                    {
                        DFM.Model.Models.UnitmanageEntity model = umanagerModel.FirstOrDefault();
                        kgUnitID = model.ID;
                    }
                }
            }

            #endregion

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            List<ContainerEntity> upCon = new List<ContainerEntity>();
            List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            List<string> deleteList = new List<string>();
            #endregion

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("转移失败" + msg);
            }

            msg = await _inventorylistingViewServices.GetProductionByInventID(tranModel.ID);
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("转移失败" + msg);
            }

            #endregion


            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = "MES-TO-SAP";
            sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
            //退库SAP
            List<SendTran> list = new List<SendTran>();


            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();
                if (id != string.Empty)
                {
                    //判断当前批次和子批次数据


                    #region 进行转移操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("转移失败，不存在转移视图实体类");
                    }
                    decimal inventQty = inventoryModel.Quantity.Value;
                    #region 这里执行判断数据并控制删除或新增

                    string sapNo = string.Empty;

                    bool isDelete = false;
                    string identicalmsg = await _materialInventoryServices.EquipmentIsIdentical(inventoryModel.EquipmentId, tranModel.EquipmentId);

                    var equpF = await _materialInventoryServices.GetEquipment(inventoryModel.EquipmentId);
                    var equpT = await _materialInventoryServices.GetEquipment(tranModel.EquipmentId);
                    if (identicalmsg != string.Empty)
                    {
                        string[] split = identicalmsg.Split(';');
                        if (split[0].Trim() == "NO" && split[0].Trim() == "0")
                        {
                            isDelete = true;
                        }

                        if (identicalmsg.Contains("NO"))
                        {
                            //这里调用SAP转移接口
                            #region SAP接口


                            SendTran model = new SendTran();

                            #region 构造实体


                            string[] fSplit = equpF.Split(';');
                            string[] tSplit = equpT.Split(";");

                            model.LINE_ID = "1";
                            model.BWART = "311";
                            model.MATNR = inventoryModel.MaterialCode;//transferView.NewMaterialCode;
                            model.MENGE = Math.Round(inventoryModel.Quantity.Value, 3);
                            model.MEINS = inventoryModel.MaxUnit;
                            //  string sendLocationType = string.Empty;
                            //if (inventoryModel.MaterialCode.Substring(0, 1) == "4")
                            //{
                            //    sendLocationType = "PKG3"; //"MFG3";
                            //}
                            //else
                            //{
                            //    sendLocationType = "MFG3"; //"MFG3";
                            //}
                            model.WERKS = "2010";
                            model.LGORT = fSplit[0];
                            model.CHARG = inventoryModel.BatchId;

                            model.UMWRK = "2010";
                            model.UMLGO = tSplit[0];
                            model.UMCHA = inventoryModel.BatchId;

                            model.SGTXT = "MES Transfer";
                            list.Add(model);

                            #endregion

                            #endregion
                        }
                    }

                    #endregion

                    #region 业务处理                   
                    if (isDelete != true)
                    {
                        //更新库房容器信息
                        MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                        models.EquipmentId = equipmentId;

                        #region 判断当前物料是否需要单位转换并改变数量

                        if (isConvert == true)
                        {
                            string unitF = inventoryModel.MaxUnit;

                            if (unitF.Trim() != "KG")
                            {
                                //这里进行查询并转换
                                var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == inventoryModel.MaterialCode && P.FormUnitName == unitF && P.ToUnitName == "KG");
                                if (uConventList != null)
                                {
                                    var uConvertModel = uConventList.FirstOrDefault();
                                    //标准值
                                    decimal fValue = uConvertModel.ConvertFormQty.Value;
                                    //目标单位
                                    decimal toValue = uConvertModel.ConvertToQty.Value;
                                    //这里处理数量
                                    inventQty = models.Quantity / fValue * toValue;

                                    models.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3);// inventQty;//更改数量
                                    models.QuantityUomId = kgUnitID;
                                }

                            }
                        }

                        #endregion

                        models.Modify(models.ID, userID);
                        upInvent.Add(models);
                    }
                    else
                    {
                        deleteList.Add(inventoryModel.ID);
                    }

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;

                    if (identicalmsg.Contains("NO"))
                    {
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移";
                    }
                    else
                    {
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移";
                    }
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.Tranremarks = tranModel.TranRemark;
                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    trans.SapPrintno = sapNo;


                    if (identicalmsg.Contains("NO"))
                    {
                        addTransferSap.Add(trans);
                    }
                    else
                    {
                        addTransfer.Add(trans);
                    }


                    #endregion

                    #region 修改容器位置


                    if (inventoryModel.ContainerId != null && inventoryModel.ContainerId != "")
                    {
                        //查询之前的位置
                        var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                        var containerModel = await _containerServices.FindEntity(whereLocation);

                        //更新容器新的位置
                        ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                        model.ID = inventoryModel.ContainerId;
                        model.EquipmentId = equipmentId;
                        model.Modify(model.ID, userID);
                        upCon.Add(model);



                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);

                        //hisModel.ID = Guid.NewGuid().ToString();
                        //   hisModel
                        hisModel.ContainerId = inventoryModel.ContainerId;
                        hisModel.Type = "";
                        hisModel.EquipmentId = equipmentId;
                        hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        hisModel.State = containerModel.Status;
                        hisModel.Comment = containerModel.Comment;
                        //hisModel.ProductOrderId 工单ID
                        //hisModel.BatchId 工单批次ID
                        hisModel.MaterialId = inventoryModel.MaterialId;
                        hisModel.SublotId = inventoryModel.SubLotId;
                        hisModel.Quantity = inventoryModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                        //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                        //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                        //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                        // hisModel.Status 批次执行状态
                        //hisModel.ContainerCode= containerModel. 容器编号
                        //hisModel.MaterialProducedActualId 物料产出记录ID
                        //hisModel.MaterialConsumedActualId 物料消耗记录ID
                        hisModel.LotId = inventoryModel.LotId;
                        hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                        conHis.Add(hisModel);
                    }


                    #endregion



                    #endregion                   

                    #endregion

                }
            }

            try
            {

                if (list != null && list.Count > 0)
                {
                    sapTransfer.SendList = list;
                }

                _unitOfWork.BeginTran();

                bool upResult = true;
                if (upInvent.Count > 0)
                {
                    upResult = await _materialInventoryServices.Update(upInvent);
                }
                bool deleteInvent = true;
                if (deleteList.Count > 0)
                {
                    deleteInvent = await _materialInventoryServices.DeleteByIds(deleteList.ToArray());
                }


                #region 调用SAP接口(调用失败直接跳过)
                string sapNo = string.Empty;

                //if ((sapTransfer.SendList != null && sapTransfer.SendList.Count > 0) && addTransferSap.Count > 0)
                //{
                //    //这里筛选对应的转移历史记录

                // List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                //    if (sapStockTranList == null)
                //    {
                //        return Failed("SAP接口调用失败");
                //    }
                //    if (sapStockTranList.Count == 0)
                //    {
                //        return Failed("SAP返回数据为空");
                //    }
                //    else if (sapStockTranList[0].Sucess == "NOK")
                //    {
                //        return Failed("SAP接口调用失败:" + sapStockTranList[0].Msg);
                //    }

                //    if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                //    {

                //        return Failed("SAP返回数据为空");
                //    }
                //    //默认认为这里的数据是一样条数
                //    for (int i = 0; i < sapStockTranList.Count; i++)
                //    {    //获取对应的物料凭证编码插入数据到新增记录里
                //        string sapNos = sapStockTranList[i].MCodeNameplateNo;
                //        sapNo = sapStockTranList[i].MCodeNameplateNo;
                //        addTransferSap[i].SapPrintno = sapNos;
                //        //  addTransfer.Add(addTransferSap[i]);

                //    }

                //    //  string sapNo = sapStockTranList[0].MCodeNameplateNo;
                //    //   trans.SapPrintno = sapNo;

                //}

                bool tranSapHis = true;
                if (addTransferSap.Count > 0)
                {
                    tranSapHis = await _materialTransferServices.Add(addTransferSap) > 0;
                }

                #endregion

                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }

                bool rqResult = true;
                if (upCon.Count > 0)
                {
                    rqResult = await _containerServices.Update(upCon);
                }

                bool hisUp = true;
                if (conHis.Count > 0)
                {
                    hisUp = await _containerHistoryServices.Add(conHis) > 0;
                }

                if (!upResult || !tranHis || !rqResult || !hisUp || !tranSapHis)//|| !addResult)
                {
                    _unitOfWork.RollbackTran();

                    return Failed("转移失败");
                }
                _unitOfWork.CommitTran();
                return Success("", "转移成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("转移失败");
            }

        }



        /// <summary>
        /// 转移按钮（非容器转移，根据物料库存进行转移）
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> TransferContainer_WL([FromBody] TransferModel tranModel)
        {
            /// </summary>
            /// <param name="id">物料库存ID(这里再编辑界面能拿到)</param>      
            /// <param name="equipmentId">存储新区域id</param>
            /// <param name="name">存储区域名称</param>
            /// <returns></returns>
            /// 
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;
            string equipmentId = tranModel.EquipmentId;
            string name = tranModel.Name;
            //if (string.IsNullOrEmpty(id))
            //{
            //    return Failed("转移失败，不存在ID");
            //}
            if (string.IsNullOrEmpty(equipmentId))
            {
                return Failed("转移失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("转移失败，不存在name");
            }

            #region 查询属性

            bool isConvert = false;
            string kgUnitID = string.Empty;

            //查询属性表
            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _uIser.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData != null)
            {
                var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                if (item != null)
                {
                    isConvert = true;
                    var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                    if (umanagerModel != null)
                    {
                        DFM.Model.Models.UnitmanageEntity model = umanagerModel.FirstOrDefault();
                        kgUnitID = model.ID;
                    }
                }
            }

            #endregion

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            List<ContainerEntity> upCon = new List<ContainerEntity>();
            List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            List<string> deleteList = new List<string>();
            #endregion

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("转移失败" + msg);
            }

            msg = await _inventorylistingViewServices.GetProductionByInventID(tranModel.ID);
            if (!string.IsNullOrEmpty(msg))
            {
                return Failed("转移失败" + msg);
            }

            #endregion

            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = "转移:MES-ToSap";
            sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");

            var inventoryModels = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(p => p.ID, tranModel.ID).ToListAsync();
            if (inventoryModels == null || (inventoryModels.Count != tranModel.ID.Length))
            {
                return Failed("请确认选中库存是否存在");
            }

            //退库SAP
            List<SendTran> list = new List<SendTran>();

            //查询Locations
            var equpT = await _materialInventoryServices.GetEquipment(tranModel.EquipmentId);

            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();
                if (id != string.Empty)
                {
                    //判断当前批次和子批次数据

                    //拿数据
                    var inventoryModel = inventoryModels.Where(p => p.ID == id).ToList().FirstOrDefault();
                    if (inventoryModel == null)
                    {
                        return Failed("转移失败，不存在转移视图实体类");
                    }
                    decimal inventQty = inventoryModel.Quantity.Value;


                    #region 进行转移操作

                    #region 跨Locatins需要转移

                    //拿当前选中数量的Locatins
                    var equpF = await _materialInventoryServices.GetEquipment(inventoryModel.EquipmentId);

                    string[] fSplit = equpF.Split(';');
                    string[] tSplit = equpT.Split(";");

                    if (fSplit == null || tSplit == null)
                    {
                        return Failed("请确认选中库存信息是否存在节点");
                    }
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    bool isSap = false;
                    //需要构造发送实体发送sap
                    if (fSplit[0] != tSplit[0])
                    {
                        isSap = true;
                        trans.WmsPrintno = inventoryModel.SubLotId;
                        trans.MesProno = inventoryModel.ProBatch;
                        trans.NewEquipmentId = tranModel.EquipmentId;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移:MES-SAP";
                        trans.Remark = "转移:MES-SAP";

                        #region 构造发送SAP实体

                        SendTran model = new SendTran();
                        model.LINE_ID = (i + 1).ToString();
                        model.BWART = "311";
                        model.MATNR = inventoryModel.MaterialCode;//transferView.NewMaterialCode;
                        model.MENGE = Math.Round(inventoryModel.Quantity.Value, 3);
                        model.MEINS = inventoryModel.MaxUnit;

                        model.WERKS = "2010";
                        model.LGORT = fSplit[0];
                        model.CHARG = inventoryModel.BatchId;

                        model.UMWRK = "2010";
                        model.UMLGO = tSplit[0];
                        model.UMCHA = inventoryModel.BatchId;

                        model.SGTXT = "MES Transfer";
                        list.Add(model);

                        #endregion



                    }

                    #region 正常转移逻辑

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                    models.EquipmentId = equipmentId;

                    #region 判断当前物料是否需要单位转换并改变数量

                    if (isConvert == true)
                    {
                        string unitF = inventoryModel.MaxUnit;

                        if (unitF.Trim() != "KG")
                        {
                            //这里进行查询并转换
                            var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == inventoryModel.MaterialCode && P.FormUnitName == unitF && P.ToUnitName == "KG");
                            if (uConventList != null)
                            {
                                var uConvertModel = uConventList.FirstOrDefault();
                                //标准值
                                decimal fValue = uConvertModel.ConvertFormQty.Value;
                                //目标单位
                                decimal toValue = uConvertModel.ConvertToQty.Value;
                                //这里处理数量
                                inventQty = models.Quantity / fValue * toValue;

                                models.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3);// inventQty;//更改数量
                                models.QuantityUomId = kgUnitID;
                            }

                        }
                    }

                    #endregion

                    models.Modify(models.ID, userID);
                    upInvent.Add(models);


                    #endregion

                    #region 写入转移历史

                    //写入历史记录

                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;

                    if (isSap != true)
                    {
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移";
                    }
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;
                    trans.Tranremarks= tranModel.TranRemark;
                    //加入不同的转移历史
                    if (isSap == true)
                    {
                        addTransferSap.Add(trans);
                    }
                    else
                    {
                        addTransfer.Add(trans);
                    }

                    #endregion

                    #endregion

                    #endregion
                }
            }

            try
            {

                if (list != null && list.Count > 0)
                {
                    #region 构造发送数据

                    //这里合并下数量
                    List<SendTran> resultDis = (from a in list
                                                group a by new
                                                {
                                                    BWART = a.BWART,
                                                    MATNR = a.MATNR,
                                                    MEINS = a.MEINS,
                                                    WERKS = a.WERKS,
                                                    LGORT = a.LGORT,
                                                    CHARG = a.CHARG,
                                                    UMWRK = a.UMWRK,
                                                    UMLGO = a.UMLGO,
                                                    UMCHA = a.UMCHA,
                                                    SGTXT = a.SGTXT
                                                } into g
                                                select new SendTran
                                                {
                                                    BWART = g.Key.BWART,
                                                    MATNR = g.Key.MATNR,
                                                    MEINS = g.Key.MEINS,
                                                    WERKS = g.Key.WERKS,
                                                    LGORT = g.Key.LGORT,
                                                    CHARG = g.Key.CHARG,
                                                    UMWRK = g.Key.UMWRK,
                                                    UMLGO = g.Key.UMLGO,
                                                    UMCHA = g.Key.UMCHA,
                                                    SGTXT = g.Key.SGTXT,
                                                    MENGE = g.Sum(p => p.MENGE)
                                                }).ToList();

                    List<SendTran> resultSend = new List<SendTran>();
                    for (int i = 0; i < resultDis.Count; i++)
                    {
                        resultDis[i].LINE_ID = (i + 1).ToString();
                        resultSend.Add(resultDis[i]);
                    }
                    sapTransfer.SendList = resultSend;
                    #endregion
                }

                _unitOfWork.BeginTran();

                bool upResult = true;
                if (upInvent.Count > 0)
                {
                    upResult = await _materialInventoryServices.Update(upInvent);
                }

                #region 调用SAP接口(调用失败直接跳过)转移历史合并

                string sapNo = string.Empty;
                if (sapTransfer.SendList != null && sapTransfer.SendList.Count > 0 && addTransferSap.Count > 0)
                {
                    List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                    if (sapStockTranList == null)
                    {
                        return Failed("SAP接口调用失败");
                    }
                    if (sapStockTranList.Count == 0)
                    {
                        _unitOfWork.RollbackTran();

                        return Failed("SAP返回数据为空");
                    }
                    else if (sapStockTranList[0].Sucess == "NOK")
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败:" + sapStockTranList[0].Msg);
                    }

                    if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }

                    //默认认为这里的数据是一样条数
                    for (int i = 0; i < sapStockTranList.Count; i++)
                    {    //获取对应的物料凭证编码插入数据到新增记录里
                        string sapNos = sapStockTranList[i].MCodeNameplateNo;
                        sapNo = sapStockTranList[i].MCodeNameplateNo;
                        if (sapNo != string.Empty)
                        {
                            continue;
                        }
                    }
                    for (int i = 0; i < addTransferSap.Count; i++)
                    {
                        addTransferSap[i].SapPrintno = sapNo;
                        addTransfer.Add(addTransferSap[i]);
                    }
                }

                #endregion

                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }
                if (!upResult || !tranHis)
                {
                    _unitOfWork.RollbackTran();

                    return Failed("转移失败");
                }
                _unitOfWork.CommitTran();
                return Success("", "转移成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("转移失败");
            }

        }


        /// <summary>
        /// 转移按钮（根据容器转移进行转移）
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> TransferContainerByContainer([FromBody] TransferModel tranModel)
        {
            //这里需要查询容器里面的所有的库存（传入的ID是容器ID）
            List<string> inventIDs = new List<string>();
            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                string conID = tranModel.ID[i];
                //查询数据
                //var whereExpressionInventory = Expressionable.Create<MaterialInventoryEntity>().And(p => p.ContainerId == conID)
                //    .ToExpression();
                List<MaterialInventoryEntity> inventList = await _materialInventoryServices.FindList(p => p.ContainerId == conID);
                foreach (var item in inventList)
                {
                    inventIDs.Add(item.ID);
                }
            }
            if (inventIDs == null || inventIDs.Count <= 0)
            {
                return Failed("转移失败，该容器没有绑定库存信息");
            }
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;
            string equipmentId = tranModel.EquipmentId;
            string name = tranModel.Name;
            //if (string.IsNullOrEmpty(id))
            //{
            //    return Failed("转移失败，不存在ID");
            //}
            if (string.IsNullOrEmpty(equipmentId))
            {
                return Failed("转移失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("转移失败，不存在name");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<ContainerEntity> upCon = new List<ContainerEntity>();
            List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            #endregion

            for (int i = 0; i < inventIDs.Count; i++)
            {
                id = inventIDs[i].ToString();
                if (id != string.Empty)
                {
                    #region 进行转移操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("转移失败，不存在转移视图实体类");
                    }

                    #region 业务处理                   

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                    models.EquipmentId = equipmentId;
                    models.Modify(models.ID, userID);
                    upInvent.Add(models);

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Transfer Inventory";
                    trans.Comment = "转移";
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;

                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    addTransfer.Add(trans);

                    #endregion

                    #region 修改容器位置

                    //查询之前的位置
                    var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                    var containerModel = await _containerServices.FindEntity(whereLocation);

                    //更新容器新的位置
                    ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                    model.ID = inventoryModel.ContainerId;
                    model.EquipmentId = equipmentId;
                    model.Modify(model.ID, userID);
                    upCon.Add(model);



                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    hisModel.Create(userID);

                    //hisModel.ID = Guid.NewGuid().ToString();
                    //   hisModel
                    hisModel.ContainerId = inventoryModel.ContainerId;
                    hisModel.Type = "";
                    hisModel.EquipmentId = equipmentId;
                    hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    hisModel.State = containerModel.Status;
                    hisModel.Comment = containerModel.Comment;
                    //hisModel.ProductOrderId 工单ID
                    //hisModel.BatchId 工单批次ID
                    hisModel.MaterialId = inventoryModel.MaterialId;
                    hisModel.SublotId = inventoryModel.SubLotId;
                    hisModel.Quantity = inventoryModel.Quantity.ToString();
                    hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    // hisModel.Status 批次执行状态
                    //hisModel.ContainerCode= containerModel. 容器编号
                    //hisModel.MaterialProducedActualId 物料产出记录ID
                    //hisModel.MaterialConsumedActualId 物料消耗记录ID
                    hisModel.LotId = inventoryModel.LotId;
                    hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    conHis.Add(hisModel);


                    #endregion



                    #endregion

                    #endregion

                }
            }

            try
            {
                _unitOfWork.BeginTran();

                bool upResult = await _materialInventoryServices.Update(upInvent);
                bool tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                bool rqResult = await _containerServices.Update(upCon);
                bool hisUp = await _containerHistoryServices.Add(conHis) > 0;


                if (!upResult || !tranHis || !rqResult || !hisUp)//|| !addResult)
                {
                    _unitOfWork.RollbackTran();

                    return Failed("转移失败");
                }
                _unitOfWork.CommitTran();
                return Success("", "转移成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("转移失败");
            }

        }

        [HttpPost]
        public async Task<MessageModel<string>> GetEquipmentIDScrap(string tranModel)
        {
            var datas = await _containerServices.GetEquipmentID(tranModel);
            if (datas == null)
            {
                return Failed("报废失败，请配置质量报废节点");
            }
            return Failed("退库失败，退货节点配置失败");
        }

        /// <summary>
        /// 报废按钮
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ScrapInventory([FromBody] TransferModel tranModel)
        {
            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            if (!string.IsNullOrEmpty(msg))
            {
                return Failed(msg);
            }

            #endregion
            string equipMentID = "";
            bool isDelete = false;

            //string sapLocation = "";
            //string positionName = "";

            var datas = await _containerServices.GetEquipmentID("FGLocation");
            if (datas == null)
            {
                return Failed("报废失败，请配置质量报废节点");
            }
            equipMentID = datas.Data;
            isDelete = datas.Data1 == "1" ? false : true;//是否管理库存
            string[] sp = datas.Data2.Split(';');

            if (sp.Count() <= 1)
            {
                return Failed("退库失败，退货节点配置失败");
            }
            //sapLocation = sp[1];
            //positionName = sp[0];

            /// </summary>
            /// <param name="id">物料库存ID(这里再编辑界面能拿到)</param>      
            /// <param name="equipmentId">存储新区域id</param>
            /// <param name="name">存储区域名称</param>
            /// <returns></returns>
            /// DFM_M_EQUIPMENT_REQUIREMENT  5 LKKSCRAP
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;
            string name = "LKKSCRAP";
            //if (string.IsNullOrEmpty(id))
            //{
            //    return Failed("转移失败，不存在ID");
            //}
            if (string.IsNullOrEmpty(equipMentID))
            {
                return Failed("报废失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("报废失败，不存在name");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            //List<ContainerEntity> upCon = new List<ContainerEntity>();
            //List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            #endregion

            _unitOfWork.BeginTran();
            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();
                if (id != string.Empty)
                {
                    #region 进行报废操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("报废失败，不存在报废视图实体类");
                    }

                    string sapLocation = inventoryModel.LocationS.ToString();

                    #region 业务处理                   

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

                    //判断是否存在(存在更新)
                    if (!string.IsNullOrEmpty(equipMentID))
                    {
                        models.Modify(models.ID, _uIser.ToString());
                        models.EquipmentId = equipMentID;
                        upInvent.Add(models);
                        trans.NewEquipmentId = equipMentID;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "报废-转移";
                    }
                    else
                    {
                        trans.NewEquipmentId = inventoryModel.EquipmentId;
                        trans.Type = "Scrap";
                        trans.Comment = "报废-删除";
                        bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                    }

                    #region 写入转移历史


                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = inventoryModel.LocationF;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                    // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;

                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;

                    trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    addTransfer.Add(trans);

                    #endregion

                    #region 修改容器位置

                    ////查询之前的位置
                    //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                    //var containerModel = await _containerServices.FindEntity(whereLocation);

                    ////更新容器新的位置
                    //ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                    //model.ID = inventoryModel.ContainerId;
                    //model.EquipmentId = equipmentId;
                    //model.Modify(model.ID, userID);
                    //upCon.Add(model);

                    ////写入容器记录表(Add)
                    //ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    //hisModel.Create(userID);

                    ////hisModel.ID = Guid.NewGuid().ToString();
                    ////   hisModel
                    //hisModel.ContainerId = inventoryModel.ContainerId;
                    //hisModel.Type = "";
                    //hisModel.EquipmentId = equipmentId;
                    //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    //hisModel.Comment = containerModel.Comment;
                    ////hisModel.ProductOrderId 工单ID
                    ////hisModel.BatchId 工单批次ID
                    //hisModel.MaterialId = inventoryModel.MaterialId;
                    //hisModel.SublotId = inventoryModel.SubLotId;
                    //hisModel.Quantity = inventoryModel.Quantity.ToString();
                    //hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    //// hisModel.Status 批次执行状态
                    ////hisModel.ContainerCode= containerModel. 容器编号
                    ////hisModel.MaterialProducedActualId 物料产出记录ID
                    ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel.LotId = inventoryModel.LotId;
                    //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    //conHis.Add(hisModel);

                    #endregion

                    #endregion

                    #endregion

                }
            }

            try
            {
                bool tranHis = await _materialTransferServices.Add(addTransfer) > 0;

                bool upInvents = true;
                if (upInvent.Count > 0)
                {
                    upInvents = await _materialInventoryServices.Update(upInvent);
                }
                if (!tranHis || !upInvents)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("报废失败");
                }
                _unitOfWork.CommitTran();
                return Success("", "报废成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("报废失败");
            }
        }




        /// <summary>
        /// 退库按钮
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReturnInventory([FromBody] TransferModel tranModel)
        {

            string equipMentID = "";
            bool isControl = false;
            string name = "LKKReturn";

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            if (!string.IsNullOrEmpty(msg))
            {
                return Failed(msg);
            }

            #endregion

            //这里是查询共产建模配置的数据
            string sapLocation = "";
            string positionName = "";
            if (tranModel.State == "NOK")
            {
                var datas = await _containerServices.GetEquipmentID("QAReturnLocation");
                if (datas == null)
                {
                    return Failed("退库失败，请配置质量退货节点");
                }
                name = "LKKQtyReturn";
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }
                sapLocation = sp[1];
                positionName = sp[0];
            }
            else
            {
                var datas = await _containerServices.GetEquipmentID("ReturnToLocation");

                if (datas == null)
                {
                    return Failed("退库失败，请配置退货节点");
                }
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }

                sapLocation = sp[1];
                positionName = sp[0];
            }

            // sapLocation = "RSBK";
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;

            if (string.IsNullOrEmpty(equipMentID))
            {
                return Failed("退库失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("退库失败，不存在name");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            //List<ContainerEntity> upCon = new List<ContainerEntity>();
            //List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            List<MaterialInventoryEntity> deleteInvent = new List<MaterialInventoryEntity>();
            #endregion


            string errorMsg = string.Empty;

            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = tranModel.Remark;//  "MES-TO-SAP";
            sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
            //退库SAP
            List<SendTran> list = new List<SendTran>();

            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();

                if (id != string.Empty)
                {
                    #region 进行退库操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("退库失败，不存在退库视图实体类");
                    }


                    string oldLocation = inventoryModel.LocationS.ToString();

                    #region 业务处理                   

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

                    //判断是否存在(存在更新)
                    if (isControl == true)
                    {
                        trans.WmsPrintno = inventoryModel.SubLotId;
                        trans.MesProno = inventoryModel.ProBatch;

                        models.Modify(models.ID, _uIser.ToString());
                        models.EquipmentId = equipMentID;
                        //upInvent.Add(models);
                        trans.NewEquipmentId = equipMentID;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "退库-转移";

                        #region 创建退库数据实体

                        SendTran model = new SendTran();
                        model.LINE_ID = (i + 1).ToString();
                        model.BWART = positionName;

                        model.MATNR = inventoryModel.MaterialCode;//transferView.NewMaterialCode;
                        model.MENGE = Math.Round(inventoryModel.Quantity.Value, 3);
                        model.MEINS = inventoryModel.MaxUnit;

                        string sendLocationType = string.Empty;
                        //if (inventoryModel.MaterialCode.Substring(0, 1) == "4")
                        //{
                        //    sendLocationType = "PKG3"; //"MFG3";
                        //}
                        //else
                        //{
                        //    sendLocationType = "MFG3"; //"MFG3";
                        //}
                        sendLocationType = sapLocation;

                        model.WERKS = "2010";
                        model.LGORT = oldLocation;
                        model.CHARG = inventoryModel.BatchId;

                        model.UMWRK = "2010";
                        model.UMLGO = sendLocationType;
                        model.UMCHA = inventoryModel.BatchId;

                        model.SGTXT = "MES Transfer";
                        list.Add(model);

                        #endregion            

                        upInvent.Add(models);
                        // trans.SapPrintno = "带填写";

                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.WmsPrintno = positionName;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        trans.Remark = tranModel.Remark;
                        addTransferSap.Add(trans);

                        #endregion

                        deleteInvent.Add(models);
                        // bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                    }
                    else
                    {
                        trans.NewEquipmentId = equipMentID;//inventoryModel.EquipmentId;
                        trans.Type = "Return";
                        trans.Comment = "退库-删除";
                        //  bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                        deleteInvent.Add(models);
                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.Remark = tranModel.Remark;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        addTransfer.Add(trans);

                        #endregion                      
                    }

                    #region 修改容器位置

                    ////查询之前的位置
                    //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                    //var containerModel = await _containerServices.FindEntity(whereLocation);

                    ////更新容器新的位置
                    //ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                    //model.ID = inventoryModel.ContainerId;
                    //model.EquipmentId = equipmentId;
                    //model.Modify(model.ID, userID);
                    //upCon.Add(model);

                    ////写入容器记录表(Add)
                    //ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    //hisModel.Create(userID);

                    ////hisModel.ID = Guid.NewGuid().ToString();
                    ////   hisModel
                    //hisModel.ContainerId = inventoryModel.ContainerId;
                    //hisModel.Type = "";
                    //hisModel.EquipmentId = equipmentId;
                    //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    //hisModel.Comment = containerModel.Comment;
                    ////hisModel.ProductOrderId 工单ID
                    ////hisModel.BatchId 工单批次ID
                    //hisModel.MaterialId = inventoryModel.MaterialId;
                    //hisModel.SublotId = inventoryModel.SubLotId;
                    //hisModel.Quantity = inventoryModel.Quantity.ToString();
                    //hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    //// hisModel.Status 批次执行状态
                    ////hisModel.ContainerCode= containerModel. 容器编号
                    ////hisModel.MaterialProducedActualId 物料产出记录ID
                    ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel.LotId = inventoryModel.LotId;
                    //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    //conHis.Add(hisModel);

                    #endregion

                    #endregion

                    #endregion
                }
            }

            try
            {
                _unitOfWork.BeginTran();

                if (list != null && list.Count > 0)
                {
                    sapTransfer.SendList = list;
                }
                if (addTransfer.Count <= 0)
                {
                    if (!string.IsNullOrEmpty(errorMsg))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed(errorMsg);
                    }
                }
                //if (upInvent.Count > 0)
                //{
                //    upInvents = await _materialInventoryServices.Update(upInvent);
                //}

                //删除库存
                bool deleteInvents = true;

                if (deleteInvent.Count > 0)
                {
                    string[] ids = deleteInvent.Select(p => p.ID).ToArray();
                    deleteInvents = await _materialInventoryServices.DeleteByIds(ids);
                }

                // deleteInvent.Add(models);

                #region 调用SAP接口(调用失败直接跳过)
                string sapNo = string.Empty;

                if (sapTransfer.SendList.Count > 0 && addTransferSap.Count > 0)
                {
                    //这里筛选对应的转移历史记录

                    List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                    if (sapStockTranList == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败");
                    }
                    if (sapStockTranList.Count == 0)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    else if (sapStockTranList[0].Sucess == "NOK")
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败:" + sapStockTranList[0].Msg);
                    }

                    if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    //默认认为这里的数据是一样条数
                    for (int i = 0; i < sapStockTranList.Count; i++)
                    {    //获取对应的物料凭证编码插入数据到新增记录里
                        string sapNos = sapStockTranList[i].MCodeNameplateNo;
                        sapNo = sapStockTranList[i].MCodeNameplateNo;
                        if (sapNo != string.Empty)
                        {
                            continue;
                        }
                    }
                    for (int i = 0; i < addTransferSap.Count; i++)
                    {
                        addTransferSap[i].SapPrintno = sapNo;
                        addTransfer.Add(addTransferSap[i]);
                    }
                }

                #endregion





                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }

                //这里进行库存转移调用接口操作
                if (!tranHis || !deleteInvents)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("退库失败:转移历史状态：" + tranHis + "删除状态：" + deleteInvents);
                }

                _unitOfWork.CommitTran();
                if (sapNo != string.Empty)
                {
                    string msgs = sapNo + ";" + sapLocation + ";" + positionName + ";" + tranModel.Remark + ";";
                    return Success(msgs, "退库成功");
                }

                return Success("", "退库成功");
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("退库失败" + ex);
            }
        }




        [HttpPost]
        public async Task<MessageModel<string>> CherkReturnData([FromBody] TransferModel tranModel)
        {
            string msg = await _inventorylistingViewServices.CherkReturnData(tranModel.ID);
            if (msg.Contains("失败"))
            {
                return Failed(msg);
            }
            return Success("", msg);
        }

        [HttpPost]
        public async Task<MessageModel<string>> CherkReturnDataByVeriyDetail([FromBody] TransferModel tranModel)
        {
            string msg = await _inventorylistingViewServices.CherkReturnDataByVeriyDetail(tranModel.ID);
            if (msg.Contains("失败"))
            {
                return Failed(msg);
            }
            return Success("", msg);
        }




        /// <summary>
        /// 合并数据并返回
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PrintPDFModel>>> MergePDFData(List<PrintPDFModel> tranModel)
        {
            var result = (from a in tranModel
                          group a by new
                          {
                              a.MaterialCode,
                              a.MaterialName,
                              a.BatchId,
                              a.MinUnit,
                              a.LocationF,
                              a.Suppiername
                          } into g
                          select new PrintPDFModel
                          {
                              MaterialCode = g.Key.MaterialCode,
                              MaterialName = g.Key.MaterialName,
                              BatchId = g.Key.BatchId,
                              MinUnit = g.Key.MinUnit,
                              LocationF = g.Key.LocationF,
                              Suppiername = g.Key.Suppiername,
                              Quantity = g.Sum(p => p.Quantity)

                          }).ToList();
            for (int i = 0; i < result.Count; i++)
            {
                result[i].index = (i + 1).ToString();
            }
            return Success(result, "获取成功");
        }


        /// <summary>
        /// 退库按钮
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReturnInventoryALL([FromBody] TransferModel tranModel)
        {

            string remarkstr = tranModel.Remark;
            if (remarkstr.Length > 25)
            {
                return Failed("备注超过最大长度12");
            }

            string equipMentID = "";
            bool isControl = false;
            string name = "LKKReturn";

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            //string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            string msg = tranModel.Msg;


            if (msg.Contains("失败"))
            {
                return Failed(msg);
            }
            string[] location = msg.Split(';');
            if (location.Length <= 1)
            {
                return Failed(msg, "请确认数据是否准确");
            }

            msg = location[1].ToString();
            #endregion

            //这里是查询共产建模配置的数据
            string sapLocation = "";
            string positionName = "";
            if (tranModel.State == "NOK")
            {
                var datas = await _containerServices.GetEquipmentID(msg);
                if (datas == null)
                {
                    return Failed("退库失败，请配置质量退货节点");
                }
                name = "LKKQtyReturn";
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }
                sapLocation = sp[1];
                positionName = sp[0];
            }
            else
            {
                var datas = await _containerServices.GetEquipmentID(msg);
                if (datas == null)
                {
                    return Failed("退库失败，请配置退货节点");
                }
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }

                sapLocation = sp[1];
                positionName = sp[0];
            }

            // sapLocation = "RSBK";
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;

            if (string.IsNullOrEmpty(equipMentID))
            {
                return Failed("退库失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("退库失败，不存在name");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            //List<ContainerEntity> upCon = new List<ContainerEntity>();
            //List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            List<MaterialInventoryEntity> deleteInvent = new List<MaterialInventoryEntity>();
            #endregion


            string errorMsg = string.Empty;

            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = tranModel.Remark;//  "MES-TO-SAP";
            sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
            //退库SAP
            List<SendTran> list = new List<SendTran>();

            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();

                if (id != string.Empty)
                {
                    #region 进行退库操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("退库失败，不存在退库视图实体类");
                    }


                    string oldLocation = inventoryModel.LocationS.ToString();

                    #region 业务处理                   

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

                    //判断是否存在(存在更新)
                    if (isControl == true)
                    {
                        trans.WmsPrintno = inventoryModel.SubLotId;
                        trans.MesProno = inventoryModel.ProBatch;

                        models.Modify(models.ID, _uIser.ToString());
                        models.EquipmentId = equipMentID;
                        //upInvent.Add(models);
                        trans.NewEquipmentId = equipMentID;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "退库-转移";

                        #region 创建退库数据实体

                        SendTran model = new SendTran();
                        model.LINE_ID = (i + 1).ToString();
                        model.BWART = positionName;

                        model.MATNR = inventoryModel.MaterialCode;//transferView.NewMaterialCode;
                        model.MENGE = Math.Round(inventoryModel.Quantity.Value, 3); // inventoryModel.Quantity.Value;
                        model.MEINS = inventoryModel.MaxUnit;

                        string sendLocationType = string.Empty;
                        //if (inventoryModel.MaterialCode.Substring(0, 1) == "4")
                        //{
                        //    sendLocationType = "PKG3"; //"MFG3";
                        //}
                        //else
                        //{
                        //    sendLocationType = "MFG3"; //"MFG3";
                        //}
                        sendLocationType = sapLocation;

                        model.WERKS = "2010";
                        model.LGORT = oldLocation;
                        model.CHARG = inventoryModel.BatchId;

                        model.UMWRK = "2010";
                        model.UMLGO = sendLocationType;
                        model.UMCHA = inventoryModel.BatchId;

                        model.SGTXT = "MES Transfer";
                        list.Add(model);

                        #endregion            

                        upInvent.Add(models);
                        // trans.SapPrintno = "带填写";

                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.WmsPrintno = positionName;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        trans.Remark = tranModel.Remark;
                        addTransferSap.Add(trans);

                        #endregion

                        deleteInvent.Add(models);
                        // bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                    }
                    else
                    {
                        trans.NewEquipmentId = equipMentID;//inventoryModel.EquipmentId;
                        trans.Type = "Return";
                        trans.Comment = "退库-删除";
                        //  bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                        deleteInvent.Add(models);
                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.Remark = tranModel.Remark;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        addTransfer.Add(trans);

                        #endregion                      
                    }

                    #region 修改容器位置

                    ////查询之前的位置
                    //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                    //var containerModel = await _containerServices.FindEntity(whereLocation);

                    ////更新容器新的位置
                    //ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                    //model.ID = inventoryModel.ContainerId;
                    //model.EquipmentId = equipmentId;
                    //model.Modify(model.ID, userID);
                    //upCon.Add(model);

                    ////写入容器记录表(Add)
                    //ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    //hisModel.Create(userID);

                    ////hisModel.ID = Guid.NewGuid().ToString();
                    ////   hisModel
                    //hisModel.ContainerId = inventoryModel.ContainerId;
                    //hisModel.Type = "";
                    //hisModel.EquipmentId = equipmentId;
                    //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    //hisModel.Comment = containerModel.Comment;
                    ////hisModel.ProductOrderId 工单ID
                    ////hisModel.BatchId 工单批次ID
                    //hisModel.MaterialId = inventoryModel.MaterialId;
                    //hisModel.SublotId = inventoryModel.SubLotId;
                    //hisModel.Quantity = inventoryModel.Quantity.ToString();
                    //hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    //// hisModel.Status 批次执行状态
                    ////hisModel.ContainerCode= containerModel. 容器编号
                    ////hisModel.MaterialProducedActualId 物料产出记录ID
                    ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel.LotId = inventoryModel.LotId;
                    //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    //conHis.Add(hisModel);

                    #endregion

                    #endregion

                    #endregion
                }
            }

            try
            {
                _unitOfWork.BeginTran();

                if (list != null && list.Count > 0)
                {
                    //这里合并下数量
                    List<SendTran> result = (from a in list
                                             group a by new
                                             {
                                                 BWART = a.BWART,
                                                 MATNR = a.MATNR,
                                                 MEINS = a.MEINS,
                                                 WERKS = a.WERKS,
                                                 LGORT = a.LGORT,
                                                 CHARG = a.CHARG,
                                                 UMWRK = a.UMWRK,
                                                 UMLGO = a.UMLGO,
                                                 UMCHA = a.UMCHA,
                                                 SGTXT = a.SGTXT
                                             } into g
                                             select new SendTran
                                             {
                                                 BWART = g.Key.BWART,
                                                 MATNR = g.Key.MATNR,
                                                 MEINS = g.Key.MEINS,
                                                 WERKS = g.Key.WERKS,
                                                 LGORT = g.Key.LGORT,
                                                 CHARG = g.Key.CHARG,
                                                 UMWRK = g.Key.UMWRK,
                                                 UMLGO = g.Key.UMLGO,
                                                 UMCHA = g.Key.UMCHA,
                                                 SGTXT = g.Key.SGTXT,
                                                 MENGE = g.Sum(p => p.MENGE)
                                             }).ToList();

                    List<SendTran> resultSend = new List<SendTran>();
                    for (int i = 0; i < result.Count; i++)
                    {
                        result[i].LINE_ID = (i + 1).ToString();
                        resultSend.Add(result[i]);
                    }

                    sapTransfer.SendList = resultSend;
                }
                if (addTransfer.Count <= 0)
                {
                    if (!string.IsNullOrEmpty(errorMsg))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed(errorMsg);
                    }
                }
                //if (upInvent.Count > 0)
                //{
                //    upInvents = await _materialInventoryServices.Update(upInvent);
                //}

                //删除库存
                bool deleteInvents = true;

                if (deleteInvent.Count > 0)
                {
                    string[] ids = deleteInvent.Select(p => p.ID).ToArray();
                    deleteInvents = await _materialInventoryServices.DeleteByIds(ids);
                }

                // deleteInvent.Add(models);

                #region 调用SAP接口(调用失败直接跳过)
                string sapNo = string.Empty;

                if (sapTransfer.SendList.Count > 0 && addTransferSap.Count > 0)
                {
                    //这里筛选对应的转移历史记录

                    List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                    if (sapStockTranList == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败");
                    }
                    if (sapStockTranList.Count == 0)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    else if (sapStockTranList[0].Sucess == "NOK")
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败:" + sapStockTranList[0].Msg);
                    }

                    if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    //默认认为这里的数据是一样条数
                    for (int i = 0; i < sapStockTranList.Count; i++)
                    {    //获取对应的物料凭证编码插入数据到新增记录里
                        string sapNos = sapStockTranList[i].MCodeNameplateNo;
                        sapNo = sapStockTranList[i].MCodeNameplateNo;
                        if (sapNo != string.Empty)
                        {
                            continue;
                        }
                    }
                    for (int i = 0; i < addTransferSap.Count; i++)
                    {
                        addTransferSap[i].SapPrintno = sapNo;
                        addTransfer.Add(addTransferSap[i]);
                    }
                }

                #endregion





                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }

                //这里进行库存转移调用接口操作
                if (!tranHis || !deleteInvents)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("退库失败:转移历史状态：" + tranHis + "删除状态：" + deleteInvents);
                }

                _unitOfWork.CommitTran();
                if (sapNo != string.Empty)
                {
                    string msgs = sapNo + ";" + sapLocation + ";" + positionName + ";" + tranModel.Remark + ";";
                    return Success(msgs, "退库成功");
                }

                return Success("", "退库成功");
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("退库失败" + ex);
            }
        }

        /// <summary>
        /// 退库按钮
        /// </summary>
        /// <param name="tranModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReturnInventoryALLVeriyDetail([FromBody] TransferModel tranModel)
        {

            //查询数据
            var vList = await _dalVerifiyDetailEntity.Db.Queryable<PPM.Model.Models.VerifiyDetailEntity>().In(P => P.ID, tranModel.ID).ToListAsync();
            string[] SublotIds = vList.GroupBy(p => p.SublotId).Select(p => p.Key).ToArray();
            if (SublotIds == null || SublotIds.Length <= 0)
            {
                return Failed("库存信息不存在");
            }

            var inventList = await _dalInventorylistingViewEntity.Db.Queryable<InventorylistingViewEntity>().In(P => P.SlotId, SublotIds).ToListAsync();
            tranModel.ID = inventList.GroupBy(p => p.ID).Select(p => p.Key).ToArray();
            if (tranModel.ID == null || tranModel.ID.Length <= 0)
            {
                return Failed("库存信息不存在");
            }

            string remarkstr = tranModel.Remark;
            if (remarkstr.Length > 25)
            {
                return Failed("备注超过最大长度12");
            }

            string equipMentID = "";
            bool isControl = false;
            string name = "LKKReturn";

            #region 判断当前批次和子批次数据
            //查询批次状态是否为锁定
            //查询子批次状态是否为锁定
            //string msg = await _inventorylistingViewServices.GetStateByInventID(tranModel.ID);

            string msg = tranModel.Msg;


            if (msg.Contains("失败"))
            {
                return Failed(msg);
            }
            string[] location = msg.Split(';');
            if (location.Length <= 1)
            {
                return Failed(msg, "请确认数据是否准确");
            }

            msg = location[1].ToString();
            #endregion

            //这里是查询共产建模配置的数据
            string sapLocation = "";
            string positionName = "";
            if (tranModel.State == "NOK")
            {
                var datas = await _containerServices.GetEquipmentID(msg);
                if (datas == null)
                {
                    return Failed("退库失败，请配置质量退货节点");
                }
                name = "LKKQtyReturn";
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }
                sapLocation = sp[1];
                positionName = sp[0];
            }
            else
            {
                var datas = await _containerServices.GetEquipmentID(msg);
                if (datas == null)
                {
                    return Failed("退库失败，请配置退货节点");
                }
                equipMentID = datas.Data;
                isControl = datas.Data1 == "1" ? true : false;//是否管理库存

                string[] sp = datas.Data2.Split(';');
                if (sp.Count() <= 1)
                {
                    return Failed("退库失败，退货节点配置失败");
                }

                sapLocation = sp[1];
                positionName = sp[0];
            }

            // sapLocation = "RSBK";
            string userID = _uIser.Name.ToString();
            var data = new MessageModel<string>();
            string id = string.Empty;

            if (string.IsNullOrEmpty(equipMentID))
            {
                return Failed("退库失败，不存在equipmentId");
            }
            if (string.IsNullOrEmpty(name))
            {
                return Failed("退库失败，不存在name");
            }

            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<MaterialTransferEntity> addTransferSap = new List<MaterialTransferEntity>();
            //List<ContainerEntity> upCon = new List<ContainerEntity>();
            //List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            List<MaterialInventoryEntity> deleteInvent = new List<MaterialInventoryEntity>();
            #endregion


            string errorMsg = string.Empty;

            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = tranModel.Remark;//  "MES-TO-SAP";
            sapTransfer.BUDAT = DateTime.Now.ToString("yyyy-MM-dd");
            //退库SAP
            List<SendTran> list = new List<SendTran>();

            for (int i = 0; i < tranModel.ID.Length; i++)
            {
                id = tranModel.ID[i].ToString();

                if (id != string.Empty)
                {
                    #region 进行退库操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return Failed("退库失败，不存在退库视图实体类");
                    }


                    string oldLocation = inventoryModel.LocationS.ToString();

                    #region 业务处理                   

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();

                    //更新库房容器信息
                    MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

                    //判断是否存在(存在更新)
                    if (isControl == true)
                    {
                        trans.WmsPrintno = inventoryModel.SubLotId;
                        trans.MesProno = inventoryModel.ProBatch;

                        models.Modify(models.ID, _uIser.ToString());
                        models.EquipmentId = equipMentID;
                        //upInvent.Add(models);
                        trans.NewEquipmentId = equipMentID;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "退库-转移";

                        #region 创建退库数据实体

                        SendTran model = new SendTran();
                        model.LINE_ID = (i + 1).ToString();
                        model.BWART = positionName;

                        model.MATNR = inventoryModel.MaterialCode;//transferView.NewMaterialCode;
                        model.MENGE = Math.Round(inventoryModel.Quantity.Value, 3); // inventoryModel.Quantity.Value;
                        model.MEINS = inventoryModel.MaxUnit;

                        string sendLocationType = string.Empty;
                        //if (inventoryModel.MaterialCode.Substring(0, 1) == "4")
                        //{
                        //    sendLocationType = "PKG3"; //"MFG3";
                        //}
                        //else
                        //{
                        //    sendLocationType = "MFG3"; //"MFG3";
                        //}
                        sendLocationType = sapLocation;

                        model.WERKS = "2010";
                        model.LGORT = oldLocation;
                        model.CHARG = inventoryModel.BatchId;

                        model.UMWRK = "2010";
                        model.UMLGO = sendLocationType;
                        model.UMCHA = inventoryModel.BatchId;

                        model.SGTXT = "MES Transfer";
                        list.Add(model);

                        #endregion            

                        upInvent.Add(models);
                        // trans.SapPrintno = "带填写";

                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.WmsPrintno = positionName;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        trans.Remark = tranModel.Remark;
                        addTransferSap.Add(trans);

                        #endregion

                        deleteInvent.Add(models);
                        // bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                    }
                    else
                    {
                        trans.NewEquipmentId = equipMentID;//inventoryModel.EquipmentId;
                        trans.Type = "Return";
                        trans.Comment = "退库-删除";
                        //  bool deleteResult = await _materialInventoryServices.DeleteById(inventoryModel.ID);
                        deleteInvent.Add(models);
                        #region 写入转移历史

                        trans.Create(userID);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity), 3);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        trans.ProductionExecutionId = inventoryModel.ProductionRequestId;


                        // trans.NewEquipmentRequirementId =// inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;

                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;
                        trans.Remark = tranModel.Remark;
                        trans.PhysicalQuantity = inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                        addTransfer.Add(trans);

                        #endregion                      
                    }

                    #region 修改容器位置

                    ////查询之前的位置
                    //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                    //var containerModel = await _containerServices.FindEntity(whereLocation);

                    ////更新容器新的位置
                    //ContainerEntity model = await _containerServices.QueryById(inventoryModel.ContainerId);
                    //model.ID = inventoryModel.ContainerId;
                    //model.EquipmentId = equipmentId;
                    //model.Modify(model.ID, userID);
                    //upCon.Add(model);

                    ////写入容器记录表(Add)
                    //ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    //hisModel.Create(userID);

                    ////hisModel.ID = Guid.NewGuid().ToString();
                    ////   hisModel
                    //hisModel.ContainerId = inventoryModel.ContainerId;
                    //hisModel.Type = "";
                    //hisModel.EquipmentId = equipmentId;
                    //hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    //hisModel.Comment = containerModel.Comment;
                    ////hisModel.ProductOrderId 工单ID
                    ////hisModel.BatchId 工单批次ID
                    //hisModel.MaterialId = inventoryModel.MaterialId;
                    //hisModel.SublotId = inventoryModel.SubLotId;
                    //hisModel.Quantity = inventoryModel.Quantity.ToString();
                    //hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                    ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    //// hisModel.Status 批次执行状态
                    ////hisModel.ContainerCode= containerModel. 容器编号
                    ////hisModel.MaterialProducedActualId 物料产出记录ID
                    ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel.LotId = inventoryModel.LotId;
                    //hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                    //conHis.Add(hisModel);

                    #endregion

                    #endregion

                    #endregion
                }
            }

            try
            {
                _unitOfWork.BeginTran();

                if (list != null && list.Count > 0)
                {
                    //这里合并下数量
                    List<SendTran> result = (from a in list
                                             group a by new
                                             {
                                                 BWART = a.BWART,
                                                 MATNR = a.MATNR,
                                                 MEINS = a.MEINS,
                                                 WERKS = a.WERKS,
                                                 LGORT = a.LGORT,
                                                 CHARG = a.CHARG,
                                                 UMWRK = a.UMWRK,
                                                 UMLGO = a.UMLGO,
                                                 UMCHA = a.UMCHA,
                                                 SGTXT = a.SGTXT
                                             } into g
                                             select new SendTran
                                             {
                                                 BWART = g.Key.BWART,
                                                 MATNR = g.Key.MATNR,
                                                 MEINS = g.Key.MEINS,
                                                 WERKS = g.Key.WERKS,
                                                 LGORT = g.Key.LGORT,
                                                 CHARG = g.Key.CHARG,
                                                 UMWRK = g.Key.UMWRK,
                                                 UMLGO = g.Key.UMLGO,
                                                 UMCHA = g.Key.UMCHA,
                                                 SGTXT = g.Key.SGTXT,
                                                 MENGE = g.Sum(p => p.MENGE)
                                             }).ToList();

                    List<SendTran> resultSend = new List<SendTran>();
                    for (int i = 0; i < result.Count; i++)
                    {
                        result[i].LINE_ID = (i + 1).ToString();
                        resultSend.Add(result[i]);
                    }

                    sapTransfer.SendList = resultSend;
                }
                if (addTransfer.Count <= 0)
                {
                    if (!string.IsNullOrEmpty(errorMsg))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed(errorMsg);
                    }
                }
                //if (upInvent.Count > 0)
                //{
                //    upInvents = await _materialInventoryServices.Update(upInvent);
                //}

                //删除库存
                bool deleteInvents = true;

                if (deleteInvent.Count > 0)
                {
                    string[] ids = deleteInvent.Select(p => p.ID).ToArray();
                    deleteInvents = await _materialInventoryServices.DeleteByIds(ids);
                }

                // deleteInvent.Add(models);

                #region 调用SAP接口(调用失败直接跳过)


                string sapNo = string.Empty;

                if (sapTransfer.SendList.Count > 0 && addTransferSap.Count > 0)
                {
                    //这里筛选对应的转移历史记录

                    List<ReturnStockTran> sapStockTranList = _requestInventoryViewServices.Transfer(sapTransfer);    //转移接口（用于退货用，转移用）
                    if (sapStockTranList == null)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败");
                    }
                    if (sapStockTranList.Count == 0)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    else if (sapStockTranList[0].Sucess == "NOK")
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP接口调用失败:" + sapStockTranList[0].Msg);
                    }

                    if (string.IsNullOrEmpty(sapStockTranList[0].MCodeNameplateNo))
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("SAP返回数据为空");
                    }
                    //默认认为这里的数据是一样条数
                    for (int i = 0; i < sapStockTranList.Count; i++)
                    {    //获取对应的物料凭证编码插入数据到新增记录里
                        string sapNos = sapStockTranList[i].MCodeNameplateNo;
                        sapNo = sapStockTranList[i].MCodeNameplateNo;
                        if (sapNo != string.Empty)
                        {
                            continue;
                        }
                    }
                    for (int i = 0; i < addTransferSap.Count; i++)
                    {
                        addTransferSap[i].SapPrintno = sapNo;
                        addTransfer.Add(addTransferSap[i]);
                    }
                }

                #endregion



                #region 这里执行更新明细表操作

                List<PPM.Model.Models.VerifiyDetailEntity> dList = new List<PPM.Model.Models.VerifiyDetailEntity>();

                for (int x = 0; x < vList.Count; x++)
                {
                    PPM.Model.Models.VerifiyDetailEntity model = vList[x];
                    model.Modify(vList[x].ID, _uIser.Name);
                    model.Inventtype = "已退仓";
                    model.Sapno = sapNo;
                    dList.Add(model);
                }

                #endregion

                bool tranHis = true;
                if (addTransfer.Count > 0)
                {
                    tranHis = await _materialTransferServices.Add(addTransfer) > 0;
                }

                //更新明细
                bool dResult = true;
                if (dList.Count > 0)
                {
                    dResult = await _dalVerifiyDetailEntity.Update(dList);
                }

                //这里进行库存转移调用接口操作
                if (!tranHis || !deleteInvents || !dResult)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("退库失败:转移历史状态：" + tranHis + "删除状态：" + deleteInvents);
                }

                _unitOfWork.CommitTran();
                if (sapNo != string.Empty)
                {
                    string msgs = sapNo + ";" + sapLocation + ";" + positionName + ";" + tranModel.Remark + ";";
                    return Success(msgs, "退库成功");
                }

                return Success("", "退库成功");
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return Failed("退库失败" + ex);
            }
        }

        /// <summary>
        /// 清空容器
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ClearContainer([FromBody] ClearContainerStatusModel model)
        {
            var result = new MessageModel<string>();
            string userID = _uIser.Name.ToString();
            string id = model.ContainerID;
            if (string.IsNullOrEmpty(id))
            {
                return Failed("请确认是否存在器具id");
            }

            var modelView = await _containerServices.QueryById(id);
            if (modelView == null)
            {
                return Failed("请确认是否存在器具id");
            }
            //这里需要清空库存表的容器ID
            //获取视图
            // MContainerViewEntity modelView = await _mContainerViewServices.QueryById(id);

            //获取当前容器下所有的库存信息（库存基础表）
            List<MaterialInventoryEntity> inventEntity = await _materialInventoryServices.FindList(p => p.ContainerId == id);
            List<MaterialInventoryEntity> upInventList = new List<MaterialInventoryEntity>();
            try
            {
                #region 判断当前批次和子批次数据
                //查询批次状态是否为锁定

                //查询子批次状态是否为锁定
                string[] ids = inventEntity.GroupBy(P => P.ID).Select(P => P.Key).ToArray();
                string msg = await _inventorylistingViewServices.GetStateByInventID(ids);

                if (!string.IsNullOrEmpty(msg))
                {
                    return Failed(msg);
                }

                #endregion

                #region 这里需要写入容器历史表

                ContainerHistoryEntity modelList = new ContainerHistoryEntity();
                decimal inventQty = 0;

                for (int i = 0; i < inventEntity.Count; i++)
                {
                    MaterialInventoryEntity invent = new MaterialInventoryEntity();

                    invent = inventEntity[i];
                    inventQty += Math.Round(Convert.ToDecimal(inventEntity[i].Quantity), 3);// inventEntity[i].Quantity;

                    //清空容器
                    invent.ContainerId = "";
                    //更新数据
                    invent.Modify(invent.ID, userID);
                    //插入需要更新的数据
                    upInventList.Add(invent);
                }
                #region MyRegion

                //获取容器状态
                string conStatus = string.Empty;
                DicContainerstateViewEntity dicModel = await _mContainerViewServices.GetConStatusByItemvalue(modelView.Status);
                if (dicModel != null)
                {
                    conStatus = dicModel.Itemname;
                }

                #endregion


                #region 写入容器记录表



                //写入容器记录表(Add)
                ContainerHistoryEntity containerHistory = new ContainerHistoryEntity();
                containerHistory.Create(userID);
                //   hisModel
                containerHistory.ContainerId = id;
                containerHistory.ContainerCode = modelView.Name;
                containerHistory.State = modelView.Status;
                containerHistory.Type = "Clean";
                containerHistory.EquipmentId = modelView.EquipmentId;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                containerHistory.Comment = string.Format(@"New Status: empty, Previous Status: {0}", conStatus);
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                //containerHistory.Quantity = inventQty.ToString();
                //containerHistory.QuantityUomId = modelView.QUnit;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                //hisModel1.ExpirationDate = expirationDate;

                //  listTranHis.Add(hisModel1);


                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userID);
                //trans.OldStorageLocation = modelView.LocationF;
                //trans.NewStorageLocation = name;
                //trans.OldLotId = inventoryModel.LotId;
                //trans.NewLotId = inventoryModel.LotId;
                //trans.OldSublotId = inventoryModel.SubLotId;
                //trans.NewSublotId = inventoryModel.SubLotId;
                //trans.OldExpirationDate = inventoryModel.ExpirationDate;
                //trans.NewExpirationDate = inventoryModel.ExpirationDate;
                //trans.Quantity = Convert.ToInt32(inventQty);
                //trans.QuantityUomId = modelView.QUnit;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "转移";
                //trans.NewEquipmentRequirementId = modelView.id;
                //trans.OldEquipmentRequirementId = model.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = modelView.EquipmentId;
                trans.NewEquipmentId = model.EquipmentId;
                trans.OldContainerId = id;
                trans.NewContainerId = id;
                trans.Status = "empty";
                //status
                //trans.OldMaterialId = inventoryModel.MaterialId;
                //trans.NewMaterialId = inventoryModel.MaterialId;
                //trans.OldLotExternalStatus = inventoryModel.StatusF;
                //trans.OldSublotExternalStatus = inventoryModel.StatusS;
                //trans.NewLotExternalStatus = inventoryModel.StatusF;
                //trans.NewSublotExternalStatus = inventoryModel.StatusS;

                trans.PhysicalQuantity = modelView.MaxWeight == null ? "0" : modelView.MaxWeight.Value.ToString(); //物理数量
                trans.TareQuantity = modelView.TareWeight == null ? 0 : modelView.TareWeight.Value;  //皮数量

                _unitOfWork.BeginTran();

                bool tranHis = await _materialTransferServices.Add(trans) > 0;

                #endregion

                #endregion




                modelView.Modify(modelView.ID, userID);
                //这里是改变选中位置(绑定数据)
                modelView.EquipmentId = model.EquipmentId;
                modelView.Status = "2";
                bool resulCon = await _containerServices.Update(modelView);
                bool resultInvent = false;
                if (upInventList != null && upInventList.Count > 0)
                {
                    resultInvent = await _materialInventoryServices.Update(upInventList);
                }
                else
                {
                    resultInvent = true;
                }

                List<ContainerHistoryEntity> conList = new List<ContainerHistoryEntity>();
                conList.Add(containerHistory);
                bool resultHis = await _containerHistoryServices.Add(conList) > 0;

                if (!resulCon || !resultInvent || !resultHis)
                {
                    _unitOfWork.RollbackTran();
                    return Failed("清空容器失败");
                }
                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "清空容器成功";
                return result;
                //return Success("清空容器成功");
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return Failed("清空容器失败");
            }
        }

        /// <summary>
        /// 修改容器状态（容器必须是清空状态，库存表没有绑定任何数据）
        /// </summary>
        /// <param name="changeModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeContainerState([FromBody] ChangeContainerStatusModel changeModel)
        // string id,  string stateid,  string comment)
        {
            string userID = _uIser.Name.ToString();
            //器具状态
            string id = changeModel.id;
            //修改状态id
            string stateid = changeModel.stateid;
            //备注
            string comment = changeModel.comment;
            var data = new MessageModel<string>();

            //查询实体(构造要更新的实体)
            if (id == string.Empty || id == null)
            {
                return Failed("请确认是否存在器具id");
            }
            if (stateid == string.Empty || stateid == null)
            {
                return Failed("请确认是否选择容器状态");
            }

            bool result = true;

            _unitOfWork.BeginTran();
            //获取id,并更新
            var model = await _containerServices.QueryById(id);

            if (model == null)
            {
                _unitOfWork.RollbackTran();
                return Failed("当前容器不存在");
            }
            if (model.Status != "2")
            {
                _unitOfWork.RollbackTran();
                return Failed("非清空状态容器,不能进行清空");
            }
            //获取当前容器下所有的库存信息
            List<MaterialInventoryEntity> inventEntity = await _materialInventoryServices.FindList(p => p.ContainerId == id);
            if (inventEntity != null && inventEntity.Count > 0)
            {
                _unitOfWork.RollbackTran();
                return Failed("该容器正在使用中，请先清空库存在进行容器状态变更");
            }

            //获取视图
            MContainerViewEntity modelView = await _mContainerViewServices.QueryById(id);

            //获取库存信息
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.ContainerId == id)
             .ToExpression();
            var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);
            // MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

            //查询之前的位置
            //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == id).ToExpression();
            //var containerModel = await _containerServices.FindEntity(whereLocation);

            #region 插入历史表

            //写入容器记录表(Add)
            ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
            hisModel.Create(userID);

            hisModel.ContainerId = id;
            //hisModel.State = modelView.ContainerState;
            hisModel.Type = "Change Status";
            hisModel.EquipmentId = model.EquipmentId;
            if (inventoryModel != null)
            {
                hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId; hisModel.QuantityUomId = inventoryModel.QuantityUomId;
            }

            hisModel.State = model.Status;
            hisModel.Comment = model.Comment;
            //hisModel.ProductOrderId 工单ID
            //hisModel.BatchId 工单批次ID
            //hisModel.MaterialId = inventoryModel.MaterialId;
            //hisModel.SublotId = modelView.SubLotId;
            hisModel.Quantity = modelView.Quantity.ToString();

            //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
            //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
            //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
            // hisModel.Status 批次执行状态
            //hisModel.ContainerCode= containerModel. 容器编号
            //hisModel.MaterialProducedActualId 物料产出记录ID
            //hisModel.MaterialConsumedActualId 物料消耗记录ID
            hisModel.LotId = modelView.LotId;
            //hisModel.ExpirationDate = inventoryModel.ExpirationDate;

            bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

            #endregion

            //修改容器状态
            model.Status = stateid;
            model.Comment = comment;
            model.Modify(model.ID, userID);
            bool update = await _containerServices.Update(model);

            if (!update || !hisUp)
            {
                _unitOfWork.RollbackTran();
                result = false;
                return Failed("修改状态失败");
            }
            _unitOfWork.CommitTran();


            if (result)
            {
                return Success("", "修改状态成功");
            }
            else
            {
                _unitOfWork.RollbackTran();
                return Failed("修改状态失败");
            }
        }

        /// <summary>
        /// 修改容器状态（容器必须是清空状态，库存表没有绑定任何数据）
        /// </summary>
        /// <param name="changeModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeContainerState2([FromBody] ChangeContainerStatusModel changeModel)
        // string id,  string stateid,  string comment)
        {
            string userID = _uIser.Name.ToString();
            //器具状态
            string id = changeModel.id;
            //修改状态id
            string stateid = changeModel.stateid;
            //备注
            string comment = changeModel.comment;
            var data = new MessageModel<string>();

            //查询实体(构造要更新的实体)
            if (id == string.Empty || id == null)
            {
                return Failed("请确认是否存在器具id");
            }
            if (stateid == string.Empty || stateid == null)
            {
                return Failed("请确认是否选择容器状态");
            }

            bool result = true;

            _unitOfWork.BeginTran();
            //获取id,并更新
            var model = await _containerServices.QueryById(id);

            if (model == null)
            {
                _unitOfWork.RollbackTran();
                return Failed("当前容器不存在");
            }
            //if (model.Status != "2")
            //{
            //    return Failed("非清空状态容器,不能进行清空");
            //}
            //获取当前容器下所有的库存信息
            List<MaterialInventoryEntity> inventEntity = await _materialInventoryServices.FindList(p => p.ContainerId == id);
            if (inventEntity != null && inventEntity.Count > 0)
            {
                _unitOfWork.RollbackTran();
                return Failed("该容器正在使用中，请先清空库存在进行容器状态变更");
            }

            //获取视图
            MContainerViewEntity modelView = await _mContainerViewServices.QueryById(id);

            //获取库存信息
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.ContainerId == id)
             .ToExpression();
            var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);
            // MaterialInventoryEntity models = _materialInventoryServices.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();

            //查询之前的位置
            //var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == id).ToExpression();
            //var containerModel = await _containerServices.FindEntity(whereLocation);

            #region 插入历史表

            //写入容器记录表(Add)
            ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
            hisModel.Create(userID);

            hisModel.ContainerId = id;
            //hisModel.State = modelView.ContainerState;
            hisModel.Type = "Change Status";
            hisModel.EquipmentId = model.EquipmentId;
            if (inventoryModel != null)
            {
                hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId; hisModel.QuantityUomId = inventoryModel.QuantityUomId;
            }

            hisModel.State = model.Status;
            hisModel.Comment = model.Comment;
            //hisModel.ProductOrderId 工单ID
            //hisModel.BatchId 工单批次ID
            //hisModel.MaterialId = inventoryModel.MaterialId;
            //hisModel.SublotId = modelView.SubLotId;
            hisModel.Quantity = modelView.Quantity.ToString();

            //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
            //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
            //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
            // hisModel.Status 批次执行状态
            //hisModel.ContainerCode= containerModel. 容器编号
            //hisModel.MaterialProducedActualId 物料产出记录ID
            //hisModel.MaterialConsumedActualId 物料消耗记录ID
            hisModel.LotId = modelView.LotId;
            //hisModel.ExpirationDate = inventoryModel.ExpirationDate;

            bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

            #endregion

            //修改容器状态
            model.Status = stateid;
            model.Comment = comment;
            model.Modify(model.ID, userID);
            bool update = await _containerServices.Update(model);

            if (!update || !hisUp)
            {
                _unitOfWork.RollbackTran();
                result = false;
                return Failed("修改状态失败");
            }
            _unitOfWork.CommitTran();


            if (result)
            {
                return Success("", "修改状态成功");
            }
            else
            {
                return Failed("修改状态失败");
            }
        }

        /// <summary>
        /// 容器视图查询(这里需要传送ContainerID字段来定位当前库存)
        /// </summary>
        /// <param name="reqModel">容器视图实体</param>      
        /// <returns></returns>

        [HttpPost]
        public async Task<MessageModel<PageModel<InventorylistingViewEntity>>> GetInventoryListByContainerID([FromBody] InventorylistingViewRequestModel reqModel)
        {
            var data = await _inventorylistingViewServices.GetPageListByContainerID(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 添加物料到容器内部(add按钮)容器物料添加
        /// </summary>
        /// <param name="ids">ids集合(物料ID)</param>
        /// <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
        /// <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddContainerByIds(string[] ids, string containerId, string lotId)
        {
            //批量更新更新

            string id = string.Empty;
            //获取容器信息（这里包含容器的状态）
            //var model = await _inventorylistingViewServices.QueryByContairID(containerId);
            ////这里执行更新操作

            //if (model == null) 
            //{
            //    return Failed("添加物料失败,不存在物料记录");
            //}    

            _unitOfWork.BeginTran();

            bool result = false;


            for (int i = 0; i < ids.Length; i++)
            {
                id = ids[i].ToString();
                if (id != string.Empty)
                {
                    var inventModel = await _inventorylistingViewServices.QueryByMatarialID(id);

                    #region 获取状态（这里要考虑容器状态）

                    //物料状态
                    string container_Class = inventModel.ContainerClass;
                    //根据容器类型判断是否为BatchPallet 是的话转移类型就是5 add_to_batch_pallet，
                    //容器变更历史是
                    string tranerType = "Container Add";//3
                    if (container_Class == "BatchPallet")
                    {
                        tranerType = "Batch Pallet Add";//5
                    }
                    string containerHisType = "Container Inventory Add";

                    #endregion


                    //这里查询库存对应的物料的信息，执行更新容器操作
                    inventModel.ContainerId = containerId;
                    result = await _materialInventoryServices.Update(inventModel);
                    if (!result)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("添加物料失败");
                    }
                    //写入转移记录
                    #region 写入转移记录

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventModel.LocationF;
                    trans.NewStorageLocation = inventModel.LocationF;
                    trans.OldLotId = inventModel.LotId;
                    trans.NewLotId = inventModel.LotId;
                    trans.OldSublotId = inventModel.SlotId;
                    trans.NewSublotId = inventModel.SlotId;
                    trans.OldExpirationDate = inventModel.ExpirationDate;
                    trans.NewExpirationDate = inventModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventModel.Quantity), 3);
                    trans.QuantityUomId = inventModel.QuantityUomId;
                    trans.ProductionExecutionId = inventModel.ProductionRequestId;
                    //trans.Type   =  tranerType           
                    trans.Comment = tranerType;
                    trans.NewEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventModel.EquipmentId;
                    trans.NewEquipmentId = inventModel.EquipmentId;
                    trans.OldContainerId = inventModel.ContainerId;
                    trans.NewContainerId = containerId;
                    //status
                    trans.OldMaterialId = inventModel.MaterialId;
                    trans.NewMaterialId = id;
                    trans.OldLotExternalStatus = inventModel.StatusF;
                    trans.OldSublotExternalStatus = inventModel.StatusS;
                    trans.NewLotExternalStatus = inventModel.StatusF;
                    trans.NewSublotExternalStatus = inventModel.StatusS;

                    trans.PhysicalQuantity = inventModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventModel.TareWeight == null ? 0 : inventModel.TareWeight.Value;  //皮数量

                    bool tranHis = await _materialTransferServices.Add(trans) > 0;
                    if (!tranHis)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("添加物料失败");
                    }

                    #endregion
                    //写入容器历史
                    #region 写入转移记录

                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    hisModel.ID = Guid.NewGuid().ToString();
                    //   hisModel
                    hisModel.ContainerId = containerId;
                    hisModel.Type = containerHisType;
                    hisModel.EquipmentId = inventModel.EquipmentId;
                    hisModel.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    hisModel.Comment = containerHisType;
                    //hisModel.ProductOrderId 工单ID
                    //hisModel.BatchId 工单批次ID
                    hisModel.MaterialId = inventModel.MaterialId;
                    hisModel.SublotId = inventModel.SubLotId;
                    hisModel.Quantity = inventModel.Quantity.ToString();
                    hisModel.QuantityUomId = inventModel.QuantityUomId;
                    //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    // hisModel.Status 批次执行状态
                    //hisModel.ContainerCode= containerModel. 容器编号
                    //hisModel.MaterialProducedActualId 物料产出记录ID
                    //hisModel.MaterialConsumedActualId 物料消耗记录ID
                    hisModel.LotId = inventModel.LotId;
                    hisModel.ExpirationDate = inventModel.ExpirationDate;

                    bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

                    #endregion
                    if (!hisUp)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("添加物料失败");
                    }

                    if (result == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("添加物料失败");
                    }
                }
            }
            //if (failCount++ > 0)
            //{
            //    _unitOfWork.RollbackTran();
            //    return Failed("添加物料失败");
            //}


            _unitOfWork.CommitTran();

            return Success("", "添加物料成功");
        }

        /// <summary>
        /// 从容器内部移除物料
        /// </summary>
        /// <param name="ids">ids集合(物料ID)</param>
        /// <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
        /// <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> RemoveContainerByIds(string[] ids, string containerId, string lotId)
        {
            //批量更新更新

            string id = string.Empty;

            _unitOfWork.BeginTran();

            bool result = false;
            for (int i = 0; i < ids.Length; i++)
            {
                id = ids[i].ToString();
                if (id != string.Empty)
                {
                    var inventModel = await _inventorylistingViewServices.QueryByMatarialID(id);

                    #region 获取状态（这里要考虑容器状态）

                    //物料状态
                    string container_Class = inventModel.ContainerClass;
                    //根据容器类型判断是否为BatchPallet 是的话转移类型就是5 add_to_batch_pallet，
                    //容器变更历史是
                    string tranerType = "Container Add";//3
                    if (container_Class == "BatchPallet")
                    {
                        tranerType = "Batch Pallet Remove";//5
                    }
                    string containerHisType = "Container Inventory Remove";

                    #endregion


                    //这里查询库存对应的物料的信息，执行更新容器操作
                    inventModel.ContainerId = "";
                    result = await _materialInventoryServices.Update(inventModel);
                    if (!result)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }
                    //写入转移记录
                    #region 写入转移记录

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventModel.LocationF;
                    trans.NewStorageLocation = inventModel.LocationF;
                    trans.OldLotId = inventModel.LotId;
                    trans.NewLotId = inventModel.LotId;
                    trans.OldSublotId = inventModel.SlotId;
                    trans.NewSublotId = inventModel.SlotId;
                    trans.OldExpirationDate = inventModel.ExpirationDate;
                    trans.NewExpirationDate = inventModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventModel.Quantity), 3);
                    trans.QuantityUomId = inventModel.QuantityUomId;
                    trans.ProductionExecutionId = inventModel.ProductionRequestId;
                    //trans.Type   =  tranerType           
                    trans.Comment = tranerType;
                    trans.NewEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventModel.EquipmentId;
                    trans.NewEquipmentId = inventModel.EquipmentId;
                    trans.OldContainerId = inventModel.ContainerId;
                    trans.NewContainerId = "";
                    //status
                    trans.OldMaterialId = inventModel.MaterialId;
                    trans.NewMaterialId = id;
                    trans.OldLotExternalStatus = inventModel.StatusF;
                    trans.OldSublotExternalStatus = inventModel.StatusS;
                    trans.NewLotExternalStatus = inventModel.StatusF;
                    trans.NewSublotExternalStatus = inventModel.StatusS;

                    trans.PhysicalQuantity = inventModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventModel.TareWeight == null ? 0 : inventModel.TareWeight.Value;  //皮数量

                    bool tranHis = await _materialTransferServices.Add(trans) > 0;
                    if (!tranHis)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }

                    #endregion
                    //写入容器历史
                    #region 写入转移记录

                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    hisModel.ID = Guid.NewGuid().ToString();
                    //   hisModel
                    hisModel.ContainerId = "";
                    hisModel.Type = containerHisType;
                    hisModel.EquipmentId = inventModel.EquipmentId;
                    hisModel.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    hisModel.Comment = containerHisType;
                    //hisModel.ProductOrderId 工单ID
                    //hisModel.BatchId 工单批次ID
                    hisModel.MaterialId = inventModel.MaterialId;
                    hisModel.SublotId = inventModel.SubLotId;
                    hisModel.Quantity = inventModel.Quantity.ToString();
                    hisModel.QuantityUomId = inventModel.QuantityUomId;
                    //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    // hisModel.Status 批次执行状态
                    //hisModel.ContainerCode= containerModel. 容器编号
                    //hisModel.MaterialProducedActualId 物料产出记录ID
                    //hisModel.MaterialConsumedActualId 物料消耗记录ID
                    hisModel.LotId = inventModel.LotId;
                    hisModel.ExpirationDate = inventModel.ExpirationDate;

                    bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

                    #endregion
                    if (!hisUp)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }

                    if (result == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }
                }
            }
            //if (failCount++ > 0)
            //{
            //    _unitOfWork.RollbackTran();
            //    return Failed("添加物料失败");
            //}


            _unitOfWork.CommitTran();
            return Success("", "移除物料成功");
        }

        /// <summary>
        /// 删除容器中的物料信息
        /// </summary>
        /// <param name="ids">ids集合(物料ID)</param>
        /// <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
        /// <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteContainerByIds(string[] ids, string containerId, string lotId)
        {
            //批量更新更新

            string id = string.Empty;

            _unitOfWork.BeginTran();

            bool result = false;
            for (int i = 0; i < ids.Length; i++)
            {
                id = ids[i].ToString();
                if (id != string.Empty)
                {
                    var inventModel = await _inventorylistingViewServices.QueryByMatarialID(id);

                    #region 获取状态（这里要考虑容器状态）

                    //物料状态
                    string container_Class = inventModel.ContainerClass;
                    //根据容器类型判断是否为BatchPallet 是的话转移类型就是5 add_to_batch_pallet，
                    //容器变更历史是
                    string tranerType = "Container Add";//3
                    if (container_Class == "BatchPallet")
                    {
                        tranerType = "Batch Pallet Remove";//5
                    }
                    string containerHisType = "Container Inventory Remove";

                    #endregion


                    //这里查询库存对应的物料的信息，执行更新容器操作
                    inventModel.ContainerId = "";
                    result = await _materialInventoryServices.DeleteById(inventModel.ID);
                    if (!result)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }
                    //写入转移记录
                    #region 写入转移记录

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventModel.LocationF;
                    trans.NewStorageLocation = inventModel.LocationF;
                    trans.OldLotId = inventModel.LotId;
                    trans.NewLotId = inventModel.LotId;
                    trans.OldSublotId = inventModel.SlotId;
                    trans.NewSublotId = inventModel.SlotId;
                    trans.OldExpirationDate = inventModel.ExpirationDate;
                    trans.NewExpirationDate = inventModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventModel.Quantity), 3);
                    trans.QuantityUomId = inventModel.QuantityUomId;
                    trans.ProductionExecutionId = inventModel.ProductionRequestId;
                    //trans.Type   =  tranerType           
                    trans.Comment = tranerType;
                    trans.NewEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventModel.EquipmentId;
                    trans.NewEquipmentId = inventModel.EquipmentId;
                    trans.OldContainerId = inventModel.ContainerId;
                    trans.NewContainerId = "";
                    //status
                    trans.OldMaterialId = inventModel.MaterialId;
                    trans.NewMaterialId = id;
                    trans.OldLotExternalStatus = inventModel.StatusF;
                    trans.OldSublotExternalStatus = inventModel.StatusS;
                    trans.NewLotExternalStatus = inventModel.StatusF;
                    trans.NewSublotExternalStatus = inventModel.StatusS;

                    trans.PhysicalQuantity = inventModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventModel.TareWeight == null ? 0 : inventModel.TareWeight.Value;  //皮数量

                    bool tranHis = await _materialTransferServices.Add(trans) > 0;
                    if (!tranHis)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }

                    #endregion
                    //写入容器历史
                    #region 写入转移记录

                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();

                    hisModel.ID = Guid.NewGuid().ToString();
                    //   hisModel
                    hisModel.ContainerId = "";
                    hisModel.Type = containerHisType;
                    hisModel.EquipmentId = inventModel.EquipmentId;
                    hisModel.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    hisModel.Comment = containerHisType;
                    //hisModel.ProductOrderId 工单ID
                    //hisModel.BatchId 工单批次ID
                    hisModel.MaterialId = inventModel.MaterialId;
                    hisModel.SublotId = inventModel.SubLotId;
                    hisModel.Quantity = inventModel.Quantity.ToString();
                    hisModel.QuantityUomId = inventModel.QuantityUomId;
                    //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    // hisModel.Status 批次执行状态
                    //hisModel.ContainerCode= containerModel. 容器编号
                    //hisModel.MaterialProducedActualId 物料产出记录ID
                    //hisModel.MaterialConsumedActualId 物料消耗记录ID
                    hisModel.LotId = inventModel.LotId;
                    hisModel.ExpirationDate = inventModel.ExpirationDate;

                    bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

                    #endregion
                    if (!hisUp)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }

                    if (result == false)
                    {
                        _unitOfWork.RollbackTran();
                        return Failed("移除物料失败");
                    }
                }
            }
            //if (failCount++ > 0)
            //{
            //    _unitOfWork.RollbackTran();
            //    return Failed("添加物料失败");
            //}


            _unitOfWork.CommitTran();
            return Success("", "移除物料成功");
        }

        /// <summary>
        /// 查询历史数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ContainerHistoryViewEntity>>> GetPageHistoryViewList([FromBody] ContainerHistoryViewRequestModel reqModel)
        {
            var data = await _containerHistoryViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }


        #region 拆分

        /// <summary>
        /// 根据id获取其属性，SPEC （这里可能需要拆分字段）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialEntity>> GetMaterial(string id)
        {
            var data = await _materialServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 拆分数据，前端完成按包数和数量的方式进行显示，传入后端需要用实际数量
        /// </summary>
        /// <param name="splitID">拆分ID</param>
        /// <param name="equipmentId">Destination</param>
        /// <param name="quantity">拆分数量</param>
        /// <param name="isPrint">是否打印</param>
        /// <param name="selectPrint">打印地址</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveSplit([FromBody] string splitID, string equipmentId, string quantity, string isPrint, string selectPrint)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(splitID))
            {
                return Failed("请确认合并信息是否存在");
            }
            var ssccString = "123";

            #region 构造实体

            SSCCModel model = new SSCCModel();
            model.Type = "";
            model.NextCode = "";
            model.MaxCode = "";
            model.MinCode = "";
            model.Prefix = "";
            model.TableName = "";
            model.TableId = "";
            model.SequenceType = "";
            model.ResetType = "";
            model.FeatureId = "";
            model.pageIndex = 1;
            model.pageSize = 10;
            model.orderByFileds = "";
            string token = _uIser.GetToken();

            #endregion

            var ssccString1 = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
            if (ssccString1.success == true)
            {
                ssccString = ssccString1.response;
            }

            if (!string.IsNullOrEmpty(isPrint) && isPrint == "是")
            {
                //执行打印
                //selectPrint 打印机
            }

            //查询更新之前的数据
            var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == splitID)
                 .ToExpression();
            var inventoryModel = await _inventorylistingViewServices.FindEntity(whereExpressionInventory);
            if (inventoryModel == null)
            {
                return Failed("合并成功失败，未能找到合并数据");
            }

            #region 先构造好写入日志实体

            //写入历史记录
            MaterialTransferEntity trans = new MaterialTransferEntity();
            trans.ID = Guid.NewGuid().ToString();
            trans.OldStorageLocation = inventoryModel.LocationF;
            trans.NewStorageLocation = inventoryModel.LocationF;
            trans.OldLotId = inventoryModel.LotId;
            trans.NewLotId = inventoryModel.LotId;
            trans.OldSublotId = inventoryModel.SlotId;
            trans.NewSublotId = ssccString;
            trans.OldExpirationDate = inventoryModel.ExpirationDate;
            trans.NewExpirationDate = inventoryModel.ExpirationDate;
            trans.Quantity = Math.Round(Convert.ToDecimal(quantity), 3);
            trans.QuantityUomId = inventoryModel.QuantityUomId;
            trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
            //trans.Type  ="Split"             
            trans.Comment = "拆分";
            trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
            trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
            //trans.TransferGroupId
            trans.OldEquipmentId = inventoryModel.EquipmentId;
            trans.NewEquipmentId = equipmentId;
            trans.OldContainerId = inventoryModel.ContainerId;
            trans.NewContainerId = inventoryModel.ContainerId;
            //status
            trans.OldMaterialId = inventoryModel.MaterialId;
            trans.NewMaterialId = inventoryModel.MaterialId;
            trans.OldLotExternalStatus = inventoryModel.StatusF;
            trans.OldSublotExternalStatus = inventoryModel.StatusS;
            trans.NewLotExternalStatus = inventoryModel.StatusF;
            trans.NewSublotExternalStatus = inventoryModel.StatusS;
            //trans.PhysicalQuantity 物理数量
            //trans.TareQuantity 皮数量
            trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
            trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量

            #endregion

            //新增库存实体
            var modelInven = await _materialInventoryServices.QueryById(splitID);
            int splitQuantity = Convert.ToInt32(inventoryModel.Quantity - Convert.ToDecimal(quantity));

            if (splitQuantity < 0)
            {
                return Failed("拆分失败，拆分数量过大");
            }
            //改变成新增实体(改变数量和位置)
            modelInven.ID = "";
            modelInven.SublotId = ssccString;
            modelInven.Quantity = Math.Round(Convert.ToDecimal(inventoryModel.Quantity.Value), 3) - Math.Round(Convert.ToDecimal(quantity), 3); //inventoryModel.Quantity.Value - Convert.ToDecimal(quantity);
            //执行拆分
            bool result = await SaveSplitServer(splitID, splitQuantity.ToString(), modelInven);
            if (data.success)
            {                //写入日志
                             //获取视图数据
                #region 写入日志

                data.success = await _materialTransferServices.Add(trans) > 0;

                #endregion
                if (data.success)
                {
                    return Success("", "拆分成功");
                }
                else
                {
                    return Failed("拆分失败，写入日志失败");
                }
            }
            else
            {
                return Failed("拆分失败");
            }
        }

        /// <summary>
        /// 进行数据拆分
        /// </summary>
        /// <param name="splitID"></param>
        /// <param name="oldTotalQuantity"></param>
        /// <param name="insertModel"></param>
        /// <returns></returns>
        private async Task<bool> SaveSplitServer(string splitID, string oldTotalQuantity, MaterialInventoryEntity insertModel)
        {
            bool result = true;
            //更新
            _unitOfWork.BeginTran();
            MaterialInventoryEntity upmodel = new MaterialInventoryEntity();
            upmodel.ID = splitID;
            upmodel.Quantity = Math.Round(Convert.ToDecimal(oldTotalQuantity), 3);//Convert.ToDecimal(oldTotalQuantity);
            bool updateResult = await _materialInventoryServices.Update(upmodel);

            //这里需要带上类型

            //新增
            bool addResult = await _materialInventoryServices.Add(insertModel) > 0;

            if (!updateResult || !addResult)
            {
                _unitOfWork.RollbackTran();
                return false;
            }
            _unitOfWork.CommitTran();
            return result;
        }


        #endregion

    }

    #endregion
}
public class ContainerRequestModel : RequestPageModelBase
{
    public string key { get; set; }
}
