using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TimeAnalysisViewController : BaseApiController
    {
        /// <summary>
        /// TimeAnalysisView
        /// </summary>
        private readonly ITimeAnalysisViewServices _timeAnalysisViewServices;

        public TimeAnalysisViewController(ITimeAnalysisViewServices TimeAnalysisViewServices)
        {
            _timeAnalysisViewServices = TimeAnalysisViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<TimeAnalysisViewEntity>>> GetList([FromBody] TimeAnalysisViewRequestModel reqModel)
        {
            var data = await _timeAnalysisViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 计划、非计划停机时间分析（停机）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<AnalyzeDowntimeModel>> AnalyzeDowntime(TimeAnalysisViewRequestModel reqModel)
        {
            var data = await _timeAnalysisViewServices.AnalyzeDowntime(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<TimeAnalysisViewEntity>>> GetPageList([FromBody] TimeAnalysisViewRequestModel reqModel)
        {
            var data = await _timeAnalysisViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<TimeAnalysisViewEntity>> GetEntity(string id)
        {
            var data = await _timeAnalysisViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] TimeAnalysisViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _timeAnalysisViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _timeAnalysisViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] TimeAnalysisViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _timeAnalysisViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _timeAnalysisViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class TimeAnalysisViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}