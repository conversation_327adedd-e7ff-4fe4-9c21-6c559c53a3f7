using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TransferHistoryViewController : BaseApiController
    {
        /// <summary>
        /// TransferHistoryView
        /// </summary>
        private readonly ITransferHistoryViewServices _transferHistoryViewServices;
        private readonly ITransferbinViewServices _transferbinViewServices;

        public TransferHistoryViewController(ITransferHistoryViewServices TransferHistoryViewServices, ITransferbinViewServices transferbinViewServices)
        {
            _transferHistoryViewServices = TransferHistoryViewServices;
            _transferbinViewServices = transferbinViewServices;
        }




        [HttpPost]
        public async Task<MessageModel<List<TransferbinViewEntity>>> GetBINListALL()
        {
            var data = await _transferbinViewServices.GetListALL();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询物料转移历史（查询条件下拉框来自于webAIP soure(DFM_M_EQUIPMENT LEVEL='ProductLine')   soure bin(DFM_M_EQUIPMENT_REQUIREMENT LEVEL='3')）
        /// 后面是查询条件
        ///  StartTime，EndTime，SourceMaterial，DestinationMaterial，OldBatch，NewBatch，OLD_SUB_LOT_ID，NEW_SUB_LOT_ID，
        ///  OldSoureID，OldSoureBin，NewDestinationID，NewDestinationBin    
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]

        public async Task<MessageModel<PageModel<TransferHistoryViewEntity>>> GetPageList([FromBody] TransferHistoryViewRequestModel reqModel)
        {
            var data = await _transferHistoryViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> GetSapData([FromBody] TransferHistoryViewRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = true;

            List<TransferHistoryViewEntity> data = await _transferHistoryViewServices.GetSapData(reqModel);
            if (data.Count > 0)
            {
                string json = JsonConvert.SerializeObject(data);
                return Success(json, "获取成功");
            }

            return Failed("不存在数据");

        }


        [HttpPost]

        public async Task<MessageModel<PageModel<TransferHistoryViewEntity>>> GetPageListByConID([FromBody] TransferHistoryViewRequestModel reqModel)
        {
            var data = await _transferHistoryViewServices.GetPageListByConID(reqModel);
            return Success(data, "获取成功");
        }

    }
    //public class TransferHistoryViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}