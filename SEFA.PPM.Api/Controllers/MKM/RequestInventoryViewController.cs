using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RequestInventoryViewController : BaseApiController
    {
        /// <summary>
        /// RequestInventoryView
        /// </summary>
        private readonly IRequestInventoryViewServices _requestInventoryViewServices;

        public RequestInventoryViewController(IRequestInventoryViewServices RequestInventoryViewServices)
        {
            _requestInventoryViewServices = RequestInventoryViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<RequestInventoryViewEntity>>> GetList([FromBody] RequestInventoryViewRequestModel reqModel)
        {
            var data = await _requestInventoryViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RequestInventoryViewEntity>>> GetPageList([FromBody] RequestInventoryViewRequestModel reqModel)
        {
            var data = await _requestInventoryViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RequestInventoryViewEntity>> GetEntity(string id)
        {
            var data = await _requestInventoryViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 请料，时间点开始和结束时间，手动还是自动
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> RequestMaterials(RequestMaterialsModel reqModel)
        {
            var data = await _requestInventoryViewServices.RequestMaterials(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// JIT请料，时间点开始和结束时间，手动还是自动
        /// </summary>
        /// <param name="reqModel">
        /// 开始请料时间
        ///  string planStarTime 
        ///未来X小时
        /// int ActureTime 
        ///手动 、自动
        ///string AutoType
        /// </param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> JITRequestMaterials(JITRequestModel reqModel)
        {
            var data = await _requestInventoryViewServices.JITRequestMaterials(reqModel);
            if (data.success)
            {
                return Success("JIT成功", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 白糖类请料，时间点开始和结束时间，手动还是自动
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SugarRequestMaterials(RequestMaterialsModel reqModel)
        {
            var data = await _requestInventoryViewServices.SugarRequestMaterials(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天0点调取更新全部未完成的状态为已完成
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> updateStste(RequestMaterialsModel reqModel)
        {
            var data = await _requestInventoryViewServices.updateStste(reqModel);
            if (data.success)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }



        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RequestInventoryViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _requestInventoryViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _requestInventoryViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] RequestInventoryViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _requestInventoryViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _requestInventoryViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }


        #region Wms接口调用测试

        /// <summary>
        /// 接口测试
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> InterFaceTest()
        {
            var data = await _requestInventoryViewServices.InterFaceTest();
            if (data.success)
            {
                return Success("", "新增成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 请料界面新增按钮
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MCodeReturnModel>>> SearchRequestInsertData(AddRequestInventory reqModel)
        {
            //AddRequestInventory reqModel =new AddRequestInventory();
            //reqModel.materialId=materialId;
            //reqModel.quantity = quantity;
            //reqModel.StarTime = StarTime;
            //reqModel.EndTime = EndTime;

            var data = await _requestInventoryViewServices.SearchRequestInsertData(reqModel);

            if (data != null && data.Count > 0)
            {
                return Success(data, "获取成功");
            }
            else
            {
                return Success(data, "获取失败，请确认工单是否存在指定需求");
            }

        }

        /// <summary>
        /// 拉界面新增按钮
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MCodeReturnModel>>> SearchPULLInsertData(AddRequestInventory reqModel)
        {
            //AddRequestInventory reqModel =new AddRequestInventory();
            //reqModel.materialId=materialId;
            //reqModel.quantity = quantity;
            //reqModel.StarTime = StarTime;
            //reqModel.EndTime = EndTime;

            var data = await _requestInventoryViewServices.SearchPULLInsertData(reqModel);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 新增请料记录
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddRequestInventory(List<MCodeReturnModel> reqModel)
        {
            var data = await _requestInventoryViewServices.AddRequestInven(reqModel);
            if (data.success)
            {
                return Success("", "新增成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        #endregion

        /// <summary>
        /// 新增请料记录
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddFULLInventory(List<MCodeReturnModel> reqModel)
        {
            var data = await _requestInventoryViewServices.AddPULLInven(reqModel);
            if (data.success)
            {
                return Success("", "新增成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        #region 自动请料


        /// <summary>
        /// 每天13点定时调用该服务（原料料拉）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeMPull_13()
        {
            var data = await _requestInventoryViewServices.FixedTimeMPull_13();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "拉料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天8点定时调用该服务（原料请料）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeMRequest_8()
        {
            var data = await _requestInventoryViewServices.FixedTimeMRequest_8();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "原料请料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天14点定时调用该服务（原料请料）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeMRequest_14()
        {
            var data = await _requestInventoryViewServices.FixedTimeMRequest_14();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "原料请料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天02点定时调用该服务（物料拉料）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeRawMPull_02()
        {
            var data = await _requestInventoryViewServices.FixedTimeRawMPull_02();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "物料拉料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天0点定时调用该服务（物料请料）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeMRequest_0()
        {
            var data = await _requestInventoryViewServices.FixedTimeMRequest_0();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "物料请料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 每天12点定时调用该服务（物料请料）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> FixedTimeMRequest_12()
        {
            var data = await _requestInventoryViewServices.FixedTimeMRequest_12();
            if (data.success)
            {
                string msg = data.msg;
                if (string.IsNullOrEmpty(msg))
                {
                    msg = "物料请料成功";
                }
                return Success("", msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        #endregion
    }
    //public class RequestInventoryViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}