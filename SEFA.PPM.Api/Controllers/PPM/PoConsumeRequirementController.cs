using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;
using SEFA.PPM.Model.ViewModels.PPM;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoConsumeRequirementController : BaseApiController
    {
        /// <summary>
        /// PoConsumeRequirement
        /// </summary>
        private readonly IPoConsumeRequirementServices _poConsumeRequirementServices;

        public PoConsumeRequirementController(IPoConsumeRequirementServices PoConsumeRequirementServices)
        {
            _poConsumeRequirementServices = PoConsumeRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoConsumeRequirementEntity>>> GetList([FromBody] PoConsumeRequirementRequestModel reqModel)
        {
            var data = await _poConsumeRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoConsumeRequirementEntity>>> GetPageList([FromBody] PoConsumeRequirementRequestModel reqModel)
        {
            Expression<Func<PoConsumeRequirementEntity, bool>> whereExpression = a => true;
            var data = await _poConsumeRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoConsumeRequirementEntity>> GetEntity(string id)
        {
            var data = await _poConsumeRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoConsumeRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poConsumeRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        
        /// <summary>
        /// 创建物料需求
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<BatchConsumeRequirementEntity>> GeneratePoConsumeRequirement([FromBody] PoConsumeRequirementViewModel request)
        {
            var resutl = await _poConsumeRequirementServices.GeneratePoConsumeRequirement(request);
            return resutl;
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poConsumeRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoConsumeRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}