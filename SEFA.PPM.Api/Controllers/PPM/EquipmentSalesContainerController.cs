using AutoMapper;
using Magicodes.ExporterAndImporter.Excel;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentSalesContainerController : BaseApiController
    {
        /// <summary>
        /// EquipmentSalesContainer
        /// </summary>
        private readonly IEquipmentSalesContainerServices _equipmentSalesContainerServices;
        private readonly IUser _user;
        private readonly IMapper _mapper;
        public EquipmentSalesContainerController(IEquipmentSalesContainerServices EquipmentSalesContainerServices,
                IUser user, IMapper mapper)
        {
            _equipmentSalesContainerServices = EquipmentSalesContainerServices;
            _user = user;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<MessageModel<List<EquipmentSalesContainerModel>>> GetList([FromBody] EquipmentSalesContainerRequestModel reqModel)
        {
            var data = await _equipmentSalesContainerServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentSalesContainerModel>>> GetPageList([FromBody] EquipmentSalesContainerRequestModel reqModel)
        {
            var data = await _equipmentSalesContainerServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentSalesContainerEntity>> GetEntity(string id)
        {
            var data = await _equipmentSalesContainerServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentSalesContainerEntity request)
        {
            var data = new MessageModel<string>();

            if (string.IsNullOrEmpty(request.EquipmentId))
            {
                return Failed("缺少产线信息");
            }

            if (string.IsNullOrEmpty(request.SalesContainerId))
            {
                return Failed("缺少SalesContainer信息");
            }

            var list = await _equipmentSalesContainerServices.FindList(a => a.EquipmentId == request.EquipmentId && a.SalesContainerId == request.SalesContainerId && a.ID != request.ID);
            if (list.Count > 0)
            {
                return Failed("产线已存在该数据");
            }

            request.Deleted = 0;
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentSalesContainerServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _equipmentSalesContainerServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] EquipmentSalesContainerEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentSalesContainerServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentSalesContainerServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        #region 数据导入、导出
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportData([FromForm] FileImportDto input)
        {
            var result = await _equipmentSalesContainerServices.ImportData(input);
            if (result.Succeed)
                return Success(result.Data, "导入成功");
            else
                return Failed("导入失败：" + result.Error.Text);
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData([FromBody] EquipmentSalesContainerRequestModel reqModel)
        {
            ExcelExporter exporter = new ExcelExporter();
            var query = await _equipmentSalesContainerServices.GetList(reqModel);

            var data = _mapper.Map<List<EquipmentSalesContainerExcelDto>>(query);

            var result = await exporter.ExportAsByteArray<EquipmentSalesContainerExcelDto>(data);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"产线SalesContainer映射-{DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")}");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<EquipmentSalesContainerExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "产线SalesContainer映射导入模板");
        }

        #endregion
    }

}