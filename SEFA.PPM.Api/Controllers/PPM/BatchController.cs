using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BatchController : BaseApiController
    {
        /// <summary>
        /// Batch
        /// </summary>
        private readonly IBatchServices _batchServices;

        public BatchController(IBatchServices BatchServices)
        {
            _batchServices = BatchServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchEntity>>> GetList([FromBody] BatchRequestModel reqModel)
        {
            var data = await _batchServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchEntity>>> GetPageList([FromBody] BatchRequestModel reqModel)
        {
            Expression<Func<BatchEntity, bool>> whereExpression = a => true;
            var data = await _batchServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchEntity>> GetEntity(string id)
        {
            var data = await _batchServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> CreateBatch([FromBody] BatchEntity request)
        {
            return await _batchServices.CreateBatch(request);
        }

    }
    //public class BatchRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}