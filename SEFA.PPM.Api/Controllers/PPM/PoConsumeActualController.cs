using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoConsumeActualController : BaseApiController
    {
        /// <summary>
        /// PoConsumeActual
        /// </summary>
        private readonly IPoConsumeActualServices _poConsumeActualServices;

        public PoConsumeActualController(IPoConsumeActualServices PoConsumeActualServices)
        {
            _poConsumeActualServices = PoConsumeActualServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoConsumeActualEntity>>> GetList([FromBody] PoConsumeActualRequestModel reqModel)
        {
            var data = await _poConsumeActualServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoConsumeActualEntity>>> GetPageList([FromBody] PoConsumeActualRequestModel reqModel)
        {
            Expression<Func<PoConsumeActualEntity, bool>> whereExpression = a => true;
            var data = await _poConsumeActualServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoConsumeActualEntity>> GetEntity(string id)
        {
            var data = await _poConsumeActualServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoConsumeActualEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poConsumeActualServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poConsumeActualServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoConsumeActualRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}