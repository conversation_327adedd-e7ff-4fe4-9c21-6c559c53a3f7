using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoProducedActualController : BaseApiController
    {
        /// <summary>
        /// PoProducedActual
        /// </summary>
        private readonly IPoProducedActualServices _poProducedActualServices;

        public PoProducedActualController(IPoProducedActualServices PoProducedActualServices)
        {
            _poProducedActualServices = PoProducedActualServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoProducedActualEntity>>> GetList([FromBody] PoProducedActualRequestModel reqModel)
        {
            var data = await _poProducedActualServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoProducedActualEntity>>> GetPageList([FromBody] PoProducedActualRequestModel reqModel)
        {
            Expression<Func<PoProducedActualEntity, bool>> whereExpression = a => true;
            var data = await _poProducedActualServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoProducedActualEntity>> GetEntity(string id)
        {
            var data = await _poProducedActualServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoProducedActualEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedActualServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedActualServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoProducedActualRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}