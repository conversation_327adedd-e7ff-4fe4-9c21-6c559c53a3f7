using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.MKM;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    //[Authorize(Permissions.Name)]
    public class StandardPeriodLotController : BaseApiController
    {
        /// <summary>
        /// StandardPeriodLot
        /// </summary>
        private readonly IStandardPeriodLotServices _standardPeriodLotServices;

        public StandardPeriodLotController(IStandardPeriodLotServices StandardPeriodLotServices)
        {
            _standardPeriodLotServices = StandardPeriodLotServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<StandardPeriodLotEntity>>> GetList([FromBody] StandardPeriodLotRequestModel reqModel)
        {
            var data = await _standardPeriodLotServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<StandardPeriodLotEntity>>> GetPageList([FromBody] StandardPeriodLotRequestModel reqModel)
        {
            var data = await _standardPeriodLotServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<StandardPeriodLotEntity>> GetEntity(string id)
        {
            var data = await _standardPeriodLotServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取产线清单
        /// </summary>
        /// <param name="areaCode">车间代码 Formulation -- 配置车间 ，Packing -- 包装车间</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentEntity>>> GetLineList(string areaCode)
        {
            var data = await _standardPeriodLotServices.GetLine(areaCode, "Line");
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] StandardPeriodLotEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _standardPeriodLotServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _standardPeriodLotServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] StandardPeriodLotEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _standardPeriodLotServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _standardPeriodLotServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class StandardPeriodLotRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}