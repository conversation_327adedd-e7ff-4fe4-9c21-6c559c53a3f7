using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.PTM.Model.Models;

namespace SEFA.PTMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]

    public class PoProducedExecutionController : BaseApiController
    {
        /// <summary>
        /// PoProducedExecution
        /// </summary>
        private readonly IPoProducedExecutionServices _poProducedExecutionServices;
        private readonly IInterfaceServices _interfaceServices;


        public PoProducedExecutionController(IPoProducedExecutionServices PoProducedExecutionServices, IInterfaceServices interfaceServices)
        {
            _poProducedExecutionServices = PoProducedExecutionServices;
            _interfaceServices = interfaceServices;
        }

        #region 输垛系统接口

        /// <summary>
        /// 工单需求信息同步
        /// </summary>
        /// <param name="productionOrderId"></param>
        /// <param name="actionType"></param>
        /// <param name="lotCode"></param>
        /// <param name="quantity"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SendOrderInfoToSS(string productionOrderId, int actionType, string lotCode = "", decimal quantity = 0)
        {
            return await _interfaceServices.SendOrderInfoToSS(productionOrderId, actionType, lotCode, quantity);
        }

        /// <summary>
        /// 输垛系统-上料扫码信息同步
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<SS_Response> SS_ScanInfo(SS_Scan_Info reqModel)
        {
            return await _interfaceServices.SS_ScanInfo(reqModel);
        }

        #endregion

        #region 防伪系统接口

        /// <summary>
        /// 防伪系统获取工单信息接口
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<FWS_WorkOrderInfo> GetWorkOrderInfo(FWS_WorkOrderInfo_Res reqModel)
        {
            return await _interfaceServices.GetWorkOrderInfo(reqModel);
        }

        #endregion

        #region COLOS系统接口

        /// <summary>
        /// 发送工单信息给COLOS接口
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpPost("{equipmentId}/{orderId}")]
        public async Task<MessageModel<string>> SendWorkOrderInfoToColos(string equipmentId, string orderId)
        {
            return await _interfaceServices.SendWorkOrderInfoToColos(equipmentId, orderId);
        }

        /// <summary>
        /// COLOS系统取得标签信息接口
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<COLOS_CreateLabel_Res> GetLabel(COLOS_CreateLabel reqModel)
        {
            return await _interfaceServices.GetLabel(reqModel);
        }

        /// <summary>
        /// COLOS系统更新标签状态接口
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<COLOS_LabelStatus_Res> UpdateLabelStatus(COLOS_LabelStatus reqModel)
        {
            return await _interfaceServices.UpdateLabelStatus(reqModel);
        }

        #endregion

        #region 看能系统接口

        /// <summary>
        /// 获取Token
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<string> GetTokenFromEMS()
        {
            return await _interfaceServices.GetTokenFromEMS();
        }

        /// <summary>
        /// 获取仪表清单
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ENERGY_InstrumentList>>> GetEnergyInstrumentList(ENERGY_InstrumentList_Req reqModel)
        {
            return await _interfaceServices.GetEnergyInstrumentList(reqModel);
        }

        /// <summary>
        /// 获取仪表清单
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ENERGY_InstrumentList>>> GetEnergyInstrumentPageList(ENERGY_InstrumentList_ReqPage reqModel)
        {
            var data = await _interfaceServices.GetEnergyInstrumentPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取能耗数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetEnergyDataByDay(string type)
        {
            return await _interfaceServices.GetEnergyDataByDay(type);
        }

        [HttpPost]
        public async Task<MessageModel<ENERGY_Data>> TestGetEnergyDataByDay(DateTime start, DateTime end)
        {
            return await _interfaceServices.TestGetEnergyDataByDay(start, end);
        }

        #endregion

        #region HRS考勤系统接口

        [HttpPost]
        public async Task<MessageModel<HRSResModel>> GetDataFromHRS(Paras reqModel)
        {
            return await _interfaceServices.GetDataFromHRS(reqModel);
        }

        #endregion

        #region MMI接口

        [HttpPost]
        [AllowAnonymous]
        public async Task<MessageModel<MMI_POStart_Res>> POStart(string execId, string batchId, string equipmentId, int status)
        {
            return await _interfaceServices.POStart(execId, batchId, equipmentId, status);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<MessageModel<MMI_Res>> FeedingCompleted(string execId, string equipmentId, int tippingStatus, string productionOrderId, string number)
        {
            return await _interfaceServices.FeedingCompleted(execId, equipmentId, tippingStatus, productionOrderId, number);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<MessageModel<MMI_Res>> SyncQAStatus(string execId, string equipmentId, int qAStatus, string productionOrderId, string number)
        {
            return await _interfaceServices.QAStatusSync(execId, equipmentId, qAStatus, productionOrderId, number);
        }

        /// <summary>
        /// 物料消耗反馈_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> ConsumeFeedback(List<MMI_ConsumeFeedback> reqModel)
        {
            return await _interfaceServices.ConsumeFeedback(reqModel);
        }

        /// <summary>
        /// 生产执行信息反馈_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> ProductionFeedback(List<MMI_ProductionFeedback> reqModel)
        {
            return await _interfaceServices.ProductionFeedback(reqModel);
        }

        /// <summary>
        /// 请求投料_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> RequestFeeding(List<MMI_RequestFeeding> reqModel)
        {
            return await _interfaceServices.RequestFeeding(reqModel);
        }

        /// <summary>
        /// 发起QA_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> InitiateQA(List<MMI_InitiateQA> reqModel)
        {
            return await _poProducedExecutionServices.InitiateQA(reqModel);
        }

        /// <summary>
        /// 煮料完成出料_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> CookingCompleted(List<MMI_CookingCompleted> reqModel)
        {
            return await _interfaceServices.CookingCompleted(reqModel);
        }

        /// <summary>
        /// 储存缸到灌装机出料_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> StorageToFilling(List<MMI_StorageToFilling> reqModel)
        {
            return await _interfaceServices.StorageToFilling(reqModel);
        }

        /// <summary>
        /// 接收CIP记录_Proleit
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<MMI_Res> ReceiveCIPRecords(List<MMI_CIP> reqModel)
        {
            return await _interfaceServices.ReceiveCIPRecords(reqModel);
        }

        #endregion

        [HttpPost]
        public async Task<MessageModel<List<PoProducedExecutionEntity>>> GetList([FromBody] PoProducedExecutionRequestModel reqModel)
        {
            var data = await _poProducedExecutionServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoProducedExecutionEntity>>> GetPageList([FromBody] PoProducedExecutionRequestModel reqModel)
        {
            var data = await _poProducedExecutionServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoProducedExecutionEntity>> GetEntity(string id)
        {
            var data = await _poProducedExecutionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PoProducedExecutionEntity>> GetOrder([FromBody] PoProducedExecutionRequestModel reqModel)
        {
            var data = await _poProducedExecutionServices.GetOrder(reqModel);
            return Success(data, "获取成功");
        }

        //[HttpPost]
        //      public async Task<MessageModel<string>> ConsolPo ([FromBody] ConsolPoRequestModel reqModel) {
        //          var data = new MessageModel<string>();
        //          data.success = await _poProducedExecutionServices.ConsolPo(reqModel);
        //          if (data.success)
        //          {
        //              return Success("", "操作成功");
        //          }
        //          else
        //          {
        //              return Failed("操作失败");
        //          }
        //      }

        [HttpPost]
        public async Task<MessageModel<string>> Start([FromBody] StartPoRequestModel reqModel)
        {
            return await _poProducedExecutionServices.Start(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Stop([FromBody] StopPoRequestModel reqModel)
        {
            return await _poProducedExecutionServices.Stop(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Hold([FromBody] StopPoRequestModel reqModel)
        {
            return await _poProducedExecutionServices.Hold(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Resume([FromBody] ResumePoRequestModel reqModel)
        {
            return await _poProducedExecutionServices.Resume(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdatePo([FromBody] ResumePoRequestModel reqModel)
        {
            return await _poProducedExecutionServices.UpdatePo(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdateOrderRemark([FromBody] ProductionOrderEntity reqModel)
        {
            return await _poProducedExecutionServices.UpdateOrderRemark(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> StartNextBatch([FromBody] ProcessOrderViewEntity reqModel)
        {
            return await _poProducedExecutionServices.StartNextBatch(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialProcessDataEntity>>> GetCookOrderLtexts([FromBody] UpdateQaStatusRequestModel reqModel)
        {
            return await _poProducedExecutionServices.GetCookOrderLtexts(reqModel);
        }

        /// <summary>
        /// 获取批次号
        /// </summary>
        /// <param name="equipmentCode">设备编码</param>
        /// <param name="productionDate">生产日期</param>
        /// <returns></returns>
        //[HttpGet("{equipmentCode}/{productionDate}")]
        //public async Task<MessageModel<string>> GetBatchCode(string equipmentCode, DateTime productionDate)
        //{
        //	return await _poProducedExecutionServices.GetBatchCode(equipmentCode, productionDate);
        //}

        /// <summary>
        /// 获取批次号
        /// </summary>
        /// <param name="equipmentCode">设备编码</param>
        /// <param name="productionDate">生产日期</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetBatchCode([FromBody] GetBatchCodeModel request)
        {
            return await _poProducedExecutionServices.GetBatchCode(request);
        }

        /// <summary>
        /// 获取批次号
        /// </summary>
        /// <param name="equipmentCode">设备编码</param>
        /// <param name="productionDate">生产日期</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetBatchCodeByProLine(GetBatchCodeModel reqModel)
        {
            return await _poProducedExecutionServices.GetBatchCodeByProLine(reqModel);
        }

        /// <summary>
        /// 获取当前设备正在Running的工单
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <returns></returns>
        [HttpGet("{equipmentId}")]
        public async Task<MessageModel<string>> GetRunOrder(string equipmentId)
        {
            return await _poProducedExecutionServices.GetRunOrder(equipmentId);
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoProducedExecutionEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedExecutionServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedExecutionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoProducedExecutionRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}