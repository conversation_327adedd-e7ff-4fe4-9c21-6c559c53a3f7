using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoProducedMaterialListViewController : BaseApiController
    {
        /// <summary>
        /// PoProducedMaterialListView
        /// </summary>
        private readonly IPoProducedMaterialListViewServices _poProducedMaterialListViewServices;

        public PoProducedMaterialListViewController(IPoProducedMaterialListViewServices PoProducedMaterialListViewServices)
        {
            _poProducedMaterialListViewServices = PoProducedMaterialListViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoProducedMaterialListViewEntity>>> GetList([FromBody] PoProducedMaterialListViewRequestModel reqModel)
        {
            var data = await _poProducedMaterialListViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoProducedMaterialListViewEntity>>> GetPageList([FromBody] PoProducedMaterialListViewRequestModel reqModel)
        {
            var data = await _poProducedMaterialListViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoProducedMaterialListViewEntity>> GetEntity(string id)
        {
            var data = await _poProducedMaterialListViewServices.QueryById(id);
            return Success(data, "获取成功");
        }
    }
    //public class PoProducedMaterialListViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}