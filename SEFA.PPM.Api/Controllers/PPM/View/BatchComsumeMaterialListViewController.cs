using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BatchComsumeMaterialListViewController : BaseApiController
    {
        /// <summary>
        /// BatchComsumeMaterialListView
        /// </summary>
        private readonly IBatchComsumeMaterialListViewServices _batchComsumeMaterialListViewServices;

        public BatchComsumeMaterialListViewController(IBatchComsumeMaterialListViewServices BatchComsumeMaterialListViewServices)
        {
            _batchComsumeMaterialListViewServices = BatchComsumeMaterialListViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchComsumeMaterialListViewEntity>>> GetList([FromBody] BatchComsumeMaterialListViewModel reqModel)
        {
            var data = await _batchComsumeMaterialListViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchComsumeMaterialListViewEntity>>> GetPageList([FromBody] BatchComsumeMaterialListViewModel reqModel)
        {
            var data = await _batchComsumeMaterialListViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchComsumeMaterialListViewEntity>> GetEntity(string id)
        {
            var data = await _batchComsumeMaterialListViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchComsumeMaterialListViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchComsumeMaterialListViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchComsumeMaterialListViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BatchComsumeMaterialListViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}