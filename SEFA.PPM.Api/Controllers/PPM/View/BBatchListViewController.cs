using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.MKM.Model.Models.MKM;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class BBatchListViewController : BaseApiController
    {
        /// <summary>
        /// BBatchListView
        /// </summary>
        private readonly IBBatchListViewServices _bBatchListViewServices;

        public BBatchListViewController(IBBatchListViewServices BBatchListViewServices)
        {
            _bBatchListViewServices = BBatchListViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BBatchListViewEntity>>> GetList([FromBody] BBatchListViewRequestModel reqModel)
        {
            var data = await _bBatchListViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{productionOrderId}")]
        public async Task<MessageModel<List<SapSegmentEntity>>> GetSegmentList(string productionOrderId)
        {
            return await _bBatchListViewServices.GetSegmentList(productionOrderId);
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchListViewEntity>>> GetPageList([FromBody] BBatchListViewRequestModel reqModel)
        {
            var data = await _bBatchListViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchSelect>>> GetSegmentBatchList([FromBody] BBatchListViewRequestModel reqModel)
        {
            var data = await _bBatchListViewServices.GetSegmentBatchList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BBatchListViewEntity>> GetEntity(string id)
        {
            var data = await _bBatchListViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 修改批次状态
        /// </summary>
        /// <param name="reqModel">type=0修改批次状态 type=1修改批次备料状态</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateBatchStatus([FromBody] UpdateStatusRequestModel reqModel)
        {
            return await _bBatchListViewServices.UpdateBatchStatus(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BBatchListViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _bBatchListViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _bBatchListViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BBatchListViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}