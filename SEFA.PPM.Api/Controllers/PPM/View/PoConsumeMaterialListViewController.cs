using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class PoConsumeMaterialListViewController : BaseApiController
    {
        /// <summary>
        /// PoConsumeMaterialListView
        /// </summary>
        private readonly IPoConsumeMaterialListViewServices _poConsumeMaterialListViewServices;
        private readonly IMapper _mapper;


        public PoConsumeMaterialListViewController(IPoConsumeMaterialListViewServices PoConsumeMaterialListViewServices, IMapper mapper)
        {
            _poConsumeMaterialListViewServices = PoConsumeMaterialListViewServices;
            _mapper = mapper;
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost]
        public async Task<IActionResult> ExportData([FromBody] PoConsumeMaterialListViewRequestModel reqModel)
        {
            try
            {
                var exportData = await _poConsumeMaterialListViewServices.GetExportDataList(reqModel);
                //var exportData = _mapper.Map<List<EveryDayMaterialRequirement>>(data);
                var memoryStream = new MemoryStream();
                memoryStream.SaveAs(exportData);
                memoryStream.Seek(0, SeekOrigin.Begin);
                return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                {
                    FileDownloadName = $"每日物料需求清单-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"导出每日物料需求清单时出现错误:{ex.Message}");
            }
        }

        [HttpPost]
        public async Task<MessageModel<List<PoConsumeMaterialListViewEntity>>> GetList([FromBody] PoConsumeMaterialListViewRequestModel reqModel)
        {
            var data = await _poConsumeMaterialListViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoConsumeMaterialListViewEntity>>> GetPageList([FromBody] PoConsumeMaterialListViewRequestModel reqModel)
        {
            var data = await _poConsumeMaterialListViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoConsumeMaterialListViewEntity>> GetEntity(string id)
        {
            var data = await _poConsumeMaterialListViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> AddMaterial([FromBody] PoConsumeRequirementEntity reqModel)
        {
            return await _poConsumeMaterialListViewServices.AddMaterial(reqModel);
        }

        [HttpPost]
        public async Task<MessageModel<string>> DeleteMaterial([FromBody] string[] poConsumeRequirementIds)
        {
            return await _poConsumeMaterialListViewServices.DeleteMaterial(poConsumeRequirementIds);
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdateMaterial([FromBody] PoConsumeRequirementEntity reqModel)
        {
            return await _poConsumeMaterialListViewServices.UpdateMaterial(reqModel);
        }
    }
    //public class PoConsumeMaterialListViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}