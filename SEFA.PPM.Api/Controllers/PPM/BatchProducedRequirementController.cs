using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BatchProducedRequirementController : BaseApiController
    {
        /// <summary>
        /// BatchProducedRequirement
        /// </summary>
        private readonly IBatchProducedRequirementServices _batchProducedRequirementServices;

        public BatchProducedRequirementController(IBatchProducedRequirementServices BatchProducedRequirementServices)
        {
            _batchProducedRequirementServices = BatchProducedRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchProducedRequirementEntity>>> GetList([FromBody] BatchProducedRequirementRequestModel reqModel)
        {
            var data = await _batchProducedRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchProducedRequirementEntity>>> GetPageList([FromBody] BatchProducedRequirementRequestModel reqModel)
        {
            Expression<Func<BatchProducedRequirementEntity, bool>> whereExpression = a => true;
            var data = await _batchProducedRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchProducedRequirementEntity>> GetEntity(string id)
        {
            var data = await _batchProducedRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchProducedRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchProducedRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchProducedRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BatchProducedRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}