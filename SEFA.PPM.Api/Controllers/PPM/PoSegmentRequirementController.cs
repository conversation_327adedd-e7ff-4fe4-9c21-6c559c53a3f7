using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoSegmentRequirementController : BaseApiController
    {
        /// <summary>
        /// PoSegmentRequirement
        /// </summary>
        private readonly IPoSegmentRequirementServices _poSegmentRequirementServices;

        public PoSegmentRequirementController(IPoSegmentRequirementServices PoSegmentRequirementServices)
        {
            _poSegmentRequirementServices = PoSegmentRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoSegmentRequirementEntity>>> GetList([FromBody] PoSegmentRequirementRequestModel reqModel)
        {
            var data = await _poSegmentRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoSegmentRequirementEntity>>> GetPageList([FromBody] PoSegmentRequirementRequestModel reqModel)
        {
            Expression<Func<PoSegmentRequirementEntity, bool>> whereExpression = a => true;
            var data = await _poSegmentRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoSegmentRequirementEntity>> GetEntity(string id)
        {
            var data = await _poSegmentRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoSegmentRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poSegmentRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poSegmentRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoSegmentRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}