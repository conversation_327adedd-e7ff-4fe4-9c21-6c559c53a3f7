using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PoProducedExecutionEquController : BaseApiController
    {
        /// <summary>
        /// PoProducedExecutionEqu
        /// </summary>
        private readonly IPoProducedExecutionEquServices _poProducedExecutionEquServices;

        public PoProducedExecutionEquController(IPoProducedExecutionEquServices PoProducedExecutionEquServices)
        {
            _poProducedExecutionEquServices = PoProducedExecutionEquServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PoProducedExecutionEquEntity>>> GetList([FromBody] PoProducedExecutionEquRequestModel reqModel)
        {
            var data = await _poProducedExecutionEquServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PoProducedExecutionEquEntity>>> GetPageList([FromBody] PoProducedExecutionEquRequestModel reqModel)
        {
            Expression<Func<PoProducedExecutionEquEntity, bool>> whereExpression = a => true;
            var data = await _poProducedExecutionEquServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PoProducedExecutionEquEntity>> GetEntity(string id)
        {
            var data = await _poProducedExecutionEquServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PoProducedExecutionEquEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedExecutionEquServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _poProducedExecutionEquServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class PoProducedExecutionEquRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}