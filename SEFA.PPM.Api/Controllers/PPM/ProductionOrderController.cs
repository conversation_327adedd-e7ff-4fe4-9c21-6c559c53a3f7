using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Extensions;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    [Authorize(Permissions.Name)]
    public class ProductionOrderController : BaseApiController
    {
        /// <summary>
        /// ProductionOrder
        /// </summary>
        private readonly IProductionOrderServices _productionOrderServices;
        private readonly IOrderBomServices _orderBomServices;
        private readonly ISappackorderServices _sapPackService;
        
        public ProductionOrderController(IProductionOrderServices ProductionOrderServices, IOrderBomServices orderBomServices, ISappackorderServices sapPackService)
        {
            _productionOrderServices = ProductionOrderServices;
            _orderBomServices = orderBomServices;
            _sapPackService = sapPackService;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductionOrderEntity>>> GetList([FromBody] ProductionOrderRequestModel reqModel)
        {
            var data = await _productionOrderServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<OrderTextResponse>>> GetOrderText([FromBody] OrderTextRequest reqModel)
        {
            var data = await _productionOrderServices.GetOrderText(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductionOrderEntity>>> GetPageList([FromBody] ProductionOrderRequestModel reqModel)
        {
            Expression<Func<ProductionOrderEntity, bool>> whereExpression = a => true;
            var data = await _productionOrderServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionOrderEntity>> GetEntity(string id)
        {
            var data = await _productionOrderServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProductionOrderEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _productionOrderServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "操作成功");
            }
            else
            {
                return Failed("操作失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _productionOrderServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> CalculateSegementRequirement([FromBody] string PoId)
        {
            return await _productionOrderServices.CalculateSegementRequirement(PoId);
        }

        /// <summary>
        /// SAP回调接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> CreateCookieOrder([FromBody] MM_Cookie_Order_Res request)
        {
            return Success("", "操作成功");
        }

        /// <summary>
        /// 批量修改工单备注
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> BatchEditOrderRemark([FromBody] OrderRemarkChangeModel req)
        {
            return await _productionOrderServices.BatchEditOrderRemark(req);
        }
        /// <summary>
        /// 批量获取煮制工单BOM和Routing信息
        /// </summary>
        /// <param name="ids">工单ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetCKPoBomAndRouting(string[] ids)
        {
            return await GetPoBomAndRoutingData(ids, "CK");
        }
        /// <summary>
        /// 批量获取包装工单BOM和Routing信息
        /// </summary>
        /// <param name="ids">工单ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetPKPoBomAndRouting(string[] ids)
        {
            return await GetPoBomAndRoutingData(ids, "PK");
        }

        [HttpPost]
        public async Task<MessageModel<string>> GetPoBomAndRoutingData(string[] ids, string bomtype)
        {

            var result = new MessageModel<string>();

            var poList = await _productionOrderServices.FindList(m => ids.Contains(m.ID) && !string.IsNullOrEmpty(m.ProductionOrderNo));
            var orderNoList = poList.Select(a => a.ProductionOrderNo).ToList();
            var sapTypeList = poList.Select(a=>a.SapOrderType).ToList();

            if (orderNoList.Count() == 0)
            {
                SerilogServer.LogDebug($"无需获取的工单", "GetPoBomAndRouting ");
                result.msg = "无需同步BOM的工单";
                result.success = true;
                return result;
            }
            foreach (var routingtype in sapTypeList)
            {
                await _orderBomServices.GetSapOrderBom(new SapRequestModel
                {
                    factory = "2010",
                    orderNo = orderNoList,
                    type = bomtype,
                    start = new DateTime(2024, 1, 1),
                    end = new DateTime(2099, 1, 1)
                });

                SerilogServer.LogDebug($"获取工单BOM信息,工单数量[{orderNoList.Count}]处理结束", "GetPoBomAndRouting ");
                await _sapPackService.GetOrderRouting(new SapRequestModel
                {
                    factory = "2010",
                    orderNo = orderNoList,
                    type = routingtype,
                    start = new DateTime(2024, 1, 1),
                    end = new DateTime(2099, 1, 1)
                });
            }
           
            SerilogServer.LogDebug($"获取工单Routing信息,工单数量[{orderNoList.Count}],处理结束", "GetPoBomAndRouting ");

            result.success = true;
            result.msg = "操作成功";
            return result;
        }

        /// <summary>
        /// 比对长文本信息
        /// </summary>
        /// <param name="ids">工单ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ComparePoLongTextById (List<string> poIds) {
            if(poIds == null || poIds.Count == 0)
            {
                return Failed("请勾选后操作");
            }
            return await _productionOrderServices.ComparePoLongText(poIds);
        }
        /// <summary>
        /// 比对长文本信息Job
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ComparePoLongTextJob () {
            return await _productionOrderServices.ComparePoLongText();
        }


      
    }
    //public class ProductionOrderRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}