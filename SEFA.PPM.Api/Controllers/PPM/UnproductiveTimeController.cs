using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using static SEFA.PPM.Model.Models.PTM.GroupAndReasonModel;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UnproductiveTimeController : BaseApiController
    {
        /// <summary>
        /// UnproductiveTime
        /// </summary>
        private readonly IUnproductiveTimeServices _unproductiveTimeServices;
        private readonly IImtableUnproductivetimeServices _imUnProService;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        public UnproductiveTimeController(IUnproductiveTimeServices UnproductiveTimeServices, IImtableUnproductivetimeServices imUnProService, IUser user, IUnitOfWork unitOfWork)
        {
            _unproductiveTimeServices = UnproductiveTimeServices;
            _imUnProService = imUnProService;
            _user = user;
            _unitOfWork = unitOfWork;
        }

        [HttpPost]
        public async Task<MessageModel<List<UnproductiveTimeEntity>>> GetList([FromBody] UnproductiveTimeRequestModel reqModel)
        {
            var data = await _unproductiveTimeServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<List<GroupAndReasonList>>> GetGroupAndReasonModelList([FromBody] string search)
        {
            var data = await _unproductiveTimeServices.GetGroupAndReasonModelList(search);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<UnproductiveTimeEntity>>> GetPageList([FromBody] UnproductiveTimeRequestModel reqModel)
        {
            var data = await _unproductiveTimeServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<UnproductiveTimeEntity>> GetEntity(string id)
        {
            var data = await _unproductiveTimeServices.QueryById(id);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 定时执行往辅助工时表插入数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel> SyncInsertData()
        {
            //插入中间表
            var res = await _imUnProService.SyncInsertData();
            return res;
        }
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] UnproductiveTimeEntity request)
        {
            var data = new MessageModel<string>()
            {
                msg = "更新失败！"
            };
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _unproductiveTimeServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                var res = await _imUnProService.SyncUpdateData(request);

                data.success = await _unproductiveTimeServices.Update(request);
                if (data.success)
                {
                    data.response = res.msg;
                    data.msg = "更新成功";
                    return data;
                }
                else
                {
                    return data;
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] UnproductiveTimeEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _unproductiveTimeServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
    }
    //public class UnproductiveTimeRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}