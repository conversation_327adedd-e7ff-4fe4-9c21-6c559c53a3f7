using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BatchProductRequirementController : BaseApiController
    {
        /// <summary>
        /// BatchProductRequirement
        /// </summary>
        private readonly IBatchProductRequirementServices _batchProductRequirementServices;

        public BatchProductRequirementController(IBatchProductRequirementServices BatchProductRequirementServices)
        {
            _batchProductRequirementServices = BatchProductRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchProductRequirementEntity>>> GetList([FromBody] BatchProductRequirementRequestModel reqModel)
        {
            var data = await _batchProductRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchProductRequirementEntity>>> GetPageList([FromBody] BatchProductRequirementRequestModel reqModel)
        {
            Expression<Func<BatchProductRequirementEntity, bool>> whereExpression = a => true;
            var data = await _batchProductRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchProductRequirementEntity>> GetEntity(string id)
        {
            var data = await _batchProductRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchProductRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchProductRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchProductRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BatchProductRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}