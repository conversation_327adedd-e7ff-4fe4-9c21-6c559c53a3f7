using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UnproductiveSearchViewController : BaseApiController
    {
        /// <summary>
        /// UnproductiveSearchView
        /// </summary>
        private readonly IUnproductiveSearchViewServices _unproductiveSearchViewServices;
        private readonly IImtableUnproductivetimeServices _imUnProService;

        public UnproductiveSearchViewController(IUnproductiveSearchViewServices UnproductiveSearchViewServices, IImtableUnproductivetimeServices imUnProService)
        {
            _unproductiveSearchViewServices = UnproductiveSearchViewServices;
            _imUnProService = imUnProService;
        }

        [HttpPost]
        public async Task<MessageModel<List<UnproductiveSearchViewEntity>>> GetList([FromBody] UnproductiveSearchViewRequestModel reqModel)
        {
            var data = await _unproductiveSearchViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<UnproductiveSearchViewEntity>>> GetPageList([FromBody] UnproductiveSearchViewRequestModel reqModel)
        {
            var data = await _unproductiveSearchViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel> DeleteByIdList([FromBody] List<string> unproIdList)
        {
            var date = new MessageModel
            {
                msg = "删除成功"
            };
            var data = await _unproductiveSearchViewServices.DeleteByIdList(unproIdList);
            if (data)
            {
                var res = await _imUnProService.SyncDelData(unproIdList.ToArray());
                date.response = res.msg;
                date.success = data;
                return date;
            }
            else
            {
                date.msg = "删除失败";
                date.success = data;
                return date;
            }

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<UnproductiveSearchViewEntity>> GetEntity(string id)
        {
            var data = await _unproductiveSearchViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] UnproductiveSearchViewEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _unproductiveSearchViewServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _unproductiveSearchViewServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] UnproductiveSearchViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _unproductiveSearchViewServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _unproductiveSearchViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class UnproductiveSearchViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}