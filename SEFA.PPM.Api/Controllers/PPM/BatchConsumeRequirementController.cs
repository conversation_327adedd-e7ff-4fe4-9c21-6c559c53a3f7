using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BatchConsumeRequirementController : BaseApiController
    {
        /// <summary>
        /// BatchConsumeRequirement
        /// </summary>
        private readonly IBatchConsumeRequirementServices _batchConsumeRequirementServices;

        public BatchConsumeRequirementController(IBatchConsumeRequirementServices BatchConsumeRequirementServices)
        {
            _batchConsumeRequirementServices = BatchConsumeRequirementServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchConsumeRequirementEntity>>> GetList([FromBody] BatchConsumeRequirementRequestModel reqModel)
        {
            var data = await _batchConsumeRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }
        //[HttpPost]
        //public async Task<MessageModel<List<BatchConsumeRequirementRequestModel>>> GetConsumeMaterialList ([FromBody] BatchConsumeRequirementRequestModel reqModel) {
        //    var data = await _batchConsumeRequirementServices.GetConsumeMaterialList(reqModel);
        //    return Success(data, "获取成功");
        //}

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchConsumeRequirementEntity>>> GetPageList([FromBody] BatchConsumeRequirementRequestModel reqModel)
        {
            Expression<Func<BatchConsumeRequirementEntity, bool>> whereExpression = a => true;
            var data = await _batchConsumeRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchConsumeRequirementEntity>> GetEntity(string id)
        {
            var data = await _batchConsumeRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchConsumeRequirementEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchConsumeRequirementServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchConsumeRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BatchConsumeRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}