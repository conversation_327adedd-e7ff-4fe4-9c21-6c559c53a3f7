{"standard_implementation_rules": {"crud_operations": {"description": "增删改查(CRUD)操作标准化实现规范", "repository_pattern": {"requirement": "必须符合Repository模式，主要代码放在业务层", "implementation": {"entity_annotation": "[SugarTable]属性标注实体类", "async_methods": "使用SqlSugar异步方法", "query_methods": {"paged_query": "分页查询使用QueryPage方法", "list_query": "列表查询使用GetList或FindList方法", "by_id_query": "根据ID查询使用QueryById方法", "conditional_query": "条件查询使用Expressionable构建查询条件"}}}, "controller_layer": {"restful_apis": "实现RESTful接口，遵循标准HTTP方法和URL命名规范", "http_methods": {"POST": "用于创建、更新和复杂查询操作", "GET": "用于简单查询操作，如根据ID获取实体"}}, "service_layer": {"interface_definition": "服务接口层定义完整的CRUD方法", "business_logic": "业务实现层处理业务逻辑验证"}}, "transaction_management": {"description": "事务管理标准化实现规范", "implementation": {"begin_transaction": "在业务方法开始处调用_unitOfWork.BeginTran()开启事务", "commit_transaction": "成功操作后调用_unitOfWork.CommitTran()提交事务", "rollback_transaction": "出现异常时调用_unitOfWork.RollbackTran()回滚事务", "error_handling": {"logging": "在catch块中记录详细的错误日志", "exception_wrapping": "返回包含错误信息的统一结果对象"}}}, "user_authentication": {"description": "用户认证标准化实现规范", "implementation": {"user_context": "通过_user.Name获取登录用户名", "token_handling": "通过_user.GetToken()获取用户认证Token"}}, "entity_mapping": {"description": "实体内容赋值(MAPPER)标准化实现规范", "implementation": {"dependency_injection": "通过构造函数注入IMapper实例", "mapping_usage": "使用_mapper.Map进行实体间内容赋值"}}, "logging": {"description": "日志写法标准化实现规范", "implementation": {"logger_usage": "使用SerilogServer.LogDebug记录日志", "log_content": "记录操作详情、异常信息等用于调试和问题追踪"}}, "error_handling": {"description": "错误处理标准化实现规范", "implementation": {"logging_first": "先记录日志，然后返回错误", "consistent_responses": "返回统一格式的错误响应信息"}}}}