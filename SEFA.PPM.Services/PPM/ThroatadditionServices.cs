
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using System.Linq;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Diagnostics.Eventing.Reader;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public class ThroatadditionServices : BaseServices<ThroatadditionEntity>, IThroatadditionServices
    {
        private readonly IBaseRepository<ThroatadditionEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        public ThroatadditionServices(IBaseRepository<ThroatadditionEntity> dal,
              IBaseRepository<MaterialEntity> materialDal,
              IUser user,
              IUnitOfWork unitOfWork
            )
        {
            this._dal = dal;
            _user = user;
            _materialDal = materialDal;
            _unitOfWork = unitOfWork;
            base.BaseDal = dal;
        }

        public async Task<List<ThroatadditionEntity>> GetList(ThroatadditionRequestModel reqModel)
        {
            var whereExpression1 = Expressionable.Create<ThroatadditionEntity, MaterialEntity, MaterialEntity>()
                 .AndIF(!string.IsNullOrEmpty(reqModel.MatName),
                           (t, m, am) => t.MatName.Contains(reqModel.MatName)
                                      || t.MatAddableName.Contains(reqModel.MatName)
                                      || m.Description.Contains(reqModel.MatName)
                                      || am.Description.Contains(reqModel.MatName)
                                      || m.Code.Contains(reqModel.MatName)
                                      || am.Code.Contains(reqModel.MatName)
                            )
                .ToExpression();
            var data = await _dal.QueryMuch<ThroatadditionEntity, MaterialEntity, MaterialEntity, ThroatadditionEntity>(
                     (t, m, am) => new object[]
                     {
                          JoinType.Inner, t.MatId == m.ID,
                          JoinType.Inner, t.MatAddableId == am.ID
                     },
                     (t, m, am) => new ThroatadditionEntity
                     {
                         ID = t.ID,
                         MatId = m.ID,
                         MatAddableId = am.ID,
                         FormulaCode = m.Description,
                         MaterialCode = m.Code,
                         MatName = m.Name,
                         AddableFormulaCode = am.Description,
                         AddableMaterialCode = am.Code,
                         MatAddableName = am.Name,
                         Rate = t.Rate,
                         Deleted = t.Deleted,
                         CreateDate = t.CreateDate,
                         CreateUserId = t.CreateUserId,
                         ModifyDate = t.ModifyDate,
                         ModifyUserId = t.ModifyUserId,
                         UpdateTimeStamp = t.UpdateTimeStamp,
                     },
                      whereExpression1
                );

            //var whereExpression = Expressionable.Create<ThroatadditionEntity>()
            //                  .AndIF(!string.IsNullOrEmpty(reqModel.MatName), p => p.MatName.Contains(reqModel.MatName) || p.MatAddableName.Contains(reqModel.MatName))
            //                 .ToExpression();
            //var data = await _dal.FindList(whereExpression);
            //var matList = await _materialDal.FindList(p => p.Description != null);
            //data.ForEach( item =>
            //{
            //    item.MatCode = (matList.FirstOrDefault(p => p.ID == item.MatId))?.Description;
            //    item.AddableMatCode = (matList.FirstOrDefault(p => p.ID == item.MatAddableId))?.Description;
            //});
            return data;
        }

        public async Task<PageModel<ThroatadditionEntity>> GetPageList(ThroatadditionRequestModel reqModel)
        {
            var whereExpression1 = Expressionable.Create<ThroatadditionEntity, MaterialEntity, MaterialEntity>()
                 .AndIF(!string.IsNullOrEmpty(reqModel.MatName),
                           (t, m, am) => t.MatName.Contains(reqModel.MatName)
                                      || t.MatAddableName.Contains(reqModel.MatName)
                                      || m.Description.Contains(reqModel.MatName)
                                      || am.Description.Contains(reqModel.MatName)
                                      || m.Code.Contains(reqModel.MatName)
                                      || am.Code.Contains(reqModel.MatName)
                            )
                .ToExpression();
            var data = await _dal.QueryMuchPage<ThroatadditionEntity, MaterialEntity, MaterialEntity, ThroatadditionEntity>(
                     (t, m, am) => new object[]
                     {
                          JoinType.Inner, t.MatId == m.ID,
                          JoinType.Inner, t.MatAddableId == am.ID
                     },
                     (t, m, am) => new ThroatadditionEntity
                     {
                         ID = t.ID,
                         MatId = m.ID,
                         MatAddableId = am.ID,
                         FormulaCode = m.Description,
                         MaterialCode = m.Code,
                         MatName = m.Name,
                         AddableFormulaCode = am.Description,
                         AddableMaterialCode = am.Code,
                         MatAddableName = am.Name,
                         Rate = t.Rate,
                         Deleted = t.Deleted,
                         CreateDate = t.CreateDate,
                         CreateUserId = t.CreateUserId,
                         ModifyDate = t.ModifyDate,
                         ModifyUserId = t.ModifyUserId,
                         UpdateTimeStamp = t.UpdateTimeStamp
                     },
                      whereExpression1,
                      reqModel.pageIndex,
                      reqModel.pageSize
                );


            //var whereExpression = Expressionable.Create<ThroatadditionEntity>()
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.MatName), p => p.MatName.Contains(reqModel.MatName) || p.MatAddableName.Contains(reqModel.MatName))
            //                 .ToExpression();
            //var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            //var matList = await _materialDal.FindList(p => p.Description != null);
            //data.data.ForEach(item =>
            //{
            //    item.MatCode = (matList.FirstOrDefault(p => p.ID == item.MatId))?.Description;
            //    item.AddableMatCode = (matList.FirstOrDefault(p => p.ID == item.MatAddableId))?.Description;
            //});

            return data;
        }

        public async Task<bool> SaveForm(ThroatadditionEntity entity)
        {
            var exist  = await _dal.FindList(p => p.MatId == entity.MatId && p.MatAddableId == entity.MatAddableId && p.ID != entity.ID);
            ///如果存在相同配置，则不能添加
            if(exist.Count > 0)
            {
                return false;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {               
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                return await this.Update(entity);
            }
        }
        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="input">文件流</param>
        /// <returns></returns>
        public async Task<ResultString> ImportData( FileImportDto input)
        {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<ThroatadditionExcelDto>(stream);
            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }

            var excelData = import.Data.Where(x => !string.IsNullOrEmpty(x.MaterialCode) && !string.IsNullOrEmpty(x.AddableMaterialCode) && x.Rate <= 1 && x.Rate > 0).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据(1、喉头物料代码，可添加配方物料代码不能为空;2、添加比例需小于等于1 大于0)");
                return result;
            }

            var mtrInfos = await _materialDal.FindList(p => !string.IsNullOrEmpty(p.Description));

            var allData = await this.FindList(m => m.ID != null);

            var addList = new List<ThroatadditionEntity>();
            var updateList = new List<ThroatadditionEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];
                if (string.IsNullOrEmpty(item.MaterialCode) || string.IsNullOrEmpty(item.AddableMaterialCode) || item.Rate > 1 || item.Rate <= 0)
                    continue;

                var mtrEnity = mtrInfos.Where(x => x.Code == item.MaterialCode).FirstOrDefault();
                if (mtrEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到喉头物料表[{item.MaterialCode}]基础表信息数据");
                    return result;
                }
                var aMtrEnity = mtrInfos.Where(x => x.Code == item.AddableMaterialCode).FirstOrDefault();
                if (aMtrEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到可添加配方物料代码[{item.AddableMaterialCode}]的基础表信息数据");
                    return result;
                }

                var entity = allData.Where(p => p.MatId == mtrEnity.ID && p.MatAddableId == aMtrEnity.ID).FirstOrDefault();
                if (entity == null)
                {
                    if(addList.Any(p => p.MatId == mtrEnity.ID && p.MatAddableId == aMtrEnity.ID))
                    {
                        continue;
                    }
                    entity = new ThroatadditionEntity();
                    entity.Rate = item.Rate;
                    entity.MatId = mtrEnity.ID;
                    entity.MatAddableId = aMtrEnity.ID;
                    entity.MatName = mtrEnity.Name;
                    entity.MatAddableName = aMtrEnity.Name;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
                else
                {
                    entity.Rate = item.Rate;
                    entity.MatName = mtrEnity.Name;
                    entity.MatAddableName = aMtrEnity.Name;
                    entity.CreateCustomGuid(_user.Name);
                    updateList.Add(entity);
                }
            }
            _unitOfWork.BeginTran();
            try
            {
                if (addList.Any())
                {
                    await this.Add(addList);
                }

                if (updateList.Any())
                {
                    await this.Update(updateList);
                }

                _unitOfWork.CommitTran();

                result.Data += $"新增数据{addList.Count()}条,更新数据{updateList.Count()}条";
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.AddError(ex.Message);
            }
           
            return result;

        }
    }
}