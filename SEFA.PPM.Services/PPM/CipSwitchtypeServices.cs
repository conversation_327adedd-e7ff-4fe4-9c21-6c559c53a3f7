
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.HttpRestSharp;
using System.Linq;
using static SEFA.PTM.Services.ConsumeViewServices;
using SEFA.DFM.Model.ViewModels;
using Magicodes.ExporterAndImporter.Excel;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using MaterialEntity = SEFA.PPM.Model.Models.MKM.MaterialEntity;

namespace SEFA.PPM.Services
{
    public class CipSwitchtypeServices : BaseServices<CipSwitchtypeEntity>, ICipSwitchtypeServices
    {
        private readonly IBaseRepository<CipSwitchtypeEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        private readonly IUnitOfWork _unitOfWork;
        public IUser _user;
        public CipSwitchtypeServices(IBaseRepository<CipSwitchtypeEntity> dal,
            IBaseRepository<MaterialEntity> materialDal,
            IUnitOfWork unitOfWork,
            IUser user)
        {
            this._dal = dal;
            _user = user;
            _unitOfWork = unitOfWork;
            _materialDal = materialDal;
            base.BaseDal = dal;
        }

        public async Task<List<CipSwitchtypeEntity>> GetList(CipSwitchtypeRequestModel reqModel)
        {
            var whereExpression1 = Expressionable.Create<CipSwitchtypeEntity, MaterialEntity, MaterialEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.name),
                          (t, m, am) => t.PrematName.Contains(reqModel.name)
                                     || t.PostmatName.Contains(reqModel.name)
                                     || m.Description.Contains(reqModel.name)
                                     || am.Description.Contains(reqModel.name)
                                     || m.Code.Contains(reqModel.name)
                                     || am.Code.Contains(reqModel.name)
                           )
               .ToExpression();
            var data = await _dal.QueryMuch<CipSwitchtypeEntity, MaterialEntity, MaterialEntity, CipSwitchtypeEntity>(
                     (t, m, am) => new object[]
                     {
                          JoinType.Inner, t.PrematId == m.ID,
                          JoinType.Inner, t.PostmatId == am.ID
                     },
                     (t, m, am) => new CipSwitchtypeEntity
                     {
                         ID = t.ID,
                         Factory = t.Factory,
                         PostmatId = t.PostmatId,
                         PostmatName = t.PostmatName,
                         PostmatCode = am.Description,
                         PostMtrCode = am.Code,
                         PrematId = t.PrematId,
                         PrematName = t.PrematName,
                         PrematCode = m.Description,
                         PreMtrCode = m.Code,
                         Switchid = t.Switchid,
                         Switchname = t.Switchname,
                         Deleted = t.Deleted,
                         CreateDate = t.CreateDate,
                         CreateUserId = t.CreateUserId,
                         ModifyDate = t.ModifyDate,
                         ModifyUserId = t.ModifyUserId,
                         UpdateTimeStamp = t.UpdateTimeStamp,
                     },
                      whereExpression1
                );
            //var whereExpression = Expressionable.Create<CipSwitchtypeEntity>()
            //                 .ToExpression();
            //var data = await _dal.FindList(whereExpression);
            //var matList = await _materialDal.FindList(p => p.Description != null);
            //  MessageModel<List<DataItemDetailModel>> cipswich = await HttpHelper.PostAsync<List<DataItemDetailModel>>("DFM", "api/DataItemDetail/GetList?itemCode=CipSwitch", _user.GetToken(), new { });
            var cipswich = await _dal.Db.Queryable<DataItemDetailEntity>().Where(p => p.ItemCode == "CipSwitch" && p.Deleted == 0).ToListAsync();
            if (cipswich.Count > 0)
            {
                data.ForEach(item =>
                {
                    item.Switchname = cipswich.Where(x => x.ID == item.Switchid).FirstOrDefault()?.ItemName;
                 //   item.PostmatCode = (matList.FirstOrDefault(p => p.ID == item.PostmatId))?.Description;
                 //   item.PrematCode = (matList.FirstOrDefault(p => p.ID == item.PrematId))?.Description;
                });
            }
            return data;
        }

        //public async Task<PageModel<CipSwitchtypeEntity>> GetPageList (CipSwitchtypeRequestModel reqModel) {
        //    var whereExpression1 = Expressionable.Create<CipSwitchtypeEntity, MaterialEntity, MaterialEntity>()
        //         .AndIF(!string.IsNullOrEmpty(reqModel.name),
        //                   (t, m, am) => t.PrematName.Contains(reqModel.name)
        //                              || t.PostmatName.Contains(reqModel.name)
        //                              || m.Description.Contains(reqModel.name)
        //                              || am.Description.Contains(reqModel.name)
        //                              || m.Code.Contains(reqModel.name)
        //                              || am.Code.Contains(reqModel.name)
        //                    )
        //        .ToExpression();
        //    var data = await _dal.QueryMuchPage<CipSwitchtypeEntity, MaterialEntity, MaterialEntity, CipSwitchtypeEntity>(
        //             (t, m, am) => new object[]
        //             {
        //                  JoinType.Inner, t.PrematId == m.ID,
        //                  JoinType.Inner, t.PostmatId == am.ID
        //             },
        //             (t, m, am) => new CipSwitchtypeEntity
        //             {
        //                 ID = t.ID,
        //                 Factory = t.Factory,
        //                 PostmatId = t.PostmatId,
        //                 PostmatName = t.PostmatName,
        //                 PostmatCode = am.Description,
        //                 PrematCode = m.Description,
        //                 PrematId = t.PrematId,
        //                 PrematName = t.PrematName,
        //                 Switchid = t.Switchid,
        //                 Switchname = t.Switchname,
        //                 Deleted = t.Deleted,
        //                 CreateDate = t.CreateDate,
        //                 CreateUserId = t.CreateUserId,
        //                 ModifyDate = t.ModifyDate,
        //                 ModifyUserId = t.ModifyUserId,
        //                 UpdateTimeStamp = t.UpdateTimeStamp,
        //             },
        //              whereExpression1,
        //              reqModel.pageIndex,
        //              reqModel.pageSize
        //        );
        //    //var whereExpression = Expressionable.Create<CipSwitchtypeEntity>()
        //    //                 .ToExpression();
        //    //var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
        //    //var matList = await _materialDal.FindList(p => p.Description != null);
        //    MessageModel<List<DataItemDetailModel>> cipswich = await HttpHelper.PostAsync<List<DataItemDetailModel>>("DFM", "api/DataItemDetail/GetList?itemCode=CipSwitch", _user.GetToken(), new { });
        //    if (cipswich.response != null)
        //    {
        //        data.data.ForEach(item =>
        //        {
        //            item.Switchname = cipswich.response.Where(x => x.ID == item.Switchid).FirstOrDefault()?.ItemName;
        //            //item.PostmatCode = (matList.FirstOrDefault(p => p.ID == item.PostmatId))?.Description;
        //            // item.PrematCode = (matList.FirstOrDefault(p => p.ID == item.PrematId))?.Description;
        //        });
        //    }

        //    return data;
        //}

        public async Task<PageModel<CipSwitchtypeModel>> GetPageList (CipSwitchtypeRequestModel reqModel) {
         

            PageModel<CipSwitchtypeModel> result = new PageModel<CipSwitchtypeModel>();
            RefAsync<int> dataCount = 0;
            var data = await _dal.Db.Queryable<CipSwitchtypeEntity, MaterialEntity, MaterialEntity>(
                     (t, m, am) => new object[]
                     {
                          JoinType.Inner, t.PrematId == m.ID,
                          JoinType.Inner, t.PostmatId == am.ID
                     })
                    .Select(
                     (t, m, am) => new CipSwitchtypeModel
                     {
                         ID = t.ID,
                         PrematId = t.PrematId,
                         PrematCode = m.Description,
                         PreMaterialCode = m.Code,
                         PrematName = m.Name,
                         PostmatId = t.PostmatId,
                         PostmatCode = am.Description,
                         PostMaterialCode = am.Code,
                         PostmatName = am.Name,
                         Switchid = t.Switchid,
                         Switchname = t.Switchname,
                         Deleted = t.Deleted,
                         CreateDate = t.CreateDate,
                         CreateUserId = t.CreateUserId,
                         ModifyDate = t.ModifyDate,
                         ModifyUserId = t.ModifyUserId,
                         UpdateTimeStamp = t.UpdateTimeStamp,
                     })
                .MergeTable()
                .WhereIF(!string.IsNullOrEmpty(reqModel.name),
                           t => t.PrematName.Contains(reqModel.name)
                                      || t.PostmatCode.Contains(reqModel.name)
                                      || t.PostMaterialCode.Contains(reqModel.name)
                                      || t.PostmatName.Contains(reqModel.name)
                                      || t.PrematCode.Contains(reqModel.name)
                                      || t.PreMaterialCode.Contains(reqModel.name)
                                      || t.PrematName.Contains(reqModel.name))
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            // MessageModel<List<DataItemDetailModel>> cipswich = await HttpHelper.PostAsync<List<DataItemDetailModel>>("DFM", "api/DataItemDetail/GetList?itemCode=CipSwitch", _user.GetToken(), new { });
            var cipswich = await _dal.Db.Queryable<DataItemDetailEntity>().Where(p => p.ItemCode == "CipSwitch" && p.Deleted == 0).ToListAsync();
            if (cipswich.Count >0)
            {
                data.ForEach(item =>
                {
                    item.Switchname = cipswich.Where(x => x.ID == item.Switchid).FirstOrDefault()?.ItemName;
                    //item.PostmatCode = (matList.FirstOrDefault(p => p.ID == item.PostmatId))?.Description;
                    // item.PrematCode = (matList.FirstOrDefault(p => p.ID == item.PrematId))?.Description;
                });
            }

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(CipSwitchtypeEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="input">文件流</param>
        /// <returns></returns>
        public async Task<ResultString> ImportData(FileImportDto input)
        {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<CipSwitchtypeExcelDto>(stream);
            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }
            // MessageModel<List<DataItemDetailModel>> cipswichList = await HttpHelper.PostAsync<List<DataItemDetailModel>>("DFM", "api/DataItemDetail/GetList?itemCode=CipSwitch", _user.GetToken(), new { });
            var cipswichList = await _dal.Db.Queryable<DataItemDetailEntity>().Where(p => p.ItemCode == "CipSwitch" && p.Deleted == 0).ToListAsync();
            if ( cipswichList.Count() == 0)
            {
                result.AddError("获取CIP方式字典数据失败，中断操作！");
                return result;
            }
            var excelData = import.Data.Where(x => !string.IsNullOrEmpty(x.PreMtrCode) && !string.IsNullOrEmpty(x.PostMtrCode) && !string.IsNullOrEmpty(x.Switchname)).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据(1、配方物料编码不为空 2、切换方式不为空)");
                return result;
            }

            var mtrInfos = await _dal.Db.Queryable<MaterialEntity>().Where(p => !string.IsNullOrEmpty(p.Description)).ToListAsync();

            var allData = await this.FindList(m => m.Deleted == 0);

            var addList = new List<CipSwitchtypeEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];
                if (string.IsNullOrEmpty(item.PreMtrCode) || string.IsNullOrEmpty(item.PostMtrCode) || string.IsNullOrEmpty(item.Switchname))
                    continue;


                var mtrEnity = mtrInfos.Where(x => x.Code == item.PreMtrCode).FirstOrDefault();
                if (mtrEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到配方物料[{item.PreMtrCode}]基础表信息数据");
                    return result;
                }

                var mtrEnity2 = mtrInfos.Where(x => x.Code == item.PostMtrCode).FirstOrDefault();
                if (mtrEnity2 == null)
                {
                    result.AddError($"第[{i + 1}]行未找到配方物料[{item.PostMtrCode}]基础表信息数据");
                    return result;
                }
                var cip = cipswichList.Where(x => x.ItemName == item.Switchname).FirstOrDefault();

                if (cip == null)
                {
                    result.AddError($"第[{i + 1}]行未找到CIP方式[{item.Switchname}]的字典信息数据");
                    return result;
                }

                var entity = allData.Where(p => p.PrematId == mtrEnity.ID && p.PostmatId == mtrEnity2.ID && p.Switchid == cip.ID).FirstOrDefault();
                if (!addList.Any(p => p.PrematId == mtrEnity.ID && p.PostmatId == mtrEnity2.ID && p.Switchid == cip.ID))
                {
                    entity = new CipSwitchtypeEntity();
                    entity.Factory = "2010";
                    entity.PrematId = mtrEnity.ID;
                    entity.PrematName  = mtrEnity.Name;
                    entity.PostmatId = mtrEnity2.ID;
                    entity.PostmatName = mtrEnity2.Name;
                    entity.Switchid = cip.ID;
                    entity.Switchname = cip.ItemName;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
            }
            _unitOfWork.BeginTran();
            try
            {
                if (allData.Any() && addList.Any())
                {
                    var ids = allData.Select(a => a.ID).ToArray();
                    await this.DeleteByIds(ids);
                }

                if (addList.Any())
                {
                    await this.Add(addList);
                }

                _unitOfWork.CommitTran();

                result.Data += $"删除数据{allData.Count()}条,新增数据{addList.Count()}条";
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.AddError(ex.Message);
            }

            return result;

        }
    }
}