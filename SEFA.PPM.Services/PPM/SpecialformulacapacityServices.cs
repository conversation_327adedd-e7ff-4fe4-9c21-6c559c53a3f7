
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using System.Linq;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.ViewModels.PPM;
using Magicodes.ExporterAndImporter.Excel;
using SEFA.Base.Common.Common;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.IRepository.UnitOfWork;
using System.ComponentModel;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public class SpecialformulacapacityServices : BaseServices<SpecialformulacapacityEntity>, ISpecialformulacapacityServices
    {
        private readonly IBaseRepository<SpecialformulacapacityEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _equipmentDal;

        private readonly IBaseRepository<MaterialEntity> _materialDal;
        public IUser _user;
        private readonly IUnitOfWork _unitOfWork;
        public SpecialformulacapacityServices(IBaseRepository<SpecialformulacapacityEntity> dal,
            IBaseRepository<EquipmentEntity> equipmentDal,
            IBaseRepository<MaterialEntity> materialDal,
            IUnitOfWork unitOfWork,
            IUser user)
        {
            this._dal = dal;
            _user = user;
            _equipmentDal = equipmentDal;
            base.BaseDal = dal;
            _materialDal = materialDal;
            _unitOfWork = unitOfWork;
        }

        public async Task<List<SpecialformulacapacityModel>> GetList(SpecialformulacapacityRequestModel reqModel)
        {
            //var whereExpression = Expressionable.Create<SpecialformulacapacityEntity>()
            //                .AndIF(!string.IsNullOrEmpty(reqModel.LineName),
            //                   p => p.LineName.Contains(reqModel.LineName)
            //                        || p.MatName.Contains(reqModel.LineName)
            //                        || p.TargetlineName.Contains(reqModel.LineName)
            //                        || p.SapFormula.Contains(reqModel.LineName)
            //                        || p.MtrCode.Contains(reqModel.LineName)
            //                        )
            //                .ToExpression();
            //var data = await _dal.FindList(whereExpression);
            //var eq = await _equipmentDal.FindList(p => p.Level == "LINE");

            //var mlist = await _materialDal.FindList(a => data.Select(a => a.MatId).Contains(a.ID));

            //data.ForEach(
            //    p =>
            //    {
            //        p.TargetlineName = eq.FirstOrDefault(x => x.ID == p.Targetline)?.EquipmentCode;
            //        p.MatName = mlist.FirstOrDefault(x => x.ID == p.MatId)?.Name;
            //    }
            // );
            var data = await _dal.Db.Queryable<SpecialformulacapacityEntity, EquipmentEntity, MaterialEntity, SalescontainerEntity>(
                 (s, e, m, sc) => new object[]
                 {
                     JoinType.Inner,s.Targetline == e.ID,
                     JoinType.Left,s.MatId == m.ID,
                     JoinType.Left,s.ContainerId == sc.ID
                 })
                 .Select((s, e, m, sc) => new SpecialformulacapacityModel
                 {
                     ID = s.ID,
                     LineId = s.LineId,
                     LineName = s.LineName,
                     MatId = s.MatId,
                     MatName = m.Name,
                     MtrCode = m.Code,
                     SapFormula = m.Description,
                     ContainerId = s.ContainerId,
                     ContainerName = sc.Description,
                     Weight = s.Weight,
                     Targetline = s.Targetline,
                     ContainerCode = sc.SalesContainer,
                     Targetweight = s.Targetweight,
                     TargetlineName = e.EquipmentName,
                     CreateDate = s.CreateDate,
                     CreateUserId = s.CreateUserId,
                     Deleted = s.Deleted,
                     ModifyDate = s.ModifyDate,
                     ModifyUserId = s.ModifyUserId,
                     UpdateTimeStamp = s.UpdateTimeStamp
                 })
                 .MergeTable()
                 .WhereIF(!string.IsNullOrEmpty(reqModel.LineName),
                                p => p.LineName.Contains(reqModel.LineName)
                                     || p.MatName.Contains(reqModel.LineName)
                                     || p.TargetlineName.Contains(reqModel.LineName)
                                     || p.SapFormula.Contains(reqModel.LineName)
                                     || p.MtrCode.Contains(reqModel.LineName))
                 .OrderBy(" LineName ")
                 .ToListAsync();
            return data;
        }

        public async Task<PageModel<SpecialformulacapacityModel>> GetPageList(SpecialformulacapacityRequestModel reqModel)
        {
            //var whereExpression = Expressionable.Create<SpecialformulacapacityEntity>()
            //                 .AndIF(!string.IsNullOrEmpty(reqModel.LineName),
            //                    p => p.LineName.Contains(reqModel.LineName)
            //                         || p.MatName.Contains(reqModel.LineName)
            //                         || p.TargetlineName.Contains(reqModel.LineName)
            //                         || p.SapFormula.Contains(reqModel.LineName)
            //                         || p.MtrCode.Contains(reqModel.LineName)
            //                         )
            //                 .ToExpression();
            //var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            //var eq = await _equipmentDal.FindList(p => p.Level == "LINE");
            //data.data.ForEach(
            //    p =>
            //    {
            //        p.TargetlineName = eq.FirstOrDefault(x => x.ID == p.Targetline)?.EquipmentCode;
            //    }
            // );
            PageModel<SpecialformulacapacityModel> result = new PageModel<SpecialformulacapacityModel>();
            RefAsync<int> dataCount = 0;
            var data = await _dal.Db.Queryable<SpecialformulacapacityEntity, EquipmentEntity, MaterialEntity, SalescontainerEntity>(
                 (s, e, m, sc) => new object[]
                 {
                     JoinType.Inner,s.Targetline == e.ID,
                     JoinType.Left,s.MatId == m.ID,
                     JoinType.Left,s.ContainerId == sc.ID
                 })
                 .Select((s, e, m, sc) => new SpecialformulacapacityModel
                 {
                     ID = s.ID,
                     LineId = s.LineId,
                     LineName = s.LineName,
                     MatId = s.MatId,
                     MatName = m.Name,
                     MtrCode = m.Code,
                     SapFormula = m.Description,
                     ContainerId = s.ContainerId,
                     ContainerName = sc.Description,
                     Weight = s.Weight,
                     Targetline = s.Targetline,
                     ContainerCode = sc.SalesContainer,
                     Targetweight = s.Targetweight,
                     TargetlineName = e.EquipmentName,
                     CreateDate = s.CreateDate,
                     CreateUserId = s.CreateUserId,
                     Deleted = s.Deleted,
                     ModifyDate = s.ModifyDate,
                     ModifyUserId = s.ModifyUserId,
                     UpdateTimeStamp = s.UpdateTimeStamp
                 })
                 .MergeTable()
                 .WhereIF(!string.IsNullOrEmpty(reqModel.LineName),
                                p => p.LineName.Contains(reqModel.LineName)
                                     || p.MatName.Contains(reqModel.LineName)
                                     || p.TargetlineName.Contains(reqModel.LineName)
                                     || p.SapFormula.Contains(reqModel.LineName)
                                     || p.MtrCode.Contains(reqModel.LineName))
                 .OrderBy(" LineName ")
                 .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            result.data = data;
            result.dataCount = dataCount;
            return result;
        }

        public async Task<bool> SaveForm(SpecialformulacapacityEntity entity)
        {
            var containerInfos = await _dal.Db.Queryable<SalescontainerEntity>().ToListAsync();
            var other = await _dal.FindList(p => p.ID != entity.ID && p.LineId == entity.LineId && p.Targetline == entity.Targetline
                                     && (string.IsNullOrEmpty(entity.MatId)  || p.MatId == entity.ID)
                                     && (string.IsNullOrEmpty(entity.ContainerId) || p.ContainerId == entity.ContainerId));
            if (other.Any())
            {
                return false;
            }
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.Create(_user.Name.ToString());
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="input">文件流</param>
        /// <returns></returns>
        public async Task<ResultString> ImportData(FileImportDto input)
        {
            ResultString result = new ResultString();
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<SpecialformulacapacityExcelDto>(stream);
            if (import.Data.Count() < 1)
            {
                result.AddError("表格中没有效数据");
                return result;
            }
            // 返回 导入异常信息
            if (import.Exception != null)
            {
                result.AddError(import.Exception);
                return result;
            }

            var excelData = import.Data.Where(x => !string.IsNullOrEmpty(x.LineName) && !string.IsNullOrEmpty(x.TargetlineName)).ToList();
            if (excelData.Count() < 1)
            {
                result.AddError("表格中无有效数据(1、产线不能为空，2、煮制线不能为空)");
                return result;
            }

            var mtrInfos = await _materialDal.FindList(p => !string.IsNullOrEmpty(p.Description));
            var lineInfos = await _equipmentDal.FindList(p => p.Level == "line");
            var containerInfos = await _dal.Db.Queryable<SalescontainerEntity>().ToListAsync();

            var allData = await this.FindList(m => m.ID != null);

            var addList = new List<SpecialformulacapacityEntity>();
            for (int i = 0; i < excelData.Count; i++)
            {
                var item = excelData[i];

                var mtrEnity = mtrInfos.Where(x => x.Code == item.MtrCode).FirstOrDefault();
                var line1 = lineInfos.Where(p => p.EquipmentCode == item.LineName || p.EquipmentName == item.LineName).FirstOrDefault();
                if (line1 == null)
                {
                    result.AddError($"第[{i + 1}]行未找到产线[{item.LineName}]基础表信息数据");
                    return result;
                }

                var line2 = lineInfos.Where(p => p.EquipmentCode == item.TargetlineName || p.EquipmentName == item.TargetlineName).FirstOrDefault();
                if (line2 == null)
                {
                    result.AddError($"第[{i + 1}]行未找到煮制线[{item.TargetlineName}]基础表信息数据");
                    return result;
                }
                if (!string.IsNullOrEmpty(item.MtrCode) && mtrEnity == null)
                {
                    result.AddError($"第[{i + 1}]行未找到配方物料编码[{item.MtrCode}]基础表信息数据");
                    return result;
                }
                var container = containerInfos.Where(p => p.SalesContainer == item.ContainerCode || p.Description == item.ContainerName).FirstOrDefault();
                if ((!string.IsNullOrEmpty(item.ContainerCode) || !string.IsNullOrEmpty(item.ContainerName)) && container == null)
                {
                    result.AddError($"第[{i + 1}]行未找到规格[{item?.ContainerCode}][{item?.ContainerName}]基础表信息数据");
                    return result;
                }

                var entity = allData.FirstOrDefault();
                if (!addList.Any(p => p.LineId == line1.ID && p.Targetline == line2.ID
                                     && (mtrEnity == null || p.MatId == mtrEnity.ID)
                                     && (container == null || p.ContainerId == container.ID)
                                     ))
                {
                    entity = new SpecialformulacapacityEntity();
                    entity.LineId = line1.ID;
                    entity.LineName = line1.EquipmentCode;
                    entity.MatId = mtrEnity?.ID;
                    entity.Targetline = line2.ID;
                    entity.MatName = mtrEnity?.Name;
                    entity.ContainerId = container?.ID;
                    entity.Targetweight = item.Targetweight.HasValue ? (item.Targetweight.Value > 0 ? item.Targetweight.Value : null) : null;
                    entity.Weight = item.Weight.HasValue ? (item.Weight.Value > 0 ? item.Weight.Value : null) : null;
                    entity.CreateCustomGuid(_user.Name);
                    addList.Add(entity);
                }
            }
            _unitOfWork.BeginTran();
            try
            {
                if (allData.Any() && addList.Any())
                {
                    var ids = allData.Select(a => a.ID).ToArray();
                    await this.DeleteByIds(ids);
                }

                if (addList.Any())
                {
                    await this.Add(addList);
                }

                _unitOfWork.CommitTran();

                result.Data += $"删除数据{allData.Count()}条,新增数据{addList.Count()}条";
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.AddError(ex.Message);
            }

            return result;

        }
    }
}