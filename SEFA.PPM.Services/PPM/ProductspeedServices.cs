
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
    public class ProductspeedServices : BaseServices<ProductspeedEntity>, IProductspeedServices
    {
        private readonly IBaseRepository<ProductspeedEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        public ProductspeedServices(IBaseRepository<ProductspeedEntity> dal,
              IBaseRepository<MaterialEntity> materialDal)
        {
            this._dal = dal;
            this._materialDal = materialDal;
            base.BaseDal = dal;
        }

        public async Task<List<ProductspeedEntity>> GetList(ProductspeedRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProductspeedEntity>()
                              .AndIF(!string.IsNullOrEmpty(reqModel.MatName), p => p.Wccode.Contains(reqModel.MatName)
                               || p.MatName.Contains(reqModel.MatName) || p.SalescontainerCode.Contains(reqModel.MatName))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            var matList = await _materialDal.FindList(p => p.Description != null);
            data.ForEach(item =>
            {
                item.MatCode = (matList.FirstOrDefault(p => p.ID == item.MatId))?.Description;
            });

            return data;
        }

        public async Task<PageModel<ProductspeedEntity>> GetPageList(ProductspeedRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProductspeedEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.MatName), p => p.Wccode.Contains(reqModel.MatName) 
                               || p.MatName.Contains(reqModel.MatName) || p.SalescontainerCode.Contains(reqModel.MatName))
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
            var matList = await _materialDal.FindList(p => p.Description != null);
            data.data.ForEach(item =>
            {
                item.MatCode = (matList.FirstOrDefault(p => p.ID == item.MatId))?.Description;
            });

            return data;
        }

        public async Task<bool> SaveForm(ProductspeedEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}