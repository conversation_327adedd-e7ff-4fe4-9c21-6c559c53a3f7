
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using System.Linq;
using System;
using static SEFA.PTM.Services.ConsumeViewServices;
using Abp.Domain.Entities;
using OfficeOpenXml.Style;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public class WorkorderthroatServices : BaseServices<WorkorderthroatEntity>, IWorkorderthroatServices
    {
        private readonly IBaseRepository<WorkorderthroatEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        private readonly IBaseRepository<ProductionOrderEntity> _prodocutionorderDal;

        public IUser _user;
        public WorkorderthroatServices(IBaseRepository<WorkorderthroatEntity> dal,
               IBaseRepository<MaterialEntity> materialDal,
               IBaseRepository<ProductionOrderEntity> prodocutionorderDal,
        IUser user)
        {
            this._dal = dal;
            _user = user;
            _materialDal = materialDal;
            _prodocutionorderDal = prodocutionorderDal;
            base.BaseDal = dal;
        }

        public async Task<List<WorkorderthroatEntity>> GetList(WorkorderthroatRequestModel reqModel)
        {
            var childList = new List<string>();
            if(!string.IsNullOrEmpty(reqModel.OrderId))
            {
                childList = await _dal.Db.Queryable<ProductionOrderEntity>().Where(p => p.ParentId == reqModel.OrderId && p.Type == "batch").Select(p => p.ID).ToListAsync();
            }
            var whereExpression = Expressionable.Create<WorkorderthroatEntity>()
                             .AndIF(!string.IsNullOrEmpty(reqModel.OrderId), p => p.OrderId == reqModel.OrderId || childList.Contains( p.OrderId))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            foreach (var item in data)
            {
                var matInfo = await _materialDal.FindEntity(item.MatId);
                item.SapFormula = matInfo?.Description;
            }
            return data;
        }

        public async Task<PageModel<WorkorderthroatEntity>> GetPageList(WorkorderthroatRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<WorkorderthroatEntity, MaterialEntity, ProductionOrderEntity>()
                             .And((p, m, o) => p.OrderCode != "M")
                             .AndIF(!string.IsNullOrEmpty(reqModel.OrderCode), (p, m, o) => p.OrderCode.Contains(reqModel.OrderCode) || o.ProductionOrderNo.Contains(reqModel.OrderCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.ThroatCode), (p, m, o) => p.ThroatCode.Contains(reqModel.ThroatCode))
                             .AndIF(!string.IsNullOrEmpty(reqModel.Throatformorder), (p, m, o) => p.Throatformorder.Contains(reqModel.Throatformorder))
                             .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), (p, m, o) => p.SSCC.Contains(reqModel.SSCC))
                             .AndIF(!string.IsNullOrEmpty(reqModel.SapFormula), (p, m, o) => m.Description.Contains(reqModel.SapFormula))
                             .AndIF(reqModel.StartTime.HasValue && reqModel.StartTime.Value.Year > 2000, (p, m, o) => o.PlanDate >= reqModel.StartTime)
                             .AndIF(reqModel.EndTime.HasValue && reqModel.EndTime.Value.Year > 2000, (p, m, o) => o.PlanDate <= reqModel.EndTime)
                             .ToExpression();
            var data = await _dal.QueryMuchPage<WorkorderthroatEntity, MaterialEntity, ProductionOrderEntity, WorkorderthroatEntity>(
                       (p, m, o) => new object[]
                       {
                            JoinType.Inner, p.MatId == m.ID,
                            JoinType.Inner , p.OrderId == o.ID
                       },
                        (p, m, o) => new WorkorderthroatEntity
                        {
                            ID = p.ID,
                            OrderId = p.OrderId,
                            MatId = p.MatId,
                            CreateDate = p.CreateDate,
                            Inweight = p.Inweight,
                            MatName = p.MatName,
                            Rate = p.Rate,
                            StandardRate = p.StandardRate,
                            CreateUserId = p.CreateUserId,
                            ProductionOrderNo = o.ProductionOrderNo,
                            OrderCode = p.OrderCode,
                            ThroatCode = p.ThroatCode,
                            SapFormula = m.Description,
                            SSCC = p.SSCC,
                            Throatformorder = p.Throatformorder,
                            ModifyDate = p.ModifyDate,
                            ModifyUserId = p.ModifyUserId,
                            PlanDate = o.PlanStartTime.Value.Date,
                            LineCode = o.LineCode,
                            PoSapFormula = o.SapFormula,
                            PlanQty = o.PlanQty
                        },
                       whereExpression,
                       reqModel.pageIndex,
                       reqModel.pageSize,
                       " p.MODIFYDATE desc "
                      );
            //foreach (var item in data.data)
            //{
            //    var matInfo = await _materialDal.FindEntity(item.MatId);
            //    item.SapFormula = matInfo?.Description;
            //}

            return data;
        }

        public async Task<MessageModel<string>> SaveForm(WorkorderthroatEntity entity)
        {
            MessageModel<string> result = new MessageModel<string>
            {
                msg = "操作成功",
                success = false
            };
            var order = await _prodocutionorderDal.FindEntity(entity.OrderId);

            var planqty = 0m;
            if (order != null)
            {
                planqty = order.PlanQty;
            }
            else
            {
                var formula = await _dal.Db.Queryable<FormulascheduleEntity>().Where(p => p.ID == entity.OrderId).FirstAsync();
                if (formula != null)
                {
                    planqty = formula.Scheduleweight;
                }
                else
                {
                    result.msg = "工单不存在！";
                    return result;
                }
            }

            var exist = await _dal.FindList(p => p.OrderId == entity.OrderId);
            if (exist.Exists(p => p.ID != entity.ID && p.MatId != entity.MatId))
            {
                result.msg = "一个工单只允许加入一种喉头";
                return result;
            }
            var existThroat = exist.Where(p => p.MatId == entity.MatId && p.OrderId == entity.OrderId && p.ID != entity.ID).ToList();
            var oCount = existThroat.Sum(p => p.Inweight);
            var rate = Math.Round((entity.Inweight + oCount) / planqty, 3);
            if (rate > entity.StandardRate)
            {
                result.msg = $"添加比例{rate}大于标准上限{entity.StandardRate}！";
                return result;
            }
            var lotQty = 0m;
            if (!string.IsNullOrEmpty(entity.SSCC))
            {
                lotQty = await _dal.Db.Queryable<MaterialSubLotEntity, MaterialInventoryEntity>(
                     (ms, mi) => new object[] { JoinType.Inner, ms.ID == mi.SublotId })
                    .Where((ms, mi) => ms.SubLotId == entity.SSCC)
                    .SumAsync((ms, mi) => mi.Quantity);
                var lotUse = (await _dal.FindList(p => p.MatStatus ==0 && p.SSCC == entity.SSCC && p.OrderId != entity.OrderId && p.OrderCode != "M")).Sum(p => p.Inweight);
                lotQty -= lotUse;
                if (lotQty < entity.Inweight)
                {
                    result.msg = $"添加量{entity.Inweight}大于该追溯码对应库存量{lotQty}！";
                    return result;
                }
            }
            entity.Rate = rate;
            if (string.IsNullOrEmpty(entity.ID))
            {
                var sameSSCC = existThroat.Where(p => p.SSCC == entity.SSCC).FirstOrDefault();
                if (sameSSCC == null)
                {
                    entity.Create(_user.Name.ToString());
                    result.success = await this.Add(entity) > 0;
                }
                else
                {
                    sameSSCC.Inweight += entity.Inweight;
                    if (lotQty < sameSSCC.Inweight)
                    {
                        result.msg = $"累计添加量{sameSSCC.Inweight}大于该追溯码对应库存量{lotQty}！";
                        return result;
                    }
                    sameSSCC.Modify(sameSSCC.ID, _user.Name.ToString());
                    result.success = await this.Update(sameSSCC);
                }
            }
            else
            {
                entity.Modify(entity.ID, _user.Name.ToString());
                result.success = await this.Update(entity);
            }
            if (result.success)
            {
                if (existThroat.Count > 0)
                {
                    foreach (var item in existThroat)
                    {
                        item.Rate = rate;
                    }
                    await _dal.Update(existThroat);
                }
                result.msg = "操作成功！";
            }
            return result;
        }

        public async Task<bool> DeleteDataByIds(string[] ids)
        {
            var list = await _dal.FindList(p => ids.Contains(p.ID));
            if (list.Count == 0)
            {
                return false;
            }
            var order = await _prodocutionorderDal.FindEntity(list[0].OrderId);

            var planqty = 0m;
            if (order != null)
            {
                planqty = order.PlanQty;
            }
            else
            {
                var formula = await _dal.Db.Queryable<FormulascheduleEntity>().Where(p => p.ID == list[0].OrderId).FirstAsync();
                if (formula != null)
                {
                    planqty = formula.Scheduleweight;
                }
                else
                {
                    return false;
                }
            }
            var matids = list.Select(p => p.MatId).Distinct().ToList();

            foreach (var mat in matids)
            {
                var existThroat = list.Where(p => p.MatId == mat && !ids.Contains(p.ID)).ToList();
                var oCount = existThroat.Sum(p => p.Inweight);
                var rate = Math.Round(oCount / planqty, 3);
                foreach (var item in existThroat)
                {
                    item.Rate = rate;
                }
                await _dal.Update(existThroat);
            }
            return await _dal.DeleteByIds(ids);
        }
    }
}