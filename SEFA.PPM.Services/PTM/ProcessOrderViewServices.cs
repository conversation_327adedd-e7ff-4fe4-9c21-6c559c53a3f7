
using SEFA.PTM.IServices;
using SEFA.PTM.Model.Models;
using SEFA.PTM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base;
using OfficeOpenXml.FormulaParsing.Utilities;
using System.Linq;
using SEFA.Base.Common.HttpRestSharp;
using static SEFA.PPM.Services.TippingMlistViewServices;
using SEFA.MKM.Model.Models.MKM;
using SEFA.Base.Common.HttpContextUser;
using System;
using SEFA.MKM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.MKM;
using SEFA.PPM.Model.Models.PTM;

namespace SEFA.PTM.Services
{
	public class ProcessOrderViewServices : BaseServices<ProcessOrderViewEntity>, IProcessOrderViewServices
	{
		private readonly IBaseRepository<ProcessOrderViewEntity> _dal;
		private readonly IBaseRepository<EquipmentEntity> _dal2;
		//private readonly IBaseRepository<FunctionPropertyEntity> _funpdal;
		//private readonly IBaseRepository<EquipmentEntity> _equipdal;
		//private readonly IBaseRepository<BProductionOrderListViewEntity> _produdal;
		private readonly IUser _user;



		public ProcessOrderViewServices(IBaseRepository<ProcessOrderViewEntity> dal, IBaseRepository<EquipmentEntity> dal2, IUser user/*, IBaseRepository<FunctionPropertyEntity> dal3, IBaseRepository<EquipmentEntity> equipdal, IBaseRepository<BProductionOrderListViewEntity> produdal*/)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_dal2 = dal2;
			_user = user;
			//_funpdal = dal3;
			//_equipdal = equipdal;
			//_produdal = produdal;
		}

		public async Task<List<ProcessOrderViewEntity>> GetList2(ProcessOrderViewRequestModel reqModel)
		{
			List<ProcessOrderViewEntity> result = new List<ProcessOrderViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProcessOrderViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode == reqModel.LineCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				.AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
				a.ProcessOrder.Contains(reqModel.Search) ||
				a.MaterialName.Contains(reqModel.Search) ||
				a.MaterialCode.Contains(reqModel.Search) ||
				a.MaterialDescription.Contains(reqModel.Search) ||
				a.SegmentCode.Contains(reqModel.Search) ||
				a.SegmentName.Contains(reqModel.Search) ||
				a.Resource.Contains(reqModel.Search)

				)
				.AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime)
				.AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime)
				.AndIF((reqModel.Status?.Count ?? 0) > 0, a => reqModel.Status.Contains(a.Status))
				.AndIF((reqModel.Status?.Count ?? 0) == 0, a => a.Status == 5 || a.Status == 6 || a.Status == 2)
				.AndIF((reqModel.ExecutionStatus?.Count ?? 0) > 0, a => reqModel.ExecutionStatus.Contains(a.ExecutionStatus))
				//.AndIF(reqModel.Status == null, a => a.Status == null)
				.ToExpression();
			result = await _dal.Db.Queryable<ProcessOrderViewEntity>()
				.Where(whereExpression).OrderByDescending(x => x.StartTime).ToListAsync();
			return result;
		}

		public async Task<PageModel<ProcessOrderViewEntity>> GetPageList2(ProcessOrderViewRequestModel reqModel)
		{
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProcessOrderViewEntity>()
				 .AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
				 .AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode == reqModel.LineCode)
				 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				 .AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
				 a.ProcessOrder.Contains(reqModel.Search) ||
				 a.MaterialName.Contains(reqModel.Search) ||
				 a.MaterialCode.Contains(reqModel.Search) ||
				 a.MaterialDescription.Contains(reqModel.Search) ||
				 a.SegmentCode.Contains(reqModel.Search) ||
				 a.SegmentName.Contains(reqModel.Search) ||
				 a.Resource.Contains(reqModel.Search)

				 )
				 //.AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime)
				 //.AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime)
				 .AndIF((reqModel.Status?.Count ?? 0) > 0, a => reqModel.Status.Contains(a.Status))
				 .AndIF((reqModel.Status?.Count ?? 0) == 0, a => a.Status == 5 || a.Status == 6 || a.Status == 2)
				 .AndIF((reqModel.ExecutionStatus?.Count ?? 0) > 0, a => reqModel.ExecutionStatus.Contains(a.ExecutionStatus))
				 //.AndIF(reqModel.Status == null, a => a.Status == null)
				 .ToExpression();
			var data = await _dal.Db.Queryable<ProcessOrderViewEntity>()
				.Where(whereExpression).OrderByDescending(x => x.StartTime)
				.ToListAsync();
			//.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			//result.dataCount = dataCount;
			//result.data = data;
			//return result;

			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.PlanStartTime >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.PlanStartTime <= reqModel.EndTime.Value);
			}
			PageModel<ProcessOrderViewEntity> result = new PageModel<ProcessOrderViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			//foreach (var item in rDat)
			//{
			//	var pv = (await _funpdal.FindList(x => x.EquipmentId == item.EquipmentId && x.FunctionCode == "Consume" && x.PropertyCode == "MasterStorageTank")).FirstOrDefault();
			//	var masterStorageTank = !string.IsNullOrEmpty(pv.PropertyValue) ? pv.PropertyValue : pv.DefaultValue;
			//	if (!string.IsNullOrEmpty(masterStorageTank))
			//	{
			//		item.StorageTank = masterStorageTank;
			//		var equipment = await _dal2.FindEntity(x => x.EquipmentCode == masterStorageTank);
			//		if (equipment != null)
			//		{
			//			var runOrder = (await _dal.FindList(x => x.EquipmentId == equipment.ID && x.Status == 1 && x.EndTime == null, 1, "START_TIME DESC")).FirstOrDefault();
			//			if (runOrder != null)
			//			{
			//				var order = await _produdal.FindEntity(runOrder.ProductionOrderId);
			//				if (order != null)
			//				{
			//					item.StorageTankOrderGc = $"({order.Sequence}/{order.Count})";
			//				}
			//			}
			//		}
			//	}
			//}
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<List<ProcessOrderViewEntity>> GetList(ProcessOrderViewRequestModel reqModel)
		{
			//if (string.IsNullOrEmpty(reqModel.LineCode) && !string.IsNullOrEmpty(reqModel.EquipmentId))
			//{
			//	reqModel.LineCode = await GetLine(reqModel.EquipmentId);
			//}
			List<ProcessOrderViewEntity> result = new List<ProcessOrderViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProcessOrderViewEntity>()
				.AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
				.AndIF(!string.IsNullOrEmpty(reqModel.FillLineCode), a => a.FillLineCode != null && a.FillLineCode.Contains(reqModel.FillLineCode))
				.AndIF(!string.IsNullOrEmpty(reqModel.Segment), a => a.Segment == reqModel.Segment)
				.AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode == reqModel.LineCode)
				.AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				.AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
				a.ProcessOrder.Contains(reqModel.Search) ||
				a.MaterialName.Contains(reqModel.Search) ||
				a.MaterialCode.Contains(reqModel.Search) ||
				a.MaterialDescription.Contains(reqModel.Search) ||
				a.SegmentCode.Contains(reqModel.Search) ||
				(a.FillLineCode != null && a.FillLineCode.Contains(reqModel.Search)) ||
				a.SegmentName.Contains(reqModel.Search)||
				a.Resource.Contains(reqModel.Search)
				)
				.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				.AndIF((reqModel.Status?.Count ?? 0) > 0, a => reqModel.Status.Contains(a.Status))
				.AndIF((reqModel.Status?.Count ?? 0) == 0, a => a.Status == 5 || a.Status == 6 || a.Status == 2)
				.AndIF((reqModel.ExecutionStatus?.Count ?? 0) > 0, a => reqModel.ExecutionStatus.Contains(a.ExecutionStatus))
				//.AndIF(reqModel.Status == null, a => a.Status == null)
				.ToExpression();
			result = await _dal.Db.Queryable<ProcessOrderViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.LineCode)
				.OrderBy(x => x.PlanStartTime)
				.OrderBy(x => x.FormulaSequence)
				.OrderBy(x => x.Sequence).ToListAsync();
			return result;
		}

		public async Task<PageModel<ProcessOrderViewEntity>> GetPageList(ProcessOrderViewRequestModel reqModel)
		{
			//if (string.IsNullOrEmpty(reqModel.LineCode) && !string.IsNullOrEmpty(reqModel.EquipmentId))
			//{
			//	reqModel.LineCode = await GetLine(reqModel.EquipmentId);
			//}
			//PageModel<ProcessOrderViewEntity> result = new PageModel<ProcessOrderViewEntity>();
			RefAsync<int> dataCount = 0;
			var whereExpression = Expressionable.Create<ProcessOrderViewEntity>()
				 .AndIF(!string.IsNullOrEmpty(reqModel.Resource), a => a.Resource.Contains(reqModel.Resource))
				 .AndIF(!string.IsNullOrEmpty(reqModel.FillLineCode), a => a.FillLineCode != null && a.FillLineCode.Contains(reqModel.FillLineCode))
				 .AndIF(!string.IsNullOrEmpty(reqModel.Segment), a => a.Segment == reqModel.Segment)
				 .AndIF(!string.IsNullOrEmpty(reqModel.LineCode), a => a.LineCode == reqModel.LineCode)
				 .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
				 .AndIF(!string.IsNullOrEmpty(reqModel.Search), a =>
				 a.ProcessOrder.Contains(reqModel.Search) ||
				 a.MaterialName.Contains(reqModel.Search) ||
				 a.MaterialCode.Contains(reqModel.Search) ||
				 a.MaterialDescription.Contains(reqModel.Search) ||
				 a.SegmentCode.Contains(reqModel.Search) ||
				 (a.FillLineCode != null && a.FillLineCode.Contains(reqModel.Search)) ||
				 a.SegmentName.Contains(reqModel.Search) ||
				 a.Resource.Contains(reqModel.Search)

				 )
				 //.AndIF(reqModel.StartTime.HasValue, a => a.SapDate >= reqModel.StartTime)
				 //.AndIF(reqModel.EndTime.HasValue, a => a.SapDate <= reqModel.EndTime)
				 .AndIF((reqModel.Status?.Count ?? 0) > 0, a => reqModel.Status.Contains(a.Status))
				 .AndIF((reqModel.Status?.Count ?? 0) == 0, a => a.Status == 5 || a.Status == 6 || a.Status == 2)
				 .AndIF((reqModel.ExecutionStatus?.Count ?? 0) > 0, a => reqModel.ExecutionStatus.Contains(a.ExecutionStatus))
				 //.AndIF(reqModel.Status == null, a => a.Status == null)
				 .ToExpression();
			var data = await _dal.Db.Queryable<ProcessOrderViewEntity>()
				.Where(whereExpression)
				.OrderBy(x => x.LineCode)
				.OrderBy(x => x.SapDate)
				.OrderBy(x => x.FormulaSequence)
                .OrderBy(x => x.FillPoSequence)
                .OrderBy(x => x.Sequence)
				.ToListAsync();
			//	.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
			//result.dataCount = dataCount;
			//result.data = data;
			//return result;

			if (reqModel.StartTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate >= reqModel.StartTime.Value);
			}
			if (reqModel.EndTime.HasValue)
			{
				data = data.FindAll(a => a.SapDate <= reqModel.EndTime.Value);
			}
			PageModel<ProcessOrderViewEntity> result = new PageModel<ProcessOrderViewEntity>()
			{
				page = reqModel.pageIndex,
				pageSize = reqModel.pageSize
			};
			int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
			var rDat = data.Skip(startIndex).Take(reqModel.pageSize).ToList();
			result.dataCount = data.Count;
			result.data = rDat;
			return result;
		}

		public async Task<string> GetLine(string equipmentId)
		{
			string result = string.Empty;
			var equipments = await _dal2.Query();
			result = (await GetLineForParent(equipments, equipmentId))?.EquipmentCode;
			//return result;
			//var equipment = await _dal2.FindEntity(equipmentId);
			//if (equipment != null)
			//{
			//	//获取上层列表
			//	MessageModel<List<EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", "api/Equipment/GetProductLine", _user.GetToken(), new { equipmentCode = equipment.EquipmentCode, level = "Line" });
			//	var equipments = apiResult_Equipments.response;
			//	if (equipments?.Count > 0)
			//	{
			//		result = equipments.FirstOrDefault()?.EquipmentCode;
			//	}
			//}
			return result;
		}

		public async Task<EquipmentEntity> GetLineForParent(List<EquipmentEntity> equipments, string equipmentId)
		{
			string result = string.Empty;
			if (string.IsNullOrEmpty(equipmentId))
			{
				return null;
			}
			var equipmentEntity = equipments.Find(x => x.ID == equipmentId);
			if (equipmentEntity != null)
			{
				if (equipmentEntity.Level == "Line")
				{
					return equipmentEntity;
				}
				else
				{
					return await GetLineForParent(equipments, equipmentEntity.ParentId);
				}
			}
			return null;
		}

		/// <summary>
		/// 获取segment的设备
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<Select>>> GetSegments(ProcessOrderViewRequestModel reqModel)
		{
			var result = new MessageModel<List<Select>>
			{
				msg = "获取失败！",
				success = false,
				response = new List<Select>()
			};
			var list = (await _dal.FindList(a => a.LineCode == reqModel.LineCode && (a.Status == 5 || a.Status == 6 || a.Status == 2)))?.Select(x => new Select() { key = x.Segment, value = x.Segment })?.ToList()?.DistinctBy(x => x.key);
			if (list?.Count() > 0)
			{
				list.FirstOrDefault().isSelect = true;
				result.response = list.ToList();
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取segment的设备
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<List<EquipmentEntity>>> GetSegmentUnits(string segment)
		{
			var result = new MessageModel<List<EquipmentEntity>>
			{
				msg = "获取失败！",
				success = false,
				response = new List<EquipmentEntity>()
			};
			var equipment = await _dal2.FindEntity(x => x.EquipmentCode == segment);
			if (equipment == null)
			{
				result.msg = "未找到segment";
				return result;
			}
			//获取Function
			MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
			var equipmentAllData = apiResult_equipmentAllData.response;
			if (equipmentAllData == null)
			{
				result.msg = "equipmentAllData为空";
				return result;
			}
			var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Common");
			var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "ExecutionUnit");
			var value = item2?.ActualValue ?? item2?.DefaultValue;
			//value = "Filling-L3;Label-L3";
			var equipmentCodes = value?.Split(';', StringSplitOptions.RemoveEmptyEntries)?.ToList();
			if (equipmentCodes?.Count > 0)
			{
				result.response = (await _dal2.FindList(x => equipmentCodes.Contains(x.EquipmentCode)));
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}


		public async Task<bool> SaveForm(ProcessOrderViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}
	}
}