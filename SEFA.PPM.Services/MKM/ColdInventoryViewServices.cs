
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using System;
using System.Linq;

namespace SEFA.PPM.Services
{
    public class ColdInventoryViewServices : BaseServices<ColdInventoryViewEntity>, IColdInventoryViewServices
    {
        private readonly IBaseRepository<ColdInventoryViewEntity> _dal;
        private readonly IBaseRepository<ColdAWarehouseViewEntity> _dal1;
        private readonly IBaseRepository<ProductionOrderEntity> _dalProductionOrderEntity;
        public ColdInventoryViewServices(IBaseRepository<ColdInventoryViewEntity> dal, IBaseRepository<ColdAWarehouseViewEntity> dal1, IBaseRepository<ProductionOrderEntity> dalProductionOrderEntity = null)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _dal1 = dal1;
            _dalProductionOrderEntity = dalProductionOrderEntity;
        }

        public async Task<List<ColdInventoryViewEntity>> GetList(ColdInventoryViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ColdInventoryViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        /// <summary>
        /// 喉头仓库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<ColdInventoryViewEntity>> GetPageList(ColdInventoryViewRequestModel reqModel)
        {
            PageModel<ColdInventoryViewEntity> result = new PageModel<ColdInventoryViewEntity>();
            RefAsync<int> dataCount = 0;
            /*  var whereExpression = Expressionable.Create<ColdInventoryViewEntity>()
                  .And(p=>p.EquipmentCode==reqModel.EquipmentCode)
                               .ToExpression();*/
            // var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            var data = await _dal.Db.Queryable<ColdInventoryViewEntity>()
                .Where(Expressionable.Create<ColdInventoryViewEntity>()
                 .And(p => p.EquipmentCode == reqModel.EquipmentCode)
                .ToExpression()).OrderBy(P => P.ExpirationDate).ToListAsync();
            return data;
        }

        /// <summary>
        /// 当日出仓喉头提醒
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<ColdAWarehouseViewEntity>> DayOutStock(ColdAWarehouseViewRequestModel reqModel)
        {       
            var whereExpression = Expressionable.Create<ColdAWarehouseViewEntity>()
                  .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.MatModifydate >= Convert.ToDateTime(reqModel.StartTime))
                  .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.MatModifydate <= Convert.ToDateTime(reqModel.EndTime))
                  .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode.Contains(reqModel.EquipmentCode) || a.MatEquipmentcode.Contains(reqModel.EquipmentCode))
                  .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.Sscc.Contains(reqModel.SSCC))
                   .AndIF(!string.IsNullOrEmpty(reqModel.PlanStar), a => a.PlanDate >= Convert.ToDateTime(reqModel.PlanStar))
                  .AndIF(!string.IsNullOrEmpty(reqModel.PlanEnd), a => a.PlanDate <= Convert.ToDateTime(reqModel.PlanEnd))
                             .ToExpression();
            var data = await _dal1.FindList(whereExpression);

            //分组求和
            var groupList = data.Where(p=> p.MatStatus == 0).GroupBy(p => new { p.ThroatCode, p.MatName, p.Description, p.Throatfromorder, p.LotId, p.Sapformula, p.QtyUnit })
                .Select(p => new ColdAWarehouseViewEntity
                {
                    ThroatCode = p.Key.ThroatCode,
                    MatName = p.Key.MatName,
                    Description = p.Key.Description,
                    Throatfromorder = p.Key.Throatfromorder,
                    Sapformula = p.Key.Sapformula,
                    Inweight = p.Sum(p => p.Inweight),
                    QtyUnit = p.Key.QtyUnit
                }).ToList();

            if (groupList == null)
            {
                return new List<ColdAWarehouseViewEntity>();
            }


            return groupList.OrderBy(p => p.MatStatus).ThenByDescending(p => p.MatModifydate).ToList();


            //DateTime today = DateTime.Today; // 获取当天日期（不包含时间）
            //DateTime tomorrow = today.AddDays(0); // 获取明天的日期
            //DateTime todayStart = new DateTime(tomorrow.Year, tomorrow.Month, tomorrow.Day, 0, 0, 0); // 明天开始的时间
            //DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 明天的23:59:59

            ////筛选明天的工单
            //var proList = await _dalProductionOrderEntity.FindList(p => p.PlanDate != null && p.PlanDate.Value >= today && p.PlanDate.Value <= todayEnd);
            ////反向查询喉头数据
            //if (proList == null || proList.Count <= 0)
            //{
            //    return new List<ColdAWarehouseViewEntity>();
            //}
            //var proIDS = proList.Select(p => p.ID).ToList();

            //PageModel<ColdAWarehouseViewEntity> result = new PageModel<ColdAWarehouseViewEntity>();
            //RefAsync<int> dataCount = 0;
            //var data = await _dal1.Db.Queryable<ColdAWarehouseViewEntity>()
            //    .Where(Expressionable.Create<ColdAWarehouseViewEntity>()
            //    .And(p => p.EquipmentCode == reqModel.EquipmentCode)
            //    .And(p => proIDS.Contains(p.Order_Id))
            //    // .And(p => p.MatModifydate >= todayStart && p.MatModifydate <= todayEnd)
            //    .And(P => P.MatStatus == 0)//查未出仓的数据
            //    .ToExpression()).OrderBy(P => P.MatModifydate).ToListAsync();


            ////分组求和
            //var groupList = data.GroupBy(p => new { p.ThroatCode, p.MatName, p.Description, p.Throatfromorder, p.LotId, p.Sapformula, p.QtyUnit })
            //    .Select(p => new ColdAWarehouseViewEntity
            //    {
            //        ThroatCode = p.Key.ThroatCode,
            //        MatName = p.Key.MatName,
            //        Description = p.Key.Description,
            //        Throatfromorder = p.Key.Throatfromorder,
            //        Sapformula = p.Key.Sapformula,
            //        Inweight = p.Sum(p => p.Inweight),
            //        QtyUnit = p.Key.QtyUnit
            //    }).ToList();

            //if (groupList == null)
            //{
            //    return new List<ColdAWarehouseViewEntity>();
            //}


            //return groupList;
        }

        public async Task<bool> SaveForm(ColdInventoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}