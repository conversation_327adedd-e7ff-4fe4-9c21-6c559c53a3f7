
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class ContainerHistoryViewServices : BaseServices<ContainerHistoryViewEntity>, IContainerHistoryViewServices
    {
        private readonly IBaseRepository<ContainerHistoryViewEntity> _dal;
        public ContainerHistoryViewServices(IBaseRepository<ContainerHistoryViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ContainerHistoryViewEntity>> GetList(ContainerHistoryViewRequestModel reqModel)
        {
            List<ContainerHistoryViewEntity> result = new List<ContainerHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ContainerHistoryViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<ContainerHistoryViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<ContainerHistoryViewEntity>> GetPageList(ContainerHistoryViewRequestModel reqModel)
        {
            PageModel<ContainerHistoryViewEntity> result = new PageModel<ContainerHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ContainerHistoryViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ActionCodes), a => a.ActionCodes.Contains(reqModel.ActionCodes))
                .AndIF(!string.IsNullOrEmpty(reqModel.CommentName), a => a.CommentName.Contains(reqModel.CommentName))
                             .ToExpression();
            var data = await _dal.Db.Queryable<ContainerHistoryViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(ContainerHistoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}