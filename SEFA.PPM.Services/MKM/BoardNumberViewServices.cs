
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.PPM;

namespace SEFA.PPM.Services
{
    public class BoardNumberViewServices : BaseServices<BoardNumberViewEntity>, IBoardNumberViewServices
    {
        private readonly IBaseRepository<BoardNumberViewEntity> _dal;
        public BoardNumberViewServices(IBaseRepository<BoardNumberViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BoardNumberViewEntity>> GetList(BoardNumberViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BoardNumberViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BoardNumberViewEntity>> GetPageList(BoardNumberViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BoardNumberViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BoardNumberViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}