
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SqlSugar;
using System.Threading.Tasks;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.WebApiClients.HttpApis;
using System.Linq;
using System.ComponentModel;
using SEFA.Base.Model;
using SEFA.MKM.Model.ViewModels;
using System.Collections.Generic;
using System.Xml.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Repository.Base;
using SEFA.Base.Common.HttpRestSharp;
using static SEFA.PPM.Services.TippingMlistViewServices;
using SEFA.DFM.Model.Models.Models.ViewModel;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;

namespace SEFA.MKM.Services
{
    public class ContainerServices : BaseServices<ContainerEntity>, IContainerServices
    {
        private readonly IBaseRepository<ContainerEntity> _dal;
        private readonly IBaseRepository<DFM.Model.Models.UnitConvertEntity> _dalUnitConvertEntity;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _dalUnitmanageEntity;
        private readonly IBaseRepository<ContainerHistoryViewEntity> _ContainerHistoryViewEntityDal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<InventorylistingViewEntity> _InventorylistingViewEntityDal;
        private readonly IBaseRepository<MaterialInventoryEntity> _MaterialInventoryEntityDal;
        private readonly IBaseRepository<MaterialTransferEntity> _MaterialTransferEntityDal;
        private readonly IBaseRepository<ContainerHistoryEntity> _ContainerHistoryEntityDal;
        private readonly IBaseRepository<BpalletDetailViewEntity> _BBatchDetailViewEntityDal;
        private readonly IUser _user;
        //   private readonly ILabelPrinterServices  _containerHistoryServices;
        // private readonly IBaseRepository _materialLotServices;

        public ContainerServices(IBaseRepository<ContainerEntity> dal, IBaseRepository<ContainerHistoryViewEntity> ContainerHistoryViewEntityDal, IBaseRepository<InventorylistingViewEntity> InventorylistingViewEntityDal,
            IBaseRepository<MaterialInventoryEntity> MaterialInventoryEntity,
            IBaseRepository<MaterialTransferEntity> MaterialTransferEntityDal,
            IBaseRepository<ContainerHistoryEntity> ContainerHistoryEntity,
            IBaseRepository<BpalletDetailViewEntity> BBatchDetailViewEntityDalDal, IUnitOfWork unitOfWork,
            IUser user, IBaseRepository<InventorylistingViewEntity> inventorylistingViewEntityDal
, IBaseRepository<UnitConvertEntity> dalUnitConvertEntity
, IBaseRepository<UnitmanageEntity> dalUnitmanageEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._ContainerHistoryViewEntityDal = ContainerHistoryViewEntityDal;
            _MaterialInventoryEntityDal = MaterialInventoryEntity;
            _MaterialTransferEntityDal = MaterialTransferEntityDal;
            _ContainerHistoryEntityDal = ContainerHistoryEntity;
            _BBatchDetailViewEntityDal = BBatchDetailViewEntityDalDal;
            _unitOfWork = unitOfWork;
            _user = user;
            _InventorylistingViewEntityDal = InventorylistingViewEntityDal;
            _dalUnitConvertEntity = dalUnitConvertEntity;
            _dalUnitmanageEntity = dalUnitmanageEntity;
            // _materialLotServices=materialLotServices;
            //_inventorylistingViewServices = inventorylistingViewServices;
            //_materialTransferServices = materialTransferServices;
            //_materialInventoryServices = materialInventoryServices;
            //_containerHistoryServices = containerHistoryServices;
            //_containerViewServices = containerViewServices;
        }



        /// <summary>
        /// 获取退库或报废的equipmentID
        /// </summary>

        /// <param name="type">退库ReturnToLocation/报废 ScrapLocation</param>
        /// <returns></returns>
        public async Task<DataModel> GetEquipmentID(string type)
        {
            /// <param name="equipmentID">工厂节点的ID</param>
            string id = "02308281-5121-6559-163e-0370f6000000";
            //找工厂配置的退库和报废车间
            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + id, _user.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData == null)
            {
                return null;
            }
            //查询对应的属性
            List<EquFunctionPropertyModel> propertyList = equipmentAllData.EquipmentFunctionPropertyList;
            if (propertyList == null)
            {
                return null;
            }
            //获取属性
            List<EquipmentFunctionPropertyModel> funtionModel = propertyList[0].ActiveFunctionPropertyList;
            if (funtionModel == null)
            {
                return null;
            }
            //获取类型
            EquipmentFunctionPropertyModel model = funtionModel.Where(p => p.PropertyCode.Contains(type)).FirstOrDefault();
            if (model == null)
            {
                return null;
            }
            Dictionary<string, string> dic = new Dictionary<string, string>();
            //拿到设备
            string typeCode = model.ActualValue;
            if (string.IsNullOrEmpty(typeCode)) return null;



            string[] str = typeCode.Split(';');
            if (str.Count() <= 1)
            {
                return null;
            }
            typeCode = str[1];
            var api_equipments = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentEntity>>("DFM", "api/Equipment/GetList", _user.GetToken(), new { EquipmentCode = typeCode });
            var equipments = api_equipments.response;
            if (equipments == null || equipments.Count == 0)
            {
                return null;
            }
            var eModel = equipments.Find(P => P.EquipmentCode == typeCode);
            string typeEquipmentID = eModel.ID;

            //var api_equipmentRequirement = await HttpHelper.PostAsync<DFM.Model.Models.EquipmentStorageEntity>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
            var api_equipmentRequirement = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { EquipmentId = typeEquipmentID });


            DFM.Model.Models.EquipmentStorageEntity storage = api_equipmentRequirement.response.Find(p => p.EquipmentId == typeEquipmentID);
            if (storage == null)
            {
                return null;
            }

            var api_equirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + storage.EquipmentRequirementId, _user.GetToken(), null);
            var equirement = api_equirement.response;
            if (equirement == null)
            {
                return null;
            }
            else
            {
                var sapStorageType = equirement.ManageInventory;
                DataModel models = new DataModel();
                models.Data = typeEquipmentID;
                models.Data1 = sapStorageType;
                models.Data2 = str[0] + ";" + equirement.Code;

                return models;
            }
        }



        /// <summary>
        /// 删袋操作
        /// </summary>
        /// <param name="inventID">库存ID</param>
        /// <returns></returns>
        public async Task<bool> DeletePag(string inventID)
        {
            try
            {
                if (inventID == null)
                {
                    return false;
                }
                _unitOfWork.BeginTran();

                bool DeleteResult = false;
                bool upResult = false;

                //查询对应的物料信息
                //查询当前实体
                BpalletDetailViewEntity model = await _BBatchDetailViewEntityDal.QueryById(inventID);

                if (model == null)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }
                //查询对应得库存ID 执行更新操作
                MaterialInventoryEntity modelInvent = _MaterialInventoryEntityDal.FindList(p => p.ID == model.InventoryId).Result.FirstOrDefault();

                //查询容器历史
                ContainerHistoryEntity cHisModel = _ContainerHistoryEntityDal.FindList(p => p.ContainerId == model.ContainerId).Result.OrderByDescending(p => p.ModifyDate).FirstOrDefault();
                string userID = _user.Name.ToString();
                #region 写入容器记录表
                //写入容器记录表(Add)

                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                //hisModel.ID = Guid.NewGuid().ToString();
                hisModel.Create(userID);
                //   hisModel
                hisModel.ContainerId = cHisModel.ContainerId;
                hisModel.Type = "Container Inventory Remove";
                hisModel.EquipmentId = cHisModel.EquipmentId;
                hisModel.EquipmentRequirementId = cHisModel.EquipmentRequirementId;
                hisModel.State = cHisModel.Status;
                hisModel.Comment = cHisModel.Comment;
                hisModel.ProductOrderId = hisModel.ProductOrderId;// 工单ID
                hisModel.BatchId = hisModel.ProductOrderId; //工单批次ID
                hisModel.MaterialId = cHisModel.MaterialId;
                hisModel.SublotId = cHisModel.SublotId;
                hisModel.Quantity = cHisModel.Quantity.ToString();
                hisModel.QuantityUomId = cHisModel.QuantityUomId;
                hisModel.BatchConsumedRequirementId = cHisModel.BatchConsumedRequirementId;//批次用量需求ID
                hisModel.ConsumedRequirementId = cHisModel.ConsumedRequirementId;//工单用量需求ID
                hisModel.ProductionExecutionId = cHisModel.ProductionExecutionId;//工单执行ID
                                                                                 // hisModel.Status = cHisModel.//批次执行状态
                hisModel.ContainerCode = cHisModel.ContainerCode;//容器编号
                hisModel.MaterialProducedActualId = cHisModel.MaterialProducedActualId; //物料产出记录ID
                hisModel.MaterialConsumedActualId = cHisModel.MaterialConsumedActualId;  //物料消耗记录ID
                hisModel.LotId = cHisModel.LotId;
                hisModel.ExpirationDate = cHisModel.ExpirationDate;

                bool rqResult = await _ContainerHistoryEntityDal.Add(hisModel) > 0;

                #endregion

                //查询转移历史
                MaterialTransferEntity tHisModel = _MaterialTransferEntityDal.FindList(p => p.NewLotId == model.LotId && p.NewSublotId == model.SubLotId).Result.FirstOrDefault();

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userID);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = tHisModel.OldStorageLocation;
                trans.NewStorageLocation = tHisModel.NewStorageLocation;
                trans.OldLotId = tHisModel.OldLotId;
                trans.NewLotId = tHisModel.NewLotId;
                trans.OldSublotId = tHisModel.OldSublotId;
                trans.NewSublotId = tHisModel.NewSublotId;
                trans.OldExpirationDate = tHisModel.OldExpirationDate;
                trans.NewExpirationDate = tHisModel.NewExpirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(tHisModel.Quantity), 3); // Convert.ToInt32(tHisModel.Quantity);
                trans.QuantityUomId = tHisModel.QuantityUomId;
                trans.ProductionExecutionId = tHisModel.ProductionExecutionId;
                trans.Type = "Batch Pallet Remove";
                trans.Comment = "批次托盘-删除";
                trans.NewEquipmentRequirementId = tHisModel.NewEquipmentRequirementId;
                trans.OldEquipmentRequirementId = tHisModel.OldEquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = tHisModel.OldEquipmentId;
                trans.NewEquipmentId = tHisModel.NewEquipmentId;
                trans.OldContainerId = tHisModel.OldContainerId;
                trans.NewContainerId = tHisModel.NewContainerId;
                //status
                trans.OldMaterialId = tHisModel.OldMaterialId;
                trans.NewMaterialId = tHisModel.NewMaterialId;
                trans.OldLotExternalStatus = tHisModel.OldLotExternalStatus;
                trans.OldSublotExternalStatus = tHisModel.OldSublotExternalStatus;
                trans.NewLotExternalStatus = tHisModel.NewLotExternalStatus;
                trans.NewSublotExternalStatus = tHisModel.NewSublotExternalStatus;
                trans.PhysicalQuantity = tHisModel.PhysicalQuantity; //物理数量
                trans.TareQuantity = tHisModel.TareQuantity;  //皮数量
                bool tranHis = await _MaterialTransferEntityDal.Add(trans) > 0;

                #endregion
                //清空当前库存id,并且需要查询最新转移记录给还原对应的eqmment
                if (modelInvent != null)
                {
                    modelInvent.ContainerId = "";
                    modelInvent.EquipmentId = model.OldEquipmentId;
                    upResult = await _MaterialInventoryEntityDal.Update(modelInvent);
                }

                if (!upResult || !tranHis || !rqResult)
                {
                    _unitOfWork.RollbackTran();
                    return false;
                }

                _unitOfWork.CommitTran();
                DeleteResult = true;

                return DeleteResult;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                return false;
            }
        }

        /// <summary>
        /// 批量转移
        /// </summary>
        /// <param name="containerid">转移数据id(库存表ID)</param>
        /// <param name="id">转移数据id(库存表ID)</param>
        /// <param name="equipmentId">区域ID</param>
        /// <param name="name">区域名称</param>
        /// <returns></returns>
        public async Task<bool> TransferContainer(string[] ids, string equipmentId, string name, string comment)
        {
            /// </summary>
            /// <param name="id">物料库存ID(这里再编辑界面能拿到)</param>      
            /// <param name="equipmentId">存储新区域id</param>
            /// <param name="name">存储区域名称</param>
            /// <returns></returns>
            ///// 
            string userID = _user.Name.ToString();
            //var data = new MessageModel<string>();
            string id = string.Empty;
            //string equipmentId = tranModel.EquipmentId;
            //string name = tranModel.Name;
            ////if (string.IsNullOrEmpty(id))
            ////{
            ////    return Failed("转移失败，不存在ID");
            ////}
            //if (string.IsNullOrEmpty(equipmentId))
            //{
            //    return Failed("转移失败，不存在equipmentId");
            //}
            //if (string.IsNullOrEmpty(name))
            //{
            //    return Failed("转移失败，不存在name");
            //}
            #region 判断选中节点是否包含对应的

            if (string.IsNullOrEmpty(equipmentId))
            {
                return false;
            }

            #region 查询属性

            bool isConvert = false;
            string kgUnitID = string.Empty;

            //查询属性表
            MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _user.GetToken(), new { });
            var equipmentAllData = apiResult_equipmentAllData.response;
            if (equipmentAllData != null)
            {
                var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "MaterialPrep");
                if (item != null)
                {
                    isConvert = true;
                    var umanagerModel = await _dalUnitmanageEntity.FindList(p => p.Name == "KG");
                    if (umanagerModel != null)
                    {
                        DFM.Model.Models.UnitmanageEntity model = umanagerModel.FirstOrDefault();
                        kgUnitID = model.ID;
                    }
                }
            }

            #endregion

            #endregion


            #region 操作实体
            List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
            List<MaterialTransferEntity> addTransfer = new List<MaterialTransferEntity>();
            List<ContainerEntity> upCon = new List<ContainerEntity>();
            List<ContainerHistoryEntity> conHis = new List<ContainerHistoryEntity>();
            #endregion

            for (int i = 0; i < ids.Length; i++)
            {
                id = ids[i].ToString();
                if (id != string.Empty)
                {
                    #region 进行转移操作

                    //获取视图数据
                    var whereExpressionInventory = Expressionable.Create<InventorylistingViewEntity>().And(p => p.InventoryId == id)
                     .ToExpression();

                    var inventoryModel = await _InventorylistingViewEntityDal.FindEntity(whereExpressionInventory);

                    if (inventoryModel == null)
                    {
                        return false;
                    }



                    #region 业务处理                   

                    //更新库房容器信息
                    MaterialInventoryEntity models = _MaterialInventoryEntityDal.FindList(p => p.ID == inventoryModel.ID).Result.FirstOrDefault();
                    models.EquipmentId = equipmentId;
                    var inventQty = models.Quantity;

                    #region 判断当前物料是否需要单位转换并改变数量

                    if (isConvert == true)
                    {
                        string unitF = inventoryModel.MaxUnit;

                        if (unitF.Trim() != "KG")
                        {
                            //这里进行查询并转换
                            var uConventList = await _dalUnitConvertEntity.FindList(P => P.MaterialCode == inventoryModel.MaterialCode && P.FormUnitName == unitF && P.ToUnitName == "KG");
                            if (uConventList != null)
                            {
                                var uConvertModel = uConventList.FirstOrDefault();
                                //标准值
                                decimal fValue = uConvertModel.ConvertFormQty.Value;
                                //目标单位
                                decimal toValue = uConvertModel.ConvertToQty.Value;
                                //这里处理数量
                                inventQty = models.Quantity / fValue * toValue;

                                models.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3); //inventQty;//更改数量
                                models.QuantityUomId = kgUnitID;
                            }

                        }
                    }

                    #endregion

                    models.Modify(models.ID, userID);
                    upInvent.Add(models);

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = inventoryModel.LocationF;
                    trans.NewStorageLocation = name;
                    trans.OldLotId = inventoryModel.LotId;
                    trans.NewLotId = inventoryModel.LotId;
                    trans.OldSublotId = inventoryModel.SlotId;
                    trans.NewSublotId = inventoryModel.SlotId;
                    trans.OldExpirationDate = inventoryModel.ExpirationDate;
                    trans.NewExpirationDate = inventoryModel.ExpirationDate;
                    trans.Quantity = Math.Round(Convert.ToDecimal(inventQty), 3); // Convert.ToInt32(inventoryModel.Quantity);
                    trans.QuantityUomId = inventoryModel.QuantityUomId;
                    trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Transfer Inventory";
                    trans.Comment = comment;
                    trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = inventoryModel.EquipmentId;
                    trans.NewEquipmentId = equipmentId;
                    trans.OldContainerId = inventoryModel.ContainerId;
                    trans.NewContainerId = inventoryModel.ContainerId;
                    //status
                    trans.OldMaterialId = inventoryModel.MaterialId;
                    trans.NewMaterialId = inventoryModel.MaterialId;
                    trans.OldLotExternalStatus = inventoryModel.StatusF;
                    trans.OldSublotExternalStatus = inventoryModel.StatusS;
                    trans.NewLotExternalStatus = inventoryModel.StatusF;
                    trans.NewSublotExternalStatus = inventoryModel.StatusS;

                    trans.PhysicalQuantity = inventoryModel.MaxVolume.ToString(); //物理数量
                    trans.TareQuantity = inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value;  //皮数量
                    addTransfer.Add(trans);

                    #endregion

                    #region 修改容器位置
                    if (inventoryModel.ContainerId != null && inventoryModel.ContainerId != "")
                    {
                        //查询之前的位置
                        var whereLocation = Expressionable.Create<ContainerEntity>().And(p => p.ID == inventoryModel.ContainerId).ToExpression();
                        var containerModel = await _dal.FindEntity(whereLocation);

                        //更新容器新的位置
                        ContainerEntity model = await _dal.QueryById(inventoryModel.ContainerId);
                        model.ID = inventoryModel.ContainerId;
                        model.EquipmentId = equipmentId;
                        model.Modify(model.ID, userID);
                        upCon.Add(model);

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);

                        //hisModel.ID = Guid.NewGuid().ToString();
                        //   hisModel
                        hisModel.ContainerId = inventoryModel.ContainerId;
                        hisModel.Type = "";
                        hisModel.EquipmentId = equipmentId;
                        hisModel.EquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        hisModel.State = containerModel.Status;
                        hisModel.Comment = containerModel.Comment;
                        //hisModel.ProductOrderId 工单ID
                        //hisModel.BatchId 工单批次ID
                        hisModel.MaterialId = inventoryModel.MaterialId;
                        hisModel.SublotId = inventoryModel.SubLotId;
                        hisModel.Quantity = inventoryModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventoryModel.QuantityUomId;
                        //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                        //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                        //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                        // hisModel.Status 批次执行状态
                        //hisModel.ContainerCode= containerModel. 容器编号
                        //hisModel.MaterialProducedActualId 物料产出记录ID
                        //hisModel.MaterialConsumedActualId 物料消耗记录ID
                        hisModel.LotId = inventoryModel.LotId;
                        hisModel.ExpirationDate = inventoryModel.ExpirationDate;
                        conHis.Add(hisModel);
                    }


                    #endregion



                    #endregion

                    #endregion

                }
            }

            try
            {
                _unitOfWork.BeginTran();

                bool upResult = await _MaterialInventoryEntityDal.Update(upInvent);
                bool tranHis = await _MaterialTransferEntityDal.Add(addTransfer) > 0;


                bool rqResult = true;
                if (upCon.Count > 0)
                {
                    rqResult = await _dal.Update(upCon);
                }

                bool hisUp = true;
                if (conHis.Count > 0)
                {
                    hisUp = await _ContainerHistoryEntityDal.Add(conHis) > 0;
                }

                if (!upResult || !tranHis || !rqResult || !hisUp)//|| !addResult)
                {
                    _unitOfWork.RollbackTran();

                    return false;
                }
                _unitOfWork.CommitTran();
                return true;
            }
            catch (Exception)
            {
                _unitOfWork.RollbackTran();
                return false;
            }
        }
    }
}