
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.PPM.Model.ViewModels.MKM.View;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using System;
using Npgsql.TypeHandlers;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using InfluxDB.Client.Api.Domain;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Model.Models.Interface;
using System.Diagnostics;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.SIM;
using SEFA.DFM.Model.Models;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using SEFA.Base.Common.LogHelper;

namespace SEFA.PPM.Services
{
	public class CookRecipeViewServices : BaseServices<CookRecipeViewEntity>, ICookRecipeViewServices
	{
		private readonly IBaseRepository<CookRecipeViewEntity> _dal;
		private readonly IBaseRepository<CookLineViewEntity> _CookLineViewEntity;
		private readonly IBaseRepository<CookEquipmentViewEntity> _CookEquipmentViewEntity;
		private readonly IBaseRepository<CookNtgewViewEntity> _CookNtgewViewEntity;
		private readonly IBaseRepository<FillingTimeViewEntity> _FillingTimeViewEntity;
		private readonly IBaseRepository<CookingProgressViewEntity> _CookingProgressViewEntity;
		private readonly IBaseRepository<DFM.Model.Models.EmployeeEntity> _EmployeeEntity;
		private readonly IBaseRepository<InfluxOpcTagEntity> _InfluxOpcTagEntity;
		private readonly IInfluxDbServices _IInfluxDbServices;
		private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _EquipmentEntity;
		private readonly IBaseRepository<PerformanceEntity> _PerformanceEntity;
		public CookRecipeViewServices(IBaseRepository<CookRecipeViewEntity> dal, IBaseRepository<CookLineViewEntity> cookLineViewEntity
			, IBaseRepository<CookEquipmentViewEntity> cookEquipmentViewEntity, IBaseRepository<CookNtgewViewEntity> cookNtgewViewEntity, IBaseRepository<FillingTimeViewEntity> fillingTimeViewEntity, IBaseRepository<CookingProgressViewEntity> cookingProgressViewEntity, IBaseRepository<DFM.Model.Models.EmployeeEntity> employeeEntity, IBaseRepository<InfluxOpcTagEntity> influxOpcTagEntity, IInfluxDbServices iInfluxDbServices, IBaseRepository<DFM.Model.Models.EquipmentEntity> equipmentEntity, IBaseRepository<PerformanceEntity> performanceEntity)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_CookLineViewEntity = cookLineViewEntity;
			_CookEquipmentViewEntity = cookEquipmentViewEntity;
			_CookNtgewViewEntity = cookNtgewViewEntity;
			_FillingTimeViewEntity = fillingTimeViewEntity;
			_CookingProgressViewEntity = cookingProgressViewEntity;
			_EmployeeEntity = employeeEntity;
			_InfluxOpcTagEntity = influxOpcTagEntity;
			_IInfluxDbServices = iInfluxDbServices;
			_EquipmentEntity = equipmentEntity;
			_PerformanceEntity = performanceEntity;
		}

		public async Task<List<CookRecipeViewEntity>> GetList(CookRecipeViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<CookRecipeViewEntity>()
							 .ToExpression();
			var data = await _dal.FindList(whereExpression);
			return data;
		}

		public async Task<PageModel<CookRecipeViewEntity>> GetPageList(CookRecipeViewRequestModel reqModel)
		{
			var whereExpression = Expressionable.Create<CookRecipeViewEntity>()
							 .ToExpression();
			var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

			return data;
		}

		public async Task<bool> SaveForm(CookRecipeViewEntity entity)
		{
			if (string.IsNullOrEmpty(entity.ID))
			{
				return await this.Add(entity) > 0;
			}
			else
			{
				return await this.Update(entity);
			}
		}

		public async Task<List<CookRecipeModel>> GetCookProcessData1()
		{
			List<CookRecipeModel> result = new List<CookRecipeModel>();
			var today = DateTime.Today;

			// 先筛选出当日计划的工单
			var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => x.SapOrderType == "ZXH2" && x.PoStatus != "4" && x.Type == "WorkOrder" && x.PlanDate == today).ToListAsync();

			var orderIds = productionOrders.Select(x => x.ID) ?? new List<string>();

			// 先筛选出batchEntity
			var batchEntities = await _dal.Db.Queryable<BatchEntity>().Where(x => orderIds.Contains(x.ProductionOrderId)).ToListAsync();

			var batchIds = batchEntities.Select(x => x.ID) ?? new List<string>();

			// 先筛选出储罐的设备ID
			var cgEquipmentIds = await _dal.Db.Queryable<FunctionPropertyVEntity>().Where(x => x.PropertyCode == "IsCookTank" && x.PropertyValue == "2").Select(x => x.EquipmentId)?.Distinct()?.ToListAsync() ?? new List<string>();

			// 先筛选出poProducedExecution
			var poProducedExecutions = await _dal.Db.Queryable<PoProducedExecutionEntity>().Where(x => x.Status == "3" && batchIds.Contains(x.BatchId) && cgEquipmentIds.Contains(x.RunEquipmentId)).ToListAsync();

			// 先筛选 ParameterGroupEntity
			var parameterGroupEntities = await _dal.Db.Queryable<ParameterGroupEntity>()
				.Where(pg => pg.GroupName == "出料质检单")
				.ToListAsync();

			var groupIds = parameterGroupEntities.Select(x => x.ID).ToList() ?? new List<string>();

			// 先筛选 LogsheetEntity
			var logsheetEntities = await _dal.Db.Queryable<LogsheetEntity>()
				.Where(l => (l.Status == 1 || l.Status == 2) && groupIds.Contains(l.ParameterGroupId))
				.ToListAsync();

			var logsheetIds = logsheetEntities.Select(x => x.ID) ?? new List<string>();

			// 先筛选 LogsheetDetailEntity
			var logsheetDetailEntities = await _dal.Db.Queryable<LogsheetDetailEntity>()
				.Where(ld => !string.IsNullOrEmpty(ld.Value) &&
							 ld.ParameterName == "通过结果" &&
							 ld.Value == "通过" &&
							 logsheetIds.Contains(ld.LogsheetId))
				.ToListAsync();

			var groups1 = productionOrders.GroupBy(x => new { x.LineCode, x.SapFormula });
			foreach (var item in groups1)
			{
				var cookRecipe = new CookRecipeModel() { LineName = item.Key.LineCode, DescriptionName = item.Key.SapFormula, Yb = 0, Yz = 0, Yg = 0, Total = 0 };
				var orderIds_i = item.Select(x => x.ID).ToList();
				var batchEntities_i = batchEntities.Where(x => orderIds_i.Contains(x.ProductionOrderId)).ToList();
				foreach (var batch in batchEntities_i)
				{
					cookRecipe.Total++;
					if (batch.PrepStatus == "3" || batch.PrepStatus == "4" || batch.PrepStatus == "6" || batch.PrepStatus == "7" || batch.PrepStatus == "8" || batch.PrepStatus == "9")
					{
						cookRecipe.Yb++;
					}
					var logsheetIds_batch = logsheetEntities.Where(x => x.BatchId == batch.ID).Select(x => x.ID).ToList();
					var logsheetDetails = logsheetDetailEntities.Where(x => logsheetIds_batch.Contains(x.LogsheetId)).ToList();
					if (logsheetDetails?.Count > 0)
					{
						cookRecipe.Yz++;
					}
					if (poProducedExecutions.Exists(x => x.BatchId == batch.ID))
					{
						cookRecipe.Yg++;
					}
				}
				result.Add(cookRecipe);
			}
			return result;
		}

		public async Task<List<CookRecipeModel>> GetCookProcessData()
		{
			try
			{
				List<CookRecipeModel> result = new List<CookRecipeModel>();
				var today = DateTime.Today;

				// 定义常量
				const string SapOrderType = "ZXH2";
				const string PoStatus = "4";
				const string WorkOrderType = "WorkOrder";
				const string IsCookTankPropertyValue = "2";
				const string StatusCompleted = "3";
				const string PassedResult = "通过";

				// 先筛选出当日计划的工单
				var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>()
					.Where(x => x.SapOrderType == SapOrderType && x.PoStatus != PoStatus && x.Type == WorkOrderType && x.PlanDate == today)
					.ToListAsync();

				var orderIds = productionOrders.Select(x => x.ID).ToList();

				// 先筛选出batchEntity
				var batchEntities = await _dal.Db.Queryable<BatchEntity>()
					.Where(x => orderIds.Contains(x.ProductionOrderId))
					.ToListAsync();

				var batchIds = batchEntities.Select(x => x.ID).ToList();

				// 先筛选出储罐的设备ID
				var cgEquipmentIds = await _dal.Db.Queryable<FunctionPropertyVEntity>()
					.Where(x => x.PropertyCode == "IsCookTank" && x.PropertyValue == IsCookTankPropertyValue)
					.Select(x => x.EquipmentId)
					.Distinct()
					.ToListAsync();

				// 先筛选出poProducedExecution
				var poProducedExecutions = await _dal.Db.Queryable<PoProducedExecutionEntity>()
					.Where(x => x.Status == StatusCompleted && batchIds.Contains(x.BatchId) && cgEquipmentIds.Contains(x.RunEquipmentId))
					.ToListAsync();

				// 先筛选 ParameterGroupEntity
				var parameterGroupEntities = await _dal.Db.Queryable<ParameterGroupEntity>()
					.Where(pg => pg.GroupName == "出料质检单")
					.ToListAsync();

				var groupIds = parameterGroupEntities.Select(x => x.ID).ToList();

				// 先筛选 LogsheetEntity
				var logsheetEntities = await _dal.Db.Queryable<LogsheetEntity>()
					.Where(l => (l.Status == 1 || l.Status == 2) && groupIds.Contains(l.ParameterGroupId))
					.ToListAsync();

				var logsheetIds = logsheetEntities.Select(x => x.ID).ToList();

				// 先筛选 LogsheetDetailEntity
				var logsheetDetailEntities = await _dal.Db.Queryable<LogsheetDetailEntity>()
					.Where(ld => !string.IsNullOrEmpty(ld.Value) &&
								 ld.ParameterName == "通过结果" &&
								 ld.Value == PassedResult &&
								 logsheetIds.Contains(ld.LogsheetId))
					.ToListAsync();

				// 使用字典来存储 logsheetDetailEntities，以便快速查找
				var logsheetDetailDict = logsheetDetailEntities
					.GroupBy(ld => ld.LogsheetId)
					.ToDictionary(g => g.Key, g => g.ToList());

				var groups = productionOrders.GroupBy(x => new { x.LineCode, x.SapFormula });
				foreach (var item in groups)
				{
					var cookRecipe = new CookRecipeModel()
					{
						LineName = item.Key.LineCode,
						DescriptionName = item.Key.SapFormula,
						Yb = 0,
						Yz = 0,
						Yg = 0,
						Total = 0
					};
					var orderIds_i = item.Select(x => x.ID).ToList();
					var batchEntities_i = batchEntities.Where(x => orderIds_i.Contains(x.ProductionOrderId)).ToList();
					foreach (var batch in batchEntities_i)
					{
						cookRecipe.Total++;
						if (new[] { "3", "4", "6", "7", "8", "9" }.Contains(batch.PrepStatus))
						{
							cookRecipe.Yb++;
						}
						var logsheetIds_batch = logsheetEntities.Where(x => x.BatchId == batch.ID).Select(x => x.ID).ToList();
						foreach (var logsheetId in logsheetIds_batch)
						{
							if (logsheetDetailDict.TryGetValue(logsheetId, out var details) && details.Any())
							{
								cookRecipe.Yz++;
								break;
							}
						}
						if (poProducedExecutions.Any(x => x.BatchId == batch.ID))
						{
							cookRecipe.Yg++;
						}
					}
					result.Add(cookRecipe);
				}

				return result;
			}
			catch (Exception ex)
			{
				// 记录日志或抛出自定义异常
				throw new Exception("获取烹饪过程数据时发生错误", ex);
			}
		}


		/// <summary>
		/// 煮料看板
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<List<CookRecipeModel>>> GetSchedule()
		{
			MessageModel<List<CookRecipeModel>> result = new MessageModel<List<CookRecipeModel>>
			{
				success = false,
				msg = "获取失败！",
			};

			List<CookRecipeModel> entities = new List<CookRecipeModel>();
			try
			{
				SerilogServer.LogDebug($"开始加载数据-GetCookProcessData", "GetScheduleLog");
				var data = await GetCookProcessData();
				SerilogServer.LogDebug($"数据加载完成-GetCookProcessData", "GetScheduleLog");

				var Linedata = await _CookLineViewEntity.FindList(p => p.ID != null);
				var performanceEntities = await (_PerformanceEntity.Db.Queryable<PerformanceEntity>()
						   .Where(x => x.EndTimeUtc == null)
						   .OrderByDescending(x => x.StartTimeUtc)).ToListAsync();
				SerilogServer.LogDebug($"开始加载数据", "GetScheduleLog");
				var lineIds = Linedata.Select(x => x.LineId).Distinct().ToList();
				var ids = Linedata.Select(x => x.ID).Distinct().ToList();
				var recipe1 = await _dal.FindList(p => lineIds.Contains(p.LineId) && ids.Contains(p.ID));
				var data0 = await _FillingTimeViewEntity.FindList(p => lineIds.Contains(p.LineId) && ids.Contains(p.ID));
				var cookEquipmentViews = await _CookEquipmentViewEntity.FindList(p => lineIds.Contains(p.LineId) && ids.Contains(p.ID));
				var equipmentIds = cookEquipmentViews.Select(x => x.RunEquipmentId).Distinct().ToList();
				var tags = await _InfluxOpcTagEntity.FindList(x => equipmentIds.Contains(x.EquipmentId) && x.Name.Contains("状态") || x.Name.Contains("灌装速度"));
				var lineIds2 = recipe1.Select(x => x.FillLineId).Distinct().ToList();
				var gzjList1 = await _EquipmentEntity.FindList(p => p.Level == "Unit" && lineIds2.Contains(p.LineId) && p.Enabled == 1 && p.Deleted == 0 && p.EquipmentCode.Contains("Filler"));
				var equipmentIds2 = gzjList1.Select(x => x.ID).ToList();
				var poProducedExecutions = await _dal.Db.Queryable<PoProducedExecutionEntity>().Where(x => x.Status == "1" && equipmentIds2.Contains(x.RunEquipmentId)).OrderBy(x => x.StartTime).ToListAsync();
				SerilogServer.LogDebug($"数据加载完成", "GetScheduleLog");

				SerilogServer.LogDebug($"循环开始", "GetScheduleLog");
				foreach (var item in Linedata)
				{
					CookRecipeModel entity = new CookRecipeModel();

					#region 储缸工单预计灌装结束时间

					#region 煮缸预计结束时间

					//获取当前时间转换为
					var a = DateTime.Now.ToString("HH:mm");
					TimeSpan timeOfDay = TimeSpan.Parse(a);
					double minutes = timeOfDay.TotalMinutes;
					double minutes2 = 0;
					if (item.LineName != null && item.LineName.Contains("#"))
					{
						//获取产线名截取#号后数据
						item.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1);
					}
					entity.LineName = item.LineName;
					entity.DescriptionName = item.Name + item.Description;//配方名称/配方码

					//根据产线查
					var recipe2 = recipe1.FindAll(p => p.LineId == item.LineId && p.ID == item.ID);
					var recipe3 = recipe2.OrderBy(p => p.StartTime).FirstOrDefault();
					if (recipe3 != null)
					{

						if (recipe3.PlanEndTime != null)
						{
							var plendTime = recipe3.PlanEndTime.Value.ToString("HH:mm");
							TimeSpan planOfDay = TimeSpan.Parse(plendTime);
							minutes2 = planOfDay.TotalMinutes;
							//35=QA质检时间(15min)+buffer时间(20min)
							minutes2 += 35;
							var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes2));
						}
						else
						{

							minutes2 = 0;
							//35=QA质检时间(15min)+buffer时间(20min)
							minutes2 += 35;
							var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes2));
						}

					}

					#endregion

					#region 储缸预计结束时间
					//制造工单的总计划产出/累计制造工单报工产量
					var data1 = data0.FirstOrDefault(p => p.LineId == item.LineId && p.ID == item.ID);
					//关联包装工单SKU的单只产量重量
					//var data2 = await _CookNtgewViewEntity.FindEntity(p => p.LineId == item.LineId);
					decimal Ntgew = 0m;
					var speed = 0;
					// var timeLoss = 2;//默认先给速度赋值后续拿到后进行调整
					EquipmentEntity gzjeqment = null;
					if (recipe3 != null)
					{
						var gzjList = gzjList1.FindAll(p => p.LineId == recipe3.FillLineId);
						gzjeqment = gzjList.FirstOrDefault();
						if (gzjList?.Count > 1)
						{
							var eqIds = gzjList.Select(x => x.ID).ToList();
							var poProducedExecution = poProducedExecutions.FirstOrDefault(x => eqIds.Contains(x.RunEquipmentId));
							if (poProducedExecution != null)
							{
								gzjeqment = gzjList.FirstOrDefault(x => x.ID == poProducedExecution.RunEquipmentId);
							}
						}
						if (gzjeqment != null)
						{
							var opc = tags.FirstOrDefault(p => p.EquipmentId == gzjeqment.ID && p.Name.Contains("灌装速度"));
							if (opc != null)
							{
								try
								{
									//获取点位状态
									speed = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
								}
								catch (Exception)
								{
									speed = 0;
								}
							}
						}
					}

					decimal? endTime = 0m;
					if (speed > 0 && Ntgew > 0)
					{
						endTime = Convert.ToDecimal(minutes) + (data1.PlanQty + data1.MakeQuantity) / (Ntgew * speed);// + timeLoss;

					}
					var StorageEndTime = MinutesToHoursMinutes(Convert.ToInt32(endTime));
					#endregion
					//计算是否预警
					var time = endTime - Convert.ToDecimal(minutes2);
					#endregion
					List<EquipmentList> equipmentLists = new List<EquipmentList>();
					List<StorageEquipmentList> storageEquipmentLists = new List<StorageEquipmentList>();
					var recipe = cookEquipmentViews.FindAll(p => p.LineId == item.LineId && p.ID == item.ID);

					for (int i = 0; i < recipe.Count; i++)
					{
						var Influxdbquantity1 = 1;
						if (recipe[i].PropertyValue == "1")
						{
							EquipmentList list = new EquipmentList();
							list.EquipmentCode = recipe[i].EquipmentCode;
							if (recipe[i] != null)
							{
								var opc = tags.FirstOrDefault(p => p.EquipmentId == recipe[i].RunEquipmentId && p.Name.Contains("状态"));
								if (opc != null)
								{
									try
									{
										//获取点位状态
										Influxdbquantity1 = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
									}
									catch (Exception)
									{
										Influxdbquantity1 = 1;

									}
									//没找到点位则赋值状态为无排产
									switch (Influxdbquantity1)
									{
										case 0:
											list.state = "运行中";
											break;
										case 1:
											list.state = "无排产";
											break;
										case 2:
											list.state = "故障";
											break;
										case 3:
											list.state = "CIP";
											break;
										case 4:
											list.state = "下缸作业";
											break;
										default:
											break;
									}


								}
								else
								{
									list.state = "无排产";
								}
							}
							else
							{
								list.state = "无排产";
							}
							//设备状态后续调整
							//list.PropertyValue = recipe[i].PropertyValue;//1 煮缸 2 储缸
							equipmentLists.Add(list);
						}
						else
						{
							StorageEquipmentList list1 = new StorageEquipmentList();
							list1.EquipmentCode = recipe[i].EquipmentCode;
							if (recipe[i] != null)
							{
								var opc = tags.FirstOrDefault(p => p.EquipmentId == recipe[i].RunEquipmentId && p.Name.Contains("状态"));
								if (opc != null)
								{
									try
									{
										//获取点位状态
										Influxdbquantity1 = (int)Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.Now.AddMinutes(-10), DateTime.Now, null, opc.Tag))?.Value);
									}
									catch (Exception)
									{
										Influxdbquantity1 = 1;

									}
									//没找到点位则赋值状态为无排产
									switch (Influxdbquantity1)
									{
										case 0:
											list1.state = "运行中";
											break;
										case 1:
											list1.state = "无排产";
											break;
										case 2:
											list1.state = "故障";
											break;
										case 3:
											list1.state = "CIP";
											break;
										case 4:
											list1.state = "下缸作业";
											break;
										default:
											break;
									}
								}
								else
								{
									list1.state = "无排产";
								}
							}
							else
							{
								list1.state = "无排产";
							}
							storageEquipmentLists.Add(list1);
						}

					}
					entity.equipmentList = equipmentLists;
					entity.StorageList = storageEquipmentLists;

					if (gzjeqment != null)
					{
						var performanceEntity = performanceEntities.FirstOrDefault(x => x.EquipmentId == gzjeqment.ID);
						string Status = "无排产";
						if (performanceEntity != null)
						{
							switch (performanceEntity.Categroy)
							{
								case "生产运行":
									Status = "运行中";
									break;
								case "计划停机":
									Status = "停机";
									break;
								case "非计划停机":
									Status = "故障";
									break;
								default:
									break;
							}
						}
						entity.EquipmentgzjState = Status;
						if (gzjeqment.EquipmentName.Contains("#"))
						{
							//获取产线名截取#号后数据
							entity.EquipmentgzjName = gzjeqment.EquipmentName.Substring(gzjeqment.EquipmentName.IndexOf('#') + 1);
						}
						else
						{
							entity.EquipmentgzjName = gzjeqment.EquipmentName;
						}
					}
					else
					{
						entity.EquipmentgzjState = "无排产";
						entity.EquipmentgzjName = "";
					}
					entity.Speed = speed.ToString();
					entity.StorageEndTime = StorageEndTime;//储缸结束时间
					entity.IsWarning = time < 0 ? true : false;
					var datas = data.Where(p => p.LineName == item.LineCode && p.DescriptionName == item.ID).FirstOrDefault();
					if (datas != null)
					{
						entity.Yb = datas.Yb;
						entity.Yz = datas.Yz;
						entity.Yg = datas.Yg;
						entity.Total = datas.Total;
					}
					else
					{
						entity.Yb = 0;
						entity.Yz = 0;
						entity.Yg = 0;
						entity.Total = 0;
					}

					entities.Add(entity);
				}
				SerilogServer.LogDebug($"循环结束", "GetScheduleLog");

				result.response = entities;
				result.success = true;
				result.msg = "获取成功！";
				return result;
			}
			catch (Exception ex)
			{
				result.msg = ex.StackTrace;
				return result;
			}
		}
		/*
		 * 
        #region 获取当前执行中的配方/配方编码/储缸工单预计灌装结束时间
        /// <summary>
        /// 获取当前执行中的配方/配方编码-储缸工单预计灌装结束时间
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<CookRecipeModel>> GetRecipe()
        {
            List<CookRecipeModel> recipeModels = new List<CookRecipeModel>();
            //List<CookRecipeViewEntity>  recipes = new List<CookRecipeViewEntity>();
            var data = await _CookLineViewEntity.FindList(p=>p.ID!=null);
            foreach (var item in data)
            {
                CookRecipeModel recipeModel = new CookRecipeModel();
                var recipe = await _dal.FindList(p=>p.LineId==item.LineId);
                var recipe1 = recipe.OrderBy(p=>p.PlanStartTime).FirstOrDefault();
                if (recipe1.LineName.Contains("#"))
                {
                    //获取产线名截取#号后数据
                    recipe1.LineName = recipe1.LineName.Substring(recipe1.LineName.IndexOf('#')+1);
                }
                #region 储缸工单预计灌装结束时间

                #region 煮缸预计结束时间

                //获取当前时间转换为
                var a = DateTime.Now.ToString("HH:mm");
                TimeSpan timeOfDay = TimeSpan.Parse(a);
                double minutes = timeOfDay.TotalMinutes;

                //根据产线查
                var recipe2 = await _dal.FindList(p => p.LineId == item.LineId);
                var recipe3 = recipe2.OrderBy(p => p.PlanStartTime).FirstOrDefault();
                var plendTime = recipe3.PlanEndTime.Value.ToString("HH:mm");
                TimeSpan planOfDay = TimeSpan.Parse(plendTime);
                double minutes2 = planOfDay.TotalMinutes;
                //35=QA质检时间(15min)+buffer时间(20min)
                minutes2 += 35;
                var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes2));
                #endregion

                #region 储缸预计结束时间
                //制造工单的总计划产出/累计制造工单报工产量
                var data1 = await _FillingTimeViewEntity.FindEntity(p => p.LineId == item.LineId);
                //关联包装工单SKU的单只产量重量
                var data2 = await _CookNtgewViewEntity.FindEntity(p => p.LineId == item.LineId);
                decimal Ntgew = 1.2m;
                var speed = 2;//默认先给速度赋值后续拿到后进行调整
                var timeLoss = 2;//默认先给速度赋值后续拿到后进行调整
                var endTime = Convert.ToDecimal(minutes) + (data1.PlanQty + data1.MakeQuantity) / (Ntgew * speed) + timeLoss;
                var StorageEndTime = MinutesToHoursMinutes(Convert.ToInt32(endTime));
                #endregion
                //计算是否预警
                var time = endTime - Convert.ToDecimal(minutes2);
                #endregion
                recipeModel.LineId = recipe1.LineId;//产线ID
                recipeModel.LineName = recipe1.LineName;//产线
                recipeModel.Name = recipe1.Name;//配方名称
                recipeModel.Description = recipe1.Description;//配方编号
                recipeModel.StorageEndTime = StorageEndTime;//储缸结束时间
                //recipeModel.BoilingEndTime = BoilingEndTime;//煮缸结束时间
                recipeModel.IsWarning = time < 0 ? true : false;
                recipeModels.Add(recipeModel);
            }
            recipeModels = recipeModels.OrderBy(p => p.LineName).ToList();
            return recipeModels;
        }
        #endregion

        #region 找煮缸和储缸的设备
        /// <summary>
        /// 找煮缸和储缸的设备
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<CoodEquipmentModel>> GetEquipmentList()
        {
            List<CoodEquipmentModel> equipmentList = new List<CoodEquipmentModel>();
            var data = await _CookLineViewEntity.FindList(p => p.ID != null);
            foreach (var item in data)
            {
                CoodEquipmentModel coodEquipment = new CoodEquipmentModel();
                List<EquipmentList> eqList = new List<EquipmentList>();
                coodEquipment.LineId = item.LineId;
                coodEquipment.LineName = item.LineName.Substring(item.LineName.IndexOf('#') + 1); 
                var recipe = await _CookEquipmentViewEntity.FindList(p => p.LineId == item.LineId);
                for (int i = 0; i < recipe.Count; i++)
                {
                    EquipmentList list = new EquipmentList();
                    list.RunEquipmentId = recipe[i].RunEquipmentId;
                    list.LineId = recipe[i].LineId;
                    list.LineName = recipe[i].LineName;
                    list.EquipmentCode = recipe[i].EquipmentCode;
                    list.state = 1;//设备状态后续调整
                    list.PropertyValue = recipe[i].PropertyValue;//1 煮缸 2 储缸
                    eqList.Add(list);
                }
                coodEquipment.equipmentList= eqList;
                equipmentList.Add(coodEquipment);
            }
            equipmentList = equipmentList.OrderBy(p => p.LineName).ToList();
            return equipmentList;
        }

        #endregion
        */
		/// <summary>
		/// 分钟转换小时
		/// </summary>
		/// <param name="totalMinutes"></param>
		/// <returns></returns>
		public static string MinutesToHoursMinutes(int totalMinutes)
		{
			TimeSpan time = TimeSpan.FromMinutes(totalMinutes);
			return time.ToString(@"hh\:mm");
		}
		/*
       /// <summary>
       /// 储缸工单预计灌装结束时间
       /// </summary>
       /// <param name="reqModel"></param>
       /// <returns></returns>
      public async Task<List<FillingEndTimeModel>> GetFillingEndTime()
       {
           List<FillingEndTimeModel> endTimeList=new List<FillingEndTimeModel> ();
           //获取当前时间转换为
           var a = DateTime.Now.ToString("HH:mm");
           TimeSpan timeOfDay = TimeSpan.Parse(a);
           double minutes = timeOfDay.TotalMinutes;

           //找当天生产的产线
           var data = await _CookLineViewEntity.FindList(p => p.ID != null);
           List<CookRecipeViewEntity> recipes = new List<CookRecipeViewEntity>();
           foreach (var item in data)
           {
               #region 煮缸预计结束时间
               var recipe = await _dal.FindList(p => p.LineId == item.LineId);
               var recipe1 = recipe.OrderBy(p => p.PlanStartTime).FirstOrDefault();
               var plendTime = recipe1.PlanEndTime.Value.ToString("HH:mm");
               TimeSpan planOfDay = TimeSpan.Parse(plendTime);
               double minutes2 = planOfDay.TotalMinutes;
               //35=QA质检时间(15min)+buffer时间(20min)
               minutes2 += 35;
               var BoilingEndTime = MinutesToHoursMinutes(Convert.ToInt32(minutes2));
               #endregion

               FillingEndTimeModel fillingEndTime =new FillingEndTimeModel();

               #region 储缸预计结束时间
               //制造工单的总计划产出/累计制造工单报工产量
               var data1 = await _FillingTimeViewEntity.FindEntity(p => p.LineId== item.LineId);
               //关联包装工单SKU的单只产量重量
               var data2 = await _CookNtgewViewEntity.FindEntity(p => p.LineId == item.LineId);


               decimal Ntgew = 1.2m;
               //获取产线名截取#号后数据
               var str = item.LineName.Substring(item.LineName.IndexOf('#')+1);
               var speed = 2;//默认先给速度赋值后续拿到后进行调整
               var timeLoss = 2;//默认先给速度赋值后续拿到后进行调整
               var endTime = Convert.ToDecimal(minutes) + (data1.PlanQty + data1.MakeQuantity) / (Ntgew * speed) + timeLoss;
               var StorageEndTime = MinutesToHoursMinutes(Convert.ToInt32(endTime));
               #endregion
               //计算是否预警
               var time = endTime - Convert.ToDecimal(minutes2);
               fillingEndTime.LineId = item.LineId;
               fillingEndTime.LineName = str;
               fillingEndTime.LineId = item.LineId;
               fillingEndTime.StorageEndTime = StorageEndTime;//储缸结束时间
               fillingEndTime.BoilingEndTime = BoilingEndTime;//煮缸结束时间
               fillingEndTime.IsWarning = time < 0 ? true : false;
               endTimeList.Add(fillingEndTime);
           }
           endTimeList = endTimeList.OrderBy(p => p.LineName).ToList();
           return endTimeList;
       }*/

	}
}