using System;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using Abp.Extensions;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels.MKM;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using MaterialPropertyValueEntity = SEFA.PPM.Model.Models.MaterialPropertyValueEntity;
using UnitmanageEntity = SEFA.PPM.Model.Models.UnitmanageEntity;

namespace SEFA.MKM.Services
{
    public class BBatchDetailIiViewServices : BaseServices<BBatchDetailIiViewEntity>, IBBatchDetailIiViewServices
    {
        private readonly IBaseRepository<BBatchDetailIiViewEntity> _dal;
        private readonly IBaseRepository<ClassEntity> _classDal;
        private readonly IBaseRepository<MMaterialPropertyViewEntity> _materialPropertyDal;

        public BBatchDetailIiViewServices(IBaseRepository<BBatchDetailIiViewEntity> dal,
            IBaseRepository<ClassEntity> classDal, IBaseRepository<MMaterialPropertyViewEntity> materialPropertyDal)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._classDal = classDal;
            this._materialPropertyDal = materialPropertyDal;
        }

        #region 拼锅

        #endregion


        public async Task<List<BBatchDetailIiViewEntity>> GetList(BBatchDetailIiViewRequestModel reqModel)
        {
            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailIiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        public async Task<List<BBatchDetailIiViewEntity>> GetListByBatchID(string batchID)
        {
            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailIiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 获取对应批次的备料明细列表
        /// </summary>
        /// <param name="batchID"></param>
        /// <returns></returns>
        public async Task<List<BBatchDetailIiViewEntity>> GetBatchPreparationDetailList(string batchID,
            string eqpmentID)
        {
            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            try
            {
                List<BatchConsumeRequirementSummary> batchPreparationDetailList = await _dal.Db
                    .Queryable<BatchConsumeRequirementEntity, MaterialInventoryEntity, PoConsumeRequirementEntity,
                        BatchEntity, UnitmanageEntity, ProductionOrderEntity, MaterialEntity>((t1, t2, t3, t4, t5, t6, t7)
                        => new object[]
                        {
                            JoinType.Left, t1.ID == t2.BatchConsumeRequirementId,
                            JoinType.Left, t1.PoConsumeRequirementId == t3.ID,
                            JoinType.Left, t1.BatchId == t4.ID,
                            JoinType.Left, t3.UnitId == t5.ID,
                            JoinType.Left, t4.ProductionOrderId == t6.ID,
                            JoinType.Left, t3.MaterialId == t7.ID
                        }
                    ).Where((t1, t2, t3, t4, t5, t6, t7)
                        => t1.BatchId == batchID && t3.CurrentFlag == "1")
                    .GroupBy((t1, t2, t3, t4, t5, t6, t7) => new
                    {
                        t1.ID,
                        t3.MaterialId,
                        t7.Code,
                        t7.NAME,
                        t5.Shortname,
                        t4.Number,
                        t3.WeighingQty,
                        t2.InventoryType,
                        t1.BatchId,
                        t4.ProductionOrderId,
                        t6.ProductionOrderNo,
                    })
                    .Select((t1, t2, t3, t4, t5, t6, t7) => new BatchConsumeRequirementSummary
                    {
                        BatchConsumeRequirementId = t1.ID,
                        MaterialId = t3.MaterialId,
                        MaterialCode = t7.Code,
                        MaterialDescription = t7.NAME,
                        UnitId = t5.Shortname,
                        Number = t4.Number,
                        ChangeUnit = t5.Shortname,
                        WeighingQty = t3.WeighingQty,
                        InventoryType = t2.InventoryType,
                        BatchId = t1.BatchId,
                        ProductionOrderId = t4.ProductionOrderId,
                        ProductionOrderNo = t6.ProductionOrderNo,
                        PrepareCount = SqlFunc.AggregateCount(t2.InventoryType),
                        PreparedWeight = SqlFunc.AggregateSum(t2.Quantity)
                    }).ToListAsync();

                // 转换并去重
                result = batchPreparationDetailList.Select(x => new BBatchDetailIiViewEntity
                    {
                        // 映射字段（根据实际业务需求调整）
                        ID = x.ProductionOrderId,
                        MCode = x.MaterialCode,
                        MName = x.MaterialDescription,
                        MQuantityunit = x.UnitId,
                        MaterialUnit1 = x.UnitId,
                        QuantityTotalUnit = x.UnitId,
                        TagpSUnit = x.UnitId,
                        MQuantityTotal = x.WeighingQty ?? 0m,
                        ProductionOrderNo = x.ProductionOrderNo,
                        BatchId = x.BatchId,
                        ProductionOrderId = x.ProductionOrderId,
                        MaterialId = x.MaterialId,
                        BatchConsumeRequirementId = x.BatchConsumeRequirementId,
                    })
                    .GroupBy(x => x.BatchConsumeRequirementId) // 根据需求确定distinct逻辑
                    .Select(g => g.First())
                    .ToList();

                #region 获取批次的物料属性

                var materialIdList = batchPreparationDetailList.Select(x => x.MaterialId).Distinct().ToList();
                List<MaterialPropertyValueEntity> materialPropertyList = await _materialPropertyDal.Db
                    .Queryable<MaterialPropertyValueEntity>()
                    .Where(a => materialIdList.Contains(a.MaterialId))
                    .ToListAsync();

                List<PropertyEntity> propertyList = await _classDal.Db
                    .Queryable<PropertyEntity, ClassEntity>((t1, t2) => new object[]
                    {
                        JoinType.Left, t1.ClassId == t2.ID,
                    })
                    .Where((t1, t2) => t2.ClassCode == "MaterialClass")
                    .ToListAsync();

                #endregion

                #region 完善其他栏位数据

                foreach (var bBatchDetailIiViewEntity in result)
                {
                    List<MaterialPropertyValueEntity> materialPropertyValueList =
                        materialPropertyList.Where(a => a.MaterialId == bBatchDetailIiViewEntity.MaterialId).ToList();

                    #region 更新上下限

                    var maxPercentStr = GetMaterialPropertyValue(bBatchDetailIiViewEntity.MaterialId,
                        MaterialPreparationViewServices.PREWEIGH_TOLERANCE_MAX_PERCENT,
                        materialPropertyValueList,
                        propertyList);

                    // 转换为小数百分比（10% -> 0.1m）
                    var PreweighToleranceMaxPercent = decimal.Parse(maxPercentStr) / 100m;

                    // 后续运算示例：
                    bBatchDetailIiViewEntity.MaxPvalue =
                        bBatchDetailIiViewEntity.MQuantityTotal * (1 + PreweighToleranceMaxPercent);
                    bBatchDetailIiViewEntity.SetMax = int.Parse(maxPercentStr);

                    // 获取原始百分比字符串值
                    var minPercentStr = GetMaterialPropertyValue(bBatchDetailIiViewEntity.MaterialId,
                        MaterialPreparationViewServices.PREWEIGH_TOLERANCE_MIN_PERCENT,
                        materialPropertyValueList,
                        propertyList);

                    // 转换为小数百分比（10% -> 0.1m）
                    var PreweighToleranceMinPercent = decimal.Parse(minPercentStr) / 100m;

                    // 后续运算示例：
                    bBatchDetailIiViewEntity.MinPvalue =
                        bBatchDetailIiViewEntity.MQuantityTotal * (1 - PreweighToleranceMinPercent);
                    bBatchDetailIiViewEntity.SetMin = int.Parse(minPercentStr);

                    #endregion

                    #region 获取整袋完成备料和分包完成备料数量

                    BatchConsumeRequirementSummary fullPreparationInfo =
                        batchPreparationDetailList.FirstOrDefault(x =>
                            x.BatchConsumeRequirementId == bBatchDetailIiViewEntity.BatchConsumeRequirementId &&
                            x.MaterialId == bBatchDetailIiViewEntity.MaterialId &&
                            x.InventoryType == "Full");

                    BatchConsumeRequirementSummary partialPreparationInfo =
                        batchPreparationDetailList.FirstOrDefault(x =>
                            x.BatchConsumeRequirementId == bBatchDetailIiViewEntity.BatchConsumeRequirementId &&
                            x.MaterialId == bBatchDetailIiViewEntity.MaterialId &&
                            x.InventoryType == "Partial");

                    bBatchDetailIiViewEntity.FullFinish = Convert.ToInt32(fullPreparationInfo?.PreparedWeight ?? 0m);

                    bBatchDetailIiViewEntity.TagpS = partialPreparationInfo?.PreparedWeight ?? 0m;

                    //计算批次已完成的总重量
                    bBatchDetailIiViewEntity.MQuantity =
                        (fullPreparationInfo?.PreparedWeight ?? 0m) + (partialPreparationInfo?.PreparedWeight ?? 0m);

                    #endregion

                    bBatchDetailIiViewEntity.CompleteStates = "NG";
                    if (bBatchDetailIiViewEntity.MQuantity >= bBatchDetailIiViewEntity.MinPvalue &&
                        bBatchDetailIiViewEntity.MQuantity <= bBatchDetailIiViewEntity.MaxPvalue)
                    {
                        bBatchDetailIiViewEntity.CompleteStates = "OK";
                    }

                    var fullBagSizeStr = GetMaterialPropertyValue(bBatchDetailIiViewEntity.MaterialId,
                        MaterialPreparationViewServices.FULL_BAG_WEIGHT, materialPropertyList, propertyList);
                    bBatchDetailIiViewEntity.BagSize = fullBagSizeStr;

                    bBatchDetailIiViewEntity.ParitialPage = (bBatchDetailIiViewEntity.MQuantityTotal %
                                                             decimal.Parse(bBatchDetailIiViewEntity.BagSize))
                        .ToString();


                    bBatchDetailIiViewEntity.BagS = Convert.ToInt32(bBatchDetailIiViewEntity.FullFinish /
                                                                    decimal.Parse(bBatchDetailIiViewEntity.BagSize));

                    var fullPage = Convert.ToInt32(bBatchDetailIiViewEntity.MQuantityTotal /
                                                   Convert.ToDecimal(bBatchDetailIiViewEntity.BagSize)).ToString() +
                                   "/" +
                                   bBatchDetailIiViewEntity.BagS;

                    bBatchDetailIiViewEntity.FullPage = fullPage;
                }

                #endregion
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                throw;
            }

            return result;
        }


        /// <summary>
        /// 根据物料ID、属性编码获取属性值
        /// </summary>
        /// <param name="materialId"></param>
        /// <param name="propertyCode"></param>
        /// <param name="materialPropertyList"></param>
        /// <param name="propertyList"></param>
        /// <returns></returns>
        private string GetMaterialPropertyValue(string materialId, string propertyCode,
            List<MaterialPropertyValueEntity> materialPropertyList,
            List<PropertyEntity> propertyList)
        {
            // 优先从物料属性列表获取
            var materialProperty = materialPropertyList.FirstOrDefault(p =>
                p.MaterialId == materialId &&
                p.PropertyCode == propertyCode);

            if (materialProperty != null)
            {
                return materialProperty.PropertyValue;
            }

            // 如果物料属性不存在，从属性主表获取默认值
            var defaultProperty = propertyList.FirstOrDefault(p =>
                p.PropertyCode == propertyCode);

            return defaultProperty?.DefaultValue;
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByBatchIDS(string[] batchIDS, int pageIndex,
            int pageSize)
        {
            PageModel<BBatchDetailIiViewEntity> result = new PageModel<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>().And(p => p.BatchId.IsIn(batchIDS))
                .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailIiViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(pageIndex, pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }


        public async Task<PageModel<BBatchDetailIiViewEntity>> GetPageList(BBatchDetailIiViewRequestModel reqModel)
        {
            PageModel<BBatchDetailIiViewEntity> result = new PageModel<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailIiViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BBatchDetailIiViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}