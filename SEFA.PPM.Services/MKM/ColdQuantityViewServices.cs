
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.PPM.Model.ViewModels.MKM.View;

namespace SEFA.PPM.Services
{
    public class ColdQuantityViewServices : BaseServices<ColdQuantityViewEntity>, IColdQuantityViewServices
    {
        private readonly IBaseRepository<ColdQuantityViewEntity> _dal;
        public ColdQuantityViewServices(IBaseRepository<ColdQuantityViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
        /// <summary>
        /// 喉头仓未过期库存
        /// </summary>
        /// <returns></returns>
        public async Task<ColdQuantityModel> GetList(ColdQuantityViewRequestModel reqModel)
        {
            ColdQuantityModel model = new ColdQuantityModel();
            var whereExpression = Expressionable.Create<ColdQuantityViewEntity>()
                .And(p=> p.EquipmentCode== reqModel.EquipmentCode)
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
           var Quantity= data.Sum(p => p.Quantity);
           var unit= data.Where(p=>p.Unit!=null).FirstOrDefault();

            if (data.Count>0)
            {
                model.InventoryQuantity = Quantity;
                model.Unit = unit.Unit;
            }
            return model;
        }

        public async Task<PageModel<ColdQuantityViewEntity>> GetPageList(ColdQuantityViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ColdQuantityViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(ColdQuantityViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}