
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class BaseUniqueNumberServices : BaseServices<BaseUniqueNumberEntity>, IBaseUniqueNumberServices
    {
        private readonly IBaseRepository<BaseUniqueNumberEntity> _dal;
        public BaseUniqueNumberServices(IBaseRepository<BaseUniqueNumberEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BaseUniqueNumberEntity>> GetList(BaseUniqueNumberRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BaseUniqueNumberEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BaseUniqueNumberEntity>> GetPageList(BaseUniqueNumberRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BaseUniqueNumberEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BaseUniqueNumberEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}