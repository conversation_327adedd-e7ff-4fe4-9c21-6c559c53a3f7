
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;

namespace SEFA.PPM.Services
{
    public class DayPwoProgressViewServices : BaseServices<DayPwoProgressViewEntity>, IDayPwoProgressViewServices
    {
        private readonly IBaseRepository<DayPwoProgressViewEntity> _dal;
        public DayPwoProgressViewServices(IBaseRepository<DayPwoProgressViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<DayPwoProgressViewEntity>> GetList(DayPwoProgressViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DayPwoProgressViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<bool> SaveForm(DayPwoProgressViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}