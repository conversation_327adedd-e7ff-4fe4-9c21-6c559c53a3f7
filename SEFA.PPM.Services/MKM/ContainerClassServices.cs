
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.MKM.Services
{
    public class ContainerClassServices : BaseServices<ContainerClassEntity>, IContainerClassServices
    {
        private readonly IBaseRepository<ContainerClassEntity> _dal;
        public ContainerClassServices(IBaseRepository<ContainerClassEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}