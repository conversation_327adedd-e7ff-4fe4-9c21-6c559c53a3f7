
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;

namespace SEFA.PPM.Services
{
    public class CategoryServices : BaseServices<CategoryEntity>, ICategoryServices
    {
        private readonly IBaseRepository<CategoryEntity> _dal;
        public CategoryServices(IBaseRepository<CategoryEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<CategoryEntity>> GetList(CategoryRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CategoryEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<CategoryEntity>> GetPageList(CategoryRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CategoryEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(CategoryEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}