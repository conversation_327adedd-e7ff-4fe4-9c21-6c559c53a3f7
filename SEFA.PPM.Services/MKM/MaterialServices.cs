
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Services
{
    public class MaterialServices : BaseServices<MaterialEntity>, IMaterialServices
    {
        private readonly IBaseRepository<MaterialEntity> _dal;
        public MaterialServices(IBaseRepository<MaterialEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MaterialEntity>> GetList(MaterialRequestModel reqModel)
        {
            List<MaterialEntity> result = new List<MaterialEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<MaterialEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<MaterialEntity>> GetPageList(MaterialRequestModel reqModel)
        {
            PageModel<MaterialEntity> result = new PageModel<MaterialEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<MaterialEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(MaterialEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}