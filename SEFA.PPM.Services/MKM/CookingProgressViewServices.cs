
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.PPM.Services
{
    public class CookingProgressViewServices : BaseServices<CookingProgressViewEntity>, ICookingProgressViewServices
    {
        private readonly IBaseRepository<CookingProgressViewEntity> _dal;
        public CookingProgressViewServices(IBaseRepository<CookingProgressViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
/*        /// <summary>
        /// 煮料看板进度对照
        /// </summary>
        /// <returns></returns>
        public async Task<List<CookingProgressViewEntity>> GetSchedule()
        {
            List<CookingProgressViewEntity> entities=new List<CookingProgressViewEntity>();
            CookingProgressViewEntity entity=new CookingProgressViewEntity();
            var whereExpression = Expressionable.Create<CookingProgressViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            foreach (var item in data) 
            {
                entity.Yb = item.Yb;
                entity.Yz = item.Yz;
                entity.Yg = item.Yg;
                entity.Total = item.Total;
                entities.Add(entity);
            }
            entities = entities.OrderBy(p => p.LineName).ToList();
            return entities;
        }*/

        public async Task<PageModel<CookingProgressViewEntity>> GetPageList(CookingProgressViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CookingProgressViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(CookingProgressViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}