
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.PPM.Services
{
    public class BBdetailIiViewServices : BaseServices<BBdetailIiViewEntity>, IBBdetailIiViewServices
    {
        private readonly IBaseRepository<BBdetailIiViewEntity> _dal;
        public BBdetailIiViewServices(IBaseRepository<BBdetailIiViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BBdetailIiViewEntity>> GetList(BBdetailIiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBdetailIiViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BBdetailIiViewEntity>> GetPageList(BBdetailIiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBdetailIiViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BBdetailIiViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}