
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class BBatchDetailViewServices : BaseServices<BBatchDetailViewEntity>, IBBatchDetailViewServices
    {
        private readonly IBaseRepository<BBatchDetailViewEntity> _dal;
        public BBatchDetailViewServices(IBaseRepository<BBatchDetailViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BBatchDetailViewEntity>> GetList(BBatchDetailViewRequestModel reqModel)
        {
            List<BBatchDetailViewEntity> result = new List<BBatchDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        public async Task<List<BBatchDetailViewEntity>> GetListByProOrderID(string proOrderId)
        {
            List<BBatchDetailViewEntity> result = new List<BBatchDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailViewEntity>()
                .AndIF(!string.IsNullOrEmpty(proOrderId), a => a.ProductionOrderId == proOrderId)
                             .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailViewEntity>()
                .Where(whereExpression).OrderBy(p => p.BatchNumber).ToListAsync();
            return data;
        }

        public async Task<PageModel<BBatchDetailViewEntity>> GetPageList(BBatchDetailViewRequestModel reqModel)
        {
            PageModel<BBatchDetailViewEntity> result = new PageModel<BBatchDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BBatchDetailViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}