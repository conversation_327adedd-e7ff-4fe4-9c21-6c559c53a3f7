
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using System.Linq;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Remotion.Linq.Parsing.Structure.IntermediateModel;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Services
{
    public class TransferHistoryViewServices : BaseServices<TransferHistoryViewEntity>, ITransferHistoryViewServices
    {
        private readonly IBaseRepository<TransferHistoryViewEntity> _dal;
        private readonly IBaseRepository<MaterialTransferEntity> _daMaterialTransferEntityl;
        private readonly IBaseRepository<MaterialEntity> _dalMaterialEntity;
        private readonly IBaseRepository<MaterialLotEntity> _dalMaterialLotEntity;
        private readonly IBaseRepository<MaterialSubLotEntity> _dalMaterialSubLotEntity;
        private readonly IBaseRepository<EquipmentEntity> _dalEquipmentEntity;

        private readonly IBaseRepository<DFM.Model.Models.EquipmentRequirementEntity> _dalEquipmentRequirementEntity;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentStorageEntity> _dalEquipmentStorageEntity;

        private readonly IBaseRepository<PPM.Model.Models.ProductionOrderEntity> _dalProductionOrderEntity;

        public TransferHistoryViewServices(IBaseRepository<TransferHistoryViewEntity> dal, IBaseRepository<MaterialTransferEntity> daMaterialTransferEntityl = null, IBaseRepository<MaterialEntity> dalMaterialEntity = null, IBaseRepository<MaterialLotEntity> dalMaterialLotEntity = null, IBaseRepository<MaterialSubLotEntity> dalMaterialSubLotEntity = null, IBaseRepository<EquipmentEntity> dalEquipmentEntity = null, IBaseRepository<PPM.Model.Models.ProductionOrderEntity> dalProductionOrderEntity = null, IBaseRepository<DFM.Model.Models.EquipmentRequirementEntity> dalEquipmentRequirementEntity = null, IBaseRepository<DFM.Model.Models.EquipmentStorageEntity> dalEquipmentStorageEntity = null)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _daMaterialTransferEntityl = daMaterialTransferEntityl;
            _dalMaterialEntity = dalMaterialEntity;
            _dalMaterialLotEntity = dalMaterialLotEntity;
            _dalMaterialSubLotEntity = dalMaterialSubLotEntity;
            _dalEquipmentEntity = dalEquipmentEntity;
            _dalProductionOrderEntity = dalProductionOrderEntity;
            _dalEquipmentRequirementEntity = dalEquipmentRequirementEntity;
            _dalEquipmentStorageEntity = dalEquipmentStorageEntity;
        }

        public async Task<List<TransferHistoryViewEntity>> GetList(TransferHistoryViewRequestModel reqModel)
        {
            List<TransferHistoryViewEntity> result = new List<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        //public async Task<PageModel<TransferHistoryViewEntity>> GetPageListWhere(string whereStr)
        //{
        //    PageModel<TransferHistoryViewEntity> result = new PageModel<TransferHistoryViewEntity>();
        //    RefAsync<int> dataCount = 0;
        //    var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
        //        //加入查询条件(时间)
        //        //.AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
        //        //.AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.EndTime))
        //        //原物料数据
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.OldMaterialName.Contains(whereStr)
        //                                           || a.OldMaterialCode.Contains(whereStr))
        //        //新物料数据
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.NewMaterialName.Contains(whereStr)
        //                                           || a.NewMaterialCode.Contains(whereStr))
        //        //老批次信息
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.OldLotId.Contains(whereStr))
        //        //新批次信息
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.NewLotId.Contains(whereStr))
        //        //老子批次
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.OldSubLotId.Contains(whereStr))
        //        //新批子批次
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.NewSubLotId.Contains(whereStr))
        //        //source
        //        .AndIF(!string.IsNullOrEmpty(whereStr), a => a.OldEquipmentId.Contains(whereStr))
        //         //source bin     
        //         .AndIF(!string.IsNullOrEmpty(whereStr), a => a.NewEquipmentId.Contains(whereStr))
        //         //Destination
        //         .AndIF(!string.IsNullOrEmpty(whereStr), a => a.OldEquipmentRequirementId.Contains(whereStr))
        //         //Destination bin     
        //         .AndIF(!string.IsNullOrEmpty(whereStr), a => a.NewEquipmentRequirementId.Contains(whereStr))
        //                     .ToExpression();

        //    var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
        //        .Where(whereExpression)
        //        .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
        //    result.dataCount = dataCount;
        //    result.data = data;
        //    return result;
        //}

        public async Task<List<TransferHistoryViewEntity>> GetSapData(TransferHistoryViewRequestModel reqModel)
        {




            List<TransferHistoryViewEntity> result = new List<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
                 .AndIF(!string.IsNullOrEmpty(reqModel.SapNo), a => a.SapPrintno == reqModel.SapNo)
                             .ToExpression();
            var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
                .Where(whereExpression).ToListAsync();


            List<TransferHistoryViewEntity> resultData = (from a in data
                                                          group a by new
                                                          {
                                                              OldLotId = a.OldLotId,
                                                              OldMaterialCode = a.OldMaterialCode,
                                                              OldMaterialName = a.OldMaterialName,
                                                              OldEcode = a.OldEcode,
                                                              HUnit = a.HUnit,
                                                              Suppiername = a.Suppiername,
                                                              SapPrintno = a.SapPrintno,
                                                              WRemark = a.WRemark,
                                                              WmsPrintno = a.WmsPrintno,
                                                              NewEcode = a.NewEcode,
                                                          } into g
                                                          select new TransferHistoryViewEntity
                                                          {
                                                              OldLotId = g.Key.OldLotId,
                                                              OldMaterialCode = g.Key.OldMaterialCode,
                                                              OldMaterialName = g.Key.OldMaterialName,
                                                              OldEcode = g.Key.OldEcode,
                                                              HUnit = g.Key.HUnit,
                                                              Suppiername = g.Key.Suppiername,
                                                              SapPrintno = g.Key.SapPrintno,
                                                              WRemark = g.Key.WRemark,
                                                              WmsPrintno = g.Key.WmsPrintno,
                                                              NewEcode = g.Key.NewEcode,
                                                              Quantity = g.Sum(p => p.Quantity)
                                                          }).ToList();
            //return result;


            return resultData;
        }

        public async Task<PageModel<TransferHistoryViewEntity>> GetPageList(TransferHistoryViewRequestModel reqModel)
        {
            PageModel<TransferHistoryViewEntity> result = new PageModel<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;

            string Key = string.Empty;

            //查询分为快速查询和普通查询（普通查询全集匹配，快速查询模糊匹配只匹配物料和追溯码）
            if (!string.IsNullOrEmpty(reqModel.Key))
            {
                List<string> materialList = new List<string>();
                var rData = await _dalMaterialEntity.FindList(p => p.Code.Contains(reqModel.Key));
                if (rData != null && rData.Count > 0)
                {
                    materialList = rData.Select(p => p.ID).ToList();
                }

                List<string> batchList = new List<string>();
                var rData1 = await _dalMaterialLotEntity.FindList(p => p.LotId.Contains(reqModel.Key));
                if (rData1 != null && rData1.Count > 0)
                {
                    batchList = rData1.Select(p => p.ID).ToList();
                }

                List<string> SubLotList = new List<string>();
                var subMode = await _dalMaterialSubLotEntity.FindList(p => p.SubLotId.Contains(reqModel.Key));
                if (subMode != null && subMode.Count > 0)
                {
                    SubLotList = subMode.Select(p => p.ID).ToList();
                }

                if (materialList.Count <= 0 && batchList.Count <= 0 && SubLotList.Count <= 0)
                {
                    result.dataCount = 0;
                    result.data = new List<TransferHistoryViewEntity>();
                    return result;
                }


                //筛选数据集
                var whereExpressionKey = Expressionable.Create<MaterialTransferEntity>()
                      //容器NAME 
                      .OrIF(materialList.Count > 0, a => materialList.Contains(a.OldMaterialId) || materialList.Contains(a.NewMaterialId))
                      .OrIF(batchList.Count > 0, a => batchList.Contains(a.OldLotId) || batchList.Contains(a.NewLotId))
                       .OrIF(SubLotList.Count > 0, a => SubLotList.Contains(a.OldSublotId) || SubLotList.Contains(a.NewSublotId))
                       .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                        .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                 .ToExpression();

                var rDataList = await _daMaterialTransferEntityl.Db.Queryable<MaterialTransferEntity>()
                    .Where(whereExpressionKey).OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                result.dataCount = dataCount;

                List<string> tIDS = new List<string>();
                tIDS = rDataList.Select(P => P.ID).Distinct().ToList();
                if (tIDS.Count > 0)
                {
                    var rdata = await _dal.FindList(P => tIDS.Contains(P.ID));
                    result.data = rdata;
                }
                else
                {

                    result.data = new List<TransferHistoryViewEntity>();
                }

                return result;

            }
            else
            {
                #region 查询数据(拿不为空的条件)

                string StartTime = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.StartTime))
                {
                    StartTime = reqModel.StartTime;
                }

                string EndTime = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.EndTime))
                {
                    EndTime = reqModel.EndTime;
                }

                //物料
                List<string> SourceMaterialList = new List<string>();
                string SourceMaterial = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.SourceMaterial))
                {
                    SourceMaterial = reqModel.SourceMaterial;
                    var rData = await _dalMaterialEntity.FindList(p => p.Code == SourceMaterial);
                    if (rData != null && rData.Count > 0)
                    {
                        SourceMaterialList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                List<string> DestinationMaterialList = new List<string>();
                string DestinationMaterial = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.DestinationMaterial))
                {
                    DestinationMaterial = reqModel.DestinationMaterial;
                    var rData = await _dalMaterialEntity.FindList(p => p.Code == DestinationMaterial);
                    if (rData != null && rData.Count > 0)
                    {
                        DestinationMaterialList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //库存批次
                string OldBatch = string.Empty;
                List<string> OldBatchList = new List<string>();
                if (!string.IsNullOrEmpty(reqModel.OldBatch))
                {
                    OldBatch = reqModel.OldBatch;
                    var rData = await _dalMaterialLotEntity.FindList(p => p.LotId == OldBatch);
                    if (rData != null && rData.Count > 0)
                    {
                        OldBatchList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                List<string> NewBatchList = new List<string>();
                string NewBatch = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.NewBatch))
                {
                    NewBatch = reqModel.NewBatch;
                    var rData = await _dalMaterialLotEntity.FindList(p => p.LotId == NewBatch);
                    if (rData != null && rData.Count > 0)
                    {
                        NewBatchList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //子批次
                string OLD_SUB_LOT_ID = string.Empty;
                List<string> oldSubLotList = new List<string>();
                if (!string.IsNullOrEmpty(reqModel.OLD_SUB_LOT_ID))
                {
                    OLD_SUB_LOT_ID = reqModel.OLD_SUB_LOT_ID;
                    var rData = await _dalMaterialSubLotEntity.FindList(p => p.SubLotId == OLD_SUB_LOT_ID);
                    if (rData != null && rData.Count > 0)
                    {
                        oldSubLotList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                string NEW_SUB_LOT_ID = string.Empty;
                List<string> NewSubLotList = new List<string>();
                if (!string.IsNullOrEmpty(reqModel.NEW_SUB_LOT_ID))
                {
                    NEW_SUB_LOT_ID = reqModel.NEW_SUB_LOT_ID;
                    var rData = await _dalMaterialSubLotEntity.FindList(p => p.SubLotId == NEW_SUB_LOT_ID);
                    if (rData != null && rData.Count > 0)
                    {
                        NewSubLotList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //
                string OldSoureID = string.Empty;//code
                List<string> OldSoureIDList = new List<string>();
                if (!string.IsNullOrEmpty(reqModel.OldSoureID))
                {
                    OldSoureID = reqModel.OldSoureID;

                    var rData = await _dalEquipmentEntity.FindList(p => p.EquipmentCode == OldSoureID);
                    if (rData != null && rData.Count > 0)
                    {
                        OldSoureIDList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //用原来的数据ID O LD_EQUIPMENT_REQUIREMENT_ID
                string OldSoureBin = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.OldSoureBin))
                {
                    OldSoureBin = reqModel.OldSoureBin;
                    var rData = await _dalEquipmentRequirementEntity.FindList(p => p.ID == OldSoureBin);
                    if (rData != null && rData.Count > 0)
                    {
                        var rIDS = rData.Select(p => p.ID).ToList();
                        var eStore = await _dalEquipmentStorageEntity.FindList(P => rIDS.Contains(P.EquipmentRequirementId));

                        if (eStore != null && eStore.Count > 0)
                        {
                            var SIDS = eStore.Select(p => p.EquipmentId).ToList();
                            var eList = await _dalEquipmentEntity.FindList(P => SIDS.Contains(P.ID));

                            if (eList != null && eList.Count > 0) 
                            {
                                var eListIDS = eList.Select(p => p.ID).ToList();
                               OldSoureIDList.AddRange(eListIDS);
                               OldSoureIDList= OldSoureIDList.Distinct().ToList();
                            }
                        }
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }

                }

                string NewDestinationID = string.Empty;
                List<string> NewDestinationIDList = new List<string>();
                if (!string.IsNullOrEmpty(reqModel.NewDestinationID))
                {
                    NewDestinationID = reqModel.NewDestinationID;

                    var rData = await _dalEquipmentEntity.FindList(p => p.EquipmentCode == NewDestinationID);
                    if (rData != null && rData.Count > 0)
                    {
                        NewDestinationIDList = rData.Select(p => p.ID).ToList();
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //用原来的数据ID NEW_EQUIPMENT_REQUIREMENT_ID
                string NewDestinationBin = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.NewDestinationBin))
                {
                    NewDestinationBin = reqModel.NewDestinationBin;

                    var rData = await _dalEquipmentRequirementEntity.FindList(p => p.ID == NewDestinationBin);
                    if (rData != null && rData.Count > 0)
                    {
                        var rIDS = rData.Select(p => p.ID).ToList();
                        var eStore = await _dalEquipmentStorageEntity.FindList(P => rIDS.Contains(P.EquipmentRequirementId));

                        if (eStore != null && eStore.Count > 0)
                        {
                            var SIDS = eStore.Select(p => p.EquipmentId).ToList();
                            var eList = await _dalEquipmentEntity.FindList(P => SIDS.Contains(P.ID));

                            if (eList != null && eList.Count > 0)
                            {
                                var eListIDS = eList.Select(p => p.ID).ToList();
                                NewDestinationIDList.AddRange(eListIDS);
                                NewDestinationIDList = NewDestinationIDList.Distinct().ToList();
                            }
                        }
                    }
                    else
                    {
                        result.dataCount = 0;
                        result.data = new List<TransferHistoryViewEntity>();
                        return result;
                    }
                }

                //string MaterialID = string.Empty;
                //if (!string.IsNullOrEmpty(reqModel.MaterialID))
                //{
                //    MaterialID = reqModel.MaterialID;
                //}

                //工单
                //string OldProBatch = string.Empty;
                //List<string> oldProBatchList = new List<string>();
                //if (!string.IsNullOrEmpty(reqModel.OldProBatch))
                //{

                //    OldProBatch = reqModel.OldProBatch;

                //    var rData = await _dalProductionOrderEntity.FindList(p => p.ProductionOrderNo.Contains(OldProBatch));
                //    if (rData != null && rData.Count > 0)
                //    {
                //        oldProBatchList = rData.Select(p => p.ID).ToList();
                //    }
                //}
                //COMMENT
                string Types = string.Empty;
                if (!string.IsNullOrEmpty(reqModel.Types))
                {
                    Types = reqModel.Types;
                }

                //string NewProBatch = string.Empty;
                //List<string> NewProBatchList = new List<string>();
                //if (!string.IsNullOrEmpty(reqModel.NewProBatch))
                //{
                //    NewProBatch = reqModel.NewProBatch;

                //    var rData = await _dalProductionOrderEntity.FindList(p => p.ProductionOrderNo.Contains(NewProBatch));
                //    if (rData != null && rData.Count > 0)
                //    {
                //        NewProBatchList = rData.Select(p => p.ID).ToList();
                //    }
                //}



                //string ConID = string.Empty;
                //if (!string.IsNullOrEmpty(reqModel.ConID))
                //{
                //    ConID = reqModel.ConID;
                //}                    


                #endregion


                //筛选数据集
                var whereExpressionNOKey = Expressionable.Create<MaterialTransferEntity>()
                      //容器NAME 
                      .AndIF(SourceMaterialList.Count > 0, a => SourceMaterialList.Contains(a.OldMaterialId))
                      .AndIF(DestinationMaterialList.Count > 0, a => DestinationMaterialList.Contains(a.NewMaterialId))
                       .AndIF(OldBatchList.Count > 0, a => OldBatchList.Contains(a.OldLotId))
                       .AndIF(NewBatchList.Count > 0, a => NewBatchList.Contains(a.NewLotId))
                       .AndIF(oldSubLotList.Count > 0, a => oldSubLotList.Contains(a.OldSublotId))
                       .AndIF(NewSubLotList.Count > 0, a => NewSubLotList.Contains(a.NewSublotId))

                       .AndIF(OldSoureIDList.Count > 0, a => OldSoureIDList.Contains(a.OldEquipmentId))
                       .AndIF(NewDestinationIDList.Count > 0, a => NewDestinationIDList.Contains(a.NewEquipmentId))

                          //.AndIF(!string.IsNullOrEmpty(OldSoureBin), a => a.OldEquipmentRequirementId == OldSoureBin)
                          //.AndIF(!string.IsNullOrEmpty(NewDestinationBin), a => a.NewEquipmentRequirementId == NewDestinationBin)

                           .AndIF(!string.IsNullOrEmpty(Types), a => a.Comment.Contains(Types))

                       .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                        .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                                 .ToExpression();

                var rDataList = await _daMaterialTransferEntityl.Db.Queryable<MaterialTransferEntity>()
                    .Where(whereExpressionNOKey).OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                result.dataCount = dataCount;

                List<string> tIDS = new List<string>();
                tIDS = rDataList.Select(P => P.ID).Distinct().ToList();
                if (tIDS.Count > 0)
                {
                    var rdata = await _dal.FindList(P => tIDS.Contains(P.ID));
                    result.data = rdata;
                }
                else
                {

                    result.data = new List<TransferHistoryViewEntity>();
                }

                return result;
            }



            ////查询物料条件
            //List<string> mIDS = new List<string>();


            //if (!string.IsNullOrEmpty(reqModel.StartTime) || !string.IsNullOrEmpty(reqModel.EndTime))
            //{



            //    var whereExpression1 = Expressionable.Create<TransferHistoryViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
            //    .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime)).ToExpression();

            //    var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
            //  .Where(whereExpression1).OrderByDescending(p => p.CreateDate)
            //  .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //    //         var resultss = await _dal.FindList(p => p.CreateDate >= Convert.ToDateTime(reqModel.StartTime) && p.CreateDate <= Convert.ToDateTime(reqModel.EndTime));

            //    result.dataCount = dataCount;
            //    result.data = data;
            //    return result;
            //}
            //else
            //{


            //}







            var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
                 //加入查询条件(时间)
                 .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                 .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                 //原物料数据
                 .AndIF(!string.IsNullOrEmpty(reqModel.SourceMaterial), a => a.OldMaterialName.Contains(reqModel.SourceMaterial)
                                                    || a.OldMaterialCode.Contains(reqModel.SourceMaterial))
                 //新物料数据
                 .AndIF(!string.IsNullOrEmpty(reqModel.DestinationMaterial), a => a.NewMaterialName.Contains(reqModel.DestinationMaterial)
                                                    || a.NewMaterialCode.Contains(reqModel.DestinationMaterial))
                 //老批次信息
                 .AndIF(!string.IsNullOrEmpty(reqModel.OldBatch), a => a.OldLotId.Contains(reqModel.OldBatch))
                 //新批次信息
                 .AndIF(!string.IsNullOrEmpty(reqModel.NewBatch), a => a.NewLotId.Contains(reqModel.NewBatch))
                 //老子批次
                 .AndIF(!string.IsNullOrEmpty(reqModel.OLD_SUB_LOT_ID), a => a.OldSubLotId.Contains(reqModel.OLD_SUB_LOT_ID))
                 //新批子批次
                 .AndIF(!string.IsNullOrEmpty(reqModel.NEW_SUB_LOT_ID), a => a.NewSubLotId.Contains(reqModel.NEW_SUB_LOT_ID))
                 //source
                 .AndIF(!string.IsNullOrEmpty(reqModel.OldSoureID), a => a.OldLocationCode.Contains(reqModel.OldSoureID))
                  //source bin     
                  .AndIF(!string.IsNullOrEmpty(reqModel.OldSoureBin), a => a.OldEquipmentRequirementId == reqModel.OldSoureBin)
                  //Destination
                  .AndIF(!string.IsNullOrEmpty(reqModel.NewDestinationID), a => a.NewLocationCode.Contains(reqModel.NewDestinationID))
                  //Destination bin     
                  .AndIF(!string.IsNullOrEmpty(reqModel.NewDestinationBin), a => a.NewEquipmentRequirementId == reqModel.NewDestinationBin)
                  //MaterialID   
                  .AndIF(!string.IsNullOrEmpty(reqModel.MaterialID), a => a.OldMaterialId.Contains(reqModel.MaterialID))
                  //订单号
                  .AndIF(!string.IsNullOrEmpty(reqModel.OldProBatch), a => a.OldProBatch.Contains(reqModel.OldProBatch))
                   //类型
                   .AndIF(!string.IsNullOrEmpty(reqModel.Types), a => a.CComment.Contains(reqModel.Types))
                   .AndIF(!string.IsNullOrEmpty(reqModel.NewProBatch), a => a.NewProBatch.Contains(reqModel.NewProBatch))
                   //容器NAME 
                   .AndIF(!string.IsNullOrEmpty(reqModel.ConID), a => a.ContainerId == reqModel.ConID)
                             .AndIF(!string.IsNullOrEmpty(reqModel.Key),
                             a => a.OldLocationName.Contains(reqModel.Key)
                             || a.NewLocationName.Contains(reqModel.Key) || a.OldMaterialCode.Contains(reqModel.Key)
                             || a.OldMaterialName.Contains(reqModel.Key) || a.NewMaterialCode.Contains(reqModel.Key)
                             || a.NewMaterialName.Contains(reqModel.Key) || a.OldLotId.Contains(reqModel.Key)
                             || a.NewSubLotId.Contains(reqModel.Key) || a.OldSubLotId.Contains(reqModel.Key)
                             || a.NewSubLotId.Contains(reqModel.Key) || a.CComment.Contains(reqModel.Key)
                             || a.Suppiername.Contains(reqModel.Key))
                          .AndIF(!string.IsNullOrEmpty(reqModel.ConNameNew), a => a.NewContainerName.Contains(reqModel.ConNameNew))
                         .AndIF(!string.IsNullOrEmpty(reqModel.ConNameOld), a => a.OldContainerName.Contains(reqModel.ConNameOld))
                              .ToExpression();



            var data2 = await _dal.Db.Queryable<TransferHistoryViewEntity>()
               .Where(whereExpression).ToListAsync();
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;

        }

        public async Task<PageModel<TransferHistoryViewEntity>> GetPageListOLD20250213(TransferHistoryViewRequestModel reqModel)
        {
            PageModel<TransferHistoryViewEntity> result = new PageModel<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
                //加入查询条件(时间)
                .AndIF(!string.IsNullOrEmpty(reqModel.StartTime), a => a.CreateDate >= Convert.ToDateTime(reqModel.StartTime))
                .AndIF(!string.IsNullOrEmpty(reqModel.EndTime), a => a.CreateDate <= Convert.ToDateTime(reqModel.EndTime))
                //原物料数据
                .AndIF(!string.IsNullOrEmpty(reqModel.SourceMaterial), a => a.OldMaterialName.Contains(reqModel.SourceMaterial)
                                                   || a.OldMaterialCode.Contains(reqModel.SourceMaterial))
                //新物料数据
                .AndIF(!string.IsNullOrEmpty(reqModel.DestinationMaterial), a => a.NewMaterialName.Contains(reqModel.DestinationMaterial)
                                                   || a.NewMaterialCode.Contains(reqModel.DestinationMaterial))
                //老批次信息
                .AndIF(!string.IsNullOrEmpty(reqModel.OldBatch), a => a.OldLotId.Contains(reqModel.OldBatch))
                //新批次信息
                .AndIF(!string.IsNullOrEmpty(reqModel.NewBatch), a => a.NewLotId.Contains(reqModel.NewBatch))
                //老子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.OLD_SUB_LOT_ID), a => a.OldSubLotId.Contains(reqModel.OLD_SUB_LOT_ID))
                //新批子批次
                .AndIF(!string.IsNullOrEmpty(reqModel.NEW_SUB_LOT_ID), a => a.NewSubLotId.Contains(reqModel.NEW_SUB_LOT_ID))
                //source
                .AndIF(!string.IsNullOrEmpty(reqModel.OldSoureID), a => a.OldLocationCode.Contains(reqModel.OldSoureID))
                 //source bin     
                 .AndIF(!string.IsNullOrEmpty(reqModel.OldSoureBin), a => a.OldEquipmentRequirementId.Contains(reqModel.OldSoureBin))
                 //Destination
                 .AndIF(!string.IsNullOrEmpty(reqModel.NewDestinationID), a => a.NewLocationCode.Contains(reqModel.NewDestinationID))
                 //Destination bin     
                 .AndIF(!string.IsNullOrEmpty(reqModel.NewDestinationBin), a => a.NewEquipmentRequirementId.Contains(reqModel.NewDestinationBin))
                 //MaterialID   
                 .AndIF(!string.IsNullOrEmpty(reqModel.MaterialID), a => a.OldMaterialId.Contains(reqModel.MaterialID))
                 //订单号
                 .AndIF(!string.IsNullOrEmpty(reqModel.OldProBatch), a => a.OldProBatch.Contains(reqModel.OldProBatch))
                  //类型
                  .AndIF(!string.IsNullOrEmpty(reqModel.Types), a => a.CComment.Contains(reqModel.Types))
                  .AndIF(!string.IsNullOrEmpty(reqModel.NewProBatch), a => a.NewProBatch.Contains(reqModel.NewProBatch))
                  //容器NAME 
                  .AndIF(!string.IsNullOrEmpty(reqModel.ConID), a => a.ContainerId == reqModel.ConID)
                            .AndIF(!string.IsNullOrEmpty(reqModel.Key),
                            a => a.OldLocationName.Contains(reqModel.Key)
                            || a.NewLocationName.Contains(reqModel.Key) || a.OldMaterialCode.Contains(reqModel.Key)
                            || a.OldMaterialName.Contains(reqModel.Key) || a.NewMaterialCode.Contains(reqModel.Key)
                            || a.NewMaterialName.Contains(reqModel.Key) || a.OldLotId.Contains(reqModel.Key)
                            || a.NewSubLotId.Contains(reqModel.Key) || a.OldSubLotId.Contains(reqModel.Key)
                            || a.NewSubLotId.Contains(reqModel.Key) || a.CComment.Contains(reqModel.Key)
                            || a.Suppiername.Contains(reqModel.Key))
                         .AndIF(!string.IsNullOrEmpty(reqModel.ConNameNew), a => a.NewContainerName.Contains(reqModel.ConNameNew))
                        .AndIF(!string.IsNullOrEmpty(reqModel.ConNameOld), a => a.OldContainerName.Contains(reqModel.ConNameOld))
                             .ToExpression();



            var data2 = await _dal.Db.Queryable<TransferHistoryViewEntity>()
               .Where(whereExpression).ToListAsync();
            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderByDescending(p => p.CreateDate).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;

        }

        public async Task<PageModel<TransferHistoryViewEntity>> GetPageListByConID(TransferHistoryViewRequestModel reqModel)
        {
            PageModel<TransferHistoryViewEntity> result = new PageModel<TransferHistoryViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<TransferHistoryViewEntity>()
                  //容器NAME 
                  .AndIF(!string.IsNullOrEmpty(reqModel.ConID), a => a.ConidNew == reqModel.ConID || a.ConidOld == reqModel.ConID)
                             .ToExpression();

            var data = await _dal.Db.Queryable<TransferHistoryViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;

        }
        public async Task<bool> SaveForm(TransferHistoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}