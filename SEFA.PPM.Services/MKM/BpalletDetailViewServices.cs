
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using System;
using SEFA.MKM.Model.Models.MKM;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;
using System.Collections;
using SEFA.Base;
using SEFA.Base.Common.Common;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Services
{
    public class BpalletDetailViewServices : BaseServices<BpalletDetailViewEntity>, IBpalletDetailViewServices
    {

        private readonly IBaseRepository<BpalletDetailViewEntity> _dal;
        private readonly IBaseRepository<BatchPalletViewEntity> _BatchPalletViewEntity;
        private readonly IBaseRepository<BatchPalletSelectbViewEntity> _BatchPalletSelectbViewEntity;
        private readonly IBaseRepository<BatchPalletSelectmViewEntity> _BatchPalletSelectmViewEntity;
        private readonly IBaseRepository<ContainerEntity> _ContainerEntityDal;
        private readonly IBaseRepository<ContainerHistoryEntity> _ContainerHistoryEntityDal;
        private readonly IBaseRepository<MaterialEntity> _MaterialEntityDal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _iUser;
        private readonly IBaseRepository<MaterialInventoryEntity> _dalMaterial;
        private readonly IBaseRepository<BatchEntity> _dalBatchEntity;
        public BpalletDetailViewServices(IBaseRepository<BpalletDetailViewEntity> dal, IBaseRepository<BatchPalletSelectbViewEntity> BatchPalletSelectbViewEntity, IBaseRepository<BatchPalletSelectmViewEntity> BatchPalletSelectmViewEntity, IBaseRepository<ContainerEntity> containerEntityDal, IBaseRepository<ContainerHistoryEntity> containerHistoryEntityDal,
            IBaseRepository<BatchPalletViewEntity> batchPalletViewEntity, IBaseRepository<MaterialEntity> materialEntityDal, IUnitOfWork unitOfWork, IUser iUser, IBaseRepository<MaterialInventoryEntity> dalMaterial, IBaseRepository<BatchEntity> dalBatchEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _BatchPalletSelectbViewEntity = BatchPalletSelectbViewEntity;
            _BatchPalletSelectmViewEntity = BatchPalletSelectmViewEntity;
            _ContainerEntityDal = containerEntityDal;
            _ContainerHistoryEntityDal = containerHistoryEntityDal;
            _BatchPalletViewEntity = batchPalletViewEntity;
            _MaterialEntityDal = materialEntityDal;
            _unitOfWork = unitOfWork;
            _iUser = iUser;
            _dalMaterial = dalMaterial;
            _dalBatchEntity = dalBatchEntity;
        }

        public async Task<List<BpalletDetailViewEntity>> GetList(BpalletDetailViewRequestModel reqModel)
        {
            List<BpalletDetailViewEntity> result = new List<BpalletDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BpalletDetailViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BpalletDetailViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 查询明细列表
        /// </summary>V_PPM_B_BATCH_DETAIL_MATERIAL_VIEW
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<BpalletDetailViewEntity>> GetPageList(BpalletDetailViewRequestModel reqModel)//(BpalletDetailViewRequestModel reqModel)
        {
            PageModel<BpalletDetailViewEntity> result = new PageModel<BpalletDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BpalletDetailViewEntity>().AndIF(!string.IsNullOrEmpty(reqModel.ContainerId), a => a.ContainerId == reqModel.ContainerId)
            //    .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId == reqModel.BatchId)
                             .ToExpression();

           
            
            var data = await _dal.Db.Queryable<BpalletDetailViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }




        /// <summary>
        /// 批次ID
        /// </summary>
        /// <param name="batchID"></param>
        /// <returns></returns>
        public async Task<List<BatchPalletSelectModel>> GetListSelect(string batchID, string eqpID, string eqpName)
        {
            //1.根据批次拿到当前需要备料数据
            //对比除自己之外的配方数据，配方物料数据必须要大于等于该物料需求才可以
            //筛选满足条件的数据

            //获取产品(这里获取所有工单)
            List<BatchPalletSelectbViewEntity> bList = await _BatchPalletSelectbViewEntity.FindList(p => p.ID != null);

            //筛选当前数据
            List<BatchPalletSelectbViewEntity> thisData = bList.Where(p => p.BatchId == batchID).ToList();
            //筛选工单（这里面包含所有的物料信息）
            List<BatchPalletSelectbViewEntity> otherData = bList.Where(p => p.PrepStatus == "2" && p.BatchId != batchID).ToList();

            //首先按照物料分组判断总物料数据，必须总物料数据大于等于当前

            //按照工单和批次分组

            //获取本身的数据
            var thisSum = thisData.GroupBy(p => new { p.BatchId, p.ProductionId, p.BatchNumber, p.ProductionOrderNo })
                                     .Select(group => new
                                     {
                                         BatchId = group.Key.BatchId,
                                         ProductionId = group.Key.ProductionId,
                                         BatchNumber = group.Key.BatchNumber,
                                         ProductionOrderNo = group.Key.ProductionOrderNo,
                                         Sum = group.Count()
                                     })
                                 .ToList();
            //获取其他的数据
            var otherSum = otherData.GroupBy(p => new { p.BatchId, p.ProductionId, p.BatchNumber, p.ProductionOrderNo })
                                     .Select(group => new
                                     {
                                         BatchId = group.Key.BatchId,
                                         ProductionId = group.Key.ProductionId,
                                         BatchNumber = group.Key.BatchNumber,
                                         ProductionOrderNo = group.Key.ProductionOrderNo,
                                         Sum = group.Count()
                                     })
                                 .ToList();

            //根据物料数量数据匹配数据（筛选配方数据小于的）初次筛选
            List<BatchPalletSelectModel> bLists = new List<BatchPalletSelectModel>();
            //插入数据
            for (int i = 0; i < otherSum.Count; i++)
            {
                int sum = otherSum[i].Sum;
                if (thisSum != null && thisSum.Count > 0)
                {
                    int oldSum = thisSum[0].Sum;
                    if (sum > oldSum)
                    {
                        BatchPalletSelectModel model = new BatchPalletSelectModel();
                        model.BatchId = otherSum[i].BatchId.Trim();
                        model.ProductionId = otherSum[i].ProductionId.Trim();
                        model.BatchNumber = otherSum[i].BatchNumber.Trim();
                        model.ProductionOrderNo = otherSum[i].ProductionOrderNo.Trim();
                        bLists.Add(model);
                    }
                }
            }

            List<BatchPalletSelectModel> lastReturn = new List<BatchPalletSelectModel>();
            //筛选过的配方信息
            var selectData = bLists.GroupBy(p => new { p.BatchId, p.ProductionId, p.ProductionOrderNo, p.BatchNumber }).ToList();
            //按照物料需求分组
            var mCodes = thisData.GroupBy(p => new { p.Code, p.MQuantity }).ToList();

            for (int i = 0; i < selectData.Count; i++)
            {
                string batchId = selectData[i].Key.BatchId;
                string productionId = selectData[i].Key.ProductionId;
                string batchNumber = selectData[i].Key.BatchNumber;

                //筛选工单数据
                //var result = bLists.Where(p => p.BatchId == batchId && p.ProductionId == productionId && p.BatchNumber == batchNumber);

                //对比配方
                int Okcount = 0;
                int TotalCount = mCodes.Count;
                for (Int32 j = 0; j < mCodes.Count; j++)
                {
                    string mCode = mCodes[j].Key.Code;
                    decimal mQty = mCodes[j].Key.MQuantity == null ? 0 : mCodes[j].Key.MQuantity.Value;
                    //获取下拉
                    int numberCount = otherData.Where(p => p.Code == mCode && p.MQuantity.Value >= mQty).Count();
                    if (numberCount > 0)
                    {
                        Okcount++;
                    }
                }
                if (TotalCount == Okcount)
                {
                    BatchPalletSelectModel model = new BatchPalletSelectModel();
                    model.BatchId = batchId;
                    model.ProductionOrderNo = selectData[i].Key.ProductionOrderNo;
                    model.ProductionId = productionId;
                    model.BatchNumber = batchNumber;
                    model.EquipmentId = eqpID;
                    model.EquipmentName = eqpName;
                    lastReturn.Add(model);
                }

            }

            return lastReturn;

        }

        ///// <summary>
        ///// 获取批次栈板下拉框
        ///// </summary>
        ///// <param name="conID">容器ID</param>
        ///// <param name="proID">订单ID</param>
        ///// <returns></returns>
        //public async Task<List<BatchPalletSelectModel>> GetListSelectold(string conID, string proID)
        //{
        //    //获取产品
        //    List<BatchPalletSelectbViewEntity> bList = await _BatchPalletSelectbViewEntity.FindList(p => p.ProductionId == proID);
        //    //获取物料
        //    List<BatchPalletSelectmViewEntity> mList = await _BatchPalletSelectmViewEntity.FindList(p => p.ContainerId == conID);
        //    List<BpalletDetailViewEntity> result = new List<BpalletDetailViewEntity>();
        //    var whereExpression = Expressionable.Create<BpalletDetailViewEntity>()
        //                     .ToExpression();
        //    var data = await _dal.Db.Queryable<BpalletDetailViewEntity>().Where(whereExpression).ToListAsync();

        //    var fResult = from a in bList.AsEnumerable()
        //                  join b in mList on a.MaterialId equals b.MaterialId
        //                  select new
        //                  {
        //                      a.BatchId,
        //                      a.BatchNumber,
        //                      a.ProductionId,
        //                      a.ProductionOrderNo,
        //                      a.EquipmentId,
        //                      a.EquipmentName,
        //                      a.MQuantity,
        //                      b.IQuantity
        //                  };
        //    var result1 = from a in fResult.AsEnumerable()
        //                  group a by new
        //                  {
        //                      a.BatchId,
        //                      a.BatchNumber,
        //                      a.ProductionId,
        //                      a.ProductionOrderNo,
        //                      a.EquipmentId,
        //                      a.EquipmentName
        //                  } into g
        //                  select new
        //                  {
        //                      g.Key.BatchId,
        //                      g.Key.BatchNumber,
        //                      g.Key.ProductionId,
        //                      g.Key.ProductionOrderNo,
        //                      g.Key.EquipmentId,
        //                      g.Key.EquipmentName,
        //                      CountTotal = g.Count(),
        //                      CountOK = g.Where(p => p.MQuantity == p.IQuantity).Count(),
        //                      CountDY = g.Where(p => p.MQuantity > p.IQuantity).Count(),
        //                      CountXY = g.Where(p => p.MQuantity < p.IQuantity).Count()
        //                  }; ;

        //    //这里筛选出需要的数据（不存在库存数据小于0的数据)
        //    var result2 = result1.ToList().Where(P => P.CountXY == 0).ToList();

        //    //这里判断等于的数据
        //    var result3 = result2.Where(P => P.CountTotal == P.CountOK && P.CountDY == 0).ToList();

        //    //这里判断大于的数据
        //    var result4 = result2.Where(P => P.CountTotal != P.CountOK && P.CountDY != 0).ToList();
        //    //循环加数据
        //    List<BatchPalletSelectModel> listModel = new List<BatchPalletSelectModel>();

        //    for (int i = 0; i < result3.Count(); i++)
        //    {
        //        BatchPalletSelectModel model = new BatchPalletSelectModel();
        //        model.BatchId = result3[i].BatchId;
        //        model.BatchNumber = result3[i].BatchNumber;
        //        model.ProductionId = result3[i].ProductionId;
        //        model.ProductionOrderNo = result3[i].ProductionOrderNo;
        //        model.EquipmentId = result3[i].EquipmentId;
        //        model.EquipmentName = result3[i].EquipmentName;
        //        model.STATE = "等于";
        //        listModel.Add(model);
        //    }

        //    for (int i = 0; i < result4.Count(); i++)
        //    {
        //        BatchPalletSelectModel model = new BatchPalletSelectModel();
        //        model.BatchId = result4[i].BatchId;
        //        model.BatchNumber = result4[i].BatchNumber;
        //        model.ProductionId = result4[i].ProductionId;
        //        model.ProductionOrderNo = result4[i].ProductionOrderNo;
        //        model.EquipmentId = result4[i].EquipmentId;
        //        model.EquipmentName = result4[i].EquipmentName;
        //        model.STATE = "包含";
        //        listModel.Add(model);
        //    }

        //    return listModel;
        //}

        /// <summary>
        /// 再分配
        /// </summary>
        /// <param name="conID">容器ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="productionId">订单ID</param>
        /// <param name="equipmentId">equipmentId</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveDistribution(BatchPalletDistribution disModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            try
            {
                //在分配需要考虑将当前库存数据的批次和订单号改成新的订单号和批次号（这里只考虑当前容器上的）               

                string conID = disModel.conID;
                string batchId = disModel.batchId;
                string productionId = disModel.productionId;
                string equipmentId = disModel.equipmentId;
                //获取需要变更的容器（改状态）
                ContainerEntity model = await _ContainerEntityDal.FindEntity(conID);
                if (model == null)
                {
                    result.msg = "容器为空";
                    return result;
                }
                if (model.Status != "3")
                {
                    result.msg = "该容器未完成，无法分配";
                    return result;
                }

                BatchPalletViewEntity BpalletDetailModel = await _BatchPalletViewEntity.FindEntity(conID);
                if (BpalletDetailModel == null)
                {
                    result.msg = "不存在分配数据";
                    return result;
                }

                #region 更新容器

                string oldEquipmenID = model.EquipmentId;
                model.Modify(model.ID, _iUser.Name.ToString());
                model.Status = "1"; //打开托盘
                model.ProductionBatchId = batchId;
                model.ProductionRequestId = productionId;//获取容器

                #endregion

                _unitOfWork.BeginTran();

                #region 修改库存信息

                List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();

                //循环加入数据
                List<MaterialInventoryEntity> mList = await _dalMaterial.FindList(p => p.BatchId == batchId && p.ProductionRequestId == productionId);
                for (int i = 0; i < mList.Count; i++)
                {
                    MaterialInventoryEntity inModel = mList[i];
                    inModel.Modify(inModel.ID, _iUser.Name.ToString());
                    inModel.BatchId = batchId;
                    inModel.ProductionRequestId = productionId;
                    upInvent.Add(inModel);
                }

                #endregion

                #region 修改当前批次状态

                List<BatchEntity> bModelList = new List<BatchEntity>();
                BatchEntity baseModel = await _dalBatchEntity.FindEntity(P => P.ID == batchId);

                if (baseModel != null)
                {
                    baseModel.Modify(baseModel.ID, baseModel.ID.ToString());
                    baseModel.PrepStatus = "2";//更新为备料状态
                    bModelList.Add(baseModel);
                }

                #endregion


                bool upDate = await _ContainerEntityDal.Update(model);

                #region 写入容器记录表

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(_iUser.Name.ToString());
                //hisModel.ID = Guid.NewGuid().ToString();
                //   hisModel
                hisModel.ContainerId = model.ID;
                hisModel.Type = "Batch Pallet Reassign";
                hisModel.EquipmentId = oldEquipmenID;
                hisModel.EquipmentRequirementId = equipmentId;
                hisModel.State = model.Status;
                hisModel.Comment = model.Comment;
                hisModel.ProductOrderId = productionId;// 工单ID
                hisModel.BatchId = batchId; //工单批次ID
                hisModel.MaterialId =
                ////hisModel.SublotId = BpalletDetailModel.IdSlot;
                ////hisModel.Quantity = BpalletDetailModel.Quantity.ToString();
                ////hisModel.QuantityUomId = BpalletDetailModel.UId;
                //hisModel.BatchConsumedRequirementId = cHisModel.BatchConsumedRequirementId;//批次用量需求ID
                //hisModel.ConsumedRequirementId = cHisModel.ConsumedRequirementId;//工单用量需求ID
                //hisModel.ProductionExecutionId = cHisModel.ProductionExecutionId;//工单执行ID

                hisModel.ContainerCode = model.Name;//容器编号
                //hisModel.MaterialProducedActualId = cHisModel.MaterialProducedActualId; //物料产出记录ID
                //hisModel.MaterialConsumedActualId = cHisModel.MaterialConsumedActualId;  //物料消耗记录ID
                ////hisModel.LotId = BpalletDetailModel.IdLot;
                ////hisModel.ExpirationDate = BpalletDetailModel.ExpirationDate;

                bool rqResult = await _ContainerHistoryEntityDal.Add(hisModel) > 0;

                #endregion
                bool upInVents = true;

                if (upInvent.Count > 0)
                {
                    upInVents = await _dalMaterial.Update(upInvent);
                }

                bool upbModel = true;

                if (bModelList.Count > 0)
                {
                    upbModel = await _dalBatchEntity.Update(bModelList);
                }
                if (!upDate || !upDate || !upInVents || !upbModel)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "分配失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = ex.Message;
                return result;
            }
        }



        public async Task<bool> SaveForm(BpalletDetailViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}