
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using System;
using static SEFA.PTM.Services.ConsumeViewServices;
using System.Security.Cryptography;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.MKM.Model.ViewModels.View;
using System.Linq;
using System.Reactive;

namespace SEFA.MKM.Services
{
    public class CheckWeightViewServices : BaseServices<CheckWeightViewEntity>, ICheckWeightViewServices
    {
        private readonly IBaseRepository<CheckWeightViewEntity> _dal;
        private readonly IBaseRepository<BatchEntity> _BatchEntity;
        private readonly IBaseRepository<MaterialInventoryEntity> _MaterialInventoryEntity;
        private readonly IUnitOfWork _unitOfWork;
        public IUser _user;
        private readonly IBaseRepository<BBatchDetailMaterialViewEntity> _dalBBatchDetailMaterialViewEntity;
        private readonly IBaseRepository<MaterialTransferEntity> _dalMaterialTransferEntity;
        private readonly IBaseRepository<MaterialSubLotEntity> _materialSubLotServicesDal;
        private readonly IInventorylistingViewServices _inventorylistingViewServices;
        public CheckWeightViewServices(IBaseRepository<CheckWeightViewEntity> dal, IUnitOfWork unitOfWork, IUser user, IBaseRepository<BatchEntity> BatchEntity, IBaseRepository<MaterialInventoryEntity> materialInventoryEntity,
            IBaseRepository<BBatchDetailMaterialViewEntity> dalBBatchDetailMaterialViewEntity, IBaseRepository<MaterialTransferEntity> dalMaterialTransferEntity, IBaseRepository<MaterialSubLotEntity> materialSubLotServicesDal, IInventorylistingViewServices inventorylistingViewServices)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _unitOfWork = unitOfWork;
            _user = user;
            _BatchEntity = BatchEntity;
            _MaterialInventoryEntity = materialInventoryEntity;
            _dalBBatchDetailMaterialViewEntity = dalBBatchDetailMaterialViewEntity;
            _dalMaterialTransferEntity = dalMaterialTransferEntity;
            _materialSubLotServicesDal = materialSubLotServicesDal;
            _inventorylistingViewServices = inventorylistingViewServices;
        }

        public async Task<List<CheckWeightViewEntity>> GetList(CheckWeightViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CheckWeightViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }
        /// <summary>
        /// 复秤TOP界面
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<CheckWeightViewEntity>> GetPageList(CheckWeightViewRequestModel reqModel)
        {
            PageModel<CheckWeightViewEntity> result = new PageModel<CheckWeightViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<CheckWeightViewEntity>()
                 .AndIF(reqModel.EquipmentId != null, a => a.EquipmentId == reqModel.EquipmentId)
                 .AndIF(reqModel.StartTime.HasValue, a => a.PlanStartTime >= reqModel.StartTime)
                 .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
                 .AndIF(reqModel.EndTime.HasValue, a => a.PlanStartTime <= reqModel.EndTime)
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc).And(a => a.HType == "Partial")
                .AndIF(!string.IsNullOrEmpty(reqModel.QSearch), a => a.ShiftName.Contains(reqModel.QSearch) || a.SbSscc.Contains(reqModel.QSearch) || a.ProductionOrderNo.Contains(reqModel.QSearch)
                || a.MaterialCode.Contains(reqModel.QSearch) || a.MaterialName.Contains(reqModel.QSearch) || a.StorageLocation.Contains(reqModel.QSearch) || a.Sequence.ToString() == (reqModel.QSearch)
                 || a.LBatch.ToString() == (reqModel.QSearch))
                             .ToExpression();
            //var data = await _dal.Db.Queryable<CheckWeightViewEntity>()
            //  .Where(whereExpression).OrderBy(p => p.ProductionOrderNo).OrderByDescending(p => p.IsWeighingCheck)
            //  .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data;
            //return result;


            var data2 = await _dal.Db.Queryable<CheckWeightViewEntity>().Where(whereExpression).ToListAsync();

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          
            var rDat = data2.OrderBy(p => p.IsWeighingCheck).ThenBy(p => p.ProductionOrderNo).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;

        }

        /// <summary>
        /// 复秤下面界面
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<CheckWeightViewEntity>> ResultListDown(CheckWeightViewRequestModel reqModel)
        {
            PageModel<CheckWeightViewEntity> result = new PageModel<CheckWeightViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<CheckWeightViewEntity>().AndIF(reqModel.SubId != null, a => a.SubId == reqModel.SubId)
                             .ToExpression();
            //var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
            var data = await _dal.Db.Queryable<CheckWeightViewEntity>().Where(whereExpression).ToListAsync();
            //.ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //result.dataCount = dataCount;
            //result.data = data;
            return data;
        }

        /// <summary>
        /// 复秤确认
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ConfirmCheck(CheckWeightViewRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //批次状态
                List<BatchEntity> UpBatchModels = new List<BatchEntity>();
                List<MaterialInventoryEntity> UpMaterialModels = new List<MaterialInventoryEntity>();

                if (reqModel.InQuantity == null || reqModel.InQuantity == "")
                {
                    result.msg = "请先称重";
                    return result;
                }

                if (reqModel.subIdold == null || reqModel.subIdold == "")
                {
                    result.msg = "首页子批次不能为空";
                    return result;
                }

                var sOldData = await _inventorylistingViewServices.FindEntity(p => p.SlotId == reqModel.subIdold);
                if (sOldData == null)
                {

                    result.msg = "不存在对应的复称库存信息";
                    return result;
                }


                //查询库存数量（原有）
                var materialInvent = await _MaterialInventoryEntity.FindEntity(x => x.SublotId == reqModel.subIdold);
                decimal cha = 0;

                if (materialInvent == null)
                {
                    result.msg = "不存在库存信息";
                    return result;
                }
                //这里需要查询数据拿视图

                List<string> amount = new List<string>();
                amount.Add(materialInvent.ID);

                if (materialInvent != null)
                {
                    decimal oldQty = materialInvent.Quantity;
                    string batchID = materialInvent.BatchId;
                    //计算差值                   
                    var Quantity = Math.Round(Convert.ToDecimal(reqModel.InQuantity), 3) - Math.Round(Convert.ToDecimal(oldQty), 3); //Convert.ToDecimal(reqModel.InQuantity)  - oldQty;
                    bool tranHis = true;
                    bool saveSubLot = true;
                    bool saveInvent = true;
                    _unitOfWork.BeginTran();

                    //计算差值(称量-默认-)
                    cha = Convert.ToDecimal(reqModel.InQuantity) - Convert.ToDecimal(reqModel.DefaultQty);
                    //默认等于代表无需做移动库存操作
                    if (cha != 0)
                    {
                        Quantity = cha;
                        if (Quantity < 0) //进行库存新增
                        {
                            //对差值取绝对值
                            decimal lastQty = Math.Abs(Quantity);

                            //新增库存，这里只需要新建子批次即可，其他数据一致
                            #region 新增库存

                            MaterialInventoryEntity addinventModel = new MaterialInventoryEntity();
                            addinventModel.Quantity = Math.Round(Convert.ToDecimal(lastQty), 3);//lastQty;
                            addinventModel.QuantityUomId = materialInvent.QuantityUomId;
                            addinventModel.EquipmentId = materialInvent.EquipmentId;
                            addinventModel.Create(_user.Name.ToString());
                            addinventModel.LotId = materialInvent.LotId;
                            addinventModel.ContainerId = "";

                            #region 新建子批次

                            string sscc = string.Empty;

                            #region 获取子批次号

                            SSCCModel models = new SSCCModel();
                            models.Type = "";
                            models.NextCode = "";
                            models.MaxCode = "";
                            models.MinCode = "";
                            models.Prefix = "";
                            models.TableName = "";
                            models.TableId = "";
                            models.SequenceType = "";
                            models.ResetType = "";
                            models.FeatureId = "";
                            models.pageIndex = 1;
                            models.pageSize = 10;
                            models.orderByFileds = "";
                            string token = _user.GetToken();
                            var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                            if (ssccString.success == true)
                            {
                                sscc = ssccString.response.ToString();
                            }
                            //判断当前批次号是否存在重复数据，再次创建一次
                            List<MaterialSubLotEntity> subLotList = await _materialSubLotServicesDal.FindList(x => x.SubLotId == sscc);
                            if (subLotList != null && subLotList.Count > 0)
                            {
                                ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                                if (ssccString.success == true)
                                {
                                    sscc = ssccString.response.ToString();
                                }
                            }

                            #endregion

                            MaterialSubLotEntity submodel = new MaterialSubLotEntity();
                            submodel.Create(_user.Name.ToString());
                            submodel.ExternalStatus = "3";
                            submodel.Type = "0";
                            submodel.SubLotId = sscc;
                            addinventModel.SublotId = submodel.ID;
                            saveSubLot = await _materialSubLotServicesDal.Add(submodel) > 0;

                            #endregion

                            saveInvent = await _MaterialInventoryEntity.Add(addinventModel) > 0;

                            #endregion                            

                            #region 转移历史 -新库存新增

                            //写入历史记录
                            MaterialTransferEntity trans1 = new MaterialTransferEntity();
                            trans1.Create(_user.Name.ToString());
                            trans1.OldLotId = "";
                            trans1.OldSublotId = "";
                            trans1.OldExpirationDate = null;
                            trans1.NewExpirationDate = Convert.ToDateTime(sOldData.ExpirationDate);
                            trans1.Quantity = Math.Round(Convert.ToDecimal(lastQty), 3);
                            trans1.QuantityUomId = sOldData.UId;
                            //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                            trans1.Type = "Create Inventory";
                            trans1.Comment = "复称-创建库存";
                            trans1.OldEquipmentId = "";
                            trans1.NewEquipmentId = sOldData.EquipmentId;
                            trans1.OldContainerId = "";
                            trans1.NewContainerId = "";
                            trans1.OldLotExternalStatus = "";
                            trans1.OldSublotExternalStatus = "";
                            trans1.NewLotExternalStatus = "";
                            trans1.NewSublotExternalStatus = "";
                            trans1.NewSublotId = addinventModel.SublotId;
                            trans1.NewLotId = materialInvent.LotId;

                            tranHis = await _dalMaterialTransferEntity.Add(trans1) > 0;

                            #endregion

                        }
                        else //大于零需要进行选中库存扣减
                        {

                            var subModelNew = await _inventorylistingViewServices.FindEntity(p => p.SlotId == reqModel.SubId);
                            if (subModelNew == null)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "不存在对应的复称库存信息";
                                return result;
                            }

                            if (reqModel.SubId == null || reqModel.SubId == "")
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "请选择需要扣减的库存数据";
                                return result;
                            }

                            var Invent = await _MaterialInventoryEntity.FindEntity(x => x.SublotId == reqModel.SubId);
                            if (Invent == null)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "库存信息不存在";
                                return result;
                            }
                            amount.Add(Invent.ID);
                            decimal newInvent = Math.Round(Convert.ToDecimal(Invent.Quantity), 3) - Math.Round(Convert.ToDecimal(Quantity), 3);//Invent.Quantity - Quantity;
                            if (newInvent < 0)
                            {
                                _unitOfWork.RollbackTran();
                                result.msg = "复称库存量不足";
                                return result;
                            }
                            else if (newInvent == 0)
                            {

                                //删除库存
                                await _MaterialInventoryEntity.DeleteById(Invent.ID);

                                #region 写入转移历史-删除

                                //写入历史记录
                                MaterialTransferEntity trans = new MaterialTransferEntity();
                                trans.Create(_user.Name.ToString());
                                //trans.ID = Guid.NewGuid().ToString();
                                trans.OldStorageLocation = Invent.StorageLocation;
                                trans.NewStorageLocation = Invent.StorageLocation;
                                trans.OldLotId = Invent.LotId;
                                trans.NewLotId = Invent.LotId;
                                trans.OldSublotId = subModelNew.SlotId;
                                trans.NewSublotId = subModelNew.SlotId;
                                trans.Mode = reqModel.IsType;
                                trans.OldExpirationDate = subModelNew.ExpirationDate;
                                trans.NewExpirationDate = subModelNew.ExpirationDate;
                                trans.Quantity = Math.Round(Convert.ToDecimal(Invent.Quantity), 3); // Convert.ToInt32(Invent.Quantity);
                                trans.QuantityUomId = Invent.QuantityUomId;
                                trans.ProductionExecutionId = subModelNew.ProductionRequestId;
                                trans.NewProductionExecutionId = subModelNew.ProductionRequestId;
                                trans.Type = "Delete";
                                trans.Comment = "复称-删除库存";
                                //trans.NewEquipmentRequirementId = Invent;
                                //trans.OldEquipmentRequirementId = Invent.EquipmentId;
                                //trans.TransferGroupId
                                trans.OldEquipmentId = Invent.EquipmentId;
                                trans.NewEquipmentId = Invent.EquipmentId;
                                trans.OldContainerId = Invent.ContainerId;
                                trans.NewContainerId = Invent.ContainerId;
                                //status
                                trans.OldMaterialId = subModelNew.MaterialId;
                                trans.NewMaterialId = subModelNew.MaterialId;
                                trans.OldLotExternalStatus = subModelNew.StatusF;
                                trans.OldSublotExternalStatus = subModelNew.StatusS;

                                trans.NewLotExternalStatus = subModelNew.StatusF;
                                trans.NewSublotExternalStatus = subModelNew.StatusS;

                                //trans.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                                //trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                                tranHis = await _dalMaterialTransferEntity.Add(trans) > 0;

                                #endregion
                            }
                            else
                            {
                                //var subModel = await _dalBBatchDetailMaterialViewEntity.FindEntity(p => p.SubId == reqModel.SubId);
                                //if (subModel == null)
                                //{
                                //    _unitOfWork.RollbackTran();
                                //    result.msg = "不存在对应的复称库存信息";
                                //    return result;
                                //}

                                #region 写入转移历史

                                //写入历史记录
                                MaterialTransferEntity trans = new MaterialTransferEntity();
                                trans.Create(_user.Name.ToString());
                                //trans.ID = Guid.NewGuid().ToString();
                                trans.OldStorageLocation = Invent.StorageLocation;
                                trans.NewStorageLocation = Invent.StorageLocation;
                                trans.OldLotId = Invent.LotId;
                                trans.NewLotId = Invent.LotId;
                                trans.OldSublotId = subModelNew.SlotId;
                                trans.NewSublotId = subModelNew.SlotId;
                                trans.Mode = reqModel.IsType;
                                trans.OldExpirationDate = subModelNew.ExpirationDate;
                                trans.NewExpirationDate = subModelNew.ExpirationDate;
                                trans.Quantity = Math.Round(Convert.ToDecimal(newInvent), 3); // Convert.ToInt32(Invent.Quantity);
                                trans.QuantityUomId = Invent.QuantityUomId;
                                trans.ProductionExecutionId = subModelNew.ProductionRequestId;
                                trans.NewProductionExecutionId = subModelNew.ProductionRequestId;
                                trans.Type = "Transfer";
                                trans.Comment = "复称-库存转移";
                                //trans.NewEquipmentRequirementId = Invent;
                                //trans.OldEquipmentRequirementId = Invent.EquipmentId;
                                //trans.TransferGroupId
                                trans.OldEquipmentId = Invent.EquipmentId;
                                trans.NewEquipmentId = Invent.EquipmentId;
                                trans.OldContainerId = Invent.ContainerId;
                                trans.NewContainerId = Invent.ContainerId;
                                //status
                                trans.OldMaterialId = subModelNew.MaterialId;
                                trans.NewMaterialId = subModelNew.MaterialId;
                                trans.OldLotExternalStatus = subModelNew.StatusF;
                                trans.OldSublotExternalStatus = subModelNew.StatusS;
                                trans.NewLotExternalStatus = subModelNew.StatusF;
                                trans.NewSublotExternalStatus = subModelNew.StatusS;

                                //trans.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                                //trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                                tranHis = await _dalMaterialTransferEntity.Add(trans) > 0;

                                #endregion

                                //修改数据
                                Invent.Quantity = newInvent;
                                Invent.Modify(Invent.ID, _user.Name.ToString());
                                UpMaterialModels.Add(Invent);
                            }
                        }
                    }

                    #region 判断当前批次和子批次数据

                    //查询批次状态是否为锁定
                    //查询子批次状态是否为锁定
                    string msg = await _inventorylistingViewServices.GetStateByInventID(amount.ToArray());

                    if (!string.IsNullOrEmpty(msg))
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = msg;
                        return result;
                    }

                    #endregion


                    #region 转移历史 -本身库存扣减,记录转移后库存                  

                    //写入历史记录
                    MaterialTransferEntity transOld = new MaterialTransferEntity();
                    transOld.Create(_user.Name.ToString());
                    //trans.ID = Guid.NewGuid().ToString();
                    transOld.OldStorageLocation = materialInvent.StorageLocation;
                    transOld.NewStorageLocation = materialInvent.StorageLocation;
                    transOld.OldLotId = materialInvent.LotId;
                    transOld.NewLotId = materialInvent.LotId;
                    transOld.OldSublotId = materialInvent.SublotId;
                    transOld.NewSublotId = materialInvent.SublotId;
                    transOld.Mode = reqModel.IsType;
                    transOld.OldExpirationDate = sOldData.ExpirationDate;
                    transOld.NewExpirationDate = sOldData.ExpirationDate;
                    transOld.Quantity = Math.Round(Convert.ToDecimal(reqModel.InQuantity), 3); // Convert.ToInt32(Invent.Quantity);
                    transOld.QuantityUomId = materialInvent.QuantityUomId;
                    transOld.ProductionExecutionId = sOldData.ProductionRequestId;
                    transOld.NewProductionExecutionId = sOldData.ProductionRequestId;
                    transOld.Type = "Transfer";
                    transOld.Comment = "复称-库存转移";
                    //trans.NewEquipmentRequirementId = Invent;
                    //trans.OldEquipmentRequirementId = Invent.EquipmentId;
                    //trans.TransferGroupId
                    transOld.OldEquipmentId = materialInvent.EquipmentId;
                    transOld.NewEquipmentId = materialInvent.EquipmentId;
                    transOld.OldContainerId = materialInvent.ContainerId;
                    transOld.NewContainerId = materialInvent.ContainerId;
                    //status
                    transOld.OldMaterialId = sOldData.MaterialId;
                    transOld.NewMaterialId = sOldData.MaterialId;
                    transOld.OldLotExternalStatus = sOldData.StatusF;
                    transOld.OldSublotExternalStatus = sOldData.StatusS;
                    transOld.NewLotExternalStatus = sOldData.StatusF;
                    transOld.NewSublotExternalStatus = sOldData.StatusS;
                    //trans.PhysicalQuantity = model.MaxVolume == null ? "" : model.MaxVolume.ToString(); //物理数量
                    //trans.TareQuantity = model.TareWeight == null ? 0 : model.TareWeight.Value;  //皮数量

                    tranHis = await _dalMaterialTransferEntity.Add(transOld) > 0;

                    #endregion

                    //都需要更新数据(自己的)
                    materialInvent.Quantity = Math.Round(Convert.ToDecimal(reqModel.InQuantity), 3); //Convert.ToDecimal(reqModel.InQuantity);
                    materialInvent.IsWeighingCheck = "已复秤";
                    materialInvent.Modify(materialInvent.ID, _user.Name.ToString());
                    UpMaterialModels.Add(materialInvent);

                    #region 该表批次状态，这里要判断当前批次下所有数据都复称完成后才行

                    var ischeckList = await _dal.FindList(p => p.BatchId == batchID);

                    //所有数据
                    int total = ischeckList.Count;
                    int finishTotal = ischeckList.ToList().Where(p => p.IsWeighingCheck == "已复秤").Count();
                    //var ischeckList = await _dal.FindList(p => p.BatchId == batchID && p.IsWeighingCheck == "已复秤");
                    if (total - finishTotal == 1)
                    {
                        var BatchEntity = await _BatchEntity.FindEntity(x => x.ID == batchID);
                        BatchEntity.PrepStatus = "10";//复秤完成
                        BatchEntity.Modify(BatchEntity.ID, _user.Name.ToString());
                        UpBatchModels.Add(BatchEntity);
                    }

                    #endregion



                    bool Batch = true;
                    bool material = true;
                    if (UpBatchModels.Count > 0)
                    {
                        Batch = await _BatchEntity.Update(UpBatchModels);
                    }
                    if (UpMaterialModels.Count > 0)
                    {
                        material = await _MaterialInventoryEntity.Update(UpMaterialModels);
                    }

                    if (Batch == false || material == false)
                    {

                        _unitOfWork.RollbackTran();
                        result.success = false;
                        return result;
                    }

                    _unitOfWork.CommitTran();
                    result.success = true;
                    return result;
                }

                result.success = false;
                return result;
            }
            catch (System.Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "错误信息：" + ex.Message + ex.StackTrace;
                return result;
            }

        }
        /// <summary>
        /// 复秤库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetCheckWeight(CheckWeightModel reqModel)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;

            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
           //加入查询条件(时间)
           //batch pallet id
           .And(a => a.PId == "" || a.PId == null)
           .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
           .And(a => a.BatchId == "" || a.BatchId == null)
           .And(a => a.ContainerId == "" || a.ContainerId == null)
           .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
           .AndIF(!string.IsNullOrEmpty(reqModel.SSCC), a => a.SbSscc == reqModel.SSCC)
           .And(p => p.HType == "" || p.HType == null).ToExpression();


            var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression)
                 .OrderByDescending(p => p.CreateDate)
                 .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;

            return result;
        }
        public async Task<bool> SaveForm(CheckWeightViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}