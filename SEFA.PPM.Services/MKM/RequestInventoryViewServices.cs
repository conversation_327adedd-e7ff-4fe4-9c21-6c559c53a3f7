
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using static SEFA.MKM.Services.MaterialInventoryServices;
using System.IO;
using System.Net;
using System.Text;
using System;
using Newtonsoft.Json;
using SEFA.Base.Common.HttpContextUser;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels.View;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Collections;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.ESB;
using SEFA.Base.Common.Common;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using MongoDB.Bson;
using StackExchange.Profiling.Internal;
using ORDGR;
using static SEFA.PTM.Services.ConsumeViewServices;
using MM_WMS_STOCK_TRANSFER;
using MM_STOCK_QUERY;
using SEFA.MKM.Model.Models.MKM;
using System.Reflection.Emit;
using SEFA.Base.Common.WebApiClients.HttpApis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using Castle.Core;
using System.Net.NetworkInformation;
using ORDGI;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.ViewModels.PPM;
using System.Reactive;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.ViewModels;
using Abp.Application.Services.Dto;
using SEFA.Base.Common.LogHelper;
using LKK.Lib.Core;
using Castle.MicroKernel.SubSystems.Conversion;
using SEFA.PPM.Model.Models.MKM;


namespace SEFA.PPM.Services
{
    public class RequestInventoryViewServices : BaseServices<RequestInventoryViewEntity>, IRequestInventoryViewServices
    {

        private readonly IBaseRepository<RequestInventoryViewEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        public IUser _user;
        private readonly IBaseRepository<UnitConvertEntity> _UnitConvertEntity;
        private readonly IBaseRepository<RequestDetailEntity> _RequestDetailEntity;
        private readonly IBaseRepository<InventoryRequestEntity> _InventoryRequestEntity;
        private readonly IBaseRepository<InventoryControlEntity> _InventoryControlEntity;
        private readonly IBaseRepository<InventorylistingViewEntity> _MaterialInventdal;
        private readonly LKKESBHelper _esbHelper;
        private readonly IBaseRepository<ProductionOrderEntity> _productionOrderEntity;
        private readonly IBaseRepository<PoConsumeRequirementEntity> _poConsumeRequirementEntityDal;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _poUnitmanageEntityDal;
        private readonly IBaseRepository<MaterialEntity> _poMaterialEntity;
        private readonly IBaseRepository<MaterialGroupViewEntity> _dalMaterialGroupViewEntity;
        private readonly IBaseRepository<RequestIiiViewEntity> _RequestIiiViewEntity;

        #region 请料


        private readonly IBaseRepository<OrderBomEntity> _dalOrderBomEntity;

        private readonly IBaseRepository<DFM.Model.Models.CategoryEntity> _CategoryEntityDal;
        private readonly IBaseRepository<MaterialGroupsViewEntity> _MatGroupsEntityDal;
        private readonly IBaseRepository<PullDetailEntity> _PullDetailEntityDal;
        private readonly IBaseRepository<InventoryPullEntity> _InventoryPullEntityDal;

        private readonly IBaseRepository<DFM.Model.Models.EquipmentMaterialEntity> _EquipmentMaterialEntityDal;

        private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _EquipmentEntityDal;


        #endregion
        public RequestInventoryViewServices(IBaseRepository<RequestInventoryViewEntity> dal, IUnitOfWork unitOfWork, IUser user, IBaseRepository<UnitConvertEntity> UnitConvertEntity,
            IBaseRepository<RequestDetailEntity> RequestDetailEntity, IBaseRepository<InventoryRequestEntity> InventoryRequestEntity,
            LKKESBHelper esbHelper, IBaseRepository<InventoryControlEntity> InventoryControlEntity, IBaseRepository<InventorylistingViewEntity> MaterialInventdal, IBaseRepository<ProductionOrderEntity> productionOrderEntity,
            IBaseRepository<PoConsumeRequirementEntity> poConsumeRequirementEntityDal, IBaseRepository<DFM.Model.Models.UnitmanageEntity> poUnitmanageEntityDal, IBaseRepository<MaterialEntity> poMaterialEntity, IBaseRepository<MaterialGroupViewEntity> dalMaterialGroupViewEntity, IBaseRepository<RequestIiiViewEntity> requestIiiViewEntity, IBaseRepository<DFM.Model.Models.CategoryEntity> categoryEntityDal, IBaseRepository<MaterialGroupsViewEntity> matGroupsEntityDal, IBaseRepository<PullDetailEntity> pullDetailEntityDal, IBaseRepository<InventoryPullEntity> inventoryPullEntityDal, IBaseRepository<DFM.Model.Models.EquipmentMaterialEntity> equipmentMaterialEntity, IBaseRepository<DFM.Model.Models.EquipmentEntity> equipmentEntityDal, IBaseRepository<OrderBomEntity> dalOrderBomEntity)
        {

            this._dal = dal;
            base.BaseDal = dal;
            _unitOfWork = unitOfWork;
            _user = user;
            _UnitConvertEntity = UnitConvertEntity;
            _RequestDetailEntity = RequestDetailEntity;
            _InventoryRequestEntity = InventoryRequestEntity;
            _esbHelper = esbHelper;
            _InventoryControlEntity = InventoryControlEntity;
            _MaterialInventdal = MaterialInventdal;
            _productionOrderEntity = productionOrderEntity;
            _poConsumeRequirementEntityDal = poConsumeRequirementEntityDal;
            _poUnitmanageEntityDal = poUnitmanageEntityDal;
            _poMaterialEntity = poMaterialEntity;
            _dalMaterialGroupViewEntity = dalMaterialGroupViewEntity;
            _RequestIiiViewEntity = requestIiiViewEntity;
            _CategoryEntityDal = categoryEntityDal;
            _MatGroupsEntityDal = matGroupsEntityDal;
            _PullDetailEntityDal = pullDetailEntityDal;
            _InventoryPullEntityDal = inventoryPullEntityDal;
            _EquipmentMaterialEntityDal = equipmentMaterialEntity;
            _EquipmentEntityDal = equipmentEntityDal;
            _dalOrderBomEntity = dalOrderBomEntity;
        }

        public async Task<List<RequestInventoryViewEntity>> GetList(RequestInventoryViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<RequestInventoryViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<RequestInventoryViewEntity>> GetPageList(RequestInventoryViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<RequestInventoryViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(RequestInventoryViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        /// <summary>
        /// 将集合进行分组
        /// </summary>
        /// <param name="myList">原集合</param>
        /// <param name="GroupNum">每组的数量 ps:最后一组数量不足时按照剩余数量统计</param>
        /// <returns></returns>
        public static List<List<T>> GetGroupLists<T>(List<T> myList, int GroupNum)
        {
            List<List<T>> listGroup = new List<List<T>>();
            int j = GroupNum;
            for (int i = 0; i < myList.Count; i += GroupNum)
            {
                List<T> cList = new List<T>();
                cList = myList.Take(j).Skip(i).ToList();
                j += GroupNum;
                listGroup.Add(cList);
            }
            return listGroup;
        }


        #region WMS接口 

        #region WebService 接口

        /// <summary>
        /// 库存查询
        /// </summary>
        public async void StockQuery()
        {
            var eT_MCHBField = new List<ZRFC_WMS_MCHB>();
            var iS_MCHBField = new ZRFC_WMS_MCHB();
            var iT_MCHBField = new List<ZRFC_WMS_MCHB>();

            iS_MCHBField.WERKS = "2010";
            iS_MCHBField.LGORT = "";

            ZRFC_WMS_MCHB ZRFC_WMS_MCHB = new ZRFC_WMS_MCHB();
            ZRFC_WMS_MCHB.MATNR = "M200007712";
            ZRFC_WMS_MCHB.WERKS = "";
            ZRFC_WMS_MCHB.LGORT = "";
            ZRFC_WMS_MCHB.CHARG = "";
            iT_MCHBField.Add(ZRFC_WMS_MCHB);

            var requestSend = new ZRFC_MM_WMS_STOCK_QUERY2()
            {
                ET_MCHB = eT_MCHBField.ToArray(),
                IS_MCHB = iS_MCHBField,
                IT_MCHB = iT_MCHBField.ToArray()

            };
            var getXml = InterfaceHelper.GetSapRequestXML<ZRFC_MM_WMS_STOCK_QUERY2>(requestSend);
            var getTransfer = await _esbHelper.PostXMLString("SAP_STOCKQUERY", getXml, null);
            var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_MM_WMS_STOCK_QUERY2Response>(getTransfer.Response);

            var result = reponse.ET_MCHB;
        }


        /// <summary>
        /// 转移接口（用于退货用，转移用）
        /// </summary>
        public List<ReturnStockTran> Transfer(SapTransfer sapModel)
        {
            List<ReturnStockTran> listResult = new List<ReturnStockTran>();

            try
            {
                var eT_MSEGField = new List<ZRFC_WMS_MSEG>();
                var eT_MSGField = new List<ZRFC_WMS_MESSAGE>();
                var iS_MKPFField = new ZRFC_WMS_MKPF();
                var iT_MSEGField = new List<ZRFC_WMS_MSEG>();


                #region 构造发送实体

                ZRFC_WMS_MSEG msegModel = new ZRFC_WMS_MSEG();
                msegModel.MBLNR = "";
                msegModel.MJAHR = "";
                msegModel.ZEILE = "";
                msegModel.LINE_ID = "";
                msegModel.PARENT_ID = "";
                msegModel.BWART = "";
                msegModel.XAUTO = "";
                msegModel.MATNR = "";
                msegModel.MENGE = 0;
                msegModel.MEINS = "";
                msegModel.HSDAT = "";
                msegModel.VFDAT = "";
                msegModel.SHKZG = "";
                msegModel.WERKS = "";
                msegModel.LGORT = "";
                msegModel.CHARG = "";
                msegModel.INSMK = "";
                msegModel.UMMAT = "";
                msegModel.UMWRK = "";
                msegModel.UMLGO = "";
                msegModel.UMCHA = "";
                msegModel.LFBNR = "";
                msegModel.LFPOS = "";
                msegModel.EBELN = "";
                msegModel.EBELP = "";
                msegModel.KOSTL = "";
                msegModel.SAKTO = "";
                msegModel.WEMPF = "";
                msegModel.PS_PSP_PNR = "";
                msegModel.POSID = "";
                msegModel.LIFNR = "";
                msegModel.GRUND = "";
                msegModel.SGTXT = "";
                msegModel.VLTYP = "";
                msegModel.VLBER = "";
                msegModel.VLPLA = "";
                msegModel.NLTYP = "";
                msegModel.NLBER = "";
                msegModel.NLPLA = "";
                msegModel.LETYP = "";
                msegModel.SJAHR = "";
                msegModel.SMBLN = "";
                msegModel.SMBLP = "";
                msegModel.ERFMG = 0;
                msegModel.LFBJA = "";
                msegModel.LGNUM = "";
                msegModel.UBNUM = "";
                msegModel.AUFNR = "";
                msegModel.SOBKZ = "";
                msegModel.MAT_KDAUF = "";
                msegModel.MAT_KDPOS = "";
                msegModel.KDAUF = "";
                msegModel.KDPOS = "";
                msegModel.VBELN_IM = "";
                msegModel.VBELP_IM = "";
                msegModel.RSNUM = "";
                msegModel.RSPOS = "";
                msegModel.ABLAD = "";
                eT_MSEGField.Add(msegModel);

                ZRFC_WMS_MESSAGE zRFC_WMS_MESSAGE = new ZRFC_WMS_MESSAGE();
                zRFC_WMS_MESSAGE.MSGTYP = "";
                zRFC_WMS_MESSAGE.MSGID = "";
                zRFC_WMS_MESSAGE.MSGNR = "";
                zRFC_WMS_MESSAGE.MSGV1 = "";
                zRFC_WMS_MESSAGE.MSGV2 = "";
                zRFC_WMS_MESSAGE.MSGV3 = "";
                zRFC_WMS_MESSAGE.MSGV4 = "";
                zRFC_WMS_MESSAGE.OBJECT = "";
                zRFC_WMS_MESSAGE.TEXT = "";
                eT_MSGField.Add(zRFC_WMS_MESSAGE);


                iS_MKPFField.MBLNR = "";
                iS_MKPFField.MJAHR = "";
                iS_MKPFField.BKTXT = sapModel.BKTXT;//表头文本
                iS_MKPFField.BUDAT = sapModel.BUDAT;//过账日期
                iS_MKPFField.XBLNR = "";

                for (int i = 0; i < sapModel.SendList.Count; i++)
                {
                    #region 加入发送数据

                    ZRFC_WMS_MSEG it_msg = new ZRFC_WMS_MSEG();

                    it_msg.MBLNR = "";
                    it_msg.MJAHR = "";
                    it_msg.ZEILE = "";
                    it_msg.LINE_ID = sapModel.SendList[i].LINE_ID == string.Empty ? i.ToString() : sapModel.SendList[i].LINE_ID;  //？待确认
                    it_msg.PARENT_ID = "";
                    it_msg.BWART = sapModel.SendList[i].BWART; //移动类型
                    it_msg.XAUTO = "";
                    it_msg.MATNR = sapModel.SendList[i].MATNR; //物料
                    it_msg.MENGE = sapModel.SendList[i].MENGE;  //数量
                    it_msg.MEINS = sapModel.SendList[i].MEINS;//单位
                    it_msg.HSDAT = "";
                    it_msg.VFDAT = "";
                    it_msg.SHKZG = "";
                    it_msg.WERKS = sapModel.SendList[i].WERKS;//发货工厂代码
                    it_msg.LGORT = sapModel.SendList[i].LGORT;//发货库存地点
                    it_msg.CHARG = sapModel.SendList[i].CHARG;//发货批次
                    it_msg.INSMK = "";
                    it_msg.UMMAT = "";
                    it_msg.UMWRK = sapModel.SendList[i].UMWRK; //接收工厂代码
                    it_msg.UMLGO = sapModel.SendList[i].UMLGO;//接收库存地点
                    it_msg.UMCHA = sapModel.SendList[i].UMCHA;//接收批次 
                    it_msg.LFBNR = "";
                    it_msg.LFPOS = "";
                    it_msg.EBELN = "";
                    it_msg.EBELP = "";
                    it_msg.KOSTL = "";
                    it_msg.SAKTO = "";
                    it_msg.WEMPF = "";
                    it_msg.PS_PSP_PNR = "";
                    it_msg.POSID = "";
                    it_msg.LIFNR = "";
                    it_msg.GRUND = "";
                    it_msg.SGTXT = sapModel.SendList[i].SGTXT == string.Empty ? "MES Transfer" : sapModel.SendList[i].SGTXT;//行文本
                    it_msg.VLTYP = "";
                    it_msg.VLBER = "";
                    it_msg.VLPLA = "";
                    it_msg.NLTYP = "";
                    it_msg.NLBER = "";
                    it_msg.NLPLA = "";
                    it_msg.LETYP = "";
                    it_msg.SJAHR = "";
                    it_msg.SMBLN = "";
                    it_msg.SMBLP = "";
                    it_msg.ERFMG = 0;
                    it_msg.LFBJA = "";
                    it_msg.LGNUM = "";
                    it_msg.UBNUM = "";
                    it_msg.AUFNR = "";
                    it_msg.SOBKZ = "";
                    it_msg.MAT_KDAUF = "";
                    it_msg.MAT_KDPOS = "";
                    it_msg.KDAUF = "";
                    it_msg.KDPOS = "";
                    it_msg.VBELN_IM = "";
                    it_msg.VBELP_IM = "";
                    it_msg.RSNUM = "";
                    it_msg.RSPOS = "";
                    it_msg.ABLAD = "";
                    iT_MSEGField.Add(it_msg);

                    #endregion
                }


                var requestSend = new ZRFC_MM_WMS_STOCK_TRANSFER()
                {

                    ET_MSEG = eT_MSEGField.ToArray(),
                    ET_MSG = eT_MSGField.ToArray(),
                    IS_MKPF = iS_MKPFField,
                    IT_MSEG = iT_MSEGField.ToArray(),
                    IV_UNAME = ""

                };

                #endregion
                var sendXml = InterfaceHelper.GetSapRequestXML<ZRFC_MM_WMS_STOCK_TRANSFER>(requestSend);
                var dataReturn = _esbHelper.PostXMLString("SAP_STOCKTRANSFER", sendXml, null);
                var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_MM_WMS_STOCK_TRANSFERResponse>(dataReturn.Result.Response);

                string msg = string.Empty; ;
                string sucess = "OK";
                //获取需要的数据(物料凭证编码)
                ZRFC_WMS_MESSAGE[] msgData = reponse.ET_MSG;
                if (msgData.Length > 1)
                {
                    string message = msgData[1].TEXT;
                    if (!string.IsNullOrEmpty(message))
                    {
                        sucess = "NOK";
                        msg = message;
                    }

                }


                //这里拿下是否成功，默认没有消息就就代表成功

                ZRFC_WMS_MSEG[] list = reponse.ET_MSEG;

                //这里判断数据
                int iNumber = 0;
                if (list.Length > sapModel.SendList.Count)
                {
                    iNumber = 1;
                }

                for (int i = iNumber; i < list.Length; i++)
                {
                    ReturnStockTran model = new ReturnStockTran();
                    model.Msg = msg;
                    model.Sucess = sucess;
                    /// 物料凭证编码             
                    model.MCodeNameplateNo = list[i].MBLNR;
                    /// 物料凭证编码物料凭证年              
                    model.MCodeNameplateNoYear = list[i].MJAHR;
                    /// 物料凭证行号(返回数据行号)               
                    model.BUDAT = list[i].ZEILE;
                    /// 待确定             
                    model.LINE_ID = list[i].LINE_ID;
                    /// 传入的移动类型             
                    model.BWART = list[i].BWART;
                    /// 物料Code               
                    model.MCode = list[i].MATNR;
                    /// 物料数量              
                    model.MENGE = list[i].MENGE;
                    /// 物料单位               
                    model.MEINS = list[i].MEINS;
                    listResult.Add(model);
                }

                return listResult;
            }
            catch
            {
                return null;
            }

        }


        #endregion

        //esb接口  
        //WMS_FindBarcodeInfo 读取标签信息
        //WMS_CreatePm MES新增拉料
        //WMS_Partlock MES请料
        //WMS_FindPack 查询包装规格


        public async Task<MessageModel<string>> InterFaceTest()
        {
            var result = new MessageModel<string>();
            result.success = false;
            //    await InterFACesSC(); //获取标签
            //TextInterFace_Print();//打印
            //TextInterFace_GetRequestMaterial();//请料
            // TextInterFace_PullMaterial();//拉料
            //TextInterFace_GePacktResult();// 获取包装规格接口


            #region 调用SAP接口

            SapTransfer sapTransfer = new SapTransfer();
            sapTransfer.BKTXT = "MESTest001";
            sapTransfer.BUDAT = "2024-08-08";
            List<SendTran> list = new List<SendTran>();
            SendTran model = new SendTran();
            model.LINE_ID = "1";
            model.BWART = "311";

            model.MATNR = "M200000052";
            model.MENGE = 1;
            model.MEINS = "EA";

            model.WERKS = "2010";
            model.LGORT = "EPP1";
            model.CHARG = "M244180076";

            model.UMWRK = "2010";
            model.UMLGO = "EPP3";
            model.UMCHA = "M244180076";

            model.SGTXT = "MES Transfer test";
            list.Add(model);
            list.Add(model);
            SendTran model1 = new SendTran();
            //list.Add(model1);
            //list.Add(model1);
            // list.Add(model);
            //list.Add(model1);
            sapTransfer.SendList = list;
            Transfer(sapTransfer);    //转移接口（用于退货用，转移用）

            #endregion

            //      StockQuery();  //库存查询
            return result;
        }


        #region MyRegion


        public async Task<MessageModel<string>> InterFACesSC()
        {
            var result = new MessageModel<string>();
            result.success = false;
            TextInterFace_Getsscc();
            return result;
        }
        public async void TextInterFace_Getsscc()
        {
            ScanSend sd = new ScanSend();
            sd.itembarcode = "6000015230";
            //	am.itemcode = "6000015230,6000015231,6000015232";
            await GetSSCCInfo(sd);
        }



        /// <summary>
        /// 获取条码信息(调用接口)异步
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<ScanRootResult>> GetSSCCInfo(ScanSend model)
        {
            try
            {
                string json = JsonConvert.SerializeObject(model);
                string sendMsg = "key=WCS&data=" + json;
                var ssResult = await _esbHelper.PostString<ScanRootResult>("WMS_FindBarcodeInfo", sendMsg);
                return ssResult;
            }
            catch (Exception ex)
            {

                return new LKKESBResponse<ScanRootResult>();
            }

        }

        /// <summary>
        /// 同步获取wsm sscc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public LKKESBResponse<ScanRootResult> GetWmsSSCC(ScanSend model)
        {
            try
            {
                string json = JsonConvert.SerializeObject(model);
                string sendMsg = "key=WCS&data=" + json;
                var ssResult = _esbHelper.PostString<ScanRootResult>("WMS_FindBarcodeInfo", sendMsg);
                return ssResult.Result;
            }
            catch (Exception ex)
            {

                return new LKKESBResponse<ScanRootResult>();
            }

        }

        #endregion
        //    标签打印

        /// <summary>
        /// 接口测试(打印)
        /// </summary>
        public async void TextInterFace_Print()
        {
            //List<PrintSendDataItems> a = new List<PrintSendDataItems>();

            //PrintSendDataItems amodel1 = new PrintSendDataItems();
            //amodel1.itemcode = "4300120157";
            //amodel1.itemunitcode = "PC";
            //amodel1.batch = "D183170197";
            //amodel1.qty = 123;
            //amodel1.type = 1;
            //amodel1.invstatus = 2;
            //a.Add(amodel1);
            //a.Add(amodel1);

            ////PrintSendDataItems amodel2 = new PrintSendDataItems();
            ////amodel2.itemcode = "6300230004";
            ////amodel2.itemunitcode = "KG";
            ////amodel2.qty = 123;
            ////a.Add(amodel1);

            //await PrintLabel(a);
        }

        /// <summary>
        /// 接口测试(请料)
        /// </summary>
        public async void TextInterFace_GetRequestMaterial()
        {
            List<RequestMaterial> a = new List<RequestMaterial>();
            RequestMaterialSend am = new RequestMaterialSend();
            RequestMaterial amodel1 = new RequestMaterial();
            amodel1.partmaterialcode = "4300010088";
            amodel1.partunit = "pc";
            amodel1.linenumber = "";
            amodel1.qty = 5000;
            amodel1.batch = "";
            amodel1.date = "2024-08-15";
            amodel1.arrivetime = "2024-08-15";
            amodel1.recivestockcode = "PKG3";


            a.Add(amodel1);

            await GetRequestMaterialSend(a);
        }

        /// <summary>
        /// 接口测试(拉料)
        /// </summary>
        public async void TextInterFace_PullMaterial()
        {
            List<PullMaterial> a = new List<PullMaterial>();
            PullMaterialSend am = new PullMaterialSend();
            PullMaterial amodel1 = new PullMaterial();
            amodel1.partmaterialcode = "4300010088";
            amodel1.partunit = "pc";
            //amodel1.linenumber = "1";
            amodel1.qty = 50000;
            //amodel1.batch = "";
            amodel1.recivestockcode = "MFG3";
            amodel1.date = "2019-05-15 17:00:00";

            a.Add(amodel1);
            await GetPullMaterial(a);
        }
        /// <summary>
        /// 接口测试 获取包装规格接口
        /// </summary>
        public async void TextInterFace_GePacktResult()
        {
            PackingSizeSend am = new PackingSizeSend();
            am.itemcode = "4300120157,4300120158,4300120159";
            await GePacktResult(am);
        }

        #region 请料接口--手动创建请料时使用

        #region 请求

        /// <summary>
        /// 请料接口（MES_新增拉料需求）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PullMaterialResult>> GetRequestMaterialSend(List<RequestMaterial> model)
        {
            string json = JsonConvert.SerializeObject(model);
            // string sendMsg = "key=WCS&data=[{\"partmaterialcode\":\"4300010088\",\"partunit\":\"pc\",\"linenumber\":\"\",\"qty\":5000,\"batch\":\"\",\"date\":\"2024-08-15\",\"locationp1\":\"\",\"arrivetime\":\"2024-08-15\",\"recivestockcode\":\"PKG3\",\"remark\":\"\" }]";
            //"key=WCS&data=" + json;
            string jsons = "key=WCS&data=" + json;
            var response = await _esbHelper.PostString<PullMaterialResult>("WMS_PartLock", jsons);
            return response;
        }

        #endregion

        #endregion

        #region 获取包装规格接口

        #region 请求

        /// <summary>
        /// 获取规格属性，上限10个物料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PackingSizeResult>> GePacktResult(PackingSizeSend model)
        {
            string json = JsonConvert.SerializeObject(model);
            string sendMsg = "key=WCS&data=" + json;
            var response = await _esbHelper.PostString<PackingSizeResult>("WMS_FindPack", sendMsg);
            return response;
        }

        #endregion

        #endregion

        #region 拉料接口      

        #region 请求

        /// <summary>
        /// 请料
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PullMaterialResult>> GetPullMaterial(List<PullMaterial> model)
        {
            string json = JsonConvert.SerializeObject(model);
            string sendMsg = "key=WCS&data=" + json;
            //var response = await _esbHelper.PostString<PullMaterialResult>("WMS_CreatePm", sendMsg);
            var response = await _esbHelper.PostString<PullMaterialResult>("WMS_BatchCreatePm", sendMsg);
            return response;
        }

        #endregion

        #endregion

        #region 标签打印接口

        #region 请求

        /// <summary>
        /// 打印标签接口
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PrintResult>> PrintLabel(List<PrintSendDataItems> model)
        {
            string json = JsonConvert.SerializeObject(model);
            string sendMsg = "key=WCS&data=" + json;
            var response = await _esbHelper.PostString<PrintResult>("WMS_PrintTag", sendMsg);
            return response;
        }

        /// <summary>
        /// 同步进行打印标签操作
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<LKKESBResponse<PrintResult>> PrintLabelSynchro(List<PrintSendDataItems> model)
        {
            string json = JsonConvert.SerializeObject(model);
            string sendMsg = "key=WCS&data=" + json;
            var response = await _esbHelper.PostString<PrintResult>("WMS_PrintTag", sendMsg);
            return response;
        }


        #endregion

        #endregion

        #endregion


        #region 请料逻辑处理

        #region 函数

        /// <summary>
        /// 原物料请料逻辑，触发时间外层完成控制(已经释放工单)
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> RequestMaterials(RequestMaterialsModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            //转换时间
            if (reqModel.planStarTime == null || reqModel.planEndTime == null)
            {
                result.msg = "请选择找工单时间段";
                return result;
            }
            try
            {
                //计算已经释放工单 poStatus= 2
                //查询所有释放工单
                DateTime star = Convert.ToDateTime(reqModel.planStarTime);
                DateTime end = Convert.ToDateTime(reqModel.planEndTime);
                var data1 = await _dal.FindList(p => p.PlanStartTime >= star && p.PlanStartTime <= end && p.NeedQuantity >= p.Inventqty && p.PoStatus.Value == 2);

                #region 把有用数据拿来分组(这里需要按照需求分组)
                var data = (from a in data1
                            group a by new
                            {
                                // PlanStartTime = a.PlanStartTime.Value.Date,
                                NeedUnit = a.NeedUnit,
                                EquipmentId = a.EquipmentId,
                                Inventqty = a.Inventqty,
                                MCode = a.MCode,
                                InventUnit = a.InventUnit,
                                MId = a.MId,
                                LotState = a.LotState

                            } into g
                            select new
                            {
                                NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),
                                NeedUnit = g.Key.NeedUnit,
                                EquipmentId = g.Key.EquipmentId,
                                Inventqty = g.Key.Inventqty,
                                MCode = g.Key.MCode,
                                InventUnit = g.Key.InventUnit,
                                MId = g.Key.MId,
                                LotState = g.Key.LotState

                            }).ToList();

                #endregion

                //返回的数据集合（规格数据接口）
                List<PackingSizeDataItem> packList = new List<PackingSizeDataItem>();

                #region 计算数据

                //筛选出来
                List<string> mCodes = data.GroupBy(i => i.MCode).Select(p => p.Key).ToList();

                if (mCodes == null || mCodes.Count <= 0)
                {
                    result.msg = "无需请料";
                    result.success = true;
                    return result;
                }

                #region 循环添加LIST10条为一个集合

                List<List<string>> stringList = new List<List<string>>();
                int j = 10;
                for (int i = 0; i < mCodes.Count; i += 10)
                {
                    List<string> cList = new List<string>();
                    cList = mCodes.Take(j).Skip(i).ToList();
                    j += 10;
                    stringList.Add(cList);
                }

                #endregion

                //循环
                for (int i = 0; i < stringList.Count; i++)
                {
                    //拼接字符串
                    string strCodes = stringList[i].Aggregate((x, y) => x + "," + y);
                    PackingSizeSend model = new PackingSizeSend();
                    model.itemcode = strCodes;

                    //调用查询规格接口
                    var pR = GePacktResult(model).Result;
                    if (pR.successed == false)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }

                    //调用物料接口
                    PackingSizeResult packR = pR.Response;
                    if (packR.flag != true)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }
                    for (global::System.Int32 x = 0; x < packR.data.Count; x++)
                    {
                        PackingSizeDataItem pModel = new PackingSizeDataItem();
                        pModel.itemcode = packR.data[x].itemcode;
                        pModel.packcode = packR.data[x].packcode;
                        pModel.packQty = packR.data[x].packQty;
                        pModel.barcodeQty = packR.data[x].barcodeQty;
                        packList.Add(pModel);
                    }
                }

                #endregion

                List<RequestInventoryViewRequestModel> resultList = new List<RequestInventoryViewRequestModel>();
                //发送实体
                List<PullMaterial> pullMaterialList = new List<PullMaterial>();
                List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
                List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();
                #region 计算数据库查询出来需要进行请料的数据集合

                string mCode = string.Empty;
                decimal needQty = 0;
                string needUnit = "";
                decimal inventQty = 0;
                string inventUnit = "";
                decimal gch = 0;
                string mID = string.Empty;

                _unitOfWork.BeginTran();

                for (int i = 0; i < data.Count; i++)
                {
                    mCode = data[i].MCode;
                    needQty = data[i].NeedQuantity;
                    needUnit = data[i].NeedUnit;
                    inventQty = data[i].Inventqty == null ? 0 : data[i].Inventqty.Value;
                    inventUnit = data[i].InventUnit;
                    gch = 0;

                    //这里可能会存在单位换算给
                    var selectList = packList.Where(P => P.itemcode == mCode).ToList();
                    if (selectList != null && selectList.Count > 0)
                    {
                        //需要换算
                        if (needUnit != null && inventUnit != null)
                        {
                            if (needUnit != inventUnit)
                            {
                                //加上单位逻辑转换
                            }
                        }

                        //计算当前物料单柜总数量
                        decimal pkNumber = Convert.ToDecimal(selectList[0].packQty == null ? 0 : selectList[0].packQty) * Convert.ToDecimal(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        //1000 1100

                        //需求数量
                        decimal xqch = needQty - inventQty;
                        //需要发送的请料柜数
                        decimal totalNumber = xqch / pkNumber;
                        decimal ys = xqch % pkNumber;
                        if (ys > 0)
                        {
                            totalNumber = totalNumber + 1;

                            //重置下柜数
                            if (totalNumber % Convert.ToDecimal(1) > 0)
                            {
                                totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                            }
                        }

                        #region 写入表（这里不进行提交数据）
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = data[i].MId;
                        id = model.ID;
                        model.UomId = data[i].NeedUnit;
                        model.EquipmentId = data[i].EquipmentId;
                        model.ExternalStatus = data[i].LotState;
                        model.Type = reqModel.AutoType;
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = xqch;
                        rDModel.Status = "";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        #endregion

                        #region 构造发送数据

                        PullMaterial pmodel = new PullMaterial();



                        #region 新版

                        pmodel.partmaterialcode = mCode;
                        pmodel.partunit = inventUnit;
                        pmodel.qty = totalNumber;//totalNumber;
                        pmodel.batch = "";//批次号如何定位
                        //接收仓位
                        if (mCode.Substring(0, 1) == "4")
                        {
                            pmodel.recivestockcode = "PKG3"; //"MFG3";
                        }
                        else
                        {
                            pmodel.recivestockcode = "MFG3"; //"MFG3";
                        }
                        // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                        //到货日期(这里注意数据转换)
                        pmodel.arrivetime = reqModel.planStarTime;

                        #endregion

                        //pmodel.partmaterialcode = mCode;
                        //pmodel.partunit = inventUnit;
                        //pmodel.qty = totalNumber;
                        //if (mCode.Substring(0, 1) == "4")
                        //{
                        //    pmodel.recivestockcode = "PKG3"; //"MFG3";
                        //}
                        //else
                        //{
                        //    pmodel.recivestockcode = "MFG3"; //"MFG3";
                        //}

                        // pmodel.recivestockcode = "MFG3";//必须填写
                        pullMaterialList.Add(pmodel);

                        #endregion

                    }
                    else
                    {
                        continue;
                    }

                }

                #endregion

                #region 这里进行发送请求集合，成功后进行数据提交

                var pullR = GetPullMaterial(pullMaterialList).Result;

                if (pullR.successed == false)
                {
                    result.msg = "调用请料接口失败";
                    return result;
                }

                PullMaterialResult pullResult = pullR.Response;
                if (pullResult == null)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料请料失败,无返回数据";
                    return result;
                }
                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料请料失败,原因:" + pullResult.msg;
                    return result;
                }
                if (pullResult.flag == true)
                {
                    //执行保存数据操作
                    await _RequestDetailEntity.Add(requestDetailEntityList);
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);

                    //执行提交数据操作
                    _unitOfWork.CommitTran();

                    result.success = true;
                    result.msg = "请料成功.";
                    return result;
                }

                #endregion

                result.msg = "原物料请料失败";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "原物料请料失败" + ex.Message;
                return result;
            }

        }

        /// <summary>
        /// 白糖类请料（每周六凌晨开始）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SugarRequestMaterials(RequestMaterialsModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            decimal jzNum = 0;
            //转换时间
            if (reqModel.planStarTime == null || reqModel.planEndTime == null)
            {
                result.msg = "请选择找工单时间段";
                return result;
            }
            try
            {
                //计算未释放工单 poStatus= 1
                //查询所有未释放工单
                DateTime star = Convert.ToDateTime(Convert.ToDateTime(reqModel.planStarTime).ToString("yyyy-MM-dd") + " 00:00:00");
                DateTime end = star.AddDays(7).AddSeconds(-1);
                //这里是请求一周的料
                var data1 = await _dal.FindList(p => p.PlanStartTime >= star && p.PlanStartTime <= end && p.NeedQuantity >= p.Inventqty && p.PoStatus.Value == 1 && p.MaterialGroupName.Contains("糖"));

                #region 按照天和物料分组算出对应的需求数量

                //数据源  需求数量 物料，需求日期（当天） 4天

                //按照时间
                var dResult = (from a in data1
                               group a by new
                               {
                                   PlanStartTime = a.PlanStartTime.Value.Date,
                                   NeedUnit = a.NeedUnit,
                                   EquipmentId = a.EquipmentId,
                                   Inventqty = a.Inventqty,
                                   MCode = a.MCode,
                                   InventUnit = a.InventUnit,
                                   MId = a.MId,
                                   LotState = a.LotState
                               } into g
                               select new
                               {
                                   NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),
                                   NeedUnit = g.Key.NeedUnit,
                                   EquipmentId = g.Key.EquipmentId,
                                   Inventqty = g.Key.Inventqty,
                                   MCode = g.Key.MCode,
                                   InventUnit = g.Key.InventUnit,
                                   MId = g.Key.MId,
                                   LotState = g.Key.LotState,
                                   PlanStartTime = g.Key.PlanStartTime,

                               }).OrderBy(p => p.PlanStartTime);

                #endregion

                #region 获取所有物料需求量

                //获取当日所有物料需求量
                var data = dResult.Where(p => p.PlanStartTime == star).ToList();

                //获取后续所有物料需求信息
                var otherData = dResult.Where(p => p.PlanStartTime >= star).ToList();


                //执行调整最终数据变更

                for (int i = 0; i < data.Count(); i++)
                {
                    decimal nendQty = data[i].NeedQuantity;
                    DateTime tTime = data[i].PlanStartTime;
                    var selectData = otherData.Where(p => p.NeedQuantity > nendQty);
                    foreach (var item in selectData)
                    {
                        TimeSpan sp = item.PlanStartTime - tTime;
                        decimal fValue = jzNum;//item.NeedQuantity;

                        decimal cha = fValue - nendQty;
                        decimal addValue = cha / (decimal)sp.TotalDays;
                        nendQty = nendQty + addValue;
                    }
                }


                #endregion


                //返回的数据集合（规格数据接口）
                List<PackingSizeDataItem> packList = new List<PackingSizeDataItem>();

                #region 计算数据

                //筛选出来
                List<string> mCodes = data.GroupBy(i => i.MCode).Select(p => p.Key).ToList();

                if (mCodes == null || mCodes.Count <= 0)
                {
                    result.msg = "无需请料";
                    result.success = true;
                    return result;
                }

                #region 循环添加LIST

                List<List<string>> stringList = new List<List<string>>();
                int j = 10;
                for (int i = 0; i < mCodes.Count; i += 10)
                {
                    List<string> cList = new List<string>();
                    cList = mCodes.Take(j).Skip(i).ToList();
                    j += 10;
                    stringList.Add(cList);
                }

                #endregion

                //循环
                for (int i = 0; i < stringList.Count; i++)
                {
                    //拼接字符串
                    string strCodes = stringList[i].Aggregate((x, y) => x + "," + y);
                    PackingSizeSend model = new PackingSizeSend();
                    model.itemcode = strCodes;

                    //调用查询规格接口
                    var pR = GePacktResult(model).Result;
                    if (pR.successed == false)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }

                    //调用物料接口
                    PackingSizeResult packR = pR.Response;
                    if (packR.flag != true)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }
                    for (int x = 0; x < packR.data.Count; x++)
                    {
                        PackingSizeDataItem pModel = new PackingSizeDataItem();
                        pModel.itemcode = packR.data[x].itemcode;
                        pModel.packcode = packR.data[x].packcode;
                        pModel.packQty = packR.data[x].packQty;
                        pModel.barcodeQty = packR.data[x].barcodeQty;
                        packList.Add(pModel);
                    }
                }

                #endregion

                List<RequestInventoryViewRequestModel> resultList = new List<RequestInventoryViewRequestModel>();
                //发送实体
                List<PullMaterial> pullMaterialList = new List<PullMaterial>();
                List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
                List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();
                #region 计算数据库查询出来需要进行请料的数据集合

                string mCode = string.Empty;
                decimal needQty = 0;
                string needUnit = "";
                decimal inventQty = 0;
                string inventUnit = "";
                decimal gch = 0;
                string mID = string.Empty;

                _unitOfWork.BeginTran();

                for (int i = 0; i < data.Count; i++)
                {
                    mCode = data[i].MCode;
                    needQty = data[i].NeedQuantity;
                    needUnit = data[i].NeedUnit;
                    inventQty = data[i].Inventqty == null ? 0 : data[i].Inventqty.Value;
                    inventUnit = data[i].InventUnit;
                    gch = 0;

                    //这里可能会存在单位换算给
                    var selectList = packList.Where(P => P.itemcode == mCode).ToList();
                    if (selectList != null && selectList.Count > 0)
                    {
                        //需要换算
                        if (needUnit != null && inventUnit != null)
                        {
                            if (needUnit != inventUnit)
                            {
                                //加上单位逻辑转换
                            }
                        }

                        //计算当前物料单柜总数量
                        decimal pkNumber = Convert.ToDecimal(selectList[0].packQty == null ? 0 : selectList[0].packQty) * Convert.ToDecimal(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        //1000 1100

                        //需求数量
                        decimal xqch = needQty - inventQty;
                        //需要发送的请料柜数
                        decimal totalNumber = xqch / pkNumber;
                        decimal ys = xqch % pkNumber;
                        if (ys > 0)
                        {
                            totalNumber = totalNumber + 1;

                            //重置下柜数
                            if (totalNumber % Convert.ToDecimal(1) > 0)
                            {
                                totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                            }
                        }

                        #region 写入表（这里不进行提交数据）
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = data[i].MId;
                        id = model.ID;
                        model.UomId = data[i].NeedUnit;
                        model.EquipmentId = data[i].EquipmentId;
                        model.ExternalStatus = data[i].LotState;
                        model.Type = reqModel.AutoType;
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = xqch;
                        rDModel.Status = "";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        #endregion

                        #region 构造发送数据

                        PullMaterial pmodel = new PullMaterial();

                        #region 新版

                        pmodel.partmaterialcode = mCode;
                        pmodel.partunit = inventUnit;
                        pmodel.qty = totalNumber;//totalNumber;
                        pmodel.batch = "";//批次号如何定位
                        //接收仓位
                        if (mCode.Substring(0, 1) == "4")
                        {
                            pmodel.recivestockcode = "PKG3"; //"MFG3";
                        }
                        else
                        {
                            pmodel.recivestockcode = "MFG3"; //"MFG3";
                        }
                        // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                        //到货日期(这里注意数据转换)
                        pmodel.arrivetime = reqModel.planStarTime;

                        #endregion

                        //pmodel.partmaterialcode = mCode;
                        //pmodel.partunit = inventUnit;
                        //pmodel.qty = totalNumber;
                        //if (mCode.Substring(0, 1) == "4")
                        //{
                        //    pmodel.recivestockcode = "PKG3"; //"MFG3";
                        //}
                        //else
                        //{
                        //    pmodel.recivestockcode = "MFG3"; //"MFG3";
                        //}

                        //pmodel.recivestockcode = "MFG3";//必须填写
                        pullMaterialList.Add(pmodel);

                        #endregion

                    }
                    else
                    {
                        continue;
                    }

                }

                #endregion

                #region 这里进行发送请求集合，成功后进行数据提交

                var pullR = GetPullMaterial(pullMaterialList).Result;

                if (pullR.successed == false)
                {
                    result.msg = "调用请料接口失败";
                    return result;
                }

                PullMaterialResult pullResult = pullR.Response;
                if (pullResult == null)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料请料失败,无返回数据";
                    return result;
                }
                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料请料失败,原因:" + pullResult.msg;
                    return result;
                }
                if (pullResult.flag == true)
                {
                    //执行保存数据操作
                    await _RequestDetailEntity.Add(requestDetailEntityList);
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);

                    //执行提交数据操作
                    _unitOfWork.CommitTran();

                    result.success = true;
                    result.msg = "请料成功.";
                    return result;
                }

                #endregion

                result.msg = "原物料请料失败";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "原物料请料失败" + ex.Message;
                return result;
            }

        }


        //public string GetProtoryValue() 
        //{
        //    return "";
        //}

        /// <summary>
        /// JIT请料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> JITRequestMaterials(JITRequestModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            string jitGroupName = string.Empty;
            //先拿JIT物料
            GetGroupModel groupModel = new GetGroupModel();
            groupModel.MaterialGroupName = "JIT物料组";
            ///查询物料组
            string groupID = string.Empty;
            MessageModel<List<ReturnGetGroupModel>> apiResult_EquFunctionPropertys = await HttpHelper.PostAsync<List<ReturnGetGroupModel>>("DFM", "api/MaterialGroup/GetList", _user.GetToken(), new { MaterialGroupName = "JIT物料组" });

            List<ReturnGetGroupModel> apiResult_ = new List<ReturnGetGroupModel>();
            if (apiResult_EquFunctionPropertys.success == true)
            {
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
                apiResult_ = equFunctionPropertys;
                groupID = equFunctionPropertys[0].ID; //这里会有多个数据需要循环
            }
            else
            {
                result.msg = "物料组数据不存在";
                return result;
            }
            //需补充
            for (int i = 0; i < apiResult_.Count(); i++)
            {
                groupID = apiResult_[i].ID;
            }


            #region 属性

            // int minquy = 0;//安全水位（h）
            //   int maxquy = 0;//最高水位（h）
            //int requestquy = 0;//请料水位（h）
            int currentquy = 0;//当前水位(库存)

            // int transferTime = 0;//转柜时长（h）
            //int minnum = 0;// 最小发货托数
            // int duration = 0;// 固定频率时长（h）  触发频率

            decimal cycleTime = 0;//周转时长
            decimal maxquy = 0;//最大库存（h）
            decimal hightquy = 0;// 最高水位（物理）
            // int MaxQtyByPhysical = 0; //最大库存（物理）
            decimal requestquy = 0;//请料水位（h）
            decimal minquy = 0;  //安全水位（h）
            decimal minnum = 20;// 最小发货柜数
            decimal duration = 0; //触发频率
            #endregion

            MessageModel<List<ReturnGetGroupListModel>> getMList = await HttpHelper.PostAsync<List<ReturnGetGroupListModel>>("DFM", "api/BasePropertyValue/GetMaterialGroupPropertyList", _user.GetToken(), new { MaterialGroupid = groupID });
            if (getMList.success == true)
            {  //查询所有属性
                List<ReturnGetGroupListModel> results = getMList.response;
                cycleTime = results.Where(p => p.Code == "CycleTime").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "CycleTime").ToList()[0].Value);
                maxquy = results.Where(p => p.Code == "MaxQtyByHours").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "MaxQtyByHours").ToList()[0].Value);
                hightquy = results.Where(p => p.Code == "MaxQtyByPhysical").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "MaxQtyByPhysical").ToList()[0].Value);
                requestquy = results.Where(p => p.Code == "RequestQty").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "RequestQty").ToList()[0].Value);
                cycleTime = results.Where(p => p.Code == "CycleTime").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "CycleTime").ToList()[0].Value);
                minquy = results.Where(p => p.Code == "SafetyQty").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "SafetyQty").ToList()[0].Value);
                minnum = results.Where(p => p.Code == "MINDeliveryNum").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "MINDeliveryNum").ToList()[0].Value);
                duration = results.Where(p => p.Code == "TriggerFrequency").ToList()[0].Value == string.Empty ? 0 : Convert.ToDecimal(results.Where(p => p.Code == "TriggerFrequency").ToList()[0].Value);

            }
            //转换时间
            if (reqModel.planStarTime == null || reqModel.ActureTime == 0)
            {
                result.msg = "请选择找工单时间段";
                return result;
            }
            try
            {
                //计算未释放工单 poStatus= 1 (执行工单的消耗已经在视图里面做过处理)
                //查询所有未释放工单(默认查询的是当天一整天的)改为当前时间
                DateTime star = DateTime.Now;// Convert.ToDateTime(Convert.ToDateTime(reqModel.planStarTime).ToString("yyyy-MM-dd") + " 00:00:00");
                DateTime endTime = star.AddDays(10).AddSeconds(-1);
                //这里查询的料需要多一些然后来做筛选(7天的)
                List<RequestInventoryViewEntity> dataToday = await _dal.FindList(p => p.PlanStartTime >= star && p.PlanStartTime <= endTime && (p.PoStatus.Value == 2) && p.MaterialGroupName.Contains("JIT"));

                #region 获取未来X的需求量（requestquy）
                if (requestquy == 0)
                {
                    requestquy = 6;
                }
                DateTime end = star.AddHours(Convert.ToDouble(requestquy)).AddSeconds(-1);

                #region 按照天和物料分组算出对应的需求数量(求出来未来X需要请求的主数据)

                var gData = dataToday.Where(p => p.PlanStartTime <= end).ToList();
                //按照时间（获取未来X小时的需求量及库存）
                var dResult = (from a in gData
                               group a by new
                               {
                                   //PlanStartTime = a.PlanStartTime.Value.Date,
                                   NeedUnit = a.NeedUnit,
                                   EquipmentId = a.EquipmentId,
                                   Inventqty = a.Inventqty,
                                   MCode = a.MCode,
                                   InventUnit = a.InventUnit,
                                   MId = a.MId,
                                   LotState = a.LotState
                               } into g
                               select new
                               {
                                   NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),//- g.Key.Inventqty,//最终需求数量
                                   NeedUnit = g.Key.NeedUnit,
                                   EquipmentId = g.Key.EquipmentId,
                                   Inventqty = g.Key.Inventqty,
                                   MCode = g.Key.MCode,
                                   InventUnit = g.Key.InventUnit,
                                   MId = g.Key.MId,
                                   LotState = g.Key.LotState,
                                   //PlanStartTime = g.Key.PlanStartTime,

                               }).OrderByDescending(P => P.NeedQuantity);//.OrderBy(p => p.PlanStartTime); 请料需求必须大于库存

                #endregion

                #endregion

                #region 获取当天物料的总需求

                //这里计算时间(结束时间的当天结束时间)
                DateTime allStarTime = Convert.ToDateTime(DateTime.Now.ToString("yyyyy-MM-dd") + " 00:00:00");
                DateTime allEndTime = allStarTime.AddDays(1).AddSeconds(-1);
                var allDatas = await _dal.FindList(p => p.PlanStartTime >= allStarTime && p.PlanStartTime <= allEndTime && (p.PoStatus.Value == 2) && p.MaterialGroupName.Contains("JIT"));

                #region 按照天和物料分组算出对应的需求数量    

                var AllNowResult = (from a in allDatas
                                    group a by new
                                    {
                                        //PlanStartTime = a.PlanStartTime.Value.Date,
                                        NeedUnit = a.NeedUnit,
                                        EquipmentId = a.EquipmentId,
                                        Inventqty = a.Inventqty,
                                        MCode = a.MCode,
                                        InventUnit = a.InventUnit,
                                        MId = a.MId,
                                        LotState = a.LotState
                                    } into g
                                    select new
                                    {
                                        NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),//- g.Key.Inventqty,//最终需求数量
                                        NeedUnit = g.Key.NeedUnit,
                                        //EquipmentId = g.Key.EquipmentId,
                                        Inventqty = g.Key.Inventqty,
                                        MCode = g.Key.MCode,
                                        InventUnit = g.Key.InventUnit,
                                        MId = g.Key.MId,
                                        //LotState = g.Key.LotState,
                                        //PlanStartTime = g.Key.PlanStartTime,

                                    });//.OrderBy(p => p.PlanStartTime);

                #endregion

                #region 获取当天的剩余总需求量

                //这里计算时间(结束时间的当天结束时间)
                DateTime todayTime = Convert.ToDateTime(end.ToString("yyyyy-MM-dd") + " 00:00:00").AddSeconds(-1);
                var todayDatas = dataToday.Where(p => p.PlanStartTime > end && p.PlanStartTime <= todayTime);

                #region 按照天和物料分组算出对应的需求数量    

                var todayResult = (from a in todayDatas
                                   group a by new
                                   {
                                       //PlanStartTime = a.PlanStartTime.Value.Date,
                                       NeedUnit = a.NeedUnit,
                                       //  EquipmentId = a.EquipmentId,
                                       Inventqty = a.Inventqty,
                                       MCode = a.MCode,
                                       InventUnit = a.InventUnit,
                                       MId = a.MId
                                       //LotState = a.LotState
                                   } into g
                                   select new
                                   {
                                       NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),//- g.Key.Inventqty,//最终需求数量
                                       NeedUnit = g.Key.NeedUnit,
                                       //EquipmentId = g.Key.EquipmentId,
                                       Inventqty = g.Key.Inventqty,
                                       MCode = g.Key.MCode,
                                       InventUnit = g.Key.InventUnit,
                                       MId = g.Key.MId,
                                       //LotState = g.Key.LotState,
                                       //PlanStartTime = g.Key.PlanStartTime,

                                   });//.OrderBy(p => p.PlanStartTime);

                #endregion

                #endregion

                #region 计算请料水位数据数据源

                DateTime qlEnd = star.AddHours(Convert.ToDouble(requestquy)).AddSeconds(-1);
                var qlData = dataToday.Where(p => p.PlanStartTime <= qlEnd).ToList();

                //按照时间（获取未来X小时的需求量及库存）
                var qlResult = (from a in qlData
                                group a by new
                                {
                                    //PlanStartTime = a.PlanStartTime.Value.Date,
                                    NeedUnit = a.NeedUnit,
                                    //  EquipmentId = a.EquipmentId,
                                    Inventqty = a.Inventqty,
                                    MCode = a.MCode,
                                    InventUnit = a.InventUnit,
                                    MId = a.MId
                                    //LotState = a.LotState
                                } into g
                                select new
                                {
                                    NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),//-S g.Key.Inventqty,//最终需求数量
                                    NeedUnit = g.Key.NeedUnit,
                                    //EquipmentId = g.Key.EquipmentId,
                                    Inventqty = g.Key.Inventqty,
                                    MCode = g.Key.MCode,
                                    InventUnit = g.Key.InventUnit,
                                    MId = g.Key.MId,
                                    //LotState = g.Key.LotState,
                                    //PlanStartTime = g.Key.PlanStartTime,

                                }); //.OrderBy(p => p.PlanStartTime);


                #endregion

                #region 请求最高最水位数据源

                DateTime maxEnd = star.AddHours(Convert.ToDouble(maxquy)).AddSeconds(-1);
                var maxData = dataToday.Where(p => p.PlanStartTime <= maxEnd).ToList();

                //按照时间（获取未来X小时的需求量及库存）
                var maxResult = (from a in maxData
                                 group a by new
                                 {
                                     //PlanStartTime = a.PlanStartTime.Value.Date,
                                     NeedUnit = a.NeedUnit,
                                     //  EquipmentId = a.EquipmentId,
                                     Inventqty = a.Inventqty,
                                     MCode = a.MCode,
                                     InventUnit = a.InventUnit,
                                     MId = a.MId
                                     //LotState = a.LotState
                                 } into g
                                 select new
                                 {
                                     NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),//-S g.Key.Inventqty,//最终需求数量
                                     NeedUnit = g.Key.NeedUnit,
                                     //EquipmentId = g.Key.EquipmentId,
                                     Inventqty = g.Key.Inventqty,
                                     MCode = g.Key.MCode,
                                     InventUnit = g.Key.InventUnit,
                                     MId = g.Key.MId,
                                     //LotState = g.Key.LotState,
                                     //PlanStartTime = g.Key.PlanStartTime,

                                 }); //.OrderBy(p => p.PlanStartTime);

                #endregion

                #region 获取最低水位的数据源

                DateTime minEnd = star.AddHours(Convert.ToDouble(minquy)).AddSeconds(-1);
                var minData = dataToday.Where(p => p.PlanStartTime <= minEnd).ToList();

                //按照时间（获取未来X小时的需求量及库存）
                var minResult = (from a in minData
                                 group a by new
                                 {
                                     PlanStartTime = a.PlanStartTime.Value.Date,
                                     NeedUnit = a.NeedUnit,
                                     EquipmentId = a.EquipmentId,
                                     Inventqty = a.Inventqty,
                                     MCode = a.MCode,
                                     InventUnit = a.InventUnit,
                                     MId = a.MId,
                                     LotState = a.LotState
                                 } into g
                                 select new
                                 {
                                     NeedQuantity = g.Sum(p => p.NeedQuantity == null ? 0 : p.NeedQuantity.Value),
                                     NeedUnit = g.Key.NeedUnit,
                                     EquipmentId = g.Key.EquipmentId,
                                     Inventqty = g.Key.Inventqty,
                                     MCode = g.Key.MCode,
                                     InventUnit = g.Key.InventUnit,
                                     MId = g.Key.MId,
                                     LotState = g.Key.LotState,
                                     PlanStartTime = g.Key.PlanStartTime,

                                 }).OrderBy(p => p.PlanStartTime);


                #endregion

                #region 查询已经收料的数据（已请料数量：筛选时间为当天，状态为已收货的记录数量）

                DateTime nowDayStar = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
                DateTime NowDayEnd = nowDayStar.AddDays(1).AddSeconds(-1);

                //获取已请料数量 （获取当然请料数据）              
                var requestD = await _RequestIiiViewEntity.FindList(p => p.ActualTime != null && p.Status == "已完成" && p.Status == "已收货" && p.ActualTime >= nowDayStar && p.ActualTime <= NowDayEnd);
                var requestData = requestD.GroupBy(p => new { p.MaterialCode, p.Mid }).Select(p => new { MaterialCode = p.Key.MaterialCode, Mid = p.Key.Mid, Qty = p.Sum(p => p.ActualQuantity) }).ToList();

                #endregion

                #region 查询未收料的数据（未请料数量：筛选时间为当天，状态不等于已收货的记录数量）               

                //获取已请料数量 （获取当然请料数据）              
                var planRequestD = await _RequestIiiViewEntity.FindList(p => p.PlannedTime != null && p.Status != "已完成" && p.Status != "已收货" && p.PlannedTime >= nowDayStar && p.PlannedTime <= NowDayEnd);
                var PlanRequestData = requestD.GroupBy(p => new { p.MaterialCode, p.Mid }).Select(p => new { MaterialCode = p.Key.MaterialCode, Mid = p.Key.Mid, XQQty = p.Sum(p => p.NeedQuantity) }).ToList();

                #endregion

                #region 获取所有物料需求量

                //获取当日所有物料需求量(需求量大于库存)
                var data = dResult.Where(P => P.Inventqty < P.NeedQuantity).ToList(); //请料需求必须大于库存

                //获取后续所有物料需求信息(当天)
                var otherData = todayResult;//dResult.Where(p => p.PlanStartTime >= star).ToList();
                //需要请求的数据
                List<JITRModel> jitM = new List<JITRModel>();
                //循环需要请求的订单数据
                for (int i = 0; i < data.Count(); i++)
                {
                    //获取参数
                    decimal nendQty = data[i].NeedQuantity;
                    decimal inventQTY = data[i].Inventqty.Value;
                    string maCode = data[i].MCode;
                    string maID = data[i].MId;

                    #region 当天工单需求总量

                    //获取当前物料已请料数量
                    decimal qlQTY = 0;
                    var mDataQty = requestData.Where(p => p.Mid == maID).ToList();
                    if (mDataQty != null)
                    {
                        qlQTY = mDataQty[0].Qty;
                    }
                    //当前库存+已请料数据>请料水位 不需要触发
                    if (inventQTY + qlQTY > nendQty)
                    {
                        continue;
                    }
                    //取大值：最高水位的算法：获取最大库存（物理）：直接取；获取最大库存（时长）：算未来ｘ小时的工单需求量；比对取最小值为最高水位
                    decimal maxQ = 0;
                    var maxQTY = maxResult.Where(p => p.MId == maID).ToList();
                    if (maxQTY != null)
                    {
                        maxQ = maxQTY[0].NeedQuantity;
                    }
                    var max_water = hightquy;
                    if (maxQ < hightquy)
                    {
                        max_water = maxQ;
                    }
                    //最终请料数量=最高水位-库存-已请料数量
                    decimal lastQTY = max_water - inventQTY - qlQTY;
                    //当天需求总量
                    decimal allQTY = 0;
                    var allmQTY = AllNowResult.Where(p => p.MCode == maCode).ToList();
                    if (allmQTY != null)
                    {
                        allQTY = allmQTY[0].NeedQuantity;
                    }
                    //未收货数量
                    decimal reciveQTY = 0;
                    var reciveQTYData = PlanRequestData.Where(p => p.Mid == maID).ToList();
                    if (reciveQTYData != null)
                    {
                        reciveQTY = reciveQTYData[0].XQQty;
                    }
                    //请料数量< 当天所有工单的需求总量－库存数量－《请料明细表》中未收货的数量请料数量不变
                    if (lastQTY < allQTY - inventQTY - reciveQTY)
                    {
                        //需求不变
                    }
                    else
                    {
                        //当天剩余的总需求数量
                        var rData = todayResult.Where(p => p.MId == maID).ToList();
                        if (allmQTY != null)
                        {
                            lastQTY = allmQTY[0].NeedQuantity == 0 ? lastQTY : allmQTY[0].NeedQuantity;
                        }
                    }

                    #endregion

                    JITRModel model = new JITRModel();
                    model.MCode = maCode;
                    model.Qty = lastQTY;
                    model.NeedQuantity = lastQTY;
                    model.InventUnit = data[i].InventUnit;
                    model.NeedUnit = data[i].NeedUnit;
                    model.LotState = data[i].LotState;
                    model.EquipmentId = data[i].EquipmentId;
                    model.Inventqty = data[i].Inventqty.Value;
                    jitM.Add(model);
                }
                if (jitM == null || jitM.Count <= 0)
                {
                    result.msg = "无需请料";
                    result.success = true;
                    return result;
                }

                #endregion               

                #region 构造物料数据（处理逻辑）

                //筛选出来(需要请求的数据)
                List<string> mCodes = jitM.GroupBy(i => i.MCode).Select(p => p.Key).ToList();

                if (mCodes == null || mCodes.Count <= 0)
                {
                    result.msg = "无需请料";
                    result.success = true;
                    return result;
                }

                #region 循环添加LIST

                List<List<string>> stringList = new List<List<string>>();
                int j = 10;
                for (int i = 0; i < mCodes.Count; i += 10)
                {
                    List<string> cList = new List<string>();
                    cList = mCodes.Take(j).Skip(i).ToList();
                    j += 10;
                    stringList.Add(cList);
                }

                #endregion

                #endregion

                //返回的数据集合（规格数据接口）
                List<PackingSizeDataItem> packList = new List<PackingSizeDataItem>();

                #region 循环调用接口（物料规格接口）

                for (int i = 0; i < stringList.Count; i++)
                {
                    //拼接字符串
                    string strCodes = stringList[i].Aggregate((x, y) => x + "," + y);
                    PackingSizeSend model = new PackingSizeSend();
                    model.itemcode = strCodes;

                    //调用查询规格接口
                    var pR = GePacktResult(model).Result;
                    if (pR.successed == false)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }

                    //调用物料接口
                    PackingSizeResult packR = pR.Response;
                    if (packR.flag != true)
                    {
                        result.msg = "调用物料规格接口失败";
                        return result;
                    }
                    for (global::System.Int32 x = 0; x < packR.data.Count; x++)
                    {
                        PackingSizeDataItem pModel = new PackingSizeDataItem();
                        pModel.itemcode = packR.data[x].itemcode;
                        pModel.packcode = packR.data[x].packcode;
                        pModel.packQty = packR.data[x].packQty;
                        pModel.barcodeQty = packR.data[x].barcodeQty;
                        packList.Add(pModel);
                    }
                }

                #endregion


                //发送实体
                List<PullMaterial> pullMaterialList = new List<PullMaterial>();
                List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
                List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();

                #region 计算板数构造实体操作

                string mCode = string.Empty;
                decimal needQty = 0;
                string needUnit = "";
                decimal inventQty = 0;
                string inventUnit = "";
                decimal gch = 0;
                string mID = string.Empty;
                string mIDs = string.Empty;
                string equipmentId = string.Empty;
                string lotState = string.Empty;

                decimal totalALLNumber = 0;

                for (int i = 0; i < jitM.Count; i++)
                {
                    //属性
                    mCode = jitM[i].MCode;
                    needQty = jitM[i].NeedQuantity;
                    needUnit = jitM[i].NeedUnit;
                    inventQty = jitM[i].Inventqty == 0 ? 0 : jitM[i].Inventqty;
                    inventUnit = jitM[i].InventUnit;
                    gch = 0;
                    mIDs = jitM[i].MId;
                    equipmentId = jitM[i].EquipmentId;
                    lotState = jitM[i].LotState;

                    //筛选物料（如果没有规格直接跳过）
                    var selectList = packList.Where(P => P.itemcode == mCode).ToList();
                    if (selectList != null && selectList.Count > 0)
                    {
                        //需要换算
                        if (needUnit != null && inventUnit != null)
                        {
                            if (needUnit != inventUnit)
                            {
                                //加上单位逻辑转换
                            }
                        }

                        //计算当前物料单柜总数量
                        decimal pkNumber = Convert.ToDecimal(selectList[0].packQty == null ? 0 : selectList[0].packQty) * Convert.ToDecimal(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        //1000 1100

                        //需求数量
                        decimal xqch = jitM[i].Qty;
                        //需要发送的请料柜数
                        decimal totalNumber = xqch / pkNumber;
                        decimal ys = xqch % pkNumber;//余数
                        if (ys > 0)
                        {
                            totalALLNumber = totalNumber + 1;
                            totalNumber = totalNumber + 1;

                            //重置下柜数
                            if (totalNumber % Convert.ToDecimal(1) > 0)
                            {
                                totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                            }
                        }
                        else
                        {
                            totalALLNumber = totalNumber;
                        }

                        #region 写入表（这里不进行提交数据）

                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = mIDs;
                        id = model.ID;
                        model.UomId = needUnit;
                        model.EquipmentId = equipmentId;
                        model.ExternalStatus = lotState;
                        model.Type = reqModel.AutoType;
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = xqch;
                        rDModel.Status = "";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        #endregion

                        #region 构造发送数据（请料）

                        PullMaterial pmodel = new PullMaterial();

                        #region 新版

                        pmodel.partmaterialcode = mCode;
                        pmodel.partunit = inventUnit;
                        pmodel.qty = totalNumber;//totalNumber;
                        pmodel.batch = "";//批次号如何定位
                                          //接收仓位
                        if (mCode.Substring(0, 1) == "4")
                        {
                            pmodel.recivestockcode = "PKG3"; //"MFG3";
                        }
                        else
                        {
                            pmodel.recivestockcode = "MFG3"; //"MFG3";
                        }
                        // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                        //到货日期(这里注意数据转换)
                        pmodel.arrivetime = reqModel.planStarTime;

                        #endregion

                        //pmodel.partmaterialcode = mCode;
                        //pmodel.partunit = inventUnit;
                        //pmodel.qty = totalNumber;
                        //pmodel.recivestockcode = "MFG3";//必须填写
                        pullMaterialList.Add(pmodel);

                        #endregion

                    }
                }

                if (totalALLNumber < minnum)
                {
                    result.msg = "当前请料柜数小于请料柜数";
                    return result;
                }
                #endregion

                if (pullMaterialList != null && pullMaterialList.Count > 0)
                {
                    _unitOfWork.BeginTran();
                    //返送请料需求
                    var pullRs = GetPullMaterial(pullMaterialList).Result;
                    if (pullRs.successed == false)
                    {
                        result.msg = "调用请料接口失败";
                        return result;
                    }
                    PullMaterialResult pullResults = pullRs.Response;
                    if (pullResults == null)
                    {
                        //  _unitOfWork.RollbackTran();
                        result.msg = "JIT请料失败,无返回数据";
                        return result;
                    }
                    if (pullResults.flag == false)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "JIT请料失败,原因:" + pullResults.msg;
                        return result;
                    }
                    _unitOfWork.BeginTran();
                    if (pullResults.flag == true)
                    {
                        //执行保存数据操作
                        await _RequestDetailEntity.Add(requestDetailEntityList);
                        await _InventoryRequestEntity.Add(inventoryRequestEntityList);

                        //执行提交数据操作
                        _unitOfWork.CommitTran();

                        result.success = true;
                        result.msg = "JIT请料成功.";
                        return result;
                    }
                }

                result.msg = "JIT请料失败";
                return result;
                #endregion
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "JIT请料失败" + ex.Message;
                return result;
            }

        }



        /// <summary>
        /// 每天0点调取更新全部未完成的状态为已完成
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> updateStste(RequestMaterialsModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            List<RequestDetailEntity> updateDetail = new List<RequestDetailEntity>();
            var list = await _RequestDetailEntity.FindList(p => p.CreateDate >= Convert.ToDateTime(reqModel.planStarTime) && p.CreateDate <= Convert.ToDateTime(reqModel.planEndTime) && p.Status != "已完成");
            if (list != null)
            {
                foreach (var item in list)
                {
                    item.Status = "已完成";
                    item.Modify(item.ID, _user.Name.ToString());
                    updateDetail.Add(item);
                }
                _unitOfWork.BeginTran();
                bool resultSave2 = false;
                if (updateDetail.Count > 0)
                {
                    resultSave2 = await _RequestDetailEntity.Update(updateDetail);
                }
                if (!resultSave2)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "更新失败";
                    return result;
                }
            }
            else
            {
                result.msg = "无未完成的请料记录";
                return result;
            }
            //result.msg = "更新成功！";
            result.success = true;
            return result;
        }


        #endregion
        #endregion

        #region 新增请料记录





        /// <summary>
        /// 获取需求数据
        /// </summary>
        /// <returns></returns>
        private List<MCodeReturnModel> GetRequestMaterial(List<MCodeModel> mList, out string msg)
        {
            List<MCodeReturnModel> modelList = new List<MCodeReturnModel>();

            if (mList == null || mList.Count <= 0)
            {
                msg = "不存在数据";
                return modelList;
            }
            #region 每10行进行分组

            List<List<string>> stringList = new List<List<string>>();
            List<string> mCodes = mList.GroupBy(i => i.MaterialCode).Select(p => p.Key).ToList();
            int j = 10;



            for (int i = 0; i < mCodes.Count; i += 10)
            {
                List<string> cList = new List<string>();
                cList = mCodes.Take(j).Skip(i).ToList();
                j += 10;
                stringList.Add(cList);
            }

            #endregion

            //返回的数据集合（规格数据接口）
            List<PackingSizeDataItem> packList = new List<PackingSizeDataItem>();

            #region 测试

            //for (int i = 0; i < mList.Count; i++)
            //{
            //    PackingSizeDataItem pModel = new PackingSizeDataItem();
            //    pModel.itemcode = mList[i].MaterialCode;
            //    pModel.packcode = "50*10";
            //    pModel.packQty = "50";
            //    pModel.barcodeQty = "10";
            //    packList.Add(pModel);
            //}

            #endregion

            //循环所有需求,调用接口
            for (int i = 0; i < stringList.Count; i++)
            {
                #region 接口调用(规格查询)

                //拼接字符串(这里10个为一组调用对应的规格接口)
                var code = stringList[i].Aggregate((x, y) => x + "," + y);
                PackingSizeSend model = new PackingSizeSend();
                model.itemcode = code;
                //调用查询规格接口
                var pR = GePacktResult(model).Result;
                if (pR.successed == false)
                {
                    msg = "调用物料规格接口失败";
                    return modelList = new List<MCodeReturnModel>();
                }
                //调用物料接口
                PackingSizeResult packR = pR.Response;

                if (packR.flag != true)
                {
                    msg = "调用物料规格接口失败";
                    return modelList = new List<MCodeReturnModel>();
                }

                for (int x = 0; x < packR.data.Count; x++)
                {
                    PackingSizeDataItem pModel = new PackingSizeDataItem();
                    pModel.itemcode = packR.data[x].itemcode;
                    pModel.packcode = packR.data[x].packcode;
                    pModel.packQty = packR.data[x].packQty;
                    pModel.barcodeQty = packR.data[x].barcodeQty;
                    packList.Add(pModel);
                }

                #endregion
            }

            #region 计算最终需拼接的数据

            string mID = string.Empty;
            string mCode = string.Empty;
            string mName = string.Empty;
            string uID = string.Empty;
            string uName = string.Empty;
            decimal xQty = 0;
            decimal pkNumber = 0;
            decimal totalNumber = 0;
            string packQty = string.Empty;
            string barcodeQty = string.Empty;
            for (int i = 0; i < mList.Count; i++)
            {
                mID = mList[i].MaterialId;
                mCode = mList[i].MaterialCode;
                mName = mList[i].MaterialName;
                uID = mList[i].UnitId;
                uName = mList[i].UName;
                xQty = mList[i].Quantity;

                //筛选数据
                var selectList = packList.Where(P => P.itemcode == mCode).ToList();
                if (selectList != null && selectList.Count > 0)
                {
                    packQty = selectList[0].packQty;
                    barcodeQty = selectList[0].barcodeQty;
                    ////需要换算
                    //if (needUnit != null && inventUnit != null)
                    //{
                    //    if (needUnit != inventUnit)
                    //    {
                    //        //加上单位逻辑转换
                    //    }
                    //}

                    //计算当前物料单柜总数量
                    pkNumber = Convert.ToDecimal(selectList[0].packQty == null ? 0 : selectList[0].packQty) * Convert.ToDecimal(selectList[0].barcodeQty == null ? 0 : selectList[0].barcodeQty);
                    //1000 1100

                    //需求数量
                    decimal xqch = xQty;
                    //需要发送的请料柜数
                    totalNumber = xqch / pkNumber;
                    decimal ys = xqch % pkNumber;
                    if (ys > 0)
                    {
                        //首先转换
                        totalNumber = Convert.ToInt32(totalNumber);
                        totalNumber = totalNumber + 1;
                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }
                    #region 添加数据

                    MCodeReturnModel model = new MCodeReturnModel();
                    model.MaterialId = mID;
                    model.MaterialName = mName;
                    model.MaterialCode = mCode;
                    model.UnitId = uID;
                    model.UName = uName;
                    model.BagSize = pkNumber;
                    model.Quantity = xQty;
                    model.TrayNumber = totalNumber;
                    model.UserNumber = "";
                    model.PackQty = packQty;
                    model.BarcodeQty = barcodeQty;
                    model.ArraveTime = DateTime.Now.ToString();
                    model.Remark = string.Empty;
                    modelList.Add(model);

                    #endregion
                }
                else
                {
                    continue;
                }
            }

            #endregion
            msg = "成功";
            return modelList;
        }

        //public async Task<MessageModel<string>> AddRequestInven(List<MCodeReturnModel> reqModel) 
        //{
        //    var result = new MessageModel<string>();
        //    result.success = false;
        //    return result;
        //}

        #endregion


        #region 原料、物料的请料和拉料

        #region 通用查询函数

        #region 根据设备找需求表物料 

        /// <summary>
        /// 根据设备ID拿对应的物料信息
        /// </summary>
        /// <param name="equimentCode"></param>
        /// <param name="equimentName"></param>
        /// <returns></returns>
        public async Task<List<DFM.Model.Models.EquipmentMaterialEntity>> GetMaterialByEquiment(string equimentCode, string equimentName)
        {
            var whereExpression = Expressionable.Create<DFM.Model.Models.EquipmentEntity>()
                             .AndIF(!string.IsNullOrEmpty(equimentCode), p => p.EquipmentCode == equimentCode)
                              .AndIF(!string.IsNullOrEmpty(equimentCode), p => p.EquipmentName.Contains(equimentName))
                             .ToExpression();


            var eData = await _EquipmentEntityDal.Db.Queryable<DFM.Model.Models.EquipmentEntity>().Where(whereExpression).ToListAsync();

            var eModel = eData.FirstOrDefault();

            if (eModel == null)
            {
                return new List<DFM.Model.Models.EquipmentMaterialEntity>();

            }
            string eID = eModel.ID;
            var emData = await _EquipmentMaterialEntityDal.FindList(P => P.EquipmentId == eID);
            return emData;
        }

        #endregion



        #region 拉料

        /// <summary>
        /// 获取需求列表（毛需求）拉料(批号是当前时间点，到货时间为结束时间)
        /// </summary>
        /// <param name="star">工单计划开始时间</param>
        /// <param name="end">工单计划结束时间</param>
        /// <param name="sapOrderType">MFG3: =ZXH2 PKG3: !=ZXH2，工单，如果不传就代表不区分</param>   
        /// <param name="type">1: ZXH2;2:!=ZXH2</param>  
        ///  <param name="poStatus">2 是释放 4是备料后核对完毕</param>
        /// <returns></returns>

        public async Task<List<MCodeModel>> GetPoConsumeData(DateTime star, DateTime end, string sapOrderType, string type, string poStatus)
        {
            string bathNo = ""; //"BNo" + DateTime.Now.ToString("yyyyMMddHH");
            DateTime arrivalTime = end;
            //获取物料组数据
            var categoryList = await _CategoryEntityDal.FindList(p => p.ID != string.Empty);


            //获取已经释放工单（根据时间）
            //     var proList = await _productionOrderEntity.FindList(p => p.PlanStartTime.Value >= star && p.PlanStartTime.Value <= end && p.PoStatus == poStatus);

            //如果是1代表是物料
            var proList = await _productionOrderEntity.FindList(p => p.PlanDate.Value >= star && p.PlanDate.Value <= end);

            if (poStatus == "2")
            {
                proList = proList.Where(p => p.PoStatus == poStatus).ToList();
            }
            else if (poStatus == "1")
            {
                proList = proList.Where(p => p.PoStatus != "4" && p.PoStatus != "3").ToList(); //排除废弃和已完成工单
            }

            if (type == "1")
            {
                proList = proList.Where(p => p.SapOrderType.Contains("ZXH2")).ToList();
            }
            else if (type == "2")
            {
                proList = proList.Where(p => p.SapOrderType != "ZXH2").ToList();
            }

            string msgL = string.Empty;
            if (proList != null && proList.Count > 0)
            {
                for (int i = 0; i < proList.Count; i++)
                {
                    string proNum = proList[i].ProductionOrderNo;

                    if (i == proList.Count - 1)
                    {
                        msgL += proNum;
                    }
                    else
                    {
                        msgL += proNum + ",";
                    }
                }

            }

            string logMsg1 = string.Format(@"{0}需求获取计算开始时间{1}，结束时间{2},数量{4},工单号逗号分开：{3}", sapOrderType, star, end, msgL, proList.Count);
            //获取拉料
            SerilogServer.LogDebug(logMsg1, "GetPoConsumeData");

            string[] proIDs = proList.GroupBy(p => new { p.ID }).Select(P => P.Key.ID).ToArray();
            if (proIDs != null && proIDs.Length > 0)
            {
                //工单物料需求
                var consumeData = await _poConsumeRequirementEntityDal.Db.Queryable<PoConsumeRequirementEntity>().In(P => P.ProductionOrderId, proIDs).ToListAsync();
                if (sapOrderType == "MFG3")
                {
                    //consumeData = consumeData.Where(p => p.StorageBin.Contains("MFG3")).ToList();
                }
                else if (sapOrderType == "PKG3")
                {
                    consumeData = consumeData.Where(p => p.StorageBin.Contains("PKG3")).ToList();
                }

                var unitManager = await _poUnitmanageEntityDal.FindList(p => p.ID != null);
                var materialList = await _poMaterialEntity.FindList(p => p.ID != null);
                var materialTypeList = await _CategoryEntityDal.FindList(p => p.ID != null);

                List<MCodeModel> consumeLinq = new List<MCodeModel>();

                if (sapOrderType == "PKG3")
                {

                    //需求改为从物料拉料来源修改一下数据源，改为从PTM_I_ORDER_BOM中获取需求，Production_order_id为工单编号，ITEM_ID为请料物料的编码（需解密），
                    //ITEM_QUANTIRY_WITH_LOSS为请料需求数量，ITEM_UNIT为请料单位，WAREHOUSE_ID为仓储location（筛选PKG3的物料）
                    #region 新的逻辑

                    string[] pNumbers = proList.GroupBy(p => new { p.ProductionOrderNo }).Select(P => P.Key.ProductionOrderNo).ToArray();
                    if (pNumbers != null && pNumbers.Length > 0)
                    {
                        var orderBomS = await _dalOrderBomEntity.Db.Queryable<OrderBomEntity>().In(P => P.ProductionOrderId, pNumbers).Where(P => P.WarehouseId.Contains("PKG3")).ToListAsync();

                        //插入需求表
                        List<OrderBomEntity> listOrderBom = new List<OrderBomEntity>();
                        foreach (var orderBom in orderBomS)
                        {
                            if (string.IsNullOrEmpty(orderBom.ItemId))
                            {
                                continue;
                            }
                            var materialCode = Enigma.Decrypt(orderBom.ItemId, orderBom.TranNo);
                            var materialName = Enigma.Decrypt(orderBom.ItemDecs, orderBom.TranNo);
                            orderBom.ItemId = materialCode;
                            orderBom.ItemDecs = materialName;
                            listOrderBom.Add(orderBom);
                        }

                        //物料按照消耗来
                        consumeLinq = (from a in listOrderBom
                                       join b in unitManager on a.ItemUnit equals b.Name
                                       join c in materialList on a.ItemId equals c.Code
                                       join d in materialTypeList on c.Type equals d.Code
                                       group a by new
                                       {
                                           MaterialId = c.ID,
                                           UnitId = b.ID,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code,
                                           c.Type,
                                           TypeCode = d.Code,
                                           TypeName = d.Name
                                       }
                                          into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialType = g.Key.Type,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           //ArrivalTime = arrivalTime,
                                           //BatchNo = bathNo,
                                           Quantity = g.Sum(p => p.ItemQuantityWithLoss == null ? 0 : p.ItemQuantityWithLoss.Value)
                                       }).ToList();
                    }
                    #endregion


                    //求需求总和
                    //consumeLinq = (from a in consumeData
                    //               join b in unitManager on a.UnitId equals b.ID
                    //               join c in materialList on a.MaterialId equals c.ID
                    //               join d in materialTypeList on c.Type equals d.Code
                    //               group a by new
                    //               {
                    //                   a.MaterialId,
                    //                   a.UnitId,
                    //                   b.Name,
                    //                   MName = c.Name,
                    //                   c.Code,
                    //                   c.Type,
                    //                   TypeCode = d.Code,
                    //                   TypeName = d.Name
                    //               }
                    //                 into g
                    //               select new MCodeModel
                    //               {
                    //                   MaterialId = g.Key.MaterialId,
                    //                   UnitId = g.Key.UnitId,
                    //                   UName = g.Key.Name,
                    //                   MaterialType = g.Key.Type,
                    //                   MaterialName = g.Key.MName,
                    //                   MaterialCode = g.Key.Code,
                    //                   //ArrivalTime = arrivalTime,
                    //                   //BatchNo = bathNo,
                    //                   Quantity = g.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value)
                    //               }).ToList();

                }
                else
                {
                    //求需求总和
                    consumeLinq = (from a in consumeData
                                   join b in unitManager on a.UnitId equals b.ID
                                   join c in materialList on a.MaterialId equals c.ID
                                   join d in materialTypeList on c.Type equals d.Code
                                   group a by new
                                   {
                                       a.MaterialId,
                                       a.UnitId,
                                       b.Name,
                                       MName = c.Name,
                                       c.Code,
                                       c.Type,
                                       TypeCode = d.Code,
                                       TypeName = d.Name
                                   }
                                     into g
                                   select new MCodeModel
                                   {
                                       MaterialId = g.Key.MaterialId,
                                       UnitId = g.Key.UnitId,
                                       UName = g.Key.Name,
                                       MaterialName = g.Key.MName,
                                       MaterialCode = g.Key.Code,
                                       MaterialType = g.Key.Type,
                                       //ArrivalTime = arrivalTime,
                                       //BatchNo = bathNo,
                                       Quantity = g.Sum(p => p.Quantity == null ? 0 : p.Quantity.Value)
                                   }).ToList();

                }


                //获取对应的数据（这里来计算需求接口的数）
                string msg = string.Empty;
                //调用包装规格
                //     List<MCodeReturnModel> list = GetRequestMaterial(consumeLinq, out msg);


                string mMSG = string.Empty;


                //重新绑定数据
                List<MCodeModel> listModel = new List<MCodeModel>();
                for (int i = 0; i < consumeLinq.Count; i++)
                {
                    MCodeModel model = new MCodeModel();
                    model.MaterialType = consumeLinq[i].MaterialType;
                    model.MaterialId = consumeLinq[i].MaterialId;
                    model.MaterialName = consumeLinq[i].MaterialName;
                    model.MaterialCode = consumeLinq[i].MaterialCode;
                    model.Quantity = consumeLinq[i].Quantity;
                    model.UnitId = consumeLinq[i].UnitId;
                    model.UName = consumeLinq[i].UName;
                    model.BagSize = consumeLinq[i].BagSize;
                    model.BarcodeQty = consumeLinq[i].BarcodeQty;
                    model.PackQty = consumeLinq[i].PackQty;
                    model.ArrivalTime = arrivalTime;
                    model.BatchNo = bathNo;
                    listModel.Add(model);

                    if (i == consumeLinq.Count - 1)
                    {
                        mMSG += "物料编码:" + consumeLinq[i].MaterialCode + ",需求数量:" + consumeLinq[i].Quantity;
                    }
                    else
                    {
                        mMSG += "物料编码:" + consumeLinq[i].MaterialCode + ",需求数量:" + consumeLinq[i].Quantity + ",";
                    }
                }


                string logMsg2 = string.Format(@"{0}需求获取计算开始时间{1}，结束时间{2},需求物料编码（逗号分开）：{3}", sapOrderType, star, end, mMSG);
                //获取拉料
                SerilogServer.LogDebug(logMsg2, "GetPoConsumeData");

                return listModel;
            }

            return new List<MCodeModel>();
        }

        /// <summary>
        /// 重新计算需求数量（拉料）去掉已经请过的料（当天）
        /// </summary>
        /// <param name="star">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="consumeData">需求列表</param>
        /// <returns></returns>
        public async Task<List<MCodeModel>> GetPullData(DateTime star, DateTime end, List<MCodeModel> consumeData)
        {

            //筛选物料需求
            string[] mIDS = consumeData.GroupBy(p => new { p.MaterialId }).Select(P => P.Key.MaterialId).ToArray();
            var mpull = await _InventoryPullEntityDal.Db.Queryable<InventoryPullEntity>().In(P => P.MaterialId, mIDS).ToListAsync();
            var pullDetail = await _PullDetailEntityDal.FindList(p => p.CreateDate >= star && p.CreateDate <= end);

            var result = from a in pullDetail
                         join b in mpull on a.InventoryRequestId equals b.ID
                         group a by new
                         {
                             b.MaterialId
                         }
                                  into g
                         select new MCodeModel
                         {
                             MaterialId = g.Key.MaterialId,
                             Quantity = g.Sum(p => p.Quantity)
                         };

            //(循环减去需求数量)
            for (int i = 0; i < consumeData.Count; i++)
            {
                consumeData[i].Quantity = consumeData[i].Quantity - result.Where(p => p.MaterialId == consumeData[i].MaterialId).Sum(p => p.Quantity);
            }

            return consumeData;


        }
        /// <summary>
        /// 邢增拉料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <param name="NOreqModel"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> InsertPullDataAndNo(List<MCodeModel> reqModel, List<MCodeModel> NOreqModel, string type)
        {
            var result = new MessageModel<string>();
            result.success = false;


            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增拉料失败";
                return result;
            }

            reqModel = reqModel.Where(p => !p.MaterialCode.StartsWith("230") && !p.MaterialCode.Contains("**********")).ToList();

            SerilogServer.LogDebug($"物料拉料计算list物料总数：[{reqModel.Count + "类型:" + type}]", "GetPoBomAndRouting ");

            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;

            //新增拉料数据和发送拉料需求

            List<InventoryPullEntity> inventorypullEntityList = new List<InventoryPullEntity>();
            List<PullDetailEntity> pullDetailEntityList = new List<PullDetailEntity>();

            List<InventoryPullEntity> noinventorypullEntityList = new List<InventoryPullEntity>();
            List<PullDetailEntity> nopullDetailEntityList = new List<PullDetailEntity>();
            List<PullMaterial> pullMaterialList = new List<PullMaterial>();
            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据(构造需要拉料数据)
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;
                    batchNo = reqModel[i].BatchNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }
                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据

                    //获取物料是否存在               

                    var Requestmodel = await _InventoryPullEntityDal.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        pullDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryPullEntityDal.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryPullEntity model = new InventoryPullEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;

                        model.EquipmentId = type;


                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        inventorypullEntityList.Add(model);
                        SerilogServer.LogDebug($"获取原料拉料信息,物料：[{reqModel[i].MaterialName + ":" + reqModel[i].MaterialCode + ":" + reqModel[i].Quantity}]", "GetPoBomAndRouting ");

                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.Type = type;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        pullDetailEntityList.Add(rDModel);

                    }

                    #endregion

                    #region 拉料接口

                    #region 构造发送数据

                    PullMaterial pMmodel = new PullMaterial();
                    pMmodel.partmaterialcode = mCode;
                    pMmodel.partunit = unit;
                    pMmodel.qty = qty;//totalNumber;
                    pMmodel.batch = batchNo;
                    pMmodel.recivestockcode = type;

                    //到货日期(这里注意数据转换)
                    pMmodel.arrivetime = arriveData;
                    pullMaterialList.Add(pMmodel);

                    #endregion

                    #endregion
                }


                #region 构造不需要报工数据

                for (int i = 0; i < NOreqModel.Count; i++)
                {
                    qty = NOreqModel[i].Quantity;
                    mId = NOreqModel[i].MaterialId;
                    batchNo = NOreqModel[i].BatchNo;
                    //获取单位信息
                    unit = NOreqModel[i].UName;
                    mCode = NOreqModel[i].MaterialCode;
                    arriveData = NOreqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    packQty = NOreqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(NOreqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(NOreqModel[i].BarcodeQty);
                    }
                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据

                    //获取物料是否存在               

                    var Requestmodel = await _InventoryPullEntityDal.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        nopullDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryPullEntityDal.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryPullEntity model = new InventoryPullEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = NOreqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = NOreqModel[i].UnitId;

                        model.EquipmentId = type;


                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        noinventorypullEntityList.Add(model);
                        SerilogServer.LogDebug($"获取原料拉料信息,物料：[{NOreqModel[i].MaterialName + ":" + NOreqModel[i].MaterialCode + ":" + NOreqModel[i].Quantity}]", "GetPoBomAndRouting ");

                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.Type = type;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        nopullDetailEntityList.Add(rDModel);

                    }

                    #endregion

                    #region 拉料接口

                    #region 构造发送数据

                    PullMaterial pMmodel = new PullMaterial();
                    pMmodel.partmaterialcode = mCode;
                    pMmodel.partunit = unit;
                    pMmodel.qty = qty;//totalNumber;
                    pMmodel.batch = batchNo;
                    pMmodel.recivestockcode = type;

                    //到货日期(这里注意数据转换)
                    pMmodel.arrivetime = arriveData;
                    pullMaterialList.Add(pMmodel);

                    #endregion

                    #endregion
                }


                #endregion

                #region 拉料        

                var pullR = GetPullMaterial(pullMaterialList).Result;
                if (pullR.successed == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "调用拉料接口失败";
                    return result;
                }
                PullMaterialResult pullResult = pullR.Response;

                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "新增拉料失败" + pullResult.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据              
                bool okpull = true;
                bool oknventPull = true;

                bool nopull = true;
                bool nonventPull = true;

                okpull = await _PullDetailEntityDal.Add(pullDetailEntityList) > 0;
                if (inventorypullEntityList.Count >= 1)
                {
                    oknventPull = await _InventoryPullEntityDal.Add(inventorypullEntityList) > 0;
                }

                #region 保存没有新增的数据

                if (nopullDetailEntityList != null && nopullDetailEntityList.Count > 0)
                {
                    nopull = await _PullDetailEntityDal.Add(nopullDetailEntityList) > 0;
                }
                if (noinventorypullEntityList.Count >= 1)
                {
                    nonventPull = await _InventoryPullEntityDal.Add(noinventorypullEntityList) > 0;
                }

                #endregion



                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "新增拉料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增拉料失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 新增拉料数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <param name="reqModel">MFG3/PKG3</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> InsertPullData(List<MCodeModel> reqModel, string type)
        {
            var result = new MessageModel<string>();
            result.success = false;


            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增拉料失败";
                return result;
            }

            reqModel = reqModel.Where(p => !p.MaterialCode.StartsWith("230") && !p.MaterialCode.Contains("**********")).ToList();

            SerilogServer.LogDebug($"物料拉料计算list物料总数：[{reqModel.Count + "类型:" + type}]", "GetPoBomAndRouting ");

            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;

            //新增拉料数据和发送拉料需求

            List<InventoryPullEntity> inventorypullEntityList = new List<InventoryPullEntity>();
            List<PullDetailEntity> pullDetailEntityList = new List<PullDetailEntity>();
            List<PullMaterial> pullMaterialList = new List<PullMaterial>();
            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;
                    batchNo = reqModel[i].BatchNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }
                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据

                    //获取物料是否存在               

                    var Requestmodel = await _InventoryPullEntityDal.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        pullDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryPullEntityDal.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryPullEntity model = new InventoryPullEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;

                        model.EquipmentId = type;

                        //MaterialGroupViewEntity mModel = await _dalMaterialGroupViewEntity.FindEntity(p => p.ID == reqModel[i].MaterialId && p.MaterialGroupName == type);
                        //if (mModel != null && !string.IsNullOrEmpty(mModel.ID))
                        //{
                        //    model.EquipmentId = mModel.Groupid;
                        //}
                        //else
                        //{
                        //    _unitOfWork.RollbackTran();
                        //    result.msg = "新增拉料,请配置物料组：" + reqModel[i].MaterialCode + "-" + reqModel[i].MaterialName;
                        //    return result;
                        //}

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        inventorypullEntityList.Add(model);
                        SerilogServer.LogDebug($"获取原料拉料信息,物料：[{reqModel[i].MaterialName + ":" + reqModel[i].MaterialCode + ":" + reqModel[i].Quantity}]", "GetPoBomAndRouting ");

                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.Type = type;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        pullDetailEntityList.Add(rDModel);

                    }

                    #endregion

                    #region 拉料接口

                    #region 构造发送数据

                    PullMaterial pMmodel = new PullMaterial();
                    pMmodel.partmaterialcode = mCode;
                    pMmodel.partunit = unit;
                    pMmodel.qty = qty;//totalNumber;
                    pMmodel.batch = batchNo;
                    pMmodel.recivestockcode = type;

                    //接收仓位
                    //if (mCode.Substring(0, 1) == "4")
                    //{
                    //    pMmodel.recivestockcode = "PKG3"; //"MFG3";
                    //}
                    //else
                    //{
                    //    pMmodel.recivestockcode = "MFG3"; //"MFG3";
                    //}

                    // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                    //到货日期(这里注意数据转换)
                    pMmodel.arrivetime = arriveData;
                    pullMaterialList.Add(pMmodel);

                    #endregion

                    #endregion
                }

                #region 拉料        

                var pullR = GetPullMaterial(pullMaterialList).Result;
                if (pullR.successed == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "调用拉料接口失败";
                    return result;
                }
                PullMaterialResult pullResult = pullR.Response;

                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "新增拉料失败" + pullResult.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据              

                await _PullDetailEntityDal.Add(pullDetailEntityList); ;
                if (inventorypullEntityList.Count >= 1)
                {
                    await _InventoryPullEntityDal.Add(inventorypullEntityList);
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "新增拉料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增拉料失败" + ex.Message;
                return result;
            }
        }

        #endregion

        #region 请料

        /// <summary>
        /// 调用请料接口和创建数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> InsertRequestData(List<MCodeModel> reqModel, string type)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增请料失败";
                return result;
            }
            //去掉水和23开头料
            reqModel = reqModel.Where(p => !p.MaterialCode.StartsWith("230") && !p.MaterialCode.Contains("**********")).ToList();
            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;

            //新增拉料数据和发送拉料需求
            List<RequestMaterial> rList = new List<RequestMaterial>();
            List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
            List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();

            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;
                    batchNo = reqModel[i].BatchNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    //调用接口请料
                    #region 请料接口

                    RequestMaterial rModel = new RequestMaterial();

                    rModel.partmaterialcode = mCode;

                    rModel.partunit = unit;
                    rModel.linenumber = "";
                    rModel.qty = qty;
                    rModel.batch = batchNo;
                    rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.arrivetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.recivestockcode = type;

                    rList.Add(rModel);

                    #endregion

                    //这里计算规格
                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }

                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);

                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    //decimal totalNumber = qty / pkNumber;
                    //decimal ys = qty % pkNumber;
                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据
                    // string type = mCode.Substring(0, 1) == "4" ? "PKG3" : "MFG3";
                    //获取物料是否存在               

                    var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryRequestEntity.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;

                        model.EquipmentId = type;

                        //MaterialGroupViewEntity mModel = await _dalMaterialGroupViewEntity.FindEntity(p => p.ID == reqModel[i].MaterialId && p.MaterialGroupName == type);
                        //if (mModel != null && !string.IsNullOrEmpty(mModel.ID))
                        //{
                        //    model.EquipmentId = mModel.Groupid;
                        //}
                        //else
                        //{
                        //    result.msg = "请料失败,请配置物料组：" + reqModel[i].MaterialCode + "-" + reqModel[i].MaterialName;
                        //    return result;
                        //}

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Type = type;
                        rDModel.Status = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);
                    }

                    #endregion                    
                }

                #region 请料 先注释       

                var httpResult = await GetRequestMaterialSend(rList);
                if (httpResult.successed == false)
                {
                    result.msg = "请料接口访问失败";
                    return result;
                }
                if (httpResult.Response.flag == false)
                {
                    result.msg = httpResult.Response.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据



                await _RequestDetailEntity.Add(requestDetailEntityList);
                if (inventoryRequestEntityList.Count >= 1)
                {
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "请料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增请料失败" + ex.Message;
                return result;
            }
        }


        /// <summary>
        /// 调用请料接口和创建数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> InsertRequestDataAndNO(List<MCodeModel> reqModel, List<MCodeModel> noreqModel, string type)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增请料失败";
                return result;
            }

            //加入无需调用接口的料
            List<MCodeModel> reqModelList = new List<MCodeModel>();
            reqModelList = reqModel.Where(p => p.MaterialCode.StartsWith("230") || p.MaterialCode.Contains("**********")).ToList();
            for (int i = 0; i < reqModelList.Count; i++)
            {
                noreqModel.Add(reqModelList[i]);
            }

            //去掉水和23开头料
            reqModel = reqModel.Where(p => !p.MaterialCode.StartsWith("230") && !p.MaterialCode.Contains("**********")).ToList();

            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;

            //新增拉料数据和发送拉料需求
            List<RequestMaterial> rList = new List<RequestMaterial>();
            List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
            List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();
            List<InventoryRequestEntity> noinventoryRequestEntityList = new List<InventoryRequestEntity>();
            List<RequestDetailEntity> norequestDetailEntityList = new List<RequestDetailEntity>();
            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;
                    batchNo = reqModel[i].BatchNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    //调用接口请料
                    #region 请料接口

                    RequestMaterial rModel = new RequestMaterial();

                    rModel.partmaterialcode = mCode;

                    rModel.partunit = unit;
                    rModel.linenumber = "";
                    rModel.qty = qty;
                    rModel.batch = batchNo;
                    rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.arrivetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.recivestockcode = type;

                    rList.Add(rModel);

                    #endregion

                    //这里计算规格
                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }

                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);

                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    //decimal totalNumber = qty / pkNumber;
                    //decimal ys = qty % pkNumber;
                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据
                    // string type = mCode.Substring(0, 1) == "4" ? "PKG3" : "MFG3";
                    //获取物料是否存在               

                    var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.Remark = "是";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryRequestEntity.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;

                        model.EquipmentId = type;

                        //MaterialGroupViewEntity mModel = await _dalMaterialGroupViewEntity.FindEntity(p => p.ID == reqModel[i].MaterialId && p.MaterialGroupName == type);
                        //if (mModel != null && !string.IsNullOrEmpty(mModel.ID))
                        //{
                        //    model.EquipmentId = mModel.Groupid;
                        //}
                        //else
                        //{
                        //    result.msg = "请料失败,请配置物料组：" + reqModel[i].MaterialCode + "-" + reqModel[i].MaterialName;
                        //    return result;
                        //}

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Type = type;
                        rDModel.Status = "";
                        rDModel.Remark = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);
                    }

                    #endregion                    
                }

                #region 添加剩下的料，无需拉的

                for (int i = 0; i < noreqModel.Count; i++)
                {
                    qty = noreqModel[i].Quantity;
                    mId = noreqModel[i].MaterialId;
                    batchNo = noreqModel[i].BatchNo;
                    //获取单位信息
                    unit = noreqModel[i].UName;
                    mCode = noreqModel[i].MaterialCode;
                    arriveData = noreqModel[i].ArrivalTime.ToString("yyyy-MM-dd");

                    //调用接口请料
                    #region 请料接口

                    RequestMaterial rModel = new RequestMaterial();

                    rModel.partmaterialcode = mCode;

                    rModel.partunit = unit;
                    rModel.linenumber = "";
                    rModel.qty = qty;
                    rModel.batch = batchNo;
                    rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.arrivetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.recivestockcode = type;

                    rList.Add(rModel);

                    #endregion

                    //这里计算规格
                    packQty = noreqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(noreqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(noreqModel[i].BarcodeQty);
                    }

                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);

                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    //decimal totalNumber = qty / pkNumber;
                    //decimal ys = qty % pkNumber;
                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据
                    // string type = mCode.Substring(0, 1) == "4" ? "PKG3" : "MFG3";
                    //获取物料是否存在               

                    var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId);
                    if (Requestmodel != null)
                    {
                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Remark = "否";
                        rDModel.Type = type;
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        norequestDetailEntityList.Add(rDModel);

                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryRequestEntity.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = noreqModel[i].MaterialId;
                        model.TypeL = type;
                        id = model.ID;
                        model.UomId = noreqModel[i].UnitId;
                        model.EquipmentId = type;

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "自动";
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Type = type;
                        rDModel.Status = "";
                        rDModel.Remark = "否";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        norequestDetailEntityList.Add(rDModel);
                    }

                    #endregion                    
                }

                #endregion

                #region 请料 先注释       

                var httpResult = await GetRequestMaterialSend(rList);
                if (httpResult.successed == false)
                {
                    result.msg = "请料接口访问失败";
                    return result;
                }
                if (httpResult.Response.flag == false)
                {
                    result.msg = httpResult.Response.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据

                bool rDetail = true;
                bool rinvent = true;
                bool norDetail = true;
                bool norinvent = true;

                rDetail = await _RequestDetailEntity.Add(requestDetailEntityList) > 0;
                if (inventoryRequestEntityList.Count >= 1)
                {
                    rinvent = await _InventoryRequestEntity.Add(inventoryRequestEntityList) > 0;
                }

                //这里执行添加没包含的料
                if (norequestDetailEntityList.Count >= 1)
                {
                    norDetail = await _RequestDetailEntity.Add(norequestDetailEntityList) > 0;
                }
                if (noinventoryRequestEntityList.Count >= 1)
                {
                    norinvent = await _InventoryRequestEntity.Add(noinventoryRequestEntityList) > 0;
                }

                if (rDetail && rinvent && norDetail && norinvent)
                {
                    _unitOfWork.CommitTran();
                    result.success = true;
                    result.msg = "请料成功.";
                    return result;
                }
                else
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "新增请料失败";
                    return result;
                }
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增请料失败" + ex.Message;
                return result;
            }
        }

        #endregion

        #endregion

        #region 自动

        #region 原料拉料

        #region 服务 每天13点执行

        /// <summary>
        /// 每天13点定时调用该服务（原料拉料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeMPull_13()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 后天0到24
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(2);
            DateTime end = nowDayTime.AddDays(3).AddMilliseconds(-1);

            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "MFG3", "1", "2");

            //获取需要去掉的物料（白糖组和原料组）
            //var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("原料加工厂") || p.MaterialGroups.Contains("白糖"));
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("白糖"));
            //拿原料加工厂
            var mYL = await GetMaterialByEquiment("ProcessPlant", "");
            //筛选
            getConsume = getConsume.Where(p => p.MaterialType != "ZSFG" && p.MaterialCode != "**********").ToList();
            //去掉(白糖组和原料组)物料
            List<MCodeModel> list = new List<MCodeModel>();

            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                //排除原料加工厂数据
                else if (mYL.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                list.Add(getConsume[i]);
            }

            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "暂无需拉料的数据";
                return result;
            }
            //这里暂时注释掉,无需扣库存  //重新计算（时间用来筛选请过的料,当天）
            //var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);
            //执行拉料
            var lastResult = await InsertPullData(list, "MFG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }

        #endregion

        #endregion

        #region 原料请料

        #region 服务 每天8点执行

        /// <summary>
        /// 每天8点定时调用该服务（原料请料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeMRequest_8()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 后天0到24
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(2);
            DateTime end = nowDayTime.AddDays(3).AddMilliseconds(-1);

            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "MFG3", "1", "2");

            //获取需要去掉的物料（白糖组和原料组和原料加工厂）       
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("白糖") || p.MaterialGroups.Contains("盐味精"));
            //拿原料加工厂
            var mYL = await GetMaterialByEquiment("ProcessPlant", "");

            //豉油厂
            var cyC = await GetMaterialByEquiment("SaucePlant", "");


            //盐味精组
            List<MCodeModel> list = new List<MCodeModel>();
            List<MCodeModel> NOlist = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }
                if (mYL.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }
                if (cyC.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }

                list.Add(getConsume[i]);
            }

            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "8点暂无需请料的数据";
                return result;
            }
            //重新计算（时间用来筛选请过的料,当天）
            //      var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);
            var resultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);
            var NOresultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), NOlist);
            //      var NOresultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), NOlist);

            if (resultLast == null || resultLast.Count <= 0)
            {
                result.success = true;
                result.msg = "8点暂无需请料的数据";
                return result;
            }

            //执行请料
            var lastResult = await InsertRequestDataAndNO(resultLast, NOresultLast, "MFG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }


        public async Task<MessageModel<string>> FixedTimeMRequest_8OLD()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 后天0到24
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(2);
            DateTime end = nowDayTime.AddDays(3).AddMilliseconds(-1);

            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "MFG3", "1", "2");

            //获取需要的物料（盐味精组）
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("盐味精"));// || !p.MaterialGroups.Contains("原料加工厂"));// ;

            //盐味精组
            List<MCodeModel> list = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                list.Add(getConsume[i]);
            }

            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "8点暂无需请料的数据";
                return result;
            }

            //重新计算（时间用来筛选请过的料,当天）
            var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);

            if (resultLast == null || resultLast.Count <= 0)
            {
                result.success = true;
                result.msg = "8点暂无需请料的数据";
                return result;
            }

            //执行请料
            var lastResult = await InsertRequestData(resultLast, "MFG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }
        #endregion

        #region 服务 每天14点执行

        /// <summary>
        /// 每天14点定时调用该服务（原料请料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeMRequest_14()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 后天0到24
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(2);
            DateTime end = nowDayTime.AddDays(3).AddMilliseconds(-1);

            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "MFG3", "1", "2");

            //获取需要去掉的物料（白糖组和原料组和原料加工厂）       
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("白糖") || p.MaterialGroups.Contains("盐味精"));

            //拿原料加工厂
            var mYL = await GetMaterialByEquiment("ProcessPlant", "");


            //豉油厂
            var cyC = await GetMaterialByEquiment("SaucePlant", "");

            //去掉(白糖组和原料组)物料
            List<MCodeModel> list = new List<MCodeModel>();
            List<MCodeModel> NOlist = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }
                if (mYL.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }
                if (cyC.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    NOlist.Add(getConsume[i]);
                    continue;
                }
                list.Add(getConsume[i]);
            }
            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "14点暂无需请料的数据";
                return result;
            }
            //重新计算（时间用来筛选请过的料,当天）
            //var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);
            //var NOresultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), NOlist);
            var resultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);
            var NOresultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), NOlist);

            if (resultLast == null || resultLast.Count <= 0)
            {
                result.success = true;
                result.msg = "14点暂无需请料的数据";
                return result;
            }



            //执行请料
            var lastResult = await InsertRequestDataAndNO(resultLast, NOresultLast, "MFG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }


        public async Task<MessageModel<string>> FixedTimeMRequest_14old()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 后天0到24
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(2);
            DateTime end = nowDayTime.AddDays(3).AddMilliseconds(-1);

            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "MFG3", "1", "2");

            //获取需要去掉的物料（白糖组和原料组和原料加工厂）
            //var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("白糖") || p.MaterialGroups.Contains("盐味精") || p.MaterialGroups.Contains("原料加工厂"));
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGroups.Contains("白糖") || p.MaterialGroups.Contains("盐味精"));

            //拿原料加工厂
            var mYL = await GetMaterialByEquiment("ProcessPlant", "");

            //去掉(白糖组和原料组)物料
            List<MCodeModel> list = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                if (mYL.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                list.Add(getConsume[i]);
            }
            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "14点暂无需请料的数据";
                return result;
            }
            //重新计算（时间用来筛选请过的料,当天）
            var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);


            if (resultLast == null || resultLast.Count <= 0)
            {
                result.success = true;
                result.msg = "14点暂无需请料的数据";
                return result;
            }
            //执行请料
            var lastResult = await InsertRequestData(resultLast, "MFG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }

        #endregion

        #endregion

        #region 物料拉料

        #region 服务 每天凌晨2点执行

        /// <summary>
        /// 每天凌晨2点定时调用该服务（物料拉料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeRawMPull_02()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 拉的范围是明天0-24点 改为后天的0-24点
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            DateTime star = nowDayTime.AddDays(1);
            DateTime end = nowDayTime.AddDays(2).AddSeconds(-1);
            //DateTime star = nowDayTime.AddDays(2);
            //DateTime end = star.AddDays(3).AddSeconds(-1);
            SerilogServer.LogDebug($"物料拉料开始时间：[{star + "结束时间:" + end}]", "GetPoBomAndRouting ");

            //获取物料的需
            var getConsume = await GetPoConsumeData(star, end, "PKG3", "2", "1");

            //   var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGrouptypes.Contains("JIT"));

            List<MCodeModel> list = new List<MCodeModel>();

            SerilogServer.LogDebug($"物料拉料计算getConsume,物料总数：[{getConsume.Count + ":"}]", "GetPoBomAndRouting ");

            for (int i = 0; i < getConsume.Count; i++)
            {
                //string mID = getConsume[i].MaterialId;
                //if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                //{
                //    continue;
                //}
                list.Add(getConsume[i]);
            }
            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "暂无需拉料的数据";
                return result;
            }
            //重新计算（时间用来筛选拉过的料,当天） 暂时注释
            // var resultLast = await GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);

            //执行拉料
            SerilogServer.LogDebug($"物料拉料计算list物料总数：[{list.Count + ":"}]", "GetPoBomAndRouting ");

            var lastResult = await InsertPullData(list, "PKG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }

        #endregion

        #endregion

        #region 物料请料

        /// <summary>
        /// 重新计算需求数量（请料）-减去当天库存数量
        /// </summary>
        /// <param name="star">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="consumeData">需求列表</param>
        /// <returns></returns>
        public async Task<List<MCodeModel>> GetRequestInventData(DateTime star, DateTime end, List<MCodeModel> consumeData)
        {
            List<MCodeModel> returnList = new List<MCodeModel>();
            //筛选物料需求
            string[] mIDS = consumeData.GroupBy(p => new { p.MaterialId }).Select(P => P.Key.MaterialId).ToArray();
            var inventList = await _MaterialInventdal.Db.Queryable<InventorylistingViewEntity>().In(p => p.MaterialId, mIDS).ToListAsync();
            inventList = inventList.Where(p => string.IsNullOrEmpty(p.ProductionRequestId)).ToList();
            if (inventList != null && inventList.Count > 0)
            {
                var result = (from a in inventList
                              group a by new
                              {
                                  a.MaterialId
                              }
                                 into g
                              select new MCodeModel
                              {
                                  MaterialId = g.Key.MaterialId,
                                  Quantity = g.Sum(p => p.Quantity.Value)
                              }).ToList(); ;

                //(循环减去需求数量)
                for (int i = 0; i < consumeData.Count; i++)
                {
                    string mID = consumeData[i].MaterialId;
                    var ivnet = result.Where(p => p.MaterialId == consumeData[i].MaterialId);
                    if (ivnet != null && ivnet.Count() > 0)
                    {
                        consumeData[i].Quantity = consumeData[i].Quantity - result.Where(p => p.MaterialId == mID).Sum(p => p.Quantity);

                        if (consumeData[i].Quantity <= 0)
                        {
                            continue;
                        }
                        else
                        {
                            returnList.Add(consumeData[i]);
                        }
                    }
                    else
                    {
                        returnList.Add(consumeData[i]);
                    }
                }

                return returnList.Where(p => p.Quantity > 0).ToList();
            }

            return consumeData;


        }


        #region 服务 每天0点执行

        /// <summary>
        /// 每天0点定时调用该服务（物料请料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeMRequest_0()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 凌晨请料第二天上半天的         改为请当天的
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            //DateTime star = nowDayTime.AddDays(1);
            //DateTime end = nowDayTime.AddDays(1).AddHours(12);

            DateTime star = nowDayTime;
            DateTime end = nowDayTime.AddHours(12);

            //获取物料的需求
            var getConsume = await GetPoConsumeData(star, end, "PKG3", "2", "2");

            //获取需要去掉的物料（JIT）
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGrouptypes.Contains("JIT"));

            //去掉(JIT)物料
            List<MCodeModel> list = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;
                }
                list.Add(getConsume[i]);
            }
            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "0点暂无需请料的数据";
                return result;
            }
            //重新计算（时间用来筛选请过的料,当天） 改成了扣库存
            var resultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);// GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);

            if (resultLast == null || resultLast.Count <= 0)
            {
                result.success = true;
                result.msg = "0点暂无需请料的数据";
                return result;
            }

            //执行请料
            var lastResult = await InsertRequestData(resultLast, "PKG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }

        #endregion

        #region 服务 每天12点执行

        /// <summary>
        /// 每天12点定时调用该服务（物料请料）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageModel<string>> FixedTimeMRequest_12()
        {
            var result = new MessageModel<string>();
            result.success = false;

            //做个防错 第二天下半天   改为请当前的
            DateTime nowDayTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + " 00:00:00");
            //DateTime star = nowDayTime.AddDays(1).AddHours(12);
            //DateTime end = nowDayTime.AddDays(2).AddSeconds(-1);
            DateTime star = nowDayTime.AddHours(12);
            DateTime end = nowDayTime.AddDays(1).AddSeconds(-1);
            //获取原料的需求
            var getConsume = await GetPoConsumeData(star, end, "PKG3", "2", "2");

            //获取需要去掉的物料（白糖组和原料组）
            var mGroupType = await _MatGroupsEntityDal.FindList(p => p.MaterialGrouptypes.Contains("JIT"));

            //去掉(白糖组和原料组)物料
            List<MCodeModel> list = new List<MCodeModel>();
            for (int i = 0; i < getConsume.Count; i++)
            {
                string mID = getConsume[i].MaterialId;
                if (mGroupType.Where(p => p.MaterialId == mID).Count() > 0)
                {
                    continue;

                }
                list.Add(getConsume[i]);
            }
            if (list == null || list.Count <= 0)
            {
                result.success = true;
                result.msg = "暂无需请料的数据";
                return result;
            }

            //重新计算（时间用来筛选请过的料,当天） 改成了扣库存
            var resultLast = await GetRequestInventData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);// GetPullData(nowDayTime, nowDayTime.AddDays(1).AddSeconds(-1), list);


            //执行请料
            var lastResult = await InsertRequestData(resultLast, "PKG3");
            result.success = lastResult.success;
            result.msg = lastResult.msg;
            return result;
        }

        #endregion

        #endregion

        #endregion

        #region 手工

        #region 拉料


        /// <summary>
        /// 新增拉料记录（最后添加界面，按照物料和时间）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<List<MCodeReturnModel>> SearchPULLInsertData(AddRequestInventory reqModel)
        {
            List<MCodeReturnModel> modelList = new List<MCodeReturnModel>();
            var result = new MessageModel<string>();
            result.success = false;

            //判断两种方式是否都有才进行下去
            string mId = reqModel.materialId;
            decimal qty = reqModel.quantity;
            string star = reqModel.StarTime;
            string end = reqModel.EndTime;
            if (string.IsNullOrEmpty(mId) == true && string.IsNullOrEmpty(star) && string.IsNullOrEmpty(end))
            {
                result.msg = "请选择按时间段或按物料一种模式";
                return new List<MCodeReturnModel>();
            }
            //if (qty <= 0)
            //{
            //    result.msg = "请输入请求数据";
            //    return new List<MCodeReturnModel>();
            //}

            #region 方案一 根据物料和数量拿数据

            if (!string.IsNullOrEmpty(mId) && qty > 0)
            {
                string uID = string.Empty;
                string uName = string.Empty;
                string mName = string.Empty;
                string mCode = string.Empty;
                //拿物料基础单位
                MaterialEntity mModel = await _poMaterialEntity.FindEntity(p => p.ID == mId);
                if (mModel != null)
                {
                    MCodeModel baseModel = new MCodeModel();
                    mName = mModel.Name;
                    mCode = mModel.Code;

                    var unitManager = await _poUnitmanageEntityDal.FindEntity(p => p.ID == mModel.Unit);
                    uID = mModel.Unit;
                    if (unitManager != null)
                    {
                        uName = unitManager.Name;
                    }
                    else //默认纠错
                    {
                        uName = "KG";
                    }

                    baseModel.UnitId = uID;
                    baseModel.UName = uName;
                    baseModel.MaterialId = mId;
                    baseModel.MaterialCode = mCode;
                    baseModel.MaterialName = mName;
                    baseModel.Quantity = qty;

                    string msg = string.Empty;
                    List<MCodeModel> model = new List<MCodeModel>();
                    model.Add(baseModel);
                    //    List<MCodeReturnModel> list = GetRequestMaterial(model, out msg);

                    for (int i = 0; i < model.Count; i++)
                    {
                        MCodeReturnModel m = new MCodeReturnModel();
                        m.MaterialId = model[i].MaterialId;
                        m.MaterialName = model[i].MaterialName;
                        m.MaterialCode = model[i].MaterialCode;
                        m.BagSize = model[i].BagSize;
                        m.Quantity = model[i].Quantity;
                        m.UnitId = model[i].UnitId;
                        m.UName = model[i].UName;
                        m.LotNo = reqModel.LotNo;
                        //m.TrayNumber = ;// model[i].TrayNumber;
                        m.UserNumber = "";// model[i].;
                        m.BarcodeQty = model[i].BarcodeQty;
                        m.PackQty = model[i].PackQty;
                        m.ArraveTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        m.Remark = "";// model[i].;
                        m.Type = reqModel.TypeName;
                        modelList.Add(m);
                    }
                }
            }

            #endregion

            #region 方案二 时间段拿数据和需求

            if (!string.IsNullOrEmpty(star) && !string.IsNullOrEmpty(end))
            {
                DateTime starTime = Convert.ToDateTime(star);
                DateTime endTime = Convert.ToDateTime(end);
                //MFG3  ZXH2
                string typeName = reqModel.TypeName == "MFG3" ? "ZXH2" : "";
                //查询所有的订单信息
                var proList = await _productionOrderEntity.FindList(p => p.PlanDate.Value >= starTime && p.PlanDate.Value <= endTime && p.SapOrderType.Contains("ZXH2") && p.PoStatus == "2");
                if (typeName == string.Empty)
                {
                    proList = await _productionOrderEntity.FindList(p => p.PlanDate.Value >= starTime && p.PlanDate.Value <= endTime && p.SapOrderType != "ZXH2" && p.PoStatus == "1");
                }

                //     proList = proList.Where(p => p.ProductionOrderNo == "020115775801").ToList();

                //排除已完成和废弃工单
                string[] proIDs = proList.Where(p => p.PoStatus != "4" && p.PoStatus != "3").GroupBy(p => new { p.ID }).Select(P => P.Key.ID).ToArray();

                if (proIDs != null && proIDs.Length > 0)
                {
                    //获取所有需求数据
                    var consumeData = await _poConsumeRequirementEntityDal.Db.Queryable<PoConsumeRequirementEntity>().In(P => P.ProductionOrderId, proIDs).ToListAsync();
                    var unitManager = await _poUnitmanageEntityDal.FindList(p => p.ID != null);
                    var materialList = await _poMaterialEntity.FindList(p => p.ID != null);
                    List<MCodeModel> consumeLinq = new List<MCodeModel>();
                    //原料
                    if (reqModel.MType == "ZRAW")
                    {
                        materialList = materialList.Where(p => p.Type.Contains("ZRAW") || p.Type.Contains("ZSFG")).ToList();
                    }
                    //物料
                    else if (reqModel.MType == "ZPKG")
                    {
                        //materialList = materialList.Where(p => p.Type.Contains("ZRAW") || p.Type.Contains("ZSFG")).ToList();
                        materialList = materialList.Where(p => p.Type.Contains("ZPKG") || p.Type.Contains("ZLBS")).ToList();
                    }

                    if (reqModel.TypeName == "PKG3")
                    {
                        //需求改为从物料拉料来源修改一下数据源，改为从PTM_I_ORDER_BOM中获取需求，Production_order_id为工单编号，ITEM_ID为请料物料的编码（需解密），
                        //ITEM_QUANTIRY_WITH_LOSS为请料需求数量，ITEM_UNIT为请料单位，WAREHOUSE_ID为仓储location（筛选PKG3的物料）
                        #region 新的逻辑

                        string[] pNumbers = proList.GroupBy(p => new { p.ProductionOrderNo }).Select(P => P.Key.ProductionOrderNo).ToArray();
                        if (pNumbers != null && pNumbers.Length > 0)
                        {
                            var orderBomS = await _dalOrderBomEntity.Db.Queryable<OrderBomEntity>().In(P => P.ProductionOrderId, pNumbers).Where(P => P.WarehouseId.Contains("PKG3")).ToListAsync();

                            //插入需求表
                            List<OrderBomEntity> listOrderBom = new List<OrderBomEntity>();
                            foreach (var orderBom in orderBomS)
                            {
                                if (string.IsNullOrEmpty(orderBom.ItemId))
                                {
                                    continue;
                                }
                                var materialCode = Enigma.Decrypt(orderBom.ItemId, orderBom.TranNo);
                                var materialName = Enigma.Decrypt(orderBom.ItemDecs, orderBom.TranNo);
                                orderBom.ItemId = materialCode;
                                orderBom.ItemDecs = materialName;
                                listOrderBom.Add(orderBom);
                            }

                            //物料按照消耗来
                            consumeLinq = (from a in listOrderBom
                                           join b in unitManager on a.ItemUnit equals b.Name
                                           join c in materialList on a.ItemId equals c.Code
                                           group a by new
                                           {
                                               MaterialId = c.ID,
                                               UnitId = b.ID,
                                               b.Name,
                                               MName = c.Name,
                                               c.Code
                                           }
                                              into g
                                           select new MCodeModel
                                           {
                                               MaterialId = g.Key.MaterialId,
                                               UnitId = g.Key.UnitId,
                                               UName = g.Key.Name,
                                               MaterialName = g.Key.MName,
                                               MaterialCode = g.Key.Code,
                                               Quantity = g.Sum(p => p.ItemQuantityWithLoss == null ? 0 : p.ItemQuantityWithLoss.Value) //
                                           }).ToList();

                        }
                        #endregion


                        ////物料按照消耗来
                        //consumeLinq = (from a in consumeData
                        //               join b in unitManager on a.UnitId equals b.ID
                        //               join c in materialList on a.MaterialId equals c.ID
                        //               group a by new
                        //               {
                        //                   a.MaterialId,
                        //                   a.UnitId,
                        //                   b.Name,
                        //                   MName = c.Name,
                        //                   c.Code
                        //               }
                        //                  into g
                        //               select new MCodeModel
                        //               {
                        //                   MaterialId = g.Key.MaterialId,
                        //                   UnitId = g.Key.UnitId,
                        //                   UName = g.Key.Name,
                        //                   MaterialName = g.Key.MName,
                        //                   MaterialCode = g.Key.Code,
                        //                   Quantity = g.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value) //
                        //               }).ToList();

                    }
                    else
                    {   //求需求总和
                        consumeLinq = (from a in consumeData
                                       join b in unitManager on a.UnitId equals b.ID
                                       join c in materialList on a.MaterialId equals c.ID
                                       group a by new
                                       {
                                           a.MaterialId,
                                           a.UnitId,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code
                                       }
                                          into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           Quantity = g.Sum(p => p.Quantity.Value)
                                       }).ToList();
                    }





                    string msg = string.Empty;

                    //  List<MCodeReturnModel> list = GetRequestMaterial(consumeLinq, out msg);consumeLinq
                    for (int i = 0; i < consumeLinq.Count; i++)
                    {
                        MCodeReturnModel model = new MCodeReturnModel();
                        model.MaterialId = consumeLinq[i].MaterialId;
                        model.MaterialName = consumeLinq[i].MaterialName;
                        model.MaterialCode = consumeLinq[i].MaterialCode;
                        model.BagSize = consumeLinq[i].BagSize;
                        model.Quantity = consumeLinq[i].Quantity;
                        model.UnitId = consumeLinq[i].UnitId;
                        model.UName = consumeLinq[i].UName;
                        // model.TrayNumber = consumeLinq[i].TrayNumber;
                        //model.UserNumber = consumeLinq[i].UserNumber;
                        model.ArraveTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        model.LotNo = reqModel.LotNo;
                        //  model.Remark = consumeLinq[i].Remark;
                        model.BarcodeQty = "";
                        model.PackQty = consumeLinq[i].PackQty;
                        model.Type = reqModel.TypeName;
                        model.UserNumber = "";
                        modelList.Add(model);
                    }

                }
            }
            //排除糖       reqModel = reqModel.Where(p => !p.MaterialCode.StartsWith("230") && !p.MaterialCode.Contains("**********")).ToList()
            return modelList.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.Contains("6300230004") && !p.MaterialCode.StartsWith("230")).ToList();

            #endregion            
        }


        /// <summary>
        /// 新增拉料记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddPULLInventory(AddRequestInventory reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            decimal qty = reqModel.quantity;
            string mId = reqModel.materialId;
            try
            {
                if (mId == null || mId == "")
                {
                    result.msg = "物料不能为空";
                    return result;
                }
                if (reqModel.quantity <= 0)
                {
                    result.msg = "拉料数量不能为空";
                    return result;
                }
                //返回提示
                var aResult = await _MaterialInventdal.FindList(p => p.MaterialId == reqModel.materialId && reqModel.quantity > 0);
                var mResult = aResult.FirstOrDefault();
                if (mResult == null)
                {
                    result.msg = "物料不存在";
                    return result;
                }
                //获取单位信息
                var unit = mResult.MinUnit;
                string mCode = mResult.MaterialCode;


                #region 规格接口

                //PackingSizeSend modelSend = new PackingSizeSend();
                //modelSend.itemcode = mCode;

                ////调用查询规格接口
                //var pR = GePacktResult(modelSend).Result;
                //if (pR.successed == false)
                //{
                //    result.msg = "调用物料规格接口失败";
                //    return result;
                //}

                ////调用物料接口
                //PackingSizeResult packR = pR.Response;
                //if (packR.flag != true)
                //{
                //    result.msg = packR.msg;
                //    return result;
                //}

                #endregion
                //
                string packQty = "0"; //packR.data[0].packQty;
                string barcodeQty = "0";// packR.data[0].barcodeQty;

                //计算柜数量
                decimal pkNumber = 0;// = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                //decimal totalNumber = qty / pkNumber;
                //decimal ys = qty % pkNumber;

                decimal totalNumber = 0;
                decimal ys = 0;
                if (pkNumber != 0)
                {
                    totalNumber = qty / pkNumber;
                    ys = qty % pkNumber;
                }

                if (ys > 0)
                {
                    totalNumber = totalNumber + 1;

                    //重置下柜数
                    if (totalNumber % Convert.ToDecimal(1) > 0)
                    {
                        totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                    }
                }

                #region 新增数据

                //获取物料是否存在
                List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
                List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();

                var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId);
                if (Requestmodel != null)
                {
                    RequestDetailEntity rDModel = new RequestDetailEntity();
                    rDModel.Create(_user.Name.ToString());
                    rDModel.InventoryRequestId = Requestmodel.ID;
                    rDModel.Quantity = qty;
                    rDModel.Status = "";
                    rDModel.BagWeight = pkNumber;
                    rDModel.PalletNumber = Convert.ToInt32(barcodeQty == null ? 0 : barcodeQty);
                    rDModel.Pallet = Convert.ToInt32(totalNumber);
                    requestDetailEntityList.Add(rDModel);
                }
                else
                {
                    string id = string.Empty;
                    InventoryRequestEntity model = new InventoryRequestEntity();
                    model.Create(_user.Name.ToString());
                    model.MaterialId = mResult.MaterialId;
                    id = model.ID;
                    model.UomId = mResult.QuantityUomId;
                    model.EquipmentId = mResult.EquipmentId;
                    model.ExternalStatus = mResult.StatusF;
                    model.Type = "手动";
                    inventoryRequestEntityList.Add(model);

                    RequestDetailEntity rDModel = new RequestDetailEntity();
                    rDModel.Create(_user.Name.ToString());
                    rDModel.InventoryRequestId = id;
                    rDModel.Quantity = qty;
                    rDModel.Status = "";
                    rDModel.BagWeight = pkNumber;
                    rDModel.PalletNumber = Convert.ToInt32(barcodeQty == null ? 0 : barcodeQty);
                    rDModel.Pallet = Convert.ToInt32(totalNumber);
                    requestDetailEntityList.Add(rDModel);
                }

                #endregion

                #region 拉料接口

                #region 构造发送数据

                List<PullMaterial> pullMaterialList = new List<PullMaterial>();
                PullMaterial pmodel = new PullMaterial();


                #region 新版

                pmodel.partmaterialcode = mCode;
                pmodel.partunit = unit;
                pmodel.qty = totalNumber;//totalNumber;
                pmodel.batch = "";//批次号如何定位
                                  //接收仓位
                if (mCode.Substring(0, 1) == "4")
                {
                    pmodel.recivestockcode = "PKG3"; //"MFG3";
                }
                else
                {
                    pmodel.recivestockcode = "MFG3"; //"MFG3";
                }
                // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                //到货日期(这里注意数据转换)
                pmodel.arrivetime = reqModel.StarTime;

                #endregion

                //pmodel.partmaterialcode = mCode;
                //pmodel.partunit = unit;
                //pmodel.qty = totalNumber;
                //pmodel.recivestockcode = "MFG3";//必须填写


                pullMaterialList.Add(pmodel);

                #endregion

                var pullR = GetPullMaterial(pullMaterialList).Result;
                if (pullR.successed == false)
                {
                    result.msg = "调用拉料接口失败";
                    return result;
                }
                PullMaterialResult pullResult = pullR.Response;

                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料拉料失败" + pullResult.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据

                _unitOfWork.BeginTran();

                await _RequestDetailEntity.Add(requestDetailEntityList);
                if (inventoryRequestEntityList.Count >= 1)
                {
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "请料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "原物料请料失败" + ex.Message;
                return result;
            }
        }


        /// <summary>
        /// 新增拉料记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddPULLInven(List<MCodeReturnModel> reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增失败";
                return result;
            }
            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;
            #region List集合

            List<InventoryPullEntity> inventoryRequestEntityList = new List<InventoryPullEntity>();
            List<PullDetailEntity> requestDetailEntityList = new List<PullDetailEntity>();
            List<PullMaterial> pullMaterialList = new List<PullMaterial>();
            List<InventoryPullEntity> upList = new List<InventoryPullEntity>();
            #endregion
            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;

                    batchNo = reqModel[i].LotNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArraveTime;

                    //
                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }

                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }

                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据

                    //获取物料是否存在               

                    var Requestmodel = await _InventoryPullEntityDal.FindEntity(p => p.MaterialId == mId && p.TypeL == reqModel[i].Type);
                    if (Requestmodel != null)
                    {
                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.Batchno = batchNo;
                        rDModel.BagWeight = pkNumber;
                        rDModel.Type = reqModel[i].Type;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);

                        //执行更新
                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());

                        upList.Add(Requestmodel);

                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryPullEntity model = new InventoryPullEntity();
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        model.TypeL = reqModel[i].Type;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;
                        model.EquipmentId = reqModel[i].Type;
                        //MaterialGroupViewEntity mModel = await _dalMaterialGroupViewEntity.FindEntity(p => p.ID == reqModel[i].MaterialId && p.MaterialGroupName == reqModel[i].Type);
                        //if (mModel != null && !string.IsNullOrEmpty(mModel.ID))
                        //{
                        //    model.EquipmentId = mModel.Groupid;
                        //}
                        //else
                        //{
                        //    var resultData = await _dalMaterialGroupViewEntity.FindList(p => p.MaterialGroupName == reqModel[i].Type);
                        //    if (resultData != null)
                        //    {
                        //        model.EquipmentId = resultData[0].Groupid;
                        //    }

                        //    //result.msg = "拉料失败,请配置物料组：" + reqModel[i].MaterialCode + "-" + reqModel[i].MaterialName;
                        //    //return result;
                        //}

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "手动";

                        #region 防错

                        ////这里查询当前物料是否已经新增
                        //List<InventoryPullEntity> list = inventoryRequestEntityList.Where(p => p.MaterialId == model.MaterialId && p.TypeL == model.TypeL).ToList();
                        //if (list != null && list.Count > 0)
                        //{
                        //    id = list[0].ID;
                        //}

                        #endregion

                        inventoryRequestEntityList.Add(model);

                        PullDetailEntity rDModel = new PullDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Quantity = qty;
                        rDModel.Batchno = batchNo;
                        rDModel.Status = "";
                        rDModel.Type = reqModel[i].Type;
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        requestDetailEntityList.Add(rDModel);
                    }

                    #endregion

                    #region 拉料接口

                    #region 构造发送数据

                    //PullMaterialSend pmodel = new PullMaterialSend();

                    PullMaterial pMmodel = new PullMaterial();
                    pMmodel.partmaterialcode = mCode;
                    pMmodel.partunit = unit;
                    pMmodel.qty = qty;//totalNumber;
                    pMmodel.batch = batchNo;
                    //接收仓位
                    //if (mCode.Substring(0, 1) == "4")
                    //{
                    //    pMmodel.recivestockcode = "PKG3"; //"MFG3";
                    //}
                    //else
                    //{
                    //    pMmodel.recivestockcode = "MFG3"; //"MFG3";
                    //}

                    pMmodel.recivestockcode = reqModel[i].Type; //"MFG3";
                    //到货日期(这里注意数据转换)
                    pMmodel.arrivetime = arriveData;
                    pullMaterialList.Add(pMmodel);

                    #endregion

                    #endregion
                }

                #region 拉料 先注释             

                var pullR = GetPullMaterial(pullMaterialList).Result;
                if (pullR.successed == false)
                {
                    result.msg = "调用拉料接口失败";
                    return result;
                }
                PullMaterialResult pullResult = pullR.Response;

                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料拉料失败" + pullResult.msg;
                    return result;
                }




                #endregion


                #region 保存数据库数据

                bool result1 = true;
                bool result2 = true;
                bool result3 = true;
                result1 = await _PullDetailEntityDal.Add(requestDetailEntityList) > 0;
                if (inventoryRequestEntityList.Count >= 1)
                {
                    result2 = await _InventoryPullEntityDal.Add(inventoryRequestEntityList) > 0;
                }
                if (upList != null && upList.Count > 0)
                {
                    result3 = await _InventoryPullEntityDal.Update(upList);
                }
                if (result1 == false || result2 == false || result3 == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料拉料失败" + pullResult.msg;
                    return result;
                }
                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "请料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "原物料请料失败" + ex.Message;
                return result;
            }
        }


        #endregion

        #region 请料


        /// <summary>
        /// 新增请料记录（最后添加界面，按照物料和时间）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<List<MCodeReturnModel>> SearchRequestInsertData(AddRequestInventory reqModel)
        {
            List<MCodeReturnModel> modelList = new List<MCodeReturnModel>();
            var result = new MessageModel<string>();
            result.success = false;

            //判断两种方式是否都有才进行下去
            string mId = reqModel.materialId;
            decimal qty = reqModel.quantity;
            string star = reqModel.StarTime;
            string end = reqModel.EndTime;
            if (string.IsNullOrEmpty(mId) == true && string.IsNullOrEmpty(star) && string.IsNullOrEmpty(end))
            {
                result.msg = "请选择按时间段或按物料一种模式";
                return new List<MCodeReturnModel>();
            }
            //if (qty <= 0)
            //{
            //    result.msg = "请输入请求数据";
            //    return new List<MCodeReturnModel>();
            //}

            #region 方案一 根据物料和数量拿数据

            if (!string.IsNullOrEmpty(mId) && qty > 0)
            {
                string uID = string.Empty;
                string uName = string.Empty;
                string mName = string.Empty;
                string mCode = string.Empty;
                //拿物料基础单位
                MaterialEntity mModel = await _poMaterialEntity.FindEntity(p => p.ID == mId);
                if (mModel != null)
                {
                    MCodeModel baseModel = new MCodeModel();
                    mName = mModel.Name;
                    mCode = mModel.Code;

                    var unitManager = await _poUnitmanageEntityDal.FindEntity(p => p.ID == mModel.Unit);
                    uID = mModel.Unit;
                    if (unitManager != null)
                    {
                        uName = unitManager.Name;
                    }
                    else //默认纠错
                    {
                        uName = "KG";
                    }

                    baseModel.UnitId = uID;
                    baseModel.UName = uName;
                    baseModel.MaterialId = mId;
                    baseModel.MaterialCode = mCode;
                    baseModel.MaterialName = mName;
                    baseModel.Quantity = qty;

                    string msg = string.Empty;
                    List<MCodeModel> model = new List<MCodeModel>();
                    model.Add(baseModel);
                    List<MCodeReturnModel> list = GetRequestMaterial(model, out msg);

                    for (int i = 0; i < list.Count; i++)
                    {
                        MCodeReturnModel m = new MCodeReturnModel();
                        m.MaterialId = list[i].MaterialId;
                        m.MaterialName = list[i].MaterialName;
                        m.MaterialCode = list[i].MaterialCode;
                        m.BagSize = list[i].BagSize;
                        m.Quantity = list[i].Quantity;
                        m.UnitId = list[i].UnitId;
                        m.UName = list[i].UName;
                        m.LotNo = reqModel.LotNo;
                        m.TrayNumber = list[i].TrayNumber;
                        m.UserNumber = list[i].UserNumber;
                        m.BarcodeQty = list[i].BarcodeQty;
                        m.PackQty = list[i].PackQty;
                        m.ArraveTime = list[i].ArraveTime;
                        m.Remark = list[i].Remark;
                        m.Type = reqModel.TypeName;
                        modelList.Add(m);
                    }
                }
            }

            #endregion

            #region 方案二 时间段拿数据和需求

            if (!string.IsNullOrEmpty(star) && !string.IsNullOrEmpty(end))
            {
                DateTime starTime = Convert.ToDateTime(star);
                DateTime endTime = Convert.ToDateTime(end);
                //MFG3  ZXH2
                string typeName = reqModel.TypeName == "MFG3" ? "ZXH2" : "";
                //查询所有的订单信息
                var proList = await _productionOrderEntity.FindList(p => p.PlanDate.Value >= starTime && p.PlanDate.Value <= endTime && p.SapOrderType.Contains("ZXH2") && p.PoStatus == "2");
                if (typeName == string.Empty)
                {
                    proList = await _productionOrderEntity.FindList(p => p.PlanDate.Value >= starTime && p.PlanDate.Value <= endTime && p.SapOrderType != "ZXH2" && p.PoStatus == "2");
                }

                string[] proIDs = proList.GroupBy(p => new { p.ID }).Select(P => P.Key.ID).ToArray();
                if (proIDs != null && proIDs.Length > 0)
                {
                    //获取所有需求数据
                    var consumeData = await _poConsumeRequirementEntityDal.Db.Queryable<PoConsumeRequirementEntity>().In(P => P.ProductionOrderId, proIDs).ToListAsync();
                    var unitManager = await _poUnitmanageEntityDal.FindList(p => p.ID != null);
                    var materialList = await _poMaterialEntity.FindList(p => p.ID != null);
                    if (reqModel.MType == "ZRAW")
                    {
                        materialList = materialList.Where(p => p.Type.Contains("ZRAW") || p.Type.Contains("ZSFG")).ToList();
                    }
                    //物料
                    else if (reqModel.MType == "ZPKG")
                    {
                        materialList = materialList.Where(p => p.Type.Contains("ZPKG") || p.Type.Contains("ZLBS")).ToList();
                    }
                    List<MCodeModel> consumeLinq = new List<MCodeModel>();
                    //PKG3物料
                    if (typeName == "PKG3")
                    {  //物料按照消耗来
                        consumeLinq = (from a in consumeData
                                       join b in unitManager on a.UnitId equals b.ID
                                       join c in materialList on a.MaterialId equals c.ID
                                       group a by new
                                       {
                                           a.MaterialId,
                                           a.UnitId,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code
                                       }
                                      into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           Quantity = g.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value) //
                                       }).ToList();
                    }
                    else
                    {
                        consumeLinq = (from a in consumeData
                                       join b in unitManager on a.UnitId equals b.ID
                                       join c in materialList on a.MaterialId equals c.ID
                                       group a by new
                                       {
                                           a.MaterialId,
                                           a.UnitId,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code
                                       }
                                          into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           Quantity = g.Sum(p => p.Quantity.Value)
                                       }).ToList();
                    }
                    string msg = string.Empty;

                    List<MCodeReturnModel> list = new List<MCodeReturnModel>();

                    //这里如果是原料请料
                    if (reqModel.TypeName == "MFG3")
                    {
                        list = GetRequestMaterial(consumeLinq.Where(p => p.MaterialCode.Contains("**********") || p.MaterialCode.StartsWith("230")).ToList(), out msg);
                        for (int i = 0; i < list.Count; i++)
                        {
                            MCodeReturnModel model = new MCodeReturnModel();
                            model.MaterialId = list[i].MaterialId;
                            model.MaterialName = list[i].MaterialName;
                            model.MaterialCode = list[i].MaterialCode;
                            model.BagSize = list[i].BagSize;
                            model.Quantity = list[i].Quantity;
                            model.UnitId = list[i].UnitId;
                            model.UName = list[i].UName;
                            model.TrayNumber = list[i].TrayNumber;
                            model.UserNumber = list[i].UserNumber;
                            model.ArraveTime = list[i].ArraveTime;
                            model.LotNo = reqModel.LotNo;
                            model.Remark = "否";
                            model.BarcodeQty = list[i].BarcodeQty;
                            model.PackQty = list[i].PackQty;
                            model.Type = reqModel.TypeName;
                            modelList.Add(model);
                        }


                    }


                    list = GetRequestMaterial(consumeLinq.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.StartsWith("230")).ToList(), out msg);
                    for (int i = 0; i < list.Count; i++)
                    {
                        MCodeReturnModel model = new MCodeReturnModel();
                        model.MaterialId = list[i].MaterialId;
                        model.MaterialName = list[i].MaterialName;
                        model.MaterialCode = list[i].MaterialCode;
                        model.BagSize = list[i].BagSize;
                        model.Quantity = list[i].Quantity;
                        model.UnitId = list[i].UnitId;
                        model.UName = list[i].UName;
                        model.TrayNumber = list[i].TrayNumber;
                        model.UserNumber = list[i].UserNumber;
                        model.ArraveTime = list[i].ArraveTime;
                        model.LotNo = reqModel.LotNo;
                        model.Remark = "是";
                        model.BarcodeQty = list[i].BarcodeQty;
                        model.PackQty = list[i].PackQty;
                        model.Type = reqModel.TypeName;
                        modelList.Add(model);
                    }

                }

                if (reqModel.TypeName == "MFG3")
                {
                    return modelList.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.StartsWith("230")).ToList();
                }
                else
                {
                    return modelList.ToList();
                }
            }

            return modelList.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.StartsWith("230")).ToList();
            #endregion
        }

        /// <summary>
        /// 新增请料记录（最后添加界面，按照物料和时间）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<List<MCodeReturnModel>> SearchRequestInsertDataOLD(AddRequestInventory reqModel)
        {
            List<MCodeReturnModel> modelList = new List<MCodeReturnModel>();
            var result = new MessageModel<string>();
            result.success = false;

            //判断两种方式是否都有才进行下去
            string mId = reqModel.materialId;
            decimal qty = reqModel.quantity;
            string star = reqModel.StarTime;
            string end = reqModel.EndTime;
            if (string.IsNullOrEmpty(mId) == true && string.IsNullOrEmpty(star) && string.IsNullOrEmpty(end))
            {
                result.msg = "请选择按时间段或按物料一种模式";
                return new List<MCodeReturnModel>();
            }
            //if (qty <= 0)
            //{
            //    result.msg = "请输入请求数据";
            //    return new List<MCodeReturnModel>();
            //}

            #region 方案一 根据物料和数量拿数据

            if (!string.IsNullOrEmpty(mId) && qty > 0)
            {
                string uID = string.Empty;
                string uName = string.Empty;
                string mName = string.Empty;
                string mCode = string.Empty;
                //拿物料基础单位
                MaterialEntity mModel = await _poMaterialEntity.FindEntity(p => p.ID == mId);
                if (mModel != null)
                {
                    MCodeModel baseModel = new MCodeModel();
                    mName = mModel.Name;
                    mCode = mModel.Code;

                    var unitManager = await _poUnitmanageEntityDal.FindEntity(p => p.ID == mModel.Unit);
                    uID = mModel.Unit;
                    if (unitManager != null)
                    {
                        uName = unitManager.Name;
                    }
                    else //默认纠错
                    {
                        uName = "KG";
                    }

                    baseModel.UnitId = uID;
                    baseModel.UName = uName;
                    baseModel.MaterialId = mId;
                    baseModel.MaterialCode = mCode;
                    baseModel.MaterialName = mName;
                    baseModel.Quantity = qty;

                    string msg = string.Empty;
                    List<MCodeModel> model = new List<MCodeModel>();
                    model.Add(baseModel);
                    List<MCodeReturnModel> list = GetRequestMaterial(model, out msg);

                    for (int i = 0; i < list.Count; i++)
                    {
                        MCodeReturnModel m = new MCodeReturnModel();
                        m.MaterialId = list[i].MaterialId;
                        m.MaterialName = list[i].MaterialName;
                        m.MaterialCode = list[i].MaterialCode;
                        m.BagSize = list[i].BagSize;
                        m.Quantity = list[i].Quantity;
                        m.UnitId = list[i].UnitId;
                        m.UName = list[i].UName;
                        m.LotNo = reqModel.LotNo;
                        m.TrayNumber = list[i].TrayNumber;
                        m.UserNumber = list[i].UserNumber;
                        m.BarcodeQty = list[i].BarcodeQty;
                        m.PackQty = list[i].PackQty;
                        m.ArraveTime = list[i].ArraveTime;
                        m.Remark = list[i].Remark;
                        m.Type = reqModel.TypeName;
                        modelList.Add(m);
                    }
                }
            }

            #endregion

            #region 方案二 时间段拿数据和需求

            if (!string.IsNullOrEmpty(star) && !string.IsNullOrEmpty(end))
            {
                DateTime starTime = Convert.ToDateTime(star);
                DateTime endTime = Convert.ToDateTime(end);
                //MFG3  ZXH2
                string typeName = reqModel.TypeName == "MFG3" ? "ZXH2" : "";
                //查询所有的订单信息
                var proList = await _productionOrderEntity.FindList(p => p.PlanStartTime.Value >= starTime && p.PlanStartTime.Value <= endTime && p.SapOrderType.Contains("ZXH2") && p.PoStatus == "2");
                if (typeName == string.Empty)
                {
                    proList = await _productionOrderEntity.FindList(p => p.PlanStartTime.Value >= starTime && p.PlanStartTime.Value <= endTime && p.SapOrderType != "ZXH2" && p.PoStatus == "2");
                }

                string[] proIDs = proList.GroupBy(p => new { p.ID }).Select(P => P.Key.ID).ToArray();
                if (proIDs != null && proIDs.Length > 0)
                {
                    //获取所有需求数据
                    var consumeData = await _poConsumeRequirementEntityDal.Db.Queryable<PoConsumeRequirementEntity>().In(P => P.ProductionOrderId, proIDs).ToListAsync();
                    var unitManager = await _poUnitmanageEntityDal.FindList(p => p.ID != null);
                    var materialList = await _poMaterialEntity.FindList(p => p.ID != null);
                    if (reqModel.MType == "ZRAW")
                    {
                        materialList = materialList.Where(p => p.Type.Contains("ZRAW") || p.Type.Contains("ZSFG")).ToList();
                    }
                    //物料
                    else if (reqModel.MType == "ZPKG")
                    {
                        materialList = materialList.Where(p => p.Type.Contains("ZPKG") || p.Type.Contains("ZLBS")).ToList();
                    }
                    List<MCodeModel> consumeLinq = new List<MCodeModel>();

                    if (typeName == "PKG3")
                    {  //物料按照消耗来
                        consumeLinq = (from a in consumeData
                                       join b in unitManager on a.UnitId equals b.ID
                                       join c in materialList on a.MaterialId equals c.ID
                                       group a by new
                                       {
                                           a.MaterialId,
                                           a.UnitId,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code
                                       }
                                      into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           Quantity = g.Sum(p => p.AdjustPercentQuantity == null ? 0 : p.AdjustPercentQuantity.Value) //
                                       }).ToList();
                    }
                    else
                    {
                        consumeLinq = (from a in consumeData
                                       join b in unitManager on a.UnitId equals b.ID
                                       join c in materialList on a.MaterialId equals c.ID
                                       group a by new
                                       {
                                           a.MaterialId,
                                           a.UnitId,
                                           b.Name,
                                           MName = c.Name,
                                           c.Code
                                       }
                                          into g
                                       select new MCodeModel
                                       {
                                           MaterialId = g.Key.MaterialId,
                                           UnitId = g.Key.UnitId,
                                           UName = g.Key.Name,
                                           MaterialName = g.Key.MName,
                                           MaterialCode = g.Key.Code,
                                           Quantity = g.Sum(p => p.Quantity.Value)
                                       }).ToList();
                    }
                    string msg = string.Empty;

                    List<MCodeReturnModel> list = GetRequestMaterial(consumeLinq.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.StartsWith("230")).ToList(), out msg);

                    for (int i = 0; i < list.Count; i++)
                    {
                        MCodeReturnModel model = new MCodeReturnModel();
                        model.MaterialId = list[i].MaterialId;
                        model.MaterialName = list[i].MaterialName;
                        model.MaterialCode = list[i].MaterialCode;
                        model.BagSize = list[i].BagSize;
                        model.Quantity = list[i].Quantity;
                        model.UnitId = list[i].UnitId;
                        model.UName = list[i].UName;
                        model.TrayNumber = list[i].TrayNumber;
                        model.UserNumber = list[i].UserNumber;
                        model.ArraveTime = list[i].ArraveTime;
                        model.LotNo = reqModel.LotNo;
                        model.Remark = list[i].Remark;
                        model.BarcodeQty = list[i].BarcodeQty;
                        model.PackQty = list[i].PackQty;
                        model.Type = reqModel.TypeName;
                        modelList.Add(model);
                    }

                }
            }
            return modelList.Where(p => !p.MaterialCode.Contains("**********") && !p.MaterialCode.StartsWith("230")).ToList(); ;

            #endregion            
        }


        /// <summary>
        /// 新增请料记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddRequestInven(List<MCodeReturnModel> reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            if (reqModel == null || reqModel.Count <= 0)
            {
                result.msg = "新增失败";
                return result;
            }
            decimal qty = 0;
            string mId = "";
            string unit = string.Empty;
            string mCode = string.Empty;
            string packQty = string.Empty;
            decimal barcodeQty = 0;
            string arriveData = string.Empty;
            string batchNo = string.Empty;
            string remark = string.Empty;
            #region List集合

            List<RequestMaterial> rList = new List<RequestMaterial>();
            List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
            List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();
            #endregion
            try
            {
                _unitOfWork.BeginTran();

                //循环构造数据
                for (int i = 0; i < reqModel.Count; i++)
                {
                    qty = reqModel[i].Quantity;
                    mId = reqModel[i].MaterialId;
                    remark = reqModel[i].Remark;
                    batchNo = reqModel[i].LotNo;
                    //获取单位信息
                    unit = reqModel[i].UName;
                    mCode = reqModel[i].MaterialCode;
                    arriveData = reqModel[i].ArraveTime;

                    //调用接口请料
                    #region 请料接口

                    //构造函数
                    //RequestMaterialSend rsendModel = new RequestMaterialSend();
                    RequestMaterial rModel = new RequestMaterial();

                    rModel.partmaterialcode = mCode;
                    rModel.partunit = unit;
                    rModel.linenumber = "";
                    rModel.qty = qty;
                    rModel.batch = batchNo;
                    rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    rModel.arrivetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    //if (mCode.Substring(0, 1) == "4")
                    //{
                    //    rModel.recivestockcode = "PKG3"; //"MFG3";
                    //}
                    //else
                    //{
                    //    rModel.recivestockcode = "MFG3"; //"MFG3";
                    //}
                    rModel.recivestockcode = reqModel[i].Type; //"MFG3";

                    //rModel.qty = qty;
                    //rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    //rModel.partmaterialcode = mCode;                    
                    rList.Add(rModel);

                    #endregion

                    //
                    packQty = reqModel[i].PackQty;
                    if (!string.IsNullOrEmpty(reqModel[i].BarcodeQty))
                    {
                        barcodeQty = Convert.ToDecimal(reqModel[i].BarcodeQty);
                    }

                    //计算柜数量
                    decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                    //decimal totalNumber = qty / pkNumber;
                    //decimal ys = qty % pkNumber;

                    decimal totalNumber = 0;
                    decimal ys = 0;
                    if (pkNumber != 0)
                    {
                        totalNumber = qty / pkNumber;
                        ys = qty % pkNumber;
                    }
                    if (ys > 0)
                    {
                        totalNumber = totalNumber + 1;

                        //重置下柜数
                        if (totalNumber % Convert.ToDecimal(1) > 0)
                        {
                            totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                        }
                    }

                    #region 新增数据

                    //获取物料是否存在               

                    var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId && p.TypeL == reqModel[i].Type);
                    if (Requestmodel != null)
                    {
                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = Requestmodel.ID;
                        rDModel.Quantity = qty;
                        rDModel.Batchno = batchNo;
                        rDModel.Status = "";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        rDModel.Type = reqModel[i].Type;
                        requestDetailEntityList.Add(rDModel);
                        rDModel.Remark = remark;
                        Requestmodel.Modify(Requestmodel.ID, _user.Name.ToString());
                        await _InventoryRequestEntity.Update(Requestmodel);
                    }
                    else
                    {
                        string id = string.Empty;
                        InventoryRequestEntity model = new InventoryRequestEntity();
                        model.TypeL = reqModel[i].Type;
                        model.Create(_user.Name.ToString());
                        model.MaterialId = reqModel[i].MaterialId;
                        id = model.ID;
                        model.UomId = reqModel[i].UnitId;
                        model.EquipmentId = reqModel[i].Type;

                        //MaterialGroupViewEntity mModel = await _dalMaterialGroupViewEntity.FindEntity(p => p.ID == reqModel[i].MaterialId && p.MaterialGroupName == reqModel[i].Type);
                        //if (mModel != null && !string.IsNullOrEmpty(mModel.ID))
                        //{
                        //    model.EquipmentId = mModel.Groupid;
                        //}
                        //else
                        //{
                        //    //result.msg = "请料失败,请配置物料组：" + reqModel[i].MaterialCode + "-" + reqModel[i].MaterialName;
                        //    //return result;
                        //    var resultData = await _dalMaterialGroupViewEntity.FindList(p => p.MaterialGroupName == reqModel[i].Type);
                        //    if (resultData != null)
                        //    {
                        //        model.EquipmentId = resultData[0].Groupid;
                        //    }
                        //}

                        //  model.EquipmentId = mResult.EquipmentId; 暂时不处理
                        //  model.ExternalStatus = mResult.StatusF; 暂时不处理
                        model.Type = "手动";
                        inventoryRequestEntityList.Add(model);

                        RequestDetailEntity rDModel = new RequestDetailEntity();
                        rDModel.Create(_user.Name.ToString());
                        rDModel.InventoryRequestId = id;
                        rDModel.Type = reqModel[i].Type;
                        rDModel.Batchno = batchNo;
                        rDModel.Quantity = qty;
                        rDModel.Status = "";
                        rDModel.BagWeight = pkNumber;
                        rDModel.PalletNumber = Convert.ToInt32(barcodeQty);
                        rDModel.Pallet = Convert.ToInt32(totalNumber);
                        rDModel.Remark = remark;
                        requestDetailEntityList.Add(rDModel);
                    }

                    #endregion                    
                }



                #region 请料        

                var httpResult = await GetRequestMaterialSend(rList);
                if (httpResult.successed == false)
                {
                    result.msg = "请料接口访问失败";
                    return result;
                }
                if (httpResult.Response.flag == false)
                {
                    result.msg = httpResult.Response.msg;
                    return result;
                }

                #endregion


                #region 保存数据库数据

                await _RequestDetailEntity.Add(requestDetailEntityList);
                if (inventoryRequestEntityList.Count >= 1)
                {
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "请料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "请料失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 新增请料记录
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddRequestInventory(AddRequestInventory reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;

            decimal qty = reqModel.quantity;
            string mId = reqModel.materialId;
            try
            {
                if (mId == null || mId == "")
                {
                    result.msg = "物料不能为空";
                    return result;
                }
                if (reqModel.quantity <= 0)
                {
                    result.msg = "请料数量不能为空";
                    return result;
                }
                //返回提示
                var aResult = await _MaterialInventdal.FindList(p => p.MaterialId == reqModel.materialId && reqModel.quantity > 0);
                var mResult = aResult.FirstOrDefault();
                if (mResult == null)
                {
                    result.msg = "物料不存在";
                    return result;
                }
                //获取单位信息
                var unit = mResult.MinUnit;
                string mCode = mResult.MaterialCode;

                //调用接口请料
                #region 请料接口

                List<RequestMaterial> rList = new List<RequestMaterial>();

                RequestMaterial rModel = new RequestMaterial();


                //amodel1.partmaterialCode = "4300010088";
                //amodel1.partunit = "pc";
                //amodel1.linenumber = "1XC";
                //amodel1.qty = 1320;
                //amodel1.batch = "";
                //amodel1.recivestockcode = "amodel1";
                //amodel1.date = "2024-08-15";
                //amodel1.arrivetime = "2024-08-15";


                rModel.qty = qty;
                rModel.date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                rModel.partmaterialcode = mCode;
                rModel.recivestockcode = "PKG3"; //这里需要考虑如何拿到 PKG3
                rList.Add(rModel);
                var httpResult = await GetRequestMaterialSend(rList);
                if (httpResult.successed == false)
                {
                    result.msg = "请料接口访问失败";
                    return result;
                }
                if (httpResult.Response.flag == false)
                {
                    result.msg = httpResult.Response.msg;
                    return result;
                }

                #endregion

                #region 规格接口

                PackingSizeSend modelSend = new PackingSizeSend();
                modelSend.itemcode = mCode;

                //调用查询规格接口
                var pR = GePacktResult(modelSend).Result;
                if (pR.successed == false)
                {
                    result.msg = "调用物料规格接口失败";
                    return result;
                }

                //调用物料接口
                PackingSizeResult packR = pR.Response;
                if (packR.flag != true)
                {
                    result.msg = packR.msg;
                    return result;
                }

                #endregion
                //
                string packQty = packR.data[0].packQty;
                string barcodeQty = packR.data[0].barcodeQty;

                //计算柜数量
                decimal pkNumber = Convert.ToDecimal(packQty == null ? 0 : packQty) * Convert.ToDecimal(barcodeQty == null ? 0 : barcodeQty);
                //decimal totalNumber = qty / pkNumber;
                //decimal ys = qty % pkNumber;
                decimal totalNumber = 0;
                decimal ys = 0;
                if (pkNumber != 0)
                {
                    totalNumber = qty / pkNumber;
                    ys = qty % pkNumber;
                }
                if (ys > 0)
                {
                    totalNumber = totalNumber + 1;

                    //重置下柜数
                    if (totalNumber % Convert.ToDecimal(1) > 0)
                    {
                        totalNumber = Convert.ToDecimal(Convert.ToInt32(totalNumber) + 1);
                    }
                }

                #region 新增数据

                //获取物料是否存在
                List<InventoryRequestEntity> inventoryRequestEntityList = new List<InventoryRequestEntity>();
                List<RequestDetailEntity> requestDetailEntityList = new List<RequestDetailEntity>();

                var Requestmodel = await _InventoryRequestEntity.FindEntity(p => p.MaterialId == mId);
                if (Requestmodel != null)
                {
                    RequestDetailEntity rDModel = new RequestDetailEntity();
                    rDModel.Create(_user.Name.ToString());
                    rDModel.InventoryRequestId = Requestmodel.ID;
                    rDModel.Quantity = qty;
                    rDModel.Status = "";
                    rDModel.BagWeight = pkNumber;
                    rDModel.PalletNumber = Convert.ToInt32(barcodeQty == null ? 0 : barcodeQty);
                    rDModel.Pallet = Convert.ToInt32(totalNumber);
                    requestDetailEntityList.Add(rDModel);
                }
                else
                {
                    string id = string.Empty;
                    InventoryRequestEntity model = new InventoryRequestEntity();
                    model.Create(_user.Name.ToString());
                    model.MaterialId = mResult.MaterialId;
                    id = model.ID;
                    model.UomId = mResult.QuantityUomId;
                    model.EquipmentId = mResult.EquipmentId;
                    model.ExternalStatus = mResult.StatusF;
                    model.Type = "手动";
                    inventoryRequestEntityList.Add(model);

                    RequestDetailEntity rDModel = new RequestDetailEntity();
                    rDModel.Create(_user.Name.ToString());
                    rDModel.InventoryRequestId = id;
                    rDModel.Quantity = qty;
                    rDModel.Status = "";
                    rDModel.BagWeight = pkNumber;
                    rDModel.PalletNumber = Convert.ToInt32(barcodeQty == null ? 0 : barcodeQty);
                    rDModel.Pallet = Convert.ToInt32(totalNumber);
                    requestDetailEntityList.Add(rDModel);
                }

                #endregion

                #region 拉料接口

                #region 构造发送数据

                List<PullMaterial> pullMaterialList = new List<PullMaterial>();
                PullMaterial pmodel = new PullMaterial();


                #region 新版

                pmodel.partmaterialcode = mCode;
                pmodel.partunit = unit;
                pmodel.qty = totalNumber;//totalNumber;
                pmodel.batch = "";//批次号如何定位
                                  //接收仓位
                if (mCode.Substring(0, 1) == "4")
                {
                    pmodel.recivestockcode = "PKG3"; //"MFG3";
                }
                else
                {
                    pmodel.recivestockcode = "MFG3"; //"MFG3";
                }
                // pMmodel.recivestockcode = "MFG3"; //"MFG3";
                //到货日期(这里注意数据转换)
                pmodel.arrivetime = reqModel.StarTime;

                #endregion

                //pmodel.partmaterialcode = mCode;
                //pmodel.partunit = unit;
                //pmodel.qty = totalNumber;
                //pmodel.recivestockcode = "MFG3";//必须填写


                pullMaterialList.Add(pmodel);

                #endregion

                var pullR = GetPullMaterial(pullMaterialList).Result;
                if (pullR.successed == false)
                {
                    result.msg = "调用拉料接口失败";
                    return result;
                }
                PullMaterialResult pullResult = pullR.Response;

                if (pullResult.flag == false)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "原物料拉料失败" + pullResult.msg;
                    return result;
                }

                #endregion

                #region 保存数据库数据

                _unitOfWork.BeginTran();

                await _RequestDetailEntity.Add(requestDetailEntityList);
                if (inventoryRequestEntityList.Count >= 1)
                {
                    await _InventoryRequestEntity.Add(inventoryRequestEntityList);
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "请料成功.";
                return result;
                #endregion

            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "原物料请料失败" + ex.Message;
                return result;
            }
        }

        #endregion

        #endregion

        #endregion

    }
}