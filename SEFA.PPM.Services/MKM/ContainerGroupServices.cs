
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.MKM.Services
{
    public class ContainerGroupServices : BaseServices<ContainerGroupEntity>, IContainerGroupServices
    {
        private readonly IBaseRepository<ContainerGroupEntity> _dal;
        public ContainerGroupServices(IBaseRepository<ContainerGroupEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}