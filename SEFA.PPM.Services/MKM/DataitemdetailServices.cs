
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class DataitemdetailServices : BaseServices<DataitemdetailEntity>, IDataitemdetailServices
    {
        private readonly IBaseRepository<DataitemdetailEntity> _dal;
        public DataitemdetailServices(IBaseRepository<DataitemdetailEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<DataitemdetailEntity>> GetList(DataitemdetailRequestModel reqModel)
        {
            var data = await _dal.FindList(x => x.Itemcode.Contains(reqModel.Itemcode));//ReverseType
            return data;
        }

        public async Task<PageModel<DataitemdetailEntity>> GetPageList(DataitemdetailRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DataitemdetailEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        /// <summary>
        /// 获取Itemcode='ReverseType'反冲类型
        /// </summary>
        /// <returns></returns>
        public async Task<List<DataitemdetailEntity>> GetReasonCode()
        {
            var data = await _dal.FindList(x => x.Itemcode == "ReverseType");//ReverseType
            return data;
        }
        public async Task<bool> SaveForm(DataitemdetailEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }

        #region 称量备料

        public async Task<PageModel<DataitemdetailEntity>> GetPageList_CLBL(DataitemdetailRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DataitemdetailEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }


        #endregion
    }
}