
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.Base.Common.Common;
using SEFA.PPM.Model.Models.PPM;
using System.Linq;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.PPM.Model.ViewModels.MKM.View;
using System;
using StackExchange.Profiling.Internal;
using SEFA.DFM.Model.Models.Models.ViewModel;
using System.Security.Cryptography;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    public class GaveMaterialViewServices : BaseServices<GaveMaterialViewEntity>, IGaveMaterialViewServices
    {
        private readonly IBaseRepository<GaveMaterialViewEntity> _dal;
        private readonly IBaseRepository<BoardNumberViewEntity> _BoardNumberViewEntity;
        private readonly IBaseRepository<DeadlyMaterialViewEntity> _DeadlyMaterialViewEntity;
        private readonly IBaseRepository<RequestIiViewEntity> _RequestIiViewEntity;
        private readonly IBaseRepository<MaterialEntity> _MaterialEntity;
        private readonly IBaseRepository<UnitmanageEntity> _UnitmanageEntity;
        public GaveMaterialViewServices(IBaseRepository<GaveMaterialViewEntity> dal, IBaseRepository<BoardNumberViewEntity> boardNumberViewEntity,
                  IBaseRepository<DeadlyMaterialViewEntity> deadlyMaterialViewEntity, IBaseRepository<RequestIiViewEntity> requestIiViewEntity, IBaseRepository<MaterialEntity> MaterialEntity, IBaseRepository<UnitmanageEntity> UnitmanageEntity)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _BoardNumberViewEntity = boardNumberViewEntity;
            _DeadlyMaterialViewEntity = deadlyMaterialViewEntity;
            _RequestIiViewEntity = requestIiViewEntity;
            _MaterialEntity = MaterialEntity;
            _UnitmanageEntity = UnitmanageEntity;
        }

        public async Task<List<GaveMaterialViewEntity>> GetList(GaveMaterialViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<GaveMaterialViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }
        /// <summary>
        /// 原料库存看板上方统计
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<BoardNumberViewRequestModel> GetListTop()
        {
            BoardNumberViewRequestModel model = new BoardNumberViewRequestModel();
            DateTime todayStart = DateTime.Today; // 当天的00:00:00
            DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59

            var data = await _RequestIiViewEntity.FindList(p => p.CreateDate >= todayStart && p.CreateDate <= todayEnd && p.FType == "MFG3" && p.SType == "MFG3");
            var CompleteNum = 0;
            var PlanNum = 0;
            var Progress = 0;
            foreach (var item in data)
            {
                if (item.ActualQuantity >= item.NeedQuantity)
                {
                    CompleteNum += 1;
                }
                else
                {
                    PlanNum += 1;
                }
            }

            if (PlanNum > 0)
            {
                Progress = (CompleteNum / PlanNum) * 100;
            }
            model.CompleteNum = CompleteNum;
            model.PlanNum = PlanNum;
            model.Progress = Progress;
            return model;
        }

        /// <summary>
        /// 原料库存看板送料提醒查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>

        public async Task<List<GaveMaterialModel>> GetPageList(GaveMaterialViewRequestModel reqModel)
        {
            List<MaterialEntity> materials = new List<MaterialEntity>();
            DateTime todayStart = DateTime.Today; // 当天的00:00:00
            DateTime todayEnd = todayStart.AddDays(1).AddSeconds(-1); // 当天的23:59:59

            List<GaveMaterialModel> models = new List<GaveMaterialModel>();
            var whereExpression = Expressionable.Create<RequestIiViewEntity>()
                .And(p => p.FType == "MFG3")
                .And(p => p.SType == "MFG3")
                .And(p => p.CreateDate >= todayStart && p.CreateDate <= todayEnd)
                .ToExpression();
            var data = await _RequestIiViewEntity.FindList(whereExpression);
            var mids = data.GroupBy(p => new { p.MId }).Select(p => new { p.Key.MId }).ToList();
            foreach (var item in mids)
            {
                var MaterialData = await _MaterialEntity.FindEntity(p => p.ID == item.MId);
                materials.Add(MaterialData);
            }

            var UnitData = await _UnitmanageEntity.FindList(p => p.ID != null);

            if (data.Count > 0)
            {
                foreach (var item in data)
                {
                    var unitName = "";
                    var unit = materials.Where(p => p.ID == item.MId).Select(p => p.Unit).FirstOrDefault();
                    if (unit != null)
                    {
                        unitName = UnitData.Where(p => p.ID == unit).Select(p => p.Name).FirstOrDefault();
                    }
                    ///var unitName= UnitData.UnitName;
                    GaveMaterialModel model = new GaveMaterialModel();
                    model.MaterialName = item.MaterialName;
                    model.MaterialGroups = "MFG3";
                    model.InventoryQuantity = item.ActualQuantity;
                    model.InventoryUnit1 = unitName;
                    model.PlanQty = item.NeedQuantity;
                    model.Unit1 = unitName;
                    model.Difference = item.ActualQuantity - item.NeedQuantity;
                    models.Add(model);
                }
            }
            models = models.OrderBy(p => p.Difference).ToList();
            return models;
        }
        /// <summary>
        /// 原料库存看板临期物料清单（MFG3）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<DeadlyMaterialViewEntity>> GetListDown(DeadlyMaterialViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<DeadlyMaterialViewEntity>().ToExpression();
            var data = await _DeadlyMaterialViewEntity.Db.Queryable<DeadlyMaterialViewEntity>()
            .Where(whereExpression).OrderBy(p => p.Daysdifference)
            .ToListAsync();
      
            //分组求和
            var groupList = data.GroupBy(p => new { p.EquipmentCode, p.EquipmentName, p.MaterialCode, p.MaterialName, p.LotId, p.ExpirationDate, p.Daysdifference, p.InventoryUnit })
                .Select(p => new DeadlyMaterialViewEntity
                {
                    EquipmentCode = p.Key.EquipmentName,
                    MaterialCode = p.Key.MaterialCode,
                    LotId = p.Key.LotId,
                    MaterialName = p.Key.MaterialName,
                    ExpirationDate=p.Key.ExpirationDate,
                    Daysdifference = p.Key.Daysdifference,
                    InventoryUnit = p.Key.InventoryUnit,
                    Quantity = p.Sum(p => p.Quantity == null ? 0 : p.Quantity.Value)
                }).ToList();

            if (groupList == null) 
            {
                return new List<DeadlyMaterialViewEntity>();
            }

            return groupList;
        }

        public async Task<bool> SaveForm(GaveMaterialViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}