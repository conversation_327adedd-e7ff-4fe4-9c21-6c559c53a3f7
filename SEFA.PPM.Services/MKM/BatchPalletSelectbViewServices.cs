
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class BatchPalletSelectbViewServices : BaseServices<BatchPalletSelectbViewEntity>, IBatchPalletSelectbViewServices
    {
        private readonly IBaseRepository<BatchPalletSelectbViewEntity> _dal;
        public BatchPalletSelectbViewServices(IBaseRepository<BatchPalletSelectbViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BatchPalletSelectbViewEntity>> GetList(BatchPalletSelectbViewRequestModel reqModel)
        {
            List<BatchPalletSelectbViewEntity> result = new List<BatchPalletSelectbViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletSelectbViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchPalletSelectbViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<BatchPalletSelectbViewEntity>> GetPageList(BatchPalletSelectbViewRequestModel reqModel)
        {
            PageModel<BatchPalletSelectbViewEntity> result = new PageModel<BatchPalletSelectbViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletSelectbViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchPalletSelectbViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BatchPalletSelectbViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}