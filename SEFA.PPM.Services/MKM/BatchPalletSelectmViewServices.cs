
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class BatchPalletSelectmViewServices : BaseServices<BatchPalletSelectmViewEntity>, IBatchPalletSelectmViewServices
    {
        private readonly IBaseRepository<BatchPalletSelectmViewEntity> _dal;
        public BatchPalletSelectmViewServices(IBaseRepository<BatchPalletSelectmViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BatchPalletSelectmViewEntity>> GetList(BatchPalletSelectmViewRequestModel reqModel)
        {
            List<BatchPalletSelectmViewEntity> result = new List<BatchPalletSelectmViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletSelectmViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchPalletSelectmViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<BatchPalletSelectmViewEntity>> GetPageList(BatchPalletSelectmViewRequestModel reqModel)
        {
            PageModel<BatchPalletSelectmViewEntity> result = new PageModel<BatchPalletSelectmViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BatchPalletSelectmViewEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BatchPalletSelectmViewEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BatchPalletSelectmViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}