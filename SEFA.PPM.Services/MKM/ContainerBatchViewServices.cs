
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.MKM.Services
{
    public class ContainerBatchViewServices : BaseServices<ContainerBatchViewEntity>, IContainerBatchViewServices
    {
        private readonly IBaseRepository<ContainerBatchViewEntity> _dal;
        public ContainerBatchViewServices(IBaseRepository<ContainerBatchViewEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ContainerBatchViewEntity>> GetList(ContainerBatchViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ContainerBatchViewEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<ContainerBatchViewEntity>> GetPageList(ContainerBatchViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ContainerBatchViewEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(ContainerBatchViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}