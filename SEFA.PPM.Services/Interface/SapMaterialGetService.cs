using Castle.MicroKernel.Registration;
using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using System;
using System.Collections.Generic;
using System.Data.OscarClient;
using System.Linq;
using System.Threading.Tasks;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;

namespace SEFA.PPM.Services
{

	public class SapMaterialGetService : BaseServices<MaterialEntity>, ILkkEsbService
	{
		private readonly IBaseRepository<MaterialEntity> _dal;
		private readonly IBaseRepository<MaterialGroupEntity> _dal4;
		private readonly IBaseRepository<MaterialVersionEntity> _dal5;
		private readonly IBaseRepository<MaterialPropertyValueEntity> _dal6;
		private readonly IUnitOfWork _unitOfWork;
		private readonly string _serviceName = "SAP_MATERIALGET";
		private readonly string _propertyCode = "ShelfLife";


		public SapMaterialGetService(IBaseRepository<MaterialEntity> dal, IUnitOfWork unitOfWork, IBaseRepository<MaterialGroupEntity> dal4, IUser user, IBaseRepository<MaterialVersionEntity> dal5, IBaseRepository<MaterialPropertyValueEntity> dal6)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_dal4 = dal4;
			_unitOfWork = unitOfWork;
			var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
			_serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_MATERIALGET" : serviceName;
			_dal5 = dal5;
			_dal6 = dal6;
		}

		/// <summary>
		/// 接口名称，和ESB的MessageID一致
		/// </summary>
		public string ServiceName
		{
			get => _serviceName;
		}

		/// <summary>
		/// 执行接口
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public async Task<DataResultModel> Execute(DataRequestModel request)
		{
			var result = new DataResultModel(request)
			{
				ids = new List<string>()
			};
			var mvaddList = new List<MaterialVersionEntity>();
			var addList = new List<MaterialEntity>();
			var updateList = new List<MaterialEntity>();
			var addList2 = new List<MaterialGroupEntity>();
			var addList3 = new List<MaterialPropertyValueEntity>();
			var updateList3 = new List<MaterialPropertyValueEntity>();
			var sourceDataList = JsonConvert.DeserializeObject<List<MM_Material_Masterdata>>(request.data.ToString());
			//var materialGroups = await _dal4.Query();
			var materialVersions = (await _dal5.Query()) ?? new List<MaterialVersionEntity>();
			var materialPropertyValues = await _dal6.FindList(x => x.PropertyCode == _propertyCode) ?? new List<MaterialPropertyValueEntity>();
			//获取Category
			MessageModel<List<CategoryEntity>> apiResult_categories = await HttpHelper.PostAsync<List<CategoryEntity>>("DFM", "api/Category/GetList?Identities=MaterialType", null, null);
			if (apiResult_categories.success != true)
			{
				result.msg = apiResult_categories.msg;
				return result;
			}
			var categories = apiResult_categories.response;
			var units = new List<UnitmanageEntity>();
			MessageModel<PageModel<UnitmanageEntity>> apiResult_units = await HttpHelper.PostAsync<PageModel<UnitmanageEntity>>("DFM", "api/Unitmanage/GetPageList", null, new { pageIndex = 1, pageSize = 999999 });
			if (apiResult_units.success == true && apiResult_units.response != null && apiResult_units.response.data != null)
			{
				//获取未删除以及启用的
				units = apiResult_units.response.data.FindAll(x => x.Deleted != 1 && x.Enable == 1);
			}
			var materials = await _dal.Query();
			foreach (var item in sourceDataList)
			{
				//备件：MTART In('ZEP')
				//物料：MTART Not In('ZEP', 'ZNOV')
				//ZEP是备件，ZNOV是消耗品，两者不是就是物料（成品、半成品、生产物料等）。
				//这两种跳过，只保留物料
				if (item.mtart == "ZEP" /*|| item.mtart == "ZNOV"*/)
				{
					continue;
				}

				result.ids.Add(item.id.ToString());
				var category = categories?.Find(x => x.Code == item.mtart);
				//if (!addList2.Exists(x => x.MaterialGroupName == item.matkl_zh) && !materialGroups.Exists(x => x.MaterialGroupName == item.matkl_zh))
				//{
				//	var materialGroup = new MaterialGroupEntity()
				//	{
				//		MaterialGroupName = item.matkl_zh ?? "",
				//		Status = "1",
				//		//Remark = "",
				//		Deleted = 0,
				//	};
				//	materialGroup.CreateCustomGuid(_serviceName);
				//	addList2.Add(materialGroup);
				//}
				int.TryParse(item.mhdhb, out int i);
				var unit = units.Find(x => x.Name == item.meins);
				var materialEntity = materials.Find(x => x.Plant == item.werks && x.Code == item.matnr);
				if (materialEntity == null)
				{
					materialEntity = new MaterialEntity()
					{
						ParentId = null,
						Type = item.mtart,
						CompanyId = null,
						Plant = item.werks,
						Code = item.matnr,
						NAME = item.maktx_chi,
						Description = item.normt,
						Version = null,
						Seriescode = item.matse,
						Categorycode = item.matca,
						Family = null,
						CustomerMaterialcode = null,
						CustomerEdition = null,
						HhMaterialCode = null,
						HhEdition = null,
						Manufactor = null,
						Spec = null,
						Unit = unit?.ID,
						CustomerCode = null,
						MainWhId = null,
						CategoryName = null,
						CategoryDescription = null,
						VendorPn = null,
						VendorCode = null,
						ProductType = null,
						SourceType = item.lgpro,
						CtVersion = null,
						MaterialType = category?.ID,
						UnitOfMeasure = item.eisbe?.ToString(),
						ProductionGroup = item.matkl,
						Remarks = null,
						IsSpecial = "20241106",
						SpecialType = null,
						Invalid = null,
						Deleted = 0,
						Accesstime = null,
						Kschl1 = item.KSCHL_1_M,
						Kschl2M = item.KSCHL_2_M,
						Kschl1E = item.KSCHL_1_E,
						Kschl2E = item.KSCHL_2_E,
						MatklEn = item.matkl_en,
						MatklZh = item.matkl_zh,
						Mhdhb = i,
						Iprkz = item.iprkz,
						Lgpro = item.lgpro,
					};
					materialEntity.CreateCustomGuid(_serviceName);
					addList.Add(materialEntity);
				}
				else
				{
					materialEntity.ParentId = null;
					materialEntity.Type = item.mtart;
					materialEntity.CompanyId = null;
					materialEntity.Plant = item.werks;
					materialEntity.Code = item.matnr;
					materialEntity.NAME = item.maktx_chi;
					materialEntity.Description = item.normt;
					materialEntity.Version = null;
					materialEntity.Seriescode = item.matse;
					materialEntity.Categorycode = item.matca;
					materialEntity.Family = null;
					materialEntity.CustomerMaterialcode = null;
					materialEntity.CustomerEdition = null;
					materialEntity.HhMaterialCode = null;
					materialEntity.HhEdition = null;
					materialEntity.Manufactor = null;
					materialEntity.Spec = null;
					materialEntity.Unit = unit?.ID;
					materialEntity.CustomerCode = null;
					materialEntity.MainWhId = null;
					materialEntity.CategoryName = null;
					materialEntity.CategoryDescription = null;
					materialEntity.VendorPn = null;
					materialEntity.VendorCode = null;
					materialEntity.ProductType = null;
					materialEntity.SourceType = item.lgpro;
					materialEntity.CtVersion = null;
					materialEntity.MaterialType = category?.ID;
					materialEntity.UnitOfMeasure = item.eisbe?.ToString();
					materialEntity.ProductionGroup = item.matkl;
					materialEntity.Remarks = null;
					materialEntity.IsSpecial = "20241106";
					materialEntity.SpecialType = null;
					materialEntity.Invalid = null;
					materialEntity.Deleted = 0;
					materialEntity.Accesstime = null;
					materialEntity.Kschl1 = item.KSCHL_1_M;
					materialEntity.Kschl2M = item.KSCHL_2_M;
					materialEntity.Kschl1E = item.KSCHL_1_E;
					materialEntity.Kschl2E = item.KSCHL_2_E;
					materialEntity.MatklEn = item.matkl_en;
					materialEntity.MatklZh = item.matkl_zh;
					materialEntity.Mhdhb = i;
					materialEntity.Iprkz = item.iprkz;
					materialEntity.Lgpro = item.lgpro;
					materialEntity.Modify(materialEntity.ID, _serviceName);
					updateList.Add(materialEntity);
				}
				if (item.mtart == "ZFG" || item.mtart == "ZSFG")
				{
					if (!materialVersions.Exists(x => x.MaterialId == materialEntity.ID))
					{
						MaterialVersionEntity materialVersionEntity = new MaterialVersionEntity()
						{
							MaterialVersionNumber = "0001",
							Plantcode = item.werks,
							MaterialId = materialEntity.ID,
							Deleted = 0,
						};
						materialVersionEntity.CreateCustomGuid(_serviceName);
						mvaddList.Add(materialVersionEntity);
					}
				}

				//var materialPropertyValue = materialPropertyValues.Find(x => x.MaterialId == materialEntity.ID);
				//if (materialPropertyValue == null)
				//{
				//	materialPropertyValue = new MaterialPropertyValueEntity()
				//	{
				//		MaterialId = materialEntity.ID,
				//		PropertyCode = _propertyCode,
				//		PropertyValue = item.mhdhb,
				//		Deleted = 0,
				//		ClassId = "",
				//		Remark = ""
				//	};
				//	materialPropertyValue.CreateCustomGuid(_serviceName);
				//	addList3.Add(materialPropertyValue);
				//}
				//else
				//{
				//	if (materialPropertyValue.PropertyValue != item.mhdhb)
				//	{
				//		materialPropertyValue.PropertyValue = item.mhdhb;
				//		materialPropertyValue.Modify(materialPropertyValue.ID, _serviceName);
				//		updateList3.Add(materialPropertyValue);
				//	}
				//}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateList.Count > 0)
				{
					if (updateList.Count > 1000)
					{
						await _dal.StorageBigData(updateList);
					}
					else
					{
						await _dal.Update(updateList);
					}
				}
				if (addList.Count > 0)
				{
					if (addList.Count > 1000)
					{
						await _dal.AddBigData(addList);
					}
					else
					{
						await _dal.Add(addList);
					}
				}
				if (mvaddList.Count > 0)
				{
					if (mvaddList.Count > 1000)
					{
						await _dal5.AddBigData(mvaddList);
					}
					else
					{
						await _dal5.Add(mvaddList);
					}
				}
				//if (addList2.Count > 0)
				//{
				//	if (addList2.Count > 1000)
				//	{
				//		await _dal4.AddBigData(addList2);
				//	}
				//	else
				//	{
				//		await _dal4.Add(addList2);
				//	}
				//}
				//if (addList3.Count > 0)
				//{
				//	if (addList3.Count > 1000)
				//	{
				//		await _dal6.AddBigData(addList3);
				//	}
				//	else
				//	{
				//		await _dal6.Add(addList3);
				//	}
				//}
				//if (updateList3.Count > 0)
				//{
				//	if (updateList3.Count > 1000)
				//	{
				//		await _dal6.StorageBigData(updateList3);
				//	}
				//	else
				//	{
				//		await _dal6.Update(updateList3);
				//	}
				//}
				_unitOfWork.CommitTran();
				//result.ids = sourceDataList.Select(m => m.id.ToString()).ToList();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.Fail(ex.Message + ex.StackTrace);
			}
			return result;

		}
	}
}
