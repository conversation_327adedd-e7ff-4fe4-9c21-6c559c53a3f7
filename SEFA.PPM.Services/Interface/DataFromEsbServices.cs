using AutoMapper;
using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.MKM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.PPM.Services
{
    /// <summary>
    /// ESB推送数据实现服务示例；
    /// ServiceName需要和ESB接口中的的MessageID一致；
    /// 通过在Execute方法中，根据ESB推送的数据，执行相应的业务逻辑；将数据保存到数据库中；
    /// 处理完成后，返回处理结果；并将ID清单返回给ESB。
    /// </summary>
    public class DataFromEsbServices : BaseServices<MaterialEntity>, ILkkEsbService
    {
        private readonly IBaseRepository<MaterialEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly string _serviceName = "SAP_MATERIALGET1";

        public DataFromEsbServices(IBaseRepository<MaterialEntity> dal, IMapper mapper, IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
            _serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_MATERIALGET1" : serviceName;
        }

        /// <summary>
        /// 接口名称，和ESB的MessageID一致
        /// </summary>
        public string ServiceName
        {
            get => _serviceName;
        }

        /// <summary>
        /// 执行接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResultModel> Execute(DataRequestModel request)
        {
            var result = new DataResultModel(request);
            try
            {
                var addList = new List<MaterialEntity>();
                var updateList = new List<MaterialEntity>();
                //MaterialEntity许愿替换成实体类
                var sourceDataList = JsonConvert.DeserializeObject<List<MaterialEntity>>(request.data.ToString());
                foreach (var sourceData in sourceDataList)
                {
                    var data = await _dal.FindEntity(t => t.Plant == sourceData.Plant && t.Code == sourceData.Code);
                    if (data == null)
                    {
                        data = new MaterialEntity();
                        _mapper.Map(sourceData, data);
                        data.CreateCustomGuid(ServiceName);
                        addList.Add(data);
                    }
                    else
                    {
                        _mapper.Map(sourceData, data);
                        data.Modify(data.ID, ServiceName);
                        updateList.Add(data);
                    }
                }

                _unitOfWork.BeginTran();

                if (addList.Count > 0)
                    await _dal.Add(addList);

                if (updateList.Count > 0)
                    await _dal.Update(updateList);

                _unitOfWork.CommitTran();

                result.ids = sourceDataList.Select(m => m.ID).ToList();
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                return result.Fail(ex.Message + ex.StackTrace);
            }
        }
    }
}
