using AutoMapper;
using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Services.Interface
{
    /// <summary>
    /// ESB推送数据实现服务示例；
    /// ServiceName需要和ESB接口中的的MessageID一致；
    /// 通过在Execute方法中，根据ESB推送的数据，执行相应的业务逻辑；将数据保存到数据库中；
    /// 处理完成后，返回处理结果；并将ID清单返回给ESB。
    /// </summary>
    public class SapSalesContainerGetService : BaseServices<SalescontainerEntity>, ILkkEsbService
    {
        private readonly IBaseRepository<SalescontainerEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly string _serviceName = "SAP_SALESCONTAINERGET";

        public SapSalesContainerGetService(IBaseRepository<SalescontainerEntity> dal, IMapper mapper, IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
            _serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_SALESCONTAINERGET" : serviceName;
        }

        /// <summary>
        /// 接口名称，和ESB的MessageID一致
        /// </summary>
        public string ServiceName
        {
            get => _serviceName;
        }

        /// <summary>
        /// 执行接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResultModel> Execute(DataRequestModel request)
        {
            var result = new DataResultModel(request);
            try
            {
                //MaterialEntity许愿替换成实体类
                var sourceDataList = JsonConvert.DeserializeObject<List<PP_SAP_SalesContainer>>(request.data.ToString());
                _unitOfWork.BeginTran();
                try
                {
                    foreach (var data in sourceDataList)
                    {
                        string container = data.MAGRV;
                        SalescontainerEntity obj = await _dal.FindEntity(p => p.SalesContainer == container);
                        if (obj == null)
                        {
                            obj = new SalescontainerEntity()
                            {
                                SalesContainer = data.MAGRV,
                                Description = data.BEZEI
                            };
                            obj.CreateCustomGuid("SAP_SALESCONTAINERGET");
                            await _dal.Add(obj);
                        }
                        else
                        {
                            obj.Description = data.BEZEI;
                            obj.Modify(obj.ID, "SAP_SALESCONTAINERGET");
                            await _dal.Update(obj);
                        }
                    }
                    _unitOfWork.CommitTran();
                }
                catch (Exception ex)
                {
                    result.msg = "操作失败";
                    _unitOfWork.RollbackTran();
                    return result.Fail(ex.Message + ex.StackTrace);
                }
                return result;
            }
            catch (Exception ex)
            {
                return result.Fail(ex.Message + ex.StackTrace);
            }
        }
    }
}
