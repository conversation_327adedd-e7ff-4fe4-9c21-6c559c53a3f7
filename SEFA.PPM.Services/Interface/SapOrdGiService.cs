using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using ORDGI2;
using SEFA.Base.Common;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.PPM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{

	public class SapOrdGiService : BaseServices<PoConsumeActualEntity>, ILkkEsbService
	{
		private readonly IBaseRepository<PoConsumeActualEntity> _dal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly string _serviceName = "ESB_ORDGI";

		public SapOrdGiService(IBaseRepository<PoConsumeActualEntity> dal,
			IUnitOfWork unitOfWork)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
			_serviceName = string.IsNullOrEmpty(serviceName) ? "ESB_ORDGI" : serviceName;
		}

		/// <summary>
		/// 接口名称，和ESB的MessageID一致
		/// </summary>
		public string ServiceName
		{
			get => _serviceName;
		}

		/// <summary>
		/// 执行接口
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public async Task<DataResultModel> Execute(DataRequestModel request)
		{
			SerilogServer.LogDebug($"Request:{FAJsonConvert.ToJson(request)}", "SapOrdGiServiceLog");
			var result = new DataResultModel(request)
			{
				ids = new List<string>()
			};
			var updateList = new List<PoConsumeActualEntity>();
			var poConsumeActuals = await _dal.FindList(x => x.SendExternal == 4 && x.TranNo == request.tranNo);
			if (poConsumeActuals == null || poConsumeActuals.Count == 0)
			{
				return result;
			}
			ZRFC_PP_MES_ORDGI2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDGI2Response>(request.data.ToString());
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (string.IsNullOrEmpty(request.data.ToString()) || reponse == null || reponse.IT_RET == null || reponse.IT_RET.Length == 0)
			{
				foreach (var item in poConsumeActuals)
				{
					item.SendExternal = 2;
					item.Msg = request.msg;
					item.Modify(item.ID, request.messageId);
					updateList.Add(item);
				}
			}
			else
			{
				var ids = poConsumeActuals.Where(x => !string.IsNullOrEmpty(x.Pid)).Select(x => x.Pid);
				var poConsumeActuals2 = await _dal.FindList(x => ids.Contains(x.ID));
				if (reponse.IT_GI.Length > 0)
				{
					//为空则为消耗，不为空则为反冲
					bool flag = string.IsNullOrEmpty(reponse.IT_GI[0].MBLNR_C);
					if (reponse.IT_RET.Length > 0)
					{
						var orderNos_F = reponse.IT_RET.ToList().Where(x => x.TYPE != "S").Select(x => x.AUFNR)?.Distinct()?.ToList() ?? new List<string>();
						var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => orderNos_F.Contains(x.ProductionOrderNo)).ToListAsync();
						foreach (var item in reponse.IT_RET)
						{
							//成功或者反冲走此逻辑
							if (item.TYPE == "S" || !flag)
							{
								var srnum = int.Parse(item.SRNUM);
								PoConsumeActualEntity poConsumeActual = null;
								//消耗
								if (flag)
								{
									poConsumeActual = poConsumeActuals.Find(x => x.Index == srnum);
								}
								//反冲
								else
								{
									//通过凭证号、行号找到消耗
									var poConsumeActuals_i = poConsumeActuals2.Find(x => x.Mblnr == item.MBLNR_C && x.Zeile == item.SRNUM);
									if (poConsumeActuals_i != null)
									{
										//通过消耗id和行号找到反冲
										poConsumeActual = poConsumeActuals.FirstOrDefault(x => x.Pid == poConsumeActuals_i.ID && x.Index == srnum);
									}
								}
								if (poConsumeActual != null)
								{
									poConsumeActual.Type = item.TYPE;
									poConsumeActual.Mjahr = item.MJAHR;
									poConsumeActual.Mblnr = item.MBLNR;
									poConsumeActual.Zeile = item.ZEILE;
									poConsumeActual.Msg = item.MSG;
									poConsumeActual.SendExternal = item.TYPE == "S" ? 1 : 2;
									poConsumeActual.Modify(poConsumeActual.ID, request.messageId);
									updateList.Add(poConsumeActual);
								}
							}
							//消耗失败走此逻辑
							else
							{
								var order = productionOrders.FirstOrDefault(x => x.ProductionOrderNo == item.AUFNR);
								if (order != null)
								{
									var poConsumeActuals_F = poConsumeActuals.Where(x => x.ProductionOrderId == order.ID).ToList();
									if (poConsumeActuals_F.Count > 0)
									{
										foreach (var poConsumeActual_i in poConsumeActuals_F)
										{
											poConsumeActual_i.Type = item.TYPE;
											poConsumeActual_i.Mjahr = item.MJAHR;
											poConsumeActual_i.Mblnr = item.MBLNR;
											poConsumeActual_i.Zeile = item.ZEILE;
											poConsumeActual_i.Msg = item.MSG;
											poConsumeActual_i.SendExternal = 2;
											poConsumeActual_i.Modify(poConsumeActual_i.ID, request.messageId);
											updateList.Add(poConsumeActual_i);
										}
									}
								}
							}
						}
					}
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateList.Count > 0)
				{
					if (updateList.Count > 1000)
					{
						await _dal.StorageBigData(updateList);
					}
					else
					{
						await _dal.Update(updateList);
					}
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.Fail(ex.Message + ex.StackTrace);
			}
			return result;

		}

		///// <summary>
		///// 执行接口
		///// </summary>
		///// <param name="request"></param>
		///// <returns></returns>
		//public async Task<DataResultModel> Execute(DataRequestModel request)
		//{
		//	SerilogServer.LogDebug($"Request:{FAJsonConvert.ToJson(request)}", "SapOrdGiServiceLog");
		//	var result = new DataResultModel(request)
		//	{
		//		ids = new List<string>()
		//	};
		//	var updateList = new List<PoConsumeActualEntity>();
		//	var poConsumeActuals = await _dal.FindList(x => x.SendExternal == 4 && x.TranNo == request.tranNo);
		//	if (poConsumeActuals == null || poConsumeActuals.Count == 0)
		//	{
		//		return result;
		//	}
		//	var ids = poConsumeActuals.Where(x => !string.IsNullOrEmpty(x.Pid)).Select(x => x.Pid);
		//	var poConsumeActuals2 = await _dal.FindList(x => ids.Contains(x.ID));
		//	var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDGI2Response>(request.data.ToString());
		//	if (reponse.IT_RET.Length > 1)
		//	{
		//		for (int i = 0; i < reponse.IT_RET.Length; i++)
		//		{
		//			var poConsumeActual = poConsumeActuals.Find(x => x.Zeile == reponse.IT_RET[i].SRNUM);
		//			if (!string.IsNullOrEmpty(reponse.IT_GI[i].MBLNR_C))
		//			{
		//				//通过凭证号、行号找到消耗
		//				var poConsumeActuals_i = poConsumeActuals2.Find(x => x.Mblnr == reponse.IT_RET[i].MBLNR_C && x.Zeile == reponse.IT_RET[i].SRNUM);
		//				if (poConsumeActuals_i != null)
		//				{
		//					//通过消耗id和行号找到反冲
		//					poConsumeActual = poConsumeActuals.FirstOrDefault(x => x.Pid == poConsumeActuals_i.ID && x.Zeile == reponse.IT_RET[i].SRNUM);
		//				}
		//			}
		//			if (poConsumeActual != null)
		//			{
		//				poConsumeActual.Type = reponse.IT_RET[i].TYPE;
		//				poConsumeActual.Mjahr = reponse.IT_RET[i].MJAHR;
		//				poConsumeActual.Mblnr = reponse.IT_RET[i].MBLNR;
		//				//poConsumeActual.Zeile = reponse.IT_RET[i].ZEILE;
		//				poConsumeActual.Msg = reponse.IT_RET[i].MSG;
		//				poConsumeActual.SendExternal = reponse.IT_RET[i].TYPE == "S" ? 1 : 2;
		//				poConsumeActual.Modify(poConsumeActual.ID, request.messageId);
		//				updateList.Add(poConsumeActual);
		//			}
		//		}
		//	}
		//	else
		//	{
		//		foreach (var poConsumeActual in poConsumeActuals)
		//		{
		//			poConsumeActual.Type = reponse.IT_RET[0].TYPE;
		//			poConsumeActual.Mjahr = reponse.IT_RET[0].MJAHR;
		//			poConsumeActual.Mblnr = reponse.IT_RET[0].MBLNR;
		//			//poConsumeActual.Zeile = reponse.IT_RET[0].ZEILE;
		//			poConsumeActual.Msg = reponse.IT_RET[0].MSG;
		//			poConsumeActual.SendExternal = reponse.IT_RET[0].TYPE == "S" ? 1 : 2;
		//			poConsumeActual.Modify(poConsumeActual.ID, request.messageId);
		//			updateList.Add(poConsumeActual);
		//		}
		//	}
		//	_unitOfWork.BeginTran();
		//	try
		//	{
		//		if (updateList.Count > 0)
		//		{
		//			if (updateList.Count > 1000)
		//			{
		//				await _dal.StorageBigData(updateList);
		//			}
		//			else
		//			{
		//				await _dal.Update(updateList);
		//			}
		//		}
		//		_unitOfWork.CommitTran();
		//	}
		//	catch (Exception ex)
		//	{
		//		_unitOfWork.RollbackTran();
		//		result.Fail(ex.Message + ex.StackTrace);
		//	}
		//	return result;

		//}
	}
}
