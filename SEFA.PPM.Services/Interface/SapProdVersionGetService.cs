using AutoMapper;
using SEFA.Base.Common;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Services.BASE;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SEFA.Base.Common.LogHelper;
using MongoDB.Bson;

namespace SEFA.PPM.Services.Interface
{
    public class SapProdVersionGetService : BaseServices<SapprodversionEntity>, ILkkEsbService
    {
        private readonly IBaseRepository<SapprodversionEntity> _dal;
        private readonly IBaseRepository<MaterialEntity> _materialDal;
        private readonly IBaseRepository<MaterialVersionEntity> _materialVersionDal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly string _serviceName = "SAP_PRODVERSIONGET";

        public SapProdVersionGetService(IBaseRepository<SapprodversionEntity> dal,
            IBaseRepository<MaterialEntity> ma,
            IBaseRepository<MaterialVersionEntity> mv,
            IMapper mapper, IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._materialDal = ma;
            this._materialVersionDal = mv;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
            _serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_PRODVERSIONGET" : serviceName;
        }

        /// <summary>
        /// 接口名称，和ESB的MessageID一致
        /// </summary>
        public string ServiceName
        {
            get => _serviceName;
        }

        /// <summary>
        /// 执行接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResultModel> Execute(DataRequestModel request)
        {
            var result = new DataResultModel(request);
            try
            {
                //MaterialEntity许愿替换成实体类
                var sourceDataList = JsonConvert.DeserializeObject<List<PP_SAP_ProdVersion>>(request.data.ToString());

                SerilogServer.LogDebug(sourceDataList.ToJson(), "SAP_PRODVERSIONGET接收数据");

                _unitOfWork.BeginTran();
                try
                {
                    List<SapprodversionEntity> addList = new List<SapprodversionEntity>();
                    List<SapprodversionEntity> delList = new List<SapprodversionEntity>();
                    List<SapprodversionEntity> updateList = new List<SapprodversionEntity>();
                    List<MaterialVersionEntity> addMvList = new List<MaterialVersionEntity>();
                    var existList = await _dal.FindList(p => true);
                    var matList = await _materialDal.FindList(p => true);
                    var mvList = await _materialVersionDal.FindList(p => true);

                    foreach (var data in sourceDataList)
                    {
                        var list = existList.Where(p => p.Matnr == data.MATNR && p.Werks == data.WERKS && p.Verid == data.VERID).ToList();
                        if (list.Count() > 1)
                        {
                            delList.AddRange(list);
                        }
                        SapprodversionEntity obj = existList.FirstOrDefault(p => p.Matnr == data.MATNR && p.Werks == data.WERKS && p.Verid == data.VERID);
                        if (obj == null)
                        {
                            obj = new SapprodversionEntity()
                            {
                                Matnr = data.MATNR,
                                Werks = data.WERKS,
                                Verid = data.VERID,
                                Adatu = data.ADATU,
                                Alnal = data.ALNAL,
                                Bdatu = data.BDATU,
                                Bstma = data.BSTMA,
                                Bstmi = data.BSTMI,
                                Plnnr = data.PLNNR,
                                Plnty = data.PLNTY,
                                Stlal = data.STLAL,
                                Stlan = data.STLAN,
                                Text1 = data.TEXT1,
                                Arbpl = data.ARBPL,
                                Ktext = data.KTEXT,
                                SapCreatedate = data.Createdate
                            };
                            obj.CreateCustomGuid("SAP_PRODVERSIONGET");
                            addList.Add(obj);
                            //await _dal.Add(obj);
                        }
                        else
                        {
                            obj.Adatu = data.ADATU;
                            obj.Alnal = data.ALNAL;
                            obj.Bdatu = data.BDATU;
                            obj.Bstma = data.BSTMA;
                            obj.Bstmi = data.BSTMI;
                            obj.Plnnr = data.PLNNR;
                            obj.Plnty = data.PLNTY;
                            obj.Stlal = data.STLAL;
                            obj.Stlan = data.STLAN;
                            obj.Text1 = data.TEXT1;
                            obj.Arbpl = data.ARBPL;
                            obj.Ktext = data.KTEXT;
                            obj.SapCreatedate = data.Createdate;
                            obj.Modify(obj.ID, "SAP_PRODVERSIONGET");
                            updateList.Add(obj);
                           // await _dal.Update(obj);
                        }

                        var mtrInfo = matList.FirstOrDefault(p => p.Code == data.MATNR);
                        if (mtrInfo != null)
                        {
                            var mvinfo = mvList.FirstOrDefault(p => p.MaterialId == mtrInfo.ID && p.MaterialVersionNumber == data.VERID);
                            if (mvinfo == null)
                            {
                                mvinfo = new MaterialVersionEntity
                                {
                                    MaterialId = mtrInfo.ID,
                                    MaterialVersionNumber = data.VERID,
                                    Plantcode = data.WERKS,
                                    Remark = data.TEXT1
                                };
                                mvinfo.CreateCustomGuid("SAP_PRODVERSIONGET");
                                addMvList.Add(mvinfo);
                                //await _materialVersionDal.Add(mvinfo);
                            }
                        }
                    }
                    if(delList.Count > 0)
                    {
                        var ids = delList.Select(p => p.ID).ToArray();
                        await _dal.DeleteByIds(ids);
                    }
                    if(addList.Count > 0)
                    {
                        await _dal.AddBigData(addList);
                    }
                    if(updateList.Count >0)
                    {
                        await _dal.StorageBigData(updateList);
                    }
                    if(addMvList.Count >0)
                    {
                        if (addMvList.Count > 1000)
                        {
                            await _materialVersionDal.AddBigData(addMvList);
                        }
                        else
                        {
                            await _materialVersionDal.Add(addMvList);
                        }
                    }
                    _unitOfWork.CommitTran();
                }
                catch (Exception ex)
                {
                    result.msg = "操作失败";
                    _unitOfWork.RollbackTran();
                    return result.Fail(ex.Message + ex.StackTrace);
                }
                result.msg = "Success";
                return result;
            }
            catch (Exception ex)
            {
                return result.Fail(ex.Message + ex.StackTrace);
            }
        }
    }
}
