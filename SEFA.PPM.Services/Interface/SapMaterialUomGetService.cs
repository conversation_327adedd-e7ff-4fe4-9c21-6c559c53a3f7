using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{

	public class SapMaterialUomGetService : BaseServices<UnitConvertEntity>, ILkkEsbService
	{
		private readonly IBaseRepository<UnitConvertEntity> _dal;
		private readonly IUnitOfWork _unitOfWork;
		//private readonly IUser _user;
		private readonly string _serviceName = "SAP_MATERIALUOMGET";

		public SapMaterialUomGetService(IBaseRepository<UnitConvertEntity> dal, IUnitOfWork unitOfWork, IUser user)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
			_serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_MATERIALUOMGET" : serviceName;
			//_user = user;
		}

		/// <summary>
		/// 接口名称，和ESB的MessageID一致
		/// </summary>
		public string ServiceName
		{
			get => _serviceName;
		}

		/// <summary>
		/// 执行接口
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public async Task<DataResultModel> Execute(DataRequestModel request)
		{
            SerilogServer.LogDebug($"开始处理", "SAP_MATERIALUOMGET");
            DateTime start = DateTime.Now;
			var result = new DataResultModel(request)
			{
				ids = new List<string>()
			};
			var updateList = new List<UnitConvertEntity>();
			var addList = new List<UnitConvertEntity>();
			var units = new List<UnitmanageEntity>();
			MessageModel<PageModel<UnitmanageEntity>> apiResult_units = await HttpHelper.PostAsync<PageModel<UnitmanageEntity>>("DFM", "api/Unitmanage/GetPageList", null, new { pageIndex = 1, pageSize = 999999 });
			if (apiResult_units.success == true && apiResult_units.response != null && apiResult_units.response.data != null)
			{
				//获取未删除以及启用的
				units = apiResult_units.response.data.FindAll(x => x.Deleted != 1 && x.Enable == 1);
			}

            SerilogServer.LogDebug($"获取DFM Unitmanage 数据完成,共[{units.Count}]条" , "SAP_MATERIALUOMGET");
			if(units.Count == 0)
			{
                units = _dal.Db.Queryable<UnitmanageEntity>().Where(x => x.Deleted != 1 && x.Enable == 1).ToList();
                SerilogServer.LogDebug($"第二次获取DFM Unitmanage 数据完成,共[{units.Count}]条", "SAP_MATERIALUOMGET");
            }
            var unitConverts = await _dal.Query();

            SerilogServer.LogDebug($"获取UnitConvert已存在数据完成,共[{unitConverts.Count}]条", "SAP_MATERIALUOMGET");

            var sourceDataList = JsonConvert.DeserializeObject<List<MM_Material_UOM>>(request.data.ToString());
			SerilogServer.LogDebug($"接收数据[{sourceDataList.Count}]", "SAP_MATERIALUOMGET");
			foreach (var item in sourceDataList)
			{
				var m1 = units?.FirstOrDefault(x => x.Name == item.MEINS);
				var m2 = units?.FirstOrDefault(x => x.Name == item.MEINH);
				if (m1 == null || m2 == null)
				{
                    SerilogServer.LogDebug($"未找到单位[{item.MEINS}][{item.MEINH}]", "SAP_MATERIALUOMGET");
                    result.msg = "未找到单位";
					return result;
				}
				var unitConvert = unitConverts.FindAll(x => x.MaterialCode == item.MATNR && x.FormUnitId == m1.ID && x.ToUnitId == m2.ID)?.FirstOrDefault();
				if (unitConvert == null)
				{
					unitConvert = new UnitConvertEntity()
					{
						MaterialCode = item.MATNR,
						FormUnitId = m1?.ID,
						FormUnitName = item.MEINS,
						ToUnitId = m2?.ID,
						ToUnitName = item.MEINH,
						ConvertFormQty = item.UMREZ,
						ConvertToQty = item.UMREN,
						EffectiveBeginDate = DateTime.MinValue,
						EffectiveEndDate = DateTime.MaxValue,
						Deleted = 0,
					};
					unitConvert.CreateCustomGuid(_serviceName);
					addList.Add(unitConvert);
				}
				else
				{
					unitConvert.MaterialCode = item.MATNR;
					unitConvert.FormUnitId = m1?.ID;
					unitConvert.FormUnitName = item.MEINS;
					unitConvert.ToUnitId = m2?.ID;
					unitConvert.ToUnitName = item.MEINH;
					unitConvert.ConvertFormQty = item.UMREZ;
					unitConvert.ConvertToQty = item.UMREN;
					//unitConvert.Deleted = 0;
					unitConvert.Modify(unitConvert.ID, _serviceName);
					updateList.Add(unitConvert);
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateList.Count > 0)
				{
					if (updateList.Count > 1000)
					{
						await _dal.StorageBigData(updateList);
					}
					else
					{
						await _dal.Update(updateList);
					}
				}
				if (addList.Count > 0)
				{
					if (addList.Count > 1000)
					{
						await _dal.AddBigData(addList);
					}
					else
					{
						await _dal.Add(addList);
					}
				}
				_unitOfWork.CommitTran();
				result.ids = sourceDataList.Select(m => m.ID.ToString()).ToList();
				var span = DateTime.Now - start;
                SerilogServer.LogDebug($"处理完成,耗时[{span.TotalSeconds}]", "SAP_MATERIALUOMGET");
            }
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.Fail(ex.Message + ex.StackTrace);
                SerilogServer.LogDebug($"处理异常,[{ex.ToString()}]", "SAP_MATERIALUOMGET");
            }
			return result;
		}
	}
}
