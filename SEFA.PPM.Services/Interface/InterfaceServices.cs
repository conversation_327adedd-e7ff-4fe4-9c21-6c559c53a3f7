
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.Base.Common.HttpContextUser;
using System;
using SEFA.PPM.Model.Models.Interface;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.MKM.Model.Models;
using MaterialGroupEntity = SEFA.DFM.Model.Models.MaterialGroupEntity;
using CategoryEntity = SEFA.DFM.Model.Models.CategoryEntity;
using UnitmanageEntity = SEFA.DFM.Model.Models.UnitmanageEntity;
using SapSegmentEntity = SEFA.DFM.Model.Models.SapSegmentEntity;
using MaterialGroupMappingEntity = SEFA.DFM.Model.Models.MaterialGroupMappingEntity;
using FunctionPropertyEntity = SEFA.DFM.Model.Models.FunctionPropertyEntity;
using FunctionPropertyValueEntity = SEFA.DFM.Model.Models.FunctionPropertyValueEntity;
using EquipmentEntity = SEFA.DFM.Model.Models.EquipmentEntity;
using LabelPrintHistoryEntity = SEFA.DFM.Model.Models.LabelPrintHistoryEntity;
using EquipmentRequirementEntity = SEFA.DFM.Model.Models.EquipmentRequirementEntity;
using SapSegmentMaterialEntity = SEFA.DFM.Model.Models.SapSegmentMaterialEntity;
using SapSegmentMaterialStepEntity = SEFA.DFM.Model.Models.SapSegmentMaterialStepEntity;
using DataItemEntity = SEFA.DFM.Model.Models.DataItemEntity;
using MaterialVersionEntity = SEFA.DFM.Model.Models.MaterialVersionEntity;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using ParameterGroupEntity = SEFA.DFM.Model.Models.ParameterGroupEntity;
using FunctionEntity = SEFA.DFM.Model.Models.FunctionEntity;
using SEFA.Base.Common.HttpRestSharp;
using System.Linq;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.Common;
using Result = SEFA.PPM.Model.Models.Interface.Result;
using Error = SEFA.PPM.Model.Models.Interface.Error;
using SEFA.MKM.IServices;
using SEFA.PPM.Model.Models.SIM;
using SEFA.Base.InfluxDb;
using MongoDB.Driver;
using SEFA.PPM.Model.Models.PTM;
using SEFA.Base;
using static SEFA.PPM.Services.TippingMlistViewServices;
using SEFA.Base.Common;
using SEFA.PPM.Model.ViewModels;
using System.Data;
using Newtonsoft.Json.Linq;
using SEFA.PTM.Model.Models;
using AutoMapper;
using ResultData = SEFA.PPM.Model.Models.Interface.ResultData;
using SEFA.PPM.IServices.PTM;
using Parameter = SEFA.PPM.Model.ViewModels.Parameter;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using MongoDB.Bson;
using SEFA.Base.Extensions;
using SEFA.Base.Common.LogHelper;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using static SEFA.PTM.Services.ConsumeViewServices;
using OfficeOpenXml.Style;
using System.Collections;
using AutoMapper.Internal;
using MongoDB.Driver.Core.Clusters;
using Remotion.Linq.Clauses;
using System.Reflection.Metadata;
using System.Xml.Linq;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.DFM.Model.Models;
using UnitConvertEntity = SEFA.DFM.Model.Models.UnitConvertEntity;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using ActionPropertyEntity = SEFA.PPM.Model.Models.PTM.ActionPropertyEntity;
using EquipmentStorageEntity = SEFA.DFM.Model.Models.EquipmentStorageEntity;
using System.Reactive;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Database;
using SEFA.DFM.Model.Models.Models.ViewModel;

namespace SEFA.PPM.Services
{
	public class InterfaceServices : BaseServices<UnitConvertEntity>, IInterfaceServices
	{
		private readonly IBaseRepository<UnitConvertEntity> _dal;
		private readonly IBaseRepository<MaterialEntity> _dal2;
		private readonly IBaseRepository<MaterialGroupEntity> _dal4;
		private readonly IBaseRepository<ProductionOrderEntity> _dal5;
		private readonly IBaseRepository<BatchEntity> _dal6;
		private readonly IBaseRepository<PoSegmentRequirementEntity> _dal7;
		private readonly IBaseRepository<SapSegmentEntity> _dal8;
		private readonly IBaseRepository<SappackorderEntity> _dal9;
		private readonly IBaseRepository<ProductionOrderPropertyEntity> _dal10;
		private readonly IBaseRepository<PoConsumeMaterialListViewEntity> _dal11;
		private readonly IBaseRepository<FunctionPropertyEntity> _dal12;
		private readonly IBaseRepository<FunctionPropertyValueEntity> _dal13;
		private readonly IBaseRepository<PoProducedExecutionEntity> _dal14;
		private readonly IBaseRepository<EquipmentEntity> _dal15;
		private readonly IBaseRepository<MaterialSubLotEntity> _dal16;
		private readonly IBaseRepository<PoProducedActualEntity> _dal17;
		private readonly IBaseRepository<LabelPrintHistoryEntity> _dal18;
		private readonly IBaseRepository<MaterialInventoryEntity> _dal19;
		private readonly IBaseRepository<ProduceLocationViewEntity> _dal20;
		private readonly IBaseRepository<InventorylistingViewEntity> _dal21;
		private readonly IBaseRepository<EnergyBydayEntity> _dal22;
		private readonly IBaseRepository<EnergyByorderEntity> _dal23;
		private readonly IBaseRepository<SapSegmentMaterialEntity> _dal24;
		private readonly IBaseRepository<SapSegmentMaterialStepEntity> _dal25;
		private readonly IBaseRepository<PoConsumeRequirementEntity> _dal26;
		private readonly IBaseRepository<PoProducedRequirementEntity> _dal27;
		private readonly IBaseRepository<PoConsumeActualEntity> _dal28;
		private readonly IBaseRepository<ActionPropertyEntity> _dal29;
		private readonly IBaseRepository<BatchComsumeMaterialListViewEntity> _dal30;
		private readonly IBaseRepository<EquipmentStorageEntity> _dal31;
		private readonly IBaseRepository<EquipmentRequirementEntity> _dal32;
		private readonly IBaseRepository<DataItemEntity> _dal33;
		private readonly IBaseRepository<SEFA.DFM.Model.Models.DataItemDetailEntity> _dal34;
		private readonly IBaseRepository<MaterialVersionEntity> _dal35;
		private readonly IBaseRepository<PoExecutionHistroyViewEntity> _dal36;
		private readonly IBaseRepository<ProcessOrderViewEntity> _dal37;
		private readonly IBaseRepository<MaterialLotEntity> _dal38;
		private readonly IBaseRepository<ParaExecutionLogEntity> _paraExecutionLog;
		private readonly IBaseRepository<ParameterGroupEntity> _parameterGroupdal;
		private readonly IBaseRepository<LogsheetEntity> _logsheetdal;
		private readonly IBaseRepository<LogsheetDetailEntity> _logsheetDetaildal;
		private readonly IBaseRepository<MaterialGroupMappingEntity> _materMappingdal;
		private readonly IBaseRepository<MMaterialPropertyViewEntity> _materPropertydal;
		private readonly IContainerServices _containerServices;
		private readonly IUnitOfWork _unitOfWork;
		private readonly IUser _user;
		private readonly LKKESBHelper _esbHelper;
		private readonly InfluxDbHelper _influxDbHelper;
		private readonly IInfluxDbServices _IInfluxDbServices;
		private readonly IMapper _mapper;
		private readonly IPerformanceServices _performanceServices;
		private readonly IRequestInventoryViewServices _requestInventoryViewServices;
		private readonly IBaseRepository<FunctionEntity> _dalfunction;
		private readonly IBaseRepository<SEFA.PPM.Model.Models.PTM.FunctionPropertyVEntity> _funpdal;
		private readonly ICookConfirmationServices _cookConfirmationServices;
		private readonly IRedisBasketRepository _redisBasketRepository;
		private readonly IBaseRepository<BWeightRecordsEntity> _bwrdal;
		private readonly IBaseRepository<WorkingHourEntity> _whdal;
		private readonly IBaseRepository<CipinfoEntity> _cipInfoDal;
		private readonly IBaseRepository<CipinfoDetailEntity> _cipInfoDetailDal;
		private readonly IBaseRepository<MaterialTransferEntity> _materialTransferDal;
		private readonly IAndonServices _andonServices;
		private readonly IBaseRepository<UnitmanageEntity> _unitDal;
		private readonly IInterfaceLogServices _interfaceLogServices;
		private readonly IMaterialInventoryServices _iMaterialInventoryServices;
		private readonly IBaseRepository<PoEquipmentEntity> _dalpoeq;
		private readonly string EMSTokenKey = "EMSToken";



		public InterfaceServices(
			IBaseRepository<UnitConvertEntity> dal,
			IBaseRepository<MaterialEntity> dal2,
			IBaseRepository<MaterialGroupEntity> dal4,
			IBaseRepository<ProductionOrderEntity> dal5,
			IBaseRepository<Model.Models.BatchEntity> dal6,
			IBaseRepository<PoSegmentRequirementEntity> dal7,
			IBaseRepository<SapSegmentEntity> dal8,
			IBaseRepository<SappackorderEntity> dal9,
			IBaseRepository<ProductionOrderPropertyEntity> dal10,
			IBaseRepository<PoConsumeMaterialListViewEntity> dal11,
			IBaseRepository<FunctionPropertyEntity> dal12,
			IBaseRepository<FunctionPropertyValueEntity> dal13,
			IBaseRepository<PoProducedExecutionEntity> dal14,
			IBaseRepository<EquipmentEntity> dal15,
			IUnitOfWork unitOfWork,
			IUser user,
			LKKESBHelper esbHelper,
			IBaseRepository<MaterialSubLotEntity> dal16,
			IBaseRepository<PoProducedActualEntity> dal17,
			IBaseRepository<LabelPrintHistoryEntity> dal18,
			IBaseRepository<MaterialInventoryEntity> dal19,
			IBaseRepository<ProduceLocationViewEntity> dal20,
			IBaseRepository<InventorylistingViewEntity> dal21,
			IContainerServices containerServices,
			IBaseRepository<EnergyByorderEntity> dal23,
			IBaseRepository<EnergyBydayEntity> dal22,
			IBaseRepository<SapSegmentMaterialEntity> dal24,
			IBaseRepository<SapSegmentMaterialStepEntity> dal25,
			IBaseRepository<PoConsumeRequirementEntity> dal26,
			IBaseRepository<PoProducedRequirementEntity> dal27,
			IBaseRepository<PoConsumeActualEntity> dal28,
			InfluxDbHelper influxDbHelper,
			IBaseRepository<ActionPropertyEntity> dal29,
			IBaseRepository<EquipmentStorageEntity> dal31,
			IBaseRepository<EquipmentRequirementEntity> dal32,
			IBaseRepository<DataItemEntity> dal33,
			IBaseRepository<SEFA.DFM.Model.Models.DataItemDetailEntity> dal34,
			IBaseRepository<MaterialVersionEntity> dal35,
			IBaseRepository<PoExecutionHistroyViewEntity> dal36,
			IBaseRepository<BatchComsumeMaterialListViewEntity> dal30,
			IInfluxDbServices iInfluxDbServices,
			IBaseRepository<ProcessOrderViewEntity> dal37,
			IBaseRepository<MaterialLotEntity> dal38,
			IBaseRepository<ParaExecutionLogEntity> paraExecutionLog,
			IMapper mapper,
			IBaseRepository<ParameterGroupEntity> parameterGroupdal,
			IBaseRepository<LogsheetDetailEntity> logsheetDetaildal,
			IBaseRepository<MaterialGroupMappingEntity> materMAappingdal,
			IPerformanceServices performanceServices,
			IRequestInventoryViewServices requestInventoryViewServices,
			IBaseRepository<FunctionEntity> dalfunction,
			IBaseRepository<MMaterialPropertyViewEntity> materPropertydal,
			IBaseRepository<Model.Models.PTM.FunctionPropertyVEntity> funpdal,
			ICookConfirmationServices cookConfirmationServices,
			IBaseRepository<LogsheetEntity> logsheetdal,
			IRedisBasketRepository redisBasketRepository,
			IBaseRepository<BWeightRecordsEntity> bwrdal,
			IBaseRepository<WorkingHourEntity> whdal,
			IBaseRepository<CipinfoEntity> cipInfoDal,
			IBaseRepository<CipinfoDetailEntity> cipInfoDetailDal,
			IBaseRepository<MaterialTransferEntity> materialTransferDal,
			IAndonServices andonServices,
			IBaseRepository<UnitmanageEntity> unitDal,
			IInterfaceLogServices interfaceLogServices,
			IMaterialInventoryServices iMaterialInventoryServices,
			IBaseRepository<PoEquipmentEntity> dalpoeq)
		{
			this._dal = dal;
			this._dal2 = dal2;
			this._dal4 = dal4;
			this._dal5 = dal5;
			this._dal6 = dal6;
			this._dal7 = dal7;
			this._dal8 = dal8;
			this._dal9 = dal9;
			this._dal10 = dal10;
			this._dal11 = dal11;
			this._dal12 = dal12;
			this._dal13 = dal13;
			this._dal14 = dal14;
			this._dal15 = dal15;
			base.BaseDal = dal;
			this._unitOfWork = unitOfWork;
			this._user = user;
			_esbHelper = esbHelper;
			_dal16 = dal16;
			_dal17 = dal17;
			_dal18 = dal18;
			_dal19 = dal19;
			_dal20 = dal20;
			_dal21 = dal21;
			_containerServices = containerServices;
			_dal23 = dal23;
			_dal22 = dal22;
			_dal24 = dal24;
			_dal25 = dal25;
			_dal26 = dal26;
			_dal27 = dal27;
			_dal28 = dal28;
			_influxDbHelper = influxDbHelper;
			_dal29 = dal29;
			_dal31 = dal31;
			_dal32 = dal32;
			_dal33 = dal33;
			_dal34 = dal34;
			_dal35 = dal35;
			_dal36 = dal36;
			_dal30 = dal30;
			_IInfluxDbServices = iInfluxDbServices;
			_dal37 = dal37;
			_dal38 = dal38;
			_paraExecutionLog = paraExecutionLog;
			_mapper = mapper;
			_parameterGroupdal = parameterGroupdal;
			_logsheetDetaildal = logsheetDetaildal;
			_materMappingdal = materMAappingdal;
			_performanceServices = performanceServices;
			_requestInventoryViewServices = requestInventoryViewServices;
			_dalfunction = dalfunction;
			_materPropertydal = materPropertydal;
			_funpdal = funpdal;
			_cookConfirmationServices = cookConfirmationServices;
			_logsheetdal = logsheetdal;
			_redisBasketRepository = redisBasketRepository;
			_bwrdal = bwrdal;
			_whdal = whdal;
			_cipInfoDal = cipInfoDal;
			_cipInfoDetailDal = cipInfoDetailDal;
			_materialTransferDal = materialTransferDal;
			_andonServices = andonServices;
			_unitDal = unitDal;
			_interfaceLogServices = interfaceLogServices;
			_iMaterialInventoryServices = iMaterialInventoryServices;
			_dalpoeq = dalpoeq;
		}

		#region SAP接口

		/// <summary>
		/// 从SAP同步物料基础数据
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<string>> SyncMaterialFromSap()
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			var updateList = new List<MaterialEntity>();
			var addList = new List<MaterialEntity>();
			var addList2 = new List<MaterialGroupEntity>();
			var addList3 = new List<MaterialGroupMappingEntity>();
			var sapResult = await _esbHelper.PostJson<ESBBaseModel<List<MM_Material_Masterdata>>, string>("SAP_MATERIALGET", "");
			var materialGroups = await _dal4.Query();
			var materMappings = await _materMappingdal.Query();
			//获取Category
			MessageModel<List<CategoryEntity>> apiResult_categories = await HttpHelper.PostAsync<List<CategoryEntity>>("DFM", "api/Category/GetList?Identities=MaterialType", _user.GetToken(), null);
			if (apiResult_categories.success != true)
			{
				result.msg = apiResult_categories.msg;
				return result;
			}
			var categories = apiResult_categories.response;
			var units = await GetUnits();
			var materials = await _dal2.Query();
			if (sapResult.successed == true)
			{
				if (sapResult.Response?.data != null)
				{
					foreach (var item in sapResult.Response.data)
					{
						//备件：MTART In('ZEP')
						//物料：MTART Not In('ZEP', 'ZNOV')
						//ZEP是备件，ZNOV是消耗品，两者不是就是物料（成品、半成品、生产物料等）。
						//这两种跳过，只保留物料
						if (item.mtart == "ZEP" || item.mtart == "ZNOV")
						{
							sapResult.Response.ids.Remove(item.id.ToString());
							continue;
						}
						var s = item.lgpro;
						var category = categories?.Find(x => x.Code == item.mtart);
						var unit = units.Find(x => x.Name == item.meins);
						var materialEntity = materials.Find(x => x.Plant == item.werks && x.Code == item.matnr);
						if (materialEntity == null)
						{
							materialEntity = new MaterialEntity()
							{
								ParentId = null,
								Type = category?.Name,
								CompanyId = null,
								Plant = item.werks,
								Code = item.matnr,
								NAME = item.maktx_chi,
								Description = item.maktx_chi,
								Version = null,
								Seriescode = null,
								Categorycode = null,
								Family = null,
								CustomerMaterialcode = null,
								CustomerEdition = null,
								HhMaterialCode = null,
								HhEdition = null,
								Manufactor = null,
								Spec = null,
								Unit = unit?.ID,
								CustomerCode = null,
								MainWhId = null,
								CategoryName = null,
								CategoryDescription = null,
								VendorPn = null,
								VendorCode = null,
								ProductType = null,
								SourceType = null,
								CtVersion = null,
								MaterialType = category?.ID,
								UnitOfMeasure = item.eisbe?.ToString(),
								ProductionGroup = null,
								Remarks = null,
								IsSpecial = null,
								SpecialType = null,
								Invalid = null,
								Deleted = 0,
								Accesstime = null,
								Kschl1 = item.KSCHL_1_M,
								Kschl2M = item.KSCHL_2_M,
								Kschl1E = item.KSCHL_1_E,
								Kschl2E = item.KSCHL_2_E,
								MatklEn = item.matkl_en,
								MatklZh = item.matkl_zh,
							};
							materialEntity.CreateCustomGuid("SAP_MATERIALGET");
							addList.Add(materialEntity);
						}
						else
						{
							//materialEntity.ParentId = null;
							materialEntity.Type = category?.Name;
							//materialEntity.CompanyId = null;
							materialEntity.Plant = item.werks;
							materialEntity.Code = item.matnr;
							materialEntity.NAME = item.maktx_chi;
							materialEntity.Description = item.maktx_chi;
							//materialEntity.Version = null;
							//materialEntity.Seriescode = null;
							//materialEntity.Categorycode = null;
							//materialEntity.Family = null;
							//materialEntity.CustomerMaterialcode = null;
							//materialEntity.CustomerEdition = null;
							//materialEntity.HhMaterialCode = null;
							//materialEntity.HhEdition = null;
							//materialEntity.Manufactor = null;
							//materialEntity.Spec = null;
							materialEntity.Unit = unit?.ID;
							//materialEntity.CustomerCode = null;
							//materialEntity.MainWhId = null;
							//materialEntity.CategoryName = null;
							//materialEntity.CategoryDescription = null;
							//materialEntity.VendorPn = null;
							//materialEntity.VendorCode = null;
							//materialEntity.ProductType = null;
							//materialEntity.SourceType = null;
							//materialEntity.CtVersion = null;
							materialEntity.MaterialType = category?.ID;
							materialEntity.UnitOfMeasure = item.eisbe?.ToString();
							//materialEntity.ProductionGroup = null;
							//materialEntity.Remarks = null;
							//materialEntity.IsSpecial = null;
							//materialEntity.SpecialType = null;
							//materialEntity.Invalid = null;
							materialEntity.Deleted = 0;
							//materialEntity.Accesstime = null;
							materialEntity.Kschl1 = item.KSCHL_1_M;
							materialEntity.Kschl2M = item.KSCHL_2_M;
							materialEntity.Kschl1E = item.KSCHL_1_E;
							materialEntity.Kschl2E = item.KSCHL_2_E;
							materialEntity.MatklEn = item.matkl_en;
							materialEntity.MatklZh = item.matkl_zh;
							materialEntity.Modify(materialEntity.ID, "SAP_MATERIALGET");
							updateList.Add(materialEntity);
						}
						//MessageModel<string> apiResult_savepde = await HttpHelper.PostAsync<string>("DFM", "api/Category/SaveForm", _user.GetToken(), pde);
						if (!string.IsNullOrEmpty(item.matkl_zh) && !addList2.Exists(x => x.MaterialGroupName == item.matkl_zh))
						{
							var materialGroup = materialGroups.Find(x => x.MaterialGroupName == item.matkl_zh);
							if (materialGroup == null)
							{
								materialGroup = new MaterialGroupEntity()
								{
									MaterialGroupName = item.matkl_zh,
									Status = "1",
									//Remark = "",
									Deleted = 0,
								};
								materialGroup.CreateCustomGuid("SAP_MATERIALGET");
								addList2.Add(materialGroup);
							}
							var materialGroupMapping = materMappings.Find(x => x.MaterialId == materialEntity.ID && x.MaterialGroupId == materialGroup.ID);
							if (materialGroupMapping == null)
							{
								materialGroupMapping = new MaterialGroupMappingEntity()
								{
									MaterialGroupId = materialGroup.ID,
									MaterialId = materialEntity.ID,
									Deleted = 0
								};
								materialGroupMapping.CreateCustomGuid("SAP_MATERIALGET");
								addList3.Add(materialGroupMapping);
							}
						}
					}
					_unitOfWork.BeginTran();
					try
					{
						if (updateList.Count > 0)
						{
							if (updateList.Count > 1000)
							{
								await _dal2.StorageBigData(updateList);
							}
							else
							{
								await _dal2.Update(updateList);
							}
						}
						if (addList.Count > 0)
						{
							if (addList.Count > 1000)
							{
								await _dal2.AddBigData(addList);
							}
							else
							{
								await _dal2.Add(addList);
							}
						}
						if (addList2.Count > 0)
						{
							if (addList2.Count > 1000)
							{
								await _dal4.AddBigData(addList2);
							}
							else
							{
								await _dal4.Add(addList2);
							}
						}
						if (addList3.Count > 0)
						{
							if (addList3.Count > 1000)
							{
								await _materMappingdal.AddBigData(addList3);
							}
							else
							{
								await _materMappingdal.Add(addList3);
							}
						}
						_unitOfWork.CommitTran();
						//更新数据同步状态
						var r = await _esbHelper.PostJson<string, FlagUpdateModel>("SAP_FlagUpdate", new FlagUpdateModel(sapResult.Response.dataType, sapResult.Response.ids));
					}
					catch (Exception ex)
					{
						result.msg = ex.Message;
						_unitOfWork.RollbackTran();
						return result;
					}
				}
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 从SAP同步物料单位转换
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<string>> SyncUnitConvertFromSap()
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			var updateList = new List<UnitConvertEntity>();
			var addList = new List<UnitConvertEntity>();
			var sapResult = await _esbHelper.PostJson<ESBBaseModel<List<MM_Material_UOM>>, string>("SyncUnitConvertFromSap", "");
			var units = await GetUnits();
			var unitConverts = await _dal.Query();
			if (sapResult.successed == true)
			{
				if (sapResult.Response?.data != null)
				{
					foreach (var item in sapResult.Response.data)
					{
						var m1 = units?.Find(x => x.Name == item.MEINS);
						var m2 = units?.Find(x => x.Name == item.MEINH);
						if (m1 == null || m2 == null)
						{
							result.msg = "未找到单位";
							return result;
						}
						var unitConvert = unitConverts.FindAll(x => x.MaterialCode == item.MATNR && x.FormUnitId == m1.ID && x.ToUnitId == m2.ID)?.FirstOrDefault();
						if (unitConvert == null)
						{
							unitConvert = new UnitConvertEntity()
							{
								MaterialCode = item.MATNR,
								FormUnitId = m1?.ID,
								FormUnitName = item.MEINS,
								ToUnitId = m2?.ID,
								ToUnitName = item.MEINH,
								ConvertFormQty = item.UMREZ,
								ConvertToQty = item.UMREN,
								Deleted = 0,
							};
							unitConvert.CreateCustomGuid("SAP_MATERIALUOMGET");
							addList.Add(unitConvert);
						}
						else
						{
							unitConvert.MaterialCode = item.MATNR;
							unitConvert.FormUnitId = m1?.ID;
							unitConvert.FormUnitName = item.MEINS;
							unitConvert.ToUnitId = m2?.ID;
							unitConvert.ToUnitName = item.MEINH;
							unitConvert.ConvertFormQty = item.UMREZ;
							unitConvert.ConvertToQty = item.UMREN;
							//unitConvert.Deleted = 0;
							unitConvert.Modify(unitConvert.ID, "SAP_MATERIALUOMGET");
							updateList.Add(unitConvert);
						}
					}
					_unitOfWork.BeginTran();
					try
					{
						if (updateList.Count > 0)
						{
							if (updateList.Count > 1000)
							{
								await _dal.StorageBigData(updateList);
							}
							else
							{
								await _dal.Update(updateList);
							}
						}
						if (addList.Count > 0)
						{
							if (addList.Count > 1000)
							{
								await _dal.AddBigData(addList);
							}
							else
							{
								await _dal.Add(addList);
							}
						}
						_unitOfWork.CommitTran();
						//更新数据同步状态
						var r = await _esbHelper.PostJson<string, FlagUpdateModel>("SAP_FlagUpdate", new FlagUpdateModel(sapResult.Response.dataType, sapResult.Response.ids));
					}
					catch (Exception ex)
					{
						_unitOfWork.RollbackTran();
						result.msg = ex.Message;
						return result;
					}
				}
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		#endregion

		#region 输剁系统接口

		/// <summary>
		/// 工单需求信息同步
		/// </summary>
		/// <param name="poProducedExecutionId"></param>
		/// <param name="actionType"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SendOrderInfoToSS(string productionOrderId, int actionType, string lotCode = "", decimal quantity = 0)
		{
			SerilogServer.LogDebug($"【工单需求信息同步】接口被调用,request:productionOrderId:{productionOrderId},actionType:{actionType},lotCode:{lotCode},quantity:{quantity}", "SSInterfaceLog");
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			var productionOrder = await _dal5.FindEntity(productionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到productionOrder";
				SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}
			var equipment = await _dal15.FindEntity(x => x.EquipmentCode == productionOrder.SegmentCode);
			if (equipment == null)
			{
				result.msg = "未找到equipment";
				SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}
			var v = (await GetFunctionPropertyValue(equipment.ID, "Common", "SendWCS")).response;
			if (v != "1")
			{
				result.msg = "属性SendWCS值为0，无需发送输垛系统";
				result.success = true;
				SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}
			else
			{
				var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
				var productionOrderProperty = await _dal10.FindEntity(x => x.OrderId == productionOrderId);
				var materials_blp = await GetBLPMaterials(productionOrderId);
				if (materials_blp.Count > 0)
				{
					if (materials_blp.Count > 1)
					{
						result.msg = "该工单下找到多个玻璃瓶类型的物料，请查验数据";
						SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
						return result;
					}
					foreach (var item in materials_blp)
					{
						try
						{
							if (actionType == 0)
							{
								quantity = item.AdjustPercentQuantity.Value;
							}
							PackingSizeSend model = new PackingSizeSend();
							model.itemcode = item.MaterialCode;

							//调用查询规格接口
							var pR = await _requestInventoryViewServices.GePacktResult(model);
							if (pR.successed == false)
							{
								result.msgDev = pR.msg;
								result.msg = "调用物料规格接口失败";
								SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
								return result;
							}

							//调用物料接口
							PackingSizeResult packR = pR.Response;
							if (packR.flag != true)
							{
								result.msgDev = packR.msg;
								result.msg = "调用物料规格接口失败";
								SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
								return result;
							}
							var packQty = decimal.Parse(packR.data?.FirstOrDefault()?.packQty ?? "0");
							SS_Order_Info order = new()
							{
								MesKey = Guid.NewGuid().ToString(),
								OrderDate = productionOrder.PlanStartTime.Value.ToString("yyyy-MM-dd"),
								OrderCode = productionOrder.ProductionOrderNo,
								WorkCenter = sapOrder.Arbpl,
								MaterialCode = item.MaterialCode,
								SalesContainer = sapOrder.Bezei,
								Quantity = (int)Math.Ceiling(quantity / packQty),
								//Unit = item.UnitName,
								Unit = "板",
								PackQty = (int)Math.Ceiling(quantity / packQty),
								LotCode = lotCode,
								ActionType = actionType,
							};
							LKKESBRequest lKKESBRequest = new LKKESBRequest
							{
								tranNo = order.MesKey,
								messageId = "SHD_OrderInfoSend",
								postData = order.ToJson(),
							};
							var ssResult = await _esbHelper.PostJson<SS_Response>(lKKESBRequest);
							SerilogServer.LogDebug($"【工单需求信息同步】SS_return:{FAJsonConvert.ToJson(ssResult)}", "SSInterfaceLog");
							if (ssResult.successed != true)
							{
								result.msg = ssResult.msg;
								return result;
							}
							if (ssResult.Response?.ReturnCode != "Y")
							{
								result.msg = ssResult?.Response?.ReturnMessage;
								return result;
							}
						}
						catch (Exception ex)
						{
							result.msg = ex.Message;
							SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
							return result;
						}
					}
				}
			}
			result.msg = "操作成功！";
			result.success = true;
			SerilogServer.LogDebug($"【工单需求信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
			return result;
		}

		/// <summary>
		/// 输垛系统-上料扫码信息同步
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<SS_Response> SS_ScanInfo(SS_Scan_Info reqModel)
		{
			SerilogServer.LogDebug($"【上料扫码信息同步】接口被调用,request:{FAJsonConvert.ToJson(reqModel)}", "SSInterfaceLog");
			var result = new SS_Response()
			{
				ReturnCode = "N",
				ReturnMessageID = "",
				ReturnMessage = "",
			};

			var equipment1 = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.Line);
			if (equipment1 == null)
			{
				result.ReturnMessage = "未找到equipment";
				SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}
			var v = (await GetFunctionPropertyValue(equipment1.ID, "Common", "WCSDestination")).response;
			var equipment = await _dal15.FindEntity(x => x.EquipmentCode == v);
			if (equipment == null)
			{
				result.ReturnMessage = $"未找到equipment";
				SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}

			var inventorylistingView = await _dal21.FindEntity(x => x.Sscc == reqModel.Sublot);
			if (inventorylistingView == null)
			{
				//result.ReturnMessage = "未找到TraceCode";
				//SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				//return result;
				//收货
				SugarPrePutModel suModel = new SugarPrePutModel();
				suModel.equipmentId = equipment.ID;
				suModel.sscc = reqModel.Sublot;
				suModel.name = equipment.EquipmentCode;
				suModel.comment = "WCS上料-收货";
				var resultPutMtr = await _iMaterialInventoryServices.SugarPrePut(suModel);
				if (resultPutMtr.success)
				{
					result.ReturnCode = "Y";
				}
				result.ReturnMessage = resultPutMtr.msg;
				return result;
			}
			if (inventorylistingView.EquipmentId == equipment.ID)
			{
				result.ReturnMessage = "目的地与源地点相同，无需转移";
				SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}



			//if (inventorylistingView.StatusF == "1" || inventorylistingView.StatusS == "1")
			//{
			//	result.msg = "库存状态为锁定，不能进行转移";
			//	return result;
			//}
			try
			{
				bool flag = await _containerServices.TransferContainer(new string[] { inventorylistingView.InventoryId }, equipment.ID, equipment.EquipmentName, "WCS上料-转移");
				if (!flag)
				{
					result.ReturnMessage = "库存转移失败！";
					SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
					return result;
				}
			}
			catch (Exception ex)
			{
				result.ReturnMessage = ex.Message;
				SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
				return result;
			}
			result.ReturnMessage = "操作成功！";
			result.ReturnCode = "Y";
			SerilogServer.LogDebug($"【上料扫码信息同步】return:{FAJsonConvert.ToJson(result)}", "SSInterfaceLog");
			return result;
		}

		#endregion

		#region 防伪系统接口

		/// <summary>
		/// 获取工单信息
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<FWS_WorkOrderInfo> GetWorkOrderInfo(FWS_WorkOrderInfo_Res reqModel)
		{
			List<ResultData> orders = new List<ResultData>();
			var propertyEntity = await _dal12.FindEntity(x => x.PropertyCode == "CounterfeitLineCode");
			if (propertyEntity == null)
			{
				return new FWS_WorkOrderInfo(null, false, "未找到产线代码属性");
			}
			var functionPropertyValue = await _dal13.FindEntity(x => x.PropertyId == propertyEntity.ID && x.PropertyValue == reqModel.veran);
			if (functionPropertyValue == null)
			{
				return new FWS_WorkOrderInfo(null, false, "未找到产线代码");
			}
			var equipment = await _dal15.FindEntity(functionPropertyValue.EquipmentId);
			if (equipment == null)
			{
				return new FWS_WorkOrderInfo(null, false, "未找到产线");
			}
			var units = await GetequipmentByLine(equipment.EquipmentCode);
			string deviceId = string.Empty;
			var equipmengIds = new List<string>();
			if (reqModel.type == "1" || reqModel.type == "2")
			{
				foreach (var item in units)
				{
					var v = (await GetFunctionPropertyValue(item.ID, "POManagement", "CounterfeitOrderType")).response;
					if (v == reqModel.type)
					{
						equipmengIds.Add(item.ID);
					}
				}
			}
			else
			{
				equipmengIds = units.Select(x => x.ID).ToList();
			}
			if (equipmengIds == null || equipmengIds.Count == 0)
			{
				return new FWS_WorkOrderInfo(null, false, "当前产线下未找到相关设备");
			}
			var orderEx = (await _dal14.FindList(x => equipmengIds.Contains(x.RunEquipmentId) && x.Status == "1")).OrderByDescending(x => x.StartTime).FirstOrDefault();
			if (orderEx != null)
			{
				var productionOrder = await _dal5.FindEntity(orderEx.ProductionOrderId);
				if (productionOrder == null)
				{
					return new FWS_WorkOrderInfo(null, false, "未找到生产工单");
				}
				var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
				if (sapOrder == null)
				{
					return new FWS_WorkOrderInfo(null, false, "未找到计划工单");
				}
				var result = new ResultData()
				{
					aufnr = sapOrder.Aufnr,
					stibez = sapOrder.Matnr,
					batch = sapOrder.BatchFw,
					gamng = productionOrder.PlanQty,
					gstrp = sapOrder.Gstrp.Value.ToString("yyyyMMdd"),
					shelf = sapOrder.ShelfFw,
					normt = sapOrder.Normt,
					veran = sapOrder.VeranFw,
					vtext = sapOrder.Vtext,
					lhmg1 = sapOrder.Lhmg1Fw,
					maktx = sapOrder.Maktx,
					txt04 = "IsInProgress"
				};
				return new FWS_WorkOrderInfo(result, true, "获取成功");
			}
			else
			{
				return new FWS_WorkOrderInfo(null, false, "当前产线未运行工单");
			}
		}

		#region 已删除接口

		/// <summary>
		/// 新托信息
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<FWS_NewStdPallet_Res> NewStdPallet(FWS_NewStdPallet reqModel)
		{


			return new FWS_NewStdPallet_Res(true, new Error(0, "操作成功"), null);
		}

		/// <summary>
		/// 结托信息
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<FWS_CompletedPallet_Res> CompletedPallet(FWS_CompletedPallet reqModel)
		{


			return new FWS_CompletedPallet_Res(true, new Error(0, "操作成功"));
		}

		/// <summary>
		/// 删除托
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<FWS_DeletePallet_Res> DeletePallet(FWS_DeletePallet reqModel)
		{


			return new FWS_DeletePallet_Res(true, new Error(0, "操作成功"));
		}

		#endregion

		#endregion

		#region COLOS系统接口

		/// <summary>
		/// 发送工单信息给COLOS接口
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SendWorkOrderInfoToColos(string equipmentId, string poId)
		{
			SerilogServer.LogDebug($"【发送工单信息给COLOS】request:equipmentId:{equipmentId},poId:{poId}", "COLOSInterfaceLog");
			var result = new MessageModel<string>()
			{
				success = false,
				msg = "操作失败"
			};
			//result.success = true;
			//result.msg = "先不调用";
			//return result;
			var v = (await GetFunctionPropertyValue(equipmentId, "POManagement", "SendColos")).response;
			if (v != "1")
			{
				result.msg = "无需发送给Colos";
				SerilogServer.LogDebug($"【发送工单信息给COLOS】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var productionOrder = await _dal5.FindEntity(poId);
			if (productionOrder == null)
			{
				result.msg = "未找到生产工单";
				SerilogServer.LogDebug($"【发送工单信息给COLOS】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			//var sapOrder = await _dal9.FindEntity(x => x.Aufnr == "020111412059");
			var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			if (sapOrder == null)
			{
				result.msg = "未找到计划工单";
				SerilogServer.LogDebug($"【发送工单信息给COLOS】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var colosTargetIP = (await GetFunctionPropertyValue(equipmentId, "POManagement", "ColosTargetIP"))?.response;
			//sendOrderToColos
			COLOS_WorkOrderInfo info = new()
			{
				WorkOrder = new WorkOrder()
				{
					WorkOrderFields = new WorkOrderFields()
					{
						WorkOrderNumber = sapOrder.Aufnr,
						WorkOrderType = sapOrder.Auart,
						ArticleNumber = sapOrder.Matnr,
					},
					OrderInfomations = new OrderInfomations()
					{
						id = sapOrder.ID,
						AUFNR = sapOrder.Aufnr,
						AUART = sapOrder.Auart,
						MATNR = sapOrder.Matnr,
						MAKTX = sapOrder.Maktx,
						PSMNG = sapOrder.Psmng,
						AMEIN = sapOrder.Amein,
						ARBPL = sapOrder.Arbpl,
						VERAN = sapOrder.Veran,
						GSTRP = sapOrder.Gstrp,
						MATNR2 = sapOrder.Matnr2,
						MAKTX2 = sapOrder.Maktx2,
						KDAUF = sapOrder.Kdauf,
						KDPOS = sapOrder.Kdpos,
						LGORT = sapOrder.Lgort,
						KUNNR1 = sapOrder.Kunnr1,
						KUNNR2 = sapOrder.Kunnr2,
						PLPER = sapOrder.Plper,
						PLODD = sapOrder.Plodd,
						PLQTY = sapOrder.Plqty,
						EBELN = sapOrder.Ebeln,
						EBELP = sapOrder.Ebelp,
						DISPO = sapOrder.Dispo,
						Status = "IsInProgress",//sapOrder.Status,
						WERKS2 = sapOrder.Werks2,
						AUART_FILL = sapOrder.AuartFill,
						Flag_wcs = "1",//sapOrder.FlagMes,
						Createdate = sapOrder.CreateDate,
						MNG_PU = sapOrder.MngPu,
						NORMT = sapOrder.Normt,
						NTGEW = sapOrder.Ntgew,
						LTEXT1 = sapOrder.Ltext1,
						LTEXT2 = sapOrder.Ltext2,
						LTEXT3 = sapOrder.Ltext3,
						LTEXT4 = sapOrder.Ltext4,
						LTEXT5 = sapOrder.Ltext5,
						LTEXT6 = sapOrder.Ltext6,
						WEMNG = sapOrder.Wemng,
						MAGRV = sapOrder.Magrv,
						BEZEI = sapOrder.Bezei,
						VHART = sapOrder.Vhart,
						VTEXT = sapOrder.Vtext,
						CATEGORY = sapOrder.Category,
						BATCH_FW = sapOrder.BatchFw,
						SHELF_FW = sapOrder.ShelfFw,
						VERAN_FW = sapOrder.VeranFw,
						MAKTX_C_FW = sapOrder.MaktxCFw,
						LHMG1_FW = sapOrder.Lhmg1Fw,
						MENGE_C_FW = sapOrder.MengeCFw,
						WERKS = sapOrder.Werks,
						MATKL = sapOrder.Matkl,
						MHDHB = sapOrder.Mhdhb,
						IPRKZ = sapOrder.Iprkz,
						FERTH = sapOrder.Ferth,
						KUNNR3 = sapOrder.Kunnr3,
						KUNNR4 = sapOrder.Kunnr4,
						TargetIP = colosTargetIP
					}
				}
			};
			try
			{
				var xml = InterfaceHelper.GetColosRequestXML<WorkOrder>(info.WorkOrder);
				SerilogServer.LogDebug($"【发送工单信息给COLOS】准备调用Colos接口,request:{xml}", "COLOSInterfaceLog");
				var data = await _esbHelper.PostXMLString("COL_P3OrderInfoSend", xml, null);
				SerilogServer.LogDebug($"【发送工单信息给COLOS】调用Colos接口完成,request:{data.Response}", "COLOSInterfaceLog");
				//data.Response = "<Result><ErrorCode>0</ErrorCode><ErrorMessage /><Data /></Result>";
				var response = InterfaceHelper.ParseFromColosResponseXml<Result>(data.Response);
				if (response.ErrorCode == 200)
				{
					result.success = true;
				}
				result.msg = response.ErrorMessage;
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
			}
			SerilogServer.LogDebug($"【发送工单信息给COLOS】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
			return result;
		}

		/// <summary>
		/// COLOS取得标签信息接口
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<COLOS_CreateLabel_Res> GetLabel(COLOS_CreateLabel reqModel)
		{
			SerilogServer.LogDebug($"【COLOS取得标签信息接口】request:{FAJsonConvert.ToJson(reqModel)}", "COLOSInterfaceLog");
			var propertyEntity = await _dal12.FindEntity(x => x.PropertyCode == "LineCode");
			if (propertyEntity == null)
			{
				SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到产线代码属性", "COLOSInterfaceLog");
				return new COLOS_CreateLabel_Res(null, 400, "未找到产线代码属性");
			}
			var functionPropertyValue = await _dal13.FindEntity(x => x.PropertyId == propertyEntity.ID && x.PropertyValue == reqModel.LineNo);
			if (functionPropertyValue == null)
			{
				SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到产线代码", "COLOSInterfaceLog");
				return new COLOS_CreateLabel_Res(null, 400, "未找到产线代码");
			}
			var equipment = await _dal15.FindEntity(functionPropertyValue.EquipmentId);
			if (equipment == null)
			{
				SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到产线", "COLOSInterfaceLog");
				return new COLOS_CreateLabel_Res(null, 400, "未找到产线");
			}
			var units = await GetequipmentByLine(equipment.EquipmentCode);
			string deviceId = string.Empty;
			foreach (var item in units)
			{
				var v = (await GetFunctionPropertyValue(item.ID, "POManagement", "SendColos")).response;
				if (v == "1")
				{
					deviceId = item.ID;
					break;
				}
			}
			var orderEx = (await _dal14.FindList(x => x.RunEquipmentId == deviceId && x.Status == "1")).OrderByDescending(x => x.CreateDate).FirstOrDefault();
			if (orderEx != null)
			{
				var batch = await _dal6.FindEntity(x => x.ID == orderEx.BatchId);
				if (batch == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到batch", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到batch");
				}
				var productionOrder = await _dal5.FindEntity(orderEx.ProductionOrderId);
				if (productionOrder == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到生产工单", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到生产工单");
				}
				var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
				if (sapOrder == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到计划工单", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到计划工单");
				}
				var unitManges = await GetUnits();
				#region 获取StorageBin，生成SubLot

				var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
				var equipmentStorages = api_equipmentStorages.response;
				if (equipmentStorages == null || equipmentStorages.Count == 0)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到EquipmentStorages", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到EquipmentStorages");
				}
				var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == orderEx.RunEquipmentId);
				if (equipmentStorage == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到EquipmentStorage", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到EquipmentStorage");
				}
				var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
				var equipmentRequirement = api_equipmentRequirement.response;
				if (equipmentRequirement == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到equipmentRequirement", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到equipmentRequirement");
				}
				var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
				var sapStorageTypes = api_sapStorageTypes.response;
				if (sapStorageTypes == null || sapStorageTypes.Count == 0)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到sapStorageTypes", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到sapStorageTypes");
				}
				var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
				if (sapStorageType == null)
				{
					SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:未找到sapStorageType", "COLOSInterfaceLog");
					return new COLOS_CreateLabel_Res(null, 400, "未找到sapStorageType");
				}
				//找到当前工单最大板号
				int board = (await _dal18.FindList(x => x.ProductionOrderId == productionOrder.ID && x.LotId == batch.BatchCode, x => x.Board, false)).FirstOrDefault()?.Board ?? 0;
				board++;
				string QrCode = string.Empty;
				QrCode = await GetQrCode(productionOrder.ProductionOrderNo, batch.BatchCode, sapOrder.Plqty, board);
				MaterialSubLotEntity materialsub = new();
				materialsub.CreateCustomGuid("Colos");
				materialsub.SubLotId = QrCode;
				materialsub.Type = "0";
				materialsub.ExternalStatus = "3";
				//}
				#endregion
				int quantity = 0;//通过数据采集采集数据
				var result = new COLOS_CreateLabel_ResultData()
				{
					OrderingPlant = reqModel.OrderingPlant,
					LineNo = reqModel.LineNo,
					PrdMaterialCode = sapOrder.Matnr,
					PrdMaterialName = sapOrder.Maktx,
					PrdLot = batch.BatchCode,// sapOrder.BatchFw,
					CartonNum = quantity,
					CartonUnit = sapOrder.Amein,
					PackSpecfilication = sapOrder.Bezei,
					SaleOrderNo = sapOrder.Kdauf,
					PurchaseOrderNo = sapOrder.Ebeln + "/" + sapOrder.Ebelp,
					Boards = board,
					WorkOrderNo = sapOrder.Aufnr,
					Storage = equipmentRequirement.Code,
					QrCode = QrCode,
				};
				//var location = (await _dal20.FindList(x => x.EquipmentId == deviceId))?.FirstOrDefault();
				var location = await GetProduceLocation(deviceId);
				var poProducedActualEntity = new PoProducedActualEntity
				{
					ProductExecutionId = orderEx.ID,
					ProductionOrderId = orderEx.ProductionOrderId,
					PoProducedRequirementId = orderEx.PoProducedRequirementId,
					LotId = orderEx.LotId,
					LotStatus = 2,
					SubLotId = materialsub.ID,
					SubLotStatus = 3,
					SourceType = 0,
					EquipmentId = deviceId,
					//DesinationEquipmentId = location.EquipmentId,
					DesinationEquipmentId = location?.ID ?? "",
					UnitId = unitManges?.Find(x => x.Name == sapOrder.Amein)?.ID,
					//ShiftId = "",
					Quantity = Convert.ToDecimal(quantity)
				};
				MaterialInventoryEntity materialInventoryEntity = null;
				if (equipmentRequirement?.ManageInventory == "1")
				{
					materialInventoryEntity = new MaterialInventoryEntity
					{
						LotId = orderEx.LotId,
						SublotId = materialsub.ID,
						Quantity = Math.Round(Convert.ToDecimal(quantity), 4), // Convert.ToDecimal(quantity),
						QuantityUomId = unitManges?.Find(x => x.Name == sapOrder.Amein)?.ID,
						StorageLocation = location.EquipmentCode,
						//EquipmentId = location.EquipmentId,
						//StorageLocation = location.LocationCode,
						EquipmentId = location?.ID ?? "",
						IsPrechecked = "FALSE",
						ContainerId = "",
						//ProductionRequestId = orderEx.ProductionOrderId,
						SortOrder = 0
					};
				}

				ProduceActual produceActual = new()
				{
					ColosData = result,
					ProduceData = poProducedActualEntity,
					InventoryData = materialInventoryEntity
				};

				//写入PrintHistory
				LabelPrintHistoryEntity labelPrintHistoryEntity = new LabelPrintHistoryEntity()
				{
					LotId = batch.BatchCode,
					SublotId = materialsub.ID,
					ProductionOrderId = productionOrder.ID,
					Board = board,
					Status = "0",
					Data = FAJsonConvert.ToJson(produceActual),
					LogType = "Colos",
					PrinterId = "Colos",
					TemplateId = "Colos",
				};

				labelPrintHistoryEntity.CreateCustomGuid("Colos");
				_unitOfWork.BeginTran();
				try
				{
					await _dal16.Add(materialsub);
					await _dal18.Add(labelPrintHistoryEntity);
					_unitOfWork.CommitTran();
				}
				catch (Exception ex)
				{
					_unitOfWork.RollbackTran();
					return new COLOS_CreateLabel_Res(null, 400, ex.Message);
				}
				SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:获取成功", "COLOSInterfaceLog");
				return new COLOS_CreateLabel_Res(result, 200, "获取成功");
			}
			else
			{
				SerilogServer.LogDebug($"【COLOS取得标签信息接口】return:当前产线码垛机未运行工单", "COLOSInterfaceLog");
				return new COLOS_CreateLabel_Res(null, 400, "当前产线码垛机未运行工单");
			}
		}

		/// <summary>
		/// COLOS推送标签状态
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<COLOS_LabelStatus_Res> UpdateLabelStatus(COLOS_LabelStatus reqModel)
		{
			SerilogServer.LogDebug($"【COLOS推送标签状态】request:{FAJsonConvert.ToJson(reqModel)}", "COLOSInterfaceLog");
			var sublot = await _dal16.FindEntity(x => x.SubLotId == reqModel.QrCode);
			if (sublot == null)
			{
				SerilogServer.LogDebug($"【COLOS推送标签状态】return:未找到QrCode", "COLOSInterfaceLog");
				return new COLOS_LabelStatus_Res(400, "未找到QrCode");
			}
			var labelPrintHistory = await _dal18.FindEntity(x => x.SublotId == sublot.ID);
			if (labelPrintHistory == null)
			{
				SerilogServer.LogDebug($"【COLOS推送标签状态】return:未找到labelPrintHistory", "COLOSInterfaceLog");
				return new COLOS_LabelStatus_Res(400, "未找到labelPrintHistory");
			}
			try
			{
				ProduceActual data = null;
				if (reqModel.LabelStatus == "printed")
				{
					labelPrintHistory.Status = "1";//修改状态为已打印
				}
				else if (reqModel.LabelStatus == "labeled")
				{
					labelPrintHistory.Status = "2";//修改状态为已贴标
				}
				else
				{
					labelPrintHistory.Status = "3";//修改状态为已下线
					labelPrintHistory.Modify(labelPrintHistory.ID, "Colos");
					data = FAJsonConvert.FormJson<ProduceActual>(labelPrintHistory.Data);
					data.ProduceData.CreateCustomGuid("Colos");
					var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = data.ProduceData.EquipmentId, finddate = data.ProduceData.CreateDate.ToString("yyyy-MM-dd HH:mm:ss") });
					var producedExecutionEntity = await _dal14.FindEntity(data.ProduceData.ProductExecutionId);
					var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= producedExecutionEntity.StartTime && Convert.ToDateTime(x.EndTime) >= producedExecutionEntity.StartTime);
					data.ProduceData.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
					data.InventoryData.CreateCustomGuid("Colos");
				}
				_unitOfWork.BeginTran();
				if (data?.ProduceData != null)
				{
					await _dal17.Add(data.ProduceData);
				}
				if (data?.InventoryData != null)
				{
					await _dal19.Add(data.InventoryData);
				}
				await _dal18.Update(labelPrintHistory);
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				SerilogServer.LogDebug($"【COLOS推送标签状态】return:{ex.Message}", "COLOSInterfaceLog");
				return new COLOS_LabelStatus_Res(400, ex.Message);
			}
			SerilogServer.LogDebug($"【COLOS推送标签状态】return:更新成功", "COLOSInterfaceLog");
			return new COLOS_LabelStatus_Res(200, "更新成功");
		}

		/// <summary>
		/// COL_打印标签接口
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> SendLabelPrintToColos(COLOS_LabelPrint_r r)
		{
			var result = new MessageModel<string>()
			{
				success = false,
				msg = "操作失败"
			};
			var productionOrder = await _dal5.FindEntity(r.poId);
			if (productionOrder == null)
			{
				result.msg = "未找到工单";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var equipment = await _dal15.FindEntity(x => x.EquipmentCode == productionOrder.LineCode);
			if (equipment == null)
			{
				result.msg = "未找到equipment";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var lineCode = string.Empty;

			MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys = await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM", "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _user.GetToken(), new { EquipmentId = equipment.ID });
			var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
			if (equFunctionPropertys?.Count > 0)
			{
				foreach (var item in equFunctionPropertys)
				{
					var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "LineCode");
					if (pr != null)
					{
						lineCode = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
					}
					if (!string.IsNullOrEmpty(lineCode))
					{
						break;
					}
				}
			}
			if (lineCode == null)
			{
				result.msg = "未找到LineCode";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			if (lineCode.Length < 2)
			{
				result.msg = "LineCode位数不足两位";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			if (sapOrder == null)
			{
				result.msg = "未找到计划工单";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var materialVersion = await _dal35.FindEntity(productionOrder.MaterialVersionId);
			if (materialVersion == null)
			{
				result.msg = "未找到materialVersion";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			var material = await _dal2.FindEntity(materialVersion.MaterialId);
			if (material == null)
			{
				result.msg = "未找到material";
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			//var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
			//var equipmentStorages = api_equipmentStorages.response;
			//if (equipmentStorages == null || equipmentStorages.Count == 0)
			//{
			//	result.msg = "未找到EquipmentStorages";
			//	return result;
			//}
			//var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == r.equipmentId);
			//if (equipmentStorage == null)
			//{
			//	result.msg = "未找到EquipmentStorage";
			//	return result;
			//}
			//var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
			//var equipmentRequirement = api_equipmentRequirement.response;
			//if (equipmentRequirement == null)
			//{
			//	result.msg = "未找到equipmentRequirement";
			//	return result;
			//}
			decimal quantity = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, "tag1"))?.Value);
			//找到当前工单最大板号
			int board = (await _dal18.FindList(x => x.ProductionOrderId == productionOrder.ID && x.LotId == r.batchCode, x => x.Board, false)).FirstOrDefault()?.Board ?? 0;
			List<MaterialSubLotEntity> materialSubLots = new List<MaterialSubLotEntity>();
			List<LabelPrintHistoryEntity> labelPrintHistories = new List<LabelPrintHistoryEntity>();
			var reqModel = new COLOS_LabelPrint()
			{
				OrderingPlant = "2010",
				LineNo = lineCode[1].ToString(),
				QRCodes = new List<COLOS_LabelPrintData>()
			};
			int i;
			for (i = board + 1; i <= board + r.count; i++)
			{
				var labelPrintData = new COLOS_LabelPrintData()
				{
					OrderingPlant = "2010",
					LineNo = lineCode[1].ToString(),
					PrdMaterialCode = sapOrder.Matnr,
					PrdMaterialName = sapOrder.Maktx,
					PrdLot = r.batchCode,//sapOrder.BatchFw,
					CartonNum = quantity,
					CartonUnit = sapOrder.Amein,
					PackSpecfilication = sapOrder.Bezei,
					SaleOrderNo = sapOrder.Kdauf,
					PurchaseOrderNo = sapOrder.Ebeln + "/" + sapOrder.Ebelp,
					Boards = i,
					WorkOrderNo = sapOrder.Aufnr,
					//Storage = equipmentRequirement.Code,
					Storage = material.SourceType,
					QrCode = await GetQrCode(productionOrder.ProductionOrderNo, r.batchCode, sapOrder.Plqty, i)
				};
				reqModel.QRCodes.Add(labelPrintData);
				MaterialSubLotEntity materialsub = new MaterialSubLotEntity();
				materialsub.SubLotId = labelPrintData.QrCode;
				materialsub.Type = "0";
				materialsub.ExternalStatus = "3";
				materialsub.CreateCustomGuid(_user.Name.ToString());
				materialSubLots.Add(materialsub);
				//写入PrintHistory
				LabelPrintHistoryEntity labelPrintHistoryEntity = new LabelPrintHistoryEntity()
				{
					LotId = sapOrder.BatchFw,
					SublotId = materialsub.ID,
					ProductionOrderId = productionOrder.ID,
					Board = i,
					Status = "0",
					Data = FAJsonConvert.ToJson(labelPrintData),
					LogType = "Colos",
					PrinterId = "Colos",
					TemplateId = "Colos",
				};
				labelPrintHistoryEntity.CreateCustomGuid(_user.Name.ToString());
				labelPrintHistories.Add(labelPrintHistoryEntity);
			}
			SerilogServer.LogDebug($"【COL_打印标签接口】准备调用接口,request:{FAJsonConvert.ToJson(reqModel)}", "COLOSInterfaceLog");
			var data = await _esbHelper.PostJson<COLOS_LabelPrint_Res, COLOS_LabelPrint>("***", reqModel);
			SerilogServer.LogDebug($"【COL_打印标签接口】调用接口完成,return:{FAJsonConvert.ToJson(data)}", "COLOSInterfaceLog");
			if (data == null || data.successed == false)
			{
				result.msg = data.msg;
				return result;
			};
			_unitOfWork.BeginTran();
			try
			{
				if (materialSubLots?.Count > 0)
				{
					await _dal16.Add(materialSubLots);
				}
				if (labelPrintHistories?.Count > 0)
				{
					await _dal18.Add(labelPrintHistories);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
				return result;
			}
			result.success = true;
			result.msg = "操作成功";
			SerilogServer.LogDebug($"【COL_打印标签接口】return:{FAJsonConvert.ToJson(result)}", "COLOSInterfaceLog");
			return result;
		}

		/// <summary>
		/// 获取当前编码
		/// </summary>
		/// <param name="productionOrderId"></param>
		/// <param name="batchCode"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetQrCode(string productionOrderId, string batchCode)
		{
			var result = new MessageModel<string>()
			{
				success = false,
				msg = "操作失败"
			};
			var productionOrder = await _dal5.FindEntity(productionOrderId);
			if (productionOrder == null)
			{
				result.msg = "未找到工单";
				return result;
			}
			int board = (await _dal18.FindList(x => x.ProductionOrderId == productionOrderId && x.LotId == batchCode, x => x.Board, false)).FirstOrDefault()?.Board ?? 0;
			board++;
			var sapOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			if (sapOrder == null)
			{
				result.msg = "未找到计划工单";
				return result;
			}
			result.response = await GetQrCode(productionOrder.ProductionOrderNo, batchCode, sapOrder.Plqty, board);
			result.success = true;
			result.msg = "操作成功";
			return result;
		}

		public class ProduceActual
		{
			public COLOS_CreateLabel_ResultData ColosData { get; set; }

			public PoProducedActualEntity ProduceData { get; set; }

			public MaterialInventoryEntity InventoryData { get; set; }
		}

		#endregion

		#region 看能系统接口

		/// <summary>
		/// 从看能系统获取Token
		/// </summary>
		/// <returns></returns>
		public async Task<string> GetTokenFromEMS(bool remove = false)
		{
			//if (remove)
			//{
			//	await _redisBasketRepository.Remove(EMSTokenKey);
			//}
			string token = "";
			if (await _redisBasketRepository.Exist(EMSTokenKey))
			{
				/*获取key值*/
				token = await _redisBasketRepository.GetValue(EMSTokenKey);
				return token;
			}
			else
			{
				string userName = "LKKXHP3";
				string password = "c70a35f62e602009624641703610e017";// "e10adc3949ba59abbe56e057f20f883e";
				try
				{
					userName = Appsettings.app("EMSAuth", "UserName");
					password = Appsettings.app("EMSAuth", "Password");
				}
				catch (Exception ex)
				{

				}
				var tokenResult = await _esbHelper.PostJson<ENERGY_Res, TokenReq>("EMS_GetAuthToken", new TokenReq() { username = userName, password = password });
				if (tokenResult != null && tokenResult.successed == true && tokenResult.Response != null && tokenResult.Response.data != null && tokenResult.Response.code == 200)
				{
					var data = FAJsonConvert.FormJson<TokenRes>(tokenResult.Response.data.ToString());
					if (data != null)
					{
						token = data.token;
						/*设置值，有效期t秒*/
						await _redisBasketRepository.Set(EMSTokenKey, token, new TimeSpan(0, 0, 0, int.Parse(data.t) / 1000 - 10));
					}
				}
				return token;
			}
		}

		/// <summary>
		/// 从看能系统获取仪表数据
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<List<ENERGY_InstrumentList>>> GetEnergyInstrumentList(ENERGY_InstrumentList_Req reqModel)
		{
			var result = new MessageModel<List<ENERGY_InstrumentList>>
			{
				msg = "操作失败！",
				success = false,
				response = new List<ENERGY_InstrumentList>()
			};
			if (string.IsNullOrEmpty(reqModel.token))
			{
				reqModel.token = await GetTokenFromEMS(true);
			}
			if (string.IsNullOrEmpty(reqModel.token))
			{
				result.msg = "获取Token异常！";
				return result;
			}
			//reqModel.ForeignId = "aff59874acf243dab99202aff68df0001";
			//reqModel.roleName = "能源管理员——包装三厂";
			//try
			//{
			//	reqModel.ForeignId = Appsettings.app("EMSAuth", "ForeignId");
			//	reqModel.roleName = Appsettings.app("EMSAuth", "RoleName");
			//}
			//catch (Exception ex)
			//{

			//}
			//var energyResult = await _esbHelper.PostJson<ENERGY_Res, ENERGY_InstrumentList_Req>("EMS_GetMeterList", reqModel);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				tranNo = Guid.NewGuid().ToString(),
				messageId = "EMS_GetMeterList",
				postData = reqModel.ToJson(),
			};
			var energyResult = await _esbHelper.PostJson<ENERGY_Res>(lKKESBRequest);
			if (energyResult.Response == null)
			{
				result.msgDev = energyResult.msg;
			}
			else
			{
				result.msgDev = energyResult.Response.msg;
			}
			if (energyResult?.successed == true && energyResult.Response?.code == 200 && energyResult.Response?.data != null)
			{
				try
				{
					//result.response = energyResult.Response;
					result.response = FAJsonConvert.FormJson<List<ENERGY_InstrumentList>>(energyResult.Response.data.ToString()) ?? new List<ENERGY_InstrumentList>();
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
					return result;
				}
				result.msg = "操作成功！";
				result.success = true;
			}
			return result;
		}

		/// <summary>
		/// 从看能系统获取仪表数据
		/// </summary>
		/// <returns></returns>
		public async Task<PageModel<ENERGY_InstrumentList>> GetEnergyInstrumentPageList(ENERGY_InstrumentList_ReqPage reqModel)
		{
			var list = new List<ENERGY_InstrumentList>();
			try
			{
				if (reqModel.req == null)
				{
					reqModel.req = new ENERGY_InstrumentList_Req();
				}
				list = (await GetEnergyInstrumentList(reqModel.req)).response;
			}
			catch (Exception ex)
			{
				list = new List<ENERGY_InstrumentList>();
			}
			return list.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.MeterCode, SEFA.Base.Common.Extensions.OrderType.Asc);
		}

		public string GetdataItemId(string meterType)
		{
			string dataItemId = "";
			switch (meterType)
			{
				case "水":
					dataItemId = "9";
					break;
				case "蒸汽":
					dataItemId = "35";
					break;
				case "电":
					dataItemId = "5";
					break;
				default:
					break;
			}
			return dataItemId;
		}

		public double CalFormulaContent(string content)
		{
			if (string.IsNullOrWhiteSpace(content))
				return (double)0.0;

			content = content.Replace("0 / 0", "0");

			double value = 0.0;
			try
			{
				value = Convert.ToDouble(new DataTable().Compute(content, ""));
			}
			catch
			{
				throw new Exception($"公式：{content} 错误！");
			}
			return value;
		}

		/// <summary>
		/// 从看能系统获取能耗数据测试
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<ENERGY_Data>> TestGetEnergyDataByDay(DateTime start, DateTime end)
		{
			var result = new MessageModel<ENERGY_Data>
			{
				msg = "操作失败！",
				success = false,
			};

			ENERGY_Data_Req reqModel = new()
			{
				//roleName = "",
				//ForeignId = "",
				StartDate = start.ToString("yyyy-MM-dd HH:mm:ss"),
				EndDate = end.ToString("yyyy-MM-dd HH:mm:ss"),
				DataList = new List<ENERGY_Data_IitemReq>(),
			};
			reqModel.token = await GetTokenFromEMS();
			if (string.IsNullOrEmpty(reqModel.token))
			{
				result.msg = "获取Token异常！";
				return result;
			}
			var messageModel = await GetEnergyInstrumentList(new ENERGY_InstrumentList_Req() { token = reqModel.token });
			if (messageModel?.response != null)
			{
				foreach (var item in messageModel.response)
				{
					reqModel.DataList.Add(new ENERGY_Data_IitemReq()
					{
						device_id = item.device_id,
						MeterTypeName = item.MeterTypeName,
						MeterType = item.MeterType,
						dataItemId = GetdataItemId(item.MeterType),
						MeterCode = item.MeterCode,
						MeterCode_Name = item.MeterCode_Name,
					});
				}
			}
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				tranNo = Guid.NewGuid().ToString(),
				messageId = "EMS_GetEnergyData",
				postData = reqModel.ToJson(),
			};
			var energyResult = await _esbHelper.PostJson<ENERGY_Res>(lKKESBRequest);
			if (energyResult?.successed == true && energyResult.Response?.code == 200)
			{
				var data = FAJsonConvert.FormJson<ENERGY_Data>(energyResult.Response.data?.ToString());
				result.response = data;
				result.success = true;
				result.msg = "操作成功！";
			}
			return result;
		}

		/// <summary>
		/// 从看能系统获取能耗数据(byday)
		/// </summary>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetEnergyDataByDay(string type)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			//var levelList = new List<string>() { "Plant", "Area", "Line" };
			//var equipments = await _dal15.FindList(x => levelList.Contains(x.Level));
			//var equipments = await _dal15.Query();
			var end = DateTime.Now.Date;
			var start = end.AddDays(-1);
			if (type == "ToDay")
			{
				end = DateTime.Now;
				start = end.Date;
			}
			ENERGY_Data_Req reqModel = new()
			{
				//roleName = "",
				//ForeignId = "",
				StartDate = start.ToString("yyyy-MM-dd HH:mm:ss"),
				EndDate = end.ToString("yyyy-MM-dd HH:mm:ss"),
				DataList = new List<ENERGY_Data_IitemReq>(),
			};
			reqModel.token = await GetTokenFromEMS();
			if (string.IsNullOrEmpty(reqModel.token))
			{
				result.msg = "获取Token异常！";
				return result;
			}
			//reqModel.ForeignId = "aff59874acf243dab99202aff68df0001";
			//reqModel.roleName = "能源管理员——包装三厂";
			//try
			//{
			//	reqModel.ForeignId = Appsettings.app("EMSAuth", "ForeignId");
			//	reqModel.roleName = Appsettings.app("EMSAuth", "RoleName");
			//}
			//catch (Exception ex)
			//{

			//}
			var energyMappings = await GetEnergyMappings();
			var energyMappings1 = energyMappings?.FindAll(x => x.SumType == "Day" && x.SourceType == "External");
			var energyMappings2 = energyMappings?.FindAll(x => x.SumType == "Day" && x.SourceType == "Formula");
			foreach (var meterCode in energyMappings1)
			{
				if (!reqModel.DataList.Exists(x => x.device_id == meterCode.Value))
				{
					reqModel.DataList.Add(new ENERGY_Data_IitemReq()
					{
						device_id = meterCode.Value,
						MeterTypeName = "",
						MeterType = meterCode.Categroy,
						dataItemId = GetdataItemId(meterCode.Categroy),
						MeterCode = meterCode.Code,
						MeterCode_Name = meterCode.Name,
					});
				}
			}
			var addList = new List<EnergyBydayEntity>();
			var updateList = new List<EnergyBydayEntity>();
			//var energyResult = await _esbHelper.PostJson<ENERGY_Res, ENERGY_Data_Req>("EMS_GetEnergyData", reqModel);
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				tranNo = Guid.NewGuid().ToString(),
				messageId = "EMS_GetEnergyData",
				postData = reqModel.ToJson(),
			};
			var energyResult = await _esbHelper.PostJson<ENERGY_Res>(lKKESBRequest);
			if (energyResult?.successed == true && energyResult.Response?.code == 200)
			{
				var data = FAJsonConvert.FormJson<ENERGY_Data>(energyResult.Response.data?.ToString());
				if (data != null && data.DataList?.Count > 0)
				{
					var units = await _dal.Db.Queryable<UnitmanageEntity>().ToListAsync();
					var energyBydays = await _dal22.FindList(x => x.Date == DateTime.Parse(reqModel.StartDate).Date);
					foreach (var item in data.DataList)
					{
						var energyMappings3 = energyMappings1.FindAll(x => x.Value == item.device_id);
						foreach (var m in energyMappings3)
						{
							var energyByday = energyBydays.Find(x => x.ModelRef == m.EquipmentId && x.MeterCode == m.Code && x.EnergyType == m.Categroy && x.IsDetailed == 1);
							if (energyByday == null)
							{
								var unit = units.Find(x => x.ID == m.UnitId);
								energyByday = new EnergyBydayEntity()
								{
									Date = DateTime.Parse(data.StartDate).Date,
									ModelRef = m.EquipmentId,
									MeterName = m.Name,
									MeterCode = m.Code,
									EnergyType = m.Categroy,
									EnergyQty = item.Qty,
									Unit = unit?.Name ?? "",
									DeviceId = m.ID,
									IsDetailed = 1
								};
								energyByday.CreateCustomGuid("EMS_GetEnergyData");
								addList.Add(energyByday);
							}
							else
							{
								energyByday.EnergyQty = item.Qty;
								energyByday.Modify(energyByday.ID, "EMS_GetEnergyData");
								updateList.Add(energyByday);
							}
						}
					}

					foreach (var m in energyMappings2)
					{
						if (string.IsNullOrEmpty(m.Value))
						{
							continue;
						}
						var content = string.Empty;
						var points = m.Value.Split(new char[] { '[', ']' }, StringSplitOptions.RemoveEmptyEntries);
						foreach (var point in points)
						{
							var p = energyMappings1.Find(x => /*x.EquipmentId == m.EquipmentId && x.Categroy == m.Categroy &&*/ x.Code == point);
							if (p != null)
							{
								var itemValue = data.DataList.Find(x => x.device_id == p.Value);
								if (itemValue != null)
								{
									content += itemValue.Qty;
								}
							}
							else
							{
								content += point;
							}
						}
						var r = CalFormulaContent(content).ToString();
						decimal.TryParse(r, out decimal dr);
						var energyByday = energyBydays.Find(x => x.ModelRef == m.EquipmentId && x.MeterCode == m.Code && x.EnergyType == m.Categroy && x.IsDetailed == 0);
						if (energyByday == null)
						{
							var unit = units.Find(x => x.ID == m.UnitId);
							energyByday = new EnergyBydayEntity()
							{
								Date = DateTime.Parse(data.StartDate).Date,
								ModelRef = m.EquipmentId,
								MeterName = m.Name,
								MeterCode = m.Code,
								EnergyType = m.Categroy,
								EnergyQty = dr,
								Unit = unit?.Name ?? "",
								DeviceId = m.ID,
								IsDetailed = 0
							};
							energyByday.CreateCustomGuid("EMS_GetEnergyData");
							addList.Add(energyByday);
						}
						else
						{
							energyByday.EnergyQty = dr;
							energyByday.Modify(energyByday.ID, "EMS_GetEnergyData");
							updateList.Add(energyByday);
						}
					}
					int successCount = 0;
					_unitOfWork.BeginTran();
					try
					{
						if (addList.Count > 1000)
						{
							successCount += await _dal22.AddBigData(addList);
						}
						else if (addList.Count > 0)
						{
							successCount += await _dal22.Add(addList);
						}
						if (updateList.Count > 1000)
						{
							await _dal22.StorageBigData(updateList);
						}
						else if (updateList.Count > 0)
						{
							await _dal22.Update(updateList);
						}
						_unitOfWork.CommitTran();
					}
					catch (Exception ex)
					{
						result.msg = ex.Message;
						_unitOfWork.RollbackTran();
						return result;
					}
					result.msg = "操作成功！";
					result.success = true;
				}
			}
			return result;
		}

		/// <summary>
		/// 从看能系统获取能耗数据(byOrder)
		/// </summary>
		/// <param name="executionEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetEnergyDataByOrder(PoProducedExecutionEntity executionEntity)
		{
			return await GetEnergyDataByOrderAndTime(executionEntity, TimePeriodSplitter.SplitTimePeriodsAcrossDays(executionEntity.StartTime.Value, executionEntity.EndTime.Value));
		}

		/// <summary>
		/// 从看能系统获取能耗数据
		/// </summary>
		/// <param name="executionEntity"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetEnergyDataByOrderAndTime(PoProducedExecutionEntity executionEntity, List<Tuple<DateTime, DateTime>> timeList)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			var addList = new List<EnergyByorderEntity>();
			var updateList = new List<EnergyByorderEntity>();
			var energyMappings = await GetEnergyMappings();
			var energyMappings1 = energyMappings?.FindAll(x => x.EquipmentId == executionEntity.RunEquipmentId && x.SumType == "ProductionOrder" && x.SourceType == "External");
			var energyMappings2 = energyMappings?.FindAll(x => x.EquipmentId == executionEntity.RunEquipmentId && x.SumType == "ProductionOrder" && x.SourceType == "Formula");
			if (energyMappings1 == null || energyMappings1.Count == 0)
			{
				result.msg = "当前设备未找到energyMappings";
				return result;
			}
			//string foreignId = string.Empty;
			//string roleName = string.Empty;
			//try
			//{
			//	foreignId = Appsettings.app("EMSAuth", "ForeignId");
			//	roleName = Appsettings.app("EMSAuth", "RoleName");
			//}
			//catch (Exception ex)
			//{

			//}
			//foreignId = !string.IsNullOrEmpty(foreignId) ? foreignId : "aff59874acf243dab99202aff68df0001";
			//roleName = !string.IsNullOrEmpty(roleName) ? roleName : "能源管理员——包装三厂";
			foreach (var period in timeList)
			{
				ENERGY_Data_Req reqModel = new()
				{
					//roleName = "",
					//ForeignId = "",
					StartDate = period.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
					EndDate = period.Item2.ToString("yyyy-MM-dd HH:mm:ss"),
					DataList = new List<ENERGY_Data_IitemReq>()
				};
				reqModel.token = await GetTokenFromEMS();
				if (string.IsNullOrEmpty(reqModel.token))
				{
					result.msg = "获取Token异常！";
					return result;
				}
				//reqModel.ForeignId = foreignId;
				//reqModel.roleName = roleName;
				foreach (var meterCode in energyMappings1)
				{
					if (!reqModel.DataList.Exists(x => x.device_id == meterCode.Value))
					{
						reqModel.DataList.Add(new ENERGY_Data_IitemReq()
						{
							device_id = meterCode.Value,
							MeterTypeName = "",
							MeterType = meterCode.Categroy,
							dataItemId = GetdataItemId(meterCode.Categroy),
							MeterCode = meterCode.Code,
							MeterCode_Name = meterCode.Name,
						});
					}
				}
				//var energyResult = await _esbHelper.PostJson<ENERGY_Res, ENERGY_Data_Req>("EMS_GetEnergyData", reqModel);
				LKKESBRequest lKKESBRequest = new LKKESBRequest
				{
					tranNo = Guid.NewGuid().ToString(),
					messageId = "EMS_GetEnergyData",
					postData = reqModel.ToJson(),
				};
				var energyResult = await _esbHelper.PostJson<ENERGY_Res>(lKKESBRequest);
				if (energyResult?.successed == true)
				{
					if (energyResult.Response?.code == 200)
					{
						var data = FAJsonConvert.FormJson<ENERGY_Data>(energyResult.Response.data?.ToString());
						if (data != null && data.DataList?.Count > 0)
						{
							var energyByOrders = await _dal23.FindList(x => x.ExecutionId == executionEntity.ID);
							var units = await _dal.Db.Queryable<UnitmanageEntity>().ToListAsync();
							foreach (var item in data.DataList)
							{
								var energyMappings3 = energyMappings1.FindAll(x => x.Value == item.device_id);
								foreach (var m in energyMappings3)
								{
									var energyByOrder = energyByOrders.Find(x => x.MeterCode == m.Code && x.EnergyType == m.Categroy && x.IsDetailed == 1);
									if (energyByOrder == null)
									{
										var unit = units.Find(x => x.ID == m.UnitId);
										energyByOrder = new EnergyByorderEntity()
										{
											ExecutionId = executionEntity.ID,
											MeterName = m.Name,
											MeterCode = m.Code,
											EnergyType = m.Categroy,
											EnergyQty = item.Qty,
											Unit = unit?.Name ?? "",
											DeviceId = m.ID,
											IsDetailed = 1,
										};
										energyByOrder.CreateCustomGuid("EMS_GetEnergyData");
										addList.Add(energyByOrder);
									}
									else
									{
										energyByOrder.EnergyQty = item.Qty;
										energyByOrder.Modify(energyByOrder.ID, "EMS_GetEnergyData");
										updateList.Add(energyByOrder);
									}
								}
							}

							foreach (var m in energyMappings2)
							{
								if (string.IsNullOrEmpty(m.Value))
								{
									continue;
								}
								var content = string.Empty;
								var points = m.Value.Split(new char[] { '[', ']' }, StringSplitOptions.RemoveEmptyEntries);
								foreach (var point in points)
								{
									var p = energyMappings1.Find(x => /*x.EquipmentId == m.EquipmentId && x.Categroy == m.Categroy &&*/ x.Code == point);
									if (p != null)
									{
										var itemValue = data.DataList.Find(x => x.device_id == p.Value);
										if (itemValue != null)
										{
											content += itemValue.Qty;
										}
									}
									else
									{
										content += point;
									}
								}
								var r = CalFormulaContent(content).ToString();
								decimal.TryParse(r, out decimal dr);
								var energyByOrder = energyByOrders.Find(x => x.MeterCode == m.Code && x.EnergyType == m.Categroy && x.IsDetailed == 0);
								if (energyByOrder == null)
								{
									var unit = units.Find(x => x.ID == m.UnitId);
									energyByOrder = new EnergyByorderEntity()
									{
										ExecutionId = executionEntity.ID,
										MeterName = m.Name,
										MeterCode = m.Code,
										EnergyType = m.Categroy,
										EnergyQty = dr,
										Unit = unit?.Name ?? "",
										DeviceId = m.ID,
										IsDetailed = 0,
									};
									energyByOrder.CreateCustomGuid("EMS_GetEnergyData");
									addList.Add(energyByOrder);
								}
								else
								{
									energyByOrder.EnergyQty = dr;
									energyByOrder.Modify(energyByOrder.ID, "EMS_GetEnergyData");
									updateList.Add(energyByOrder);
								}
							}
							int successCount = 0;
							_unitOfWork.BeginTran();
							try
							{
								if (addList.Count > 1000)
								{
									successCount = await _dal23.AddBigData(addList);
								}
								else if (addList.Count > 0)
								{
									successCount = await _dal23.Add(addList);
								}
								if (updateList.Count > 1000)
								{
									await _dal23.StorageBigData(updateList);
								}
								else if (updateList.Count > 0)
								{
									await _dal23.Update(updateList);
								}
								_unitOfWork.CommitTran();
							}
							catch (Exception ex)
							{
								result.msg = ex.Message;
								_unitOfWork.RollbackTran();
								return result;
							}
							result.msg = "操作成功！";
							result.success = true;
						}
					}
				}
			}
			return result;
		}

		public async Task<List<EnergyMappingEntity>> GetEnergyMappings()
		{
			return await _dal.Db.Queryable<EnergyMappingEntity>().ToListAsync();
			//MessageModel<List<EnergyMappingDataModel>> apiResult_energyMappings = await HttpHelper.PostAsync<List<EnergyMappingDataModel>>("DFM", "api/EnergyMapping/GetList", _user.GetToken(), new { });
			//return apiResult_energyMappings.response;
		}

		#endregion

		#region 

		public async Task<EquipmentEntity> GetProduceLocation(string equipmentId)
		{
			EquipmentEntity location = null;
			var produceNodeDefault = await _dal29.FindEntity(x => x.EquipmentId == equipmentId && x.ActionCode == "Production" && x.PropertyCode == "Is Default" && (x.PropertyValue == "1" || (string.IsNullOrEmpty(x.PropertyValue) && x.DefaultValue == "1")));
			if (produceNodeDefault != null)
			{
				var produceNode = await _dal29.FindEntity(x => x.EquipmentId == equipmentId && x.ActionCode == "Production" && x.PropertyCode == "Associated Node" && x.EquipmnetActionId == produceNodeDefault.EquipmnetActionId);
				if (produceNode != null)
				{
					var produceNode_equipmentCode = !string.IsNullOrEmpty(produceNode.PropertyValue) ? produceNode.PropertyValue : produceNode.DefaultValue;
					location = await _dal15.FindEntity(x => produceNode_equipmentCode == x.EquipmentCode);
				}
			}
			return location;
		}

		public async Task<ProduceLocationViewEntity> GetProduceLocation2(string equipmentId)
		{
			return (await _dal20.FindList(x => x.EquipmentId == equipmentId))?.FirstOrDefault();
		}


		/// <summary>
		/// 获取QrCode
		/// </summary>
		/// <returns></returns>
		private async Task<string> GetQrCode(string productionOrder, string batchCode, int quantity, int sort)
		{
			return await Task.Run(() =>
			  $"{productionOrder}{batchCode}{quantity.ToString().PadLeft(5, '0')}{sort.ToString().PadLeft(4, '0')}"
			);
		}

		/// <summary>
		/// 获取units
		/// </summary>
		/// <returns></returns>
		public async Task<List<UnitmanageEntity>> GetUnits()
		{
			var result = new List<UnitmanageEntity>();
			MessageModel<PageModel<UnitmanageEntity>> apiResult_units = await HttpHelper.PostAsync<PageModel<UnitmanageEntity>>("DFM", "api/Unitmanage/GetPageList", _user.GetToken(), new { pageIndex = 1, pageSize = 999999 });
			if (apiResult_units.success == true && apiResult_units.response != null && apiResult_units.response.data != null)
			{
				//获取未删除以及启用的
				result = apiResult_units.response.data.FindAll(x => x.Deleted != 1 && x.Enable == 1);
			}
			//var result = await _unitDal.FindList(x => x.Deleted != 1 && x.Enable == 1);

			SerilogServer.LogDebug($"获取DFM Unitmanage 数据完成,共[{result.Count}]条", "GetUnits");
			SerilogServer.LogDebug($"获取DFM Unitmanage Token=" + _user.GetToken(), "GetUnits");
			if (result.Count == 0)
			{
				result = _dal.Db.Queryable<UnitmanageEntity>().Where(x => x.Deleted != 1 && x.Enable == 1).ToList();
				SerilogServer.LogDebug($"第二次获取DFM Unitmanage 数据完成,共[{result.Count}]条", "GetUnits");
			}
			return result;
		}

		/// <summary>
		/// 获取物料所在的物料组
		/// </summary>
		/// <param name="materialId"></param>
		/// <returns></returns>
		public async Task<List<MaterialGroupEntity>> GetMaterialGroups(string materialId)
		{
			List<MaterialGroupEntity> result = new List<MaterialGroupEntity>();
			//物料组关联
			MessageModel<List<MaterialGroupMappingEntity>> apiResult_GroupMappings = await HttpHelper.PostAsync<List<MaterialGroupMappingEntity>>("DFM", "api/MaterialGroupMapping/GetList", _user.GetToken(), new { });
			var groupMappingIds = apiResult_GroupMappings.response?.FindAll(x => x.MaterialId == materialId).Select(x => x.MaterialGroupId);
			if (groupMappingIds?.Count() > 0)
			{
				//物料组
				MessageModel<List<MaterialGroupEntity>> apiResult_Groups = await HttpHelper.PostAsync<List<MaterialGroupEntity>>("DFM", "api/MaterialGroup/GetList", _user.GetToken(), new { });
				result = apiResult_Groups.response?.FindAll(x => groupMappingIds.Contains(x.ID));
			};
			return result;
		}

		/// <summary>
		/// 获取物料所在的物料组
		/// </summary>
		/// <param name="materialId"></param>
		/// <returns></returns>
		public async Task<List<PoConsumeMaterialListViewEntity>> GetBLPMaterials(string productionOrderId)
		{
			var result = new List<PoConsumeMaterialListViewEntity>();
			var poConsumeMaterials = await _dal11.FindList(x => x.ProductionOrderId == productionOrderId);
			if (poConsumeMaterials?.Count > 0)
			{
				//物料组
				MessageModel<List<MaterialGroupEntity>> apiResult_Groups = await HttpHelper.PostAsync<List<MaterialGroupEntity>>("DFM", "api/MaterialGroup/GetList", _user.GetToken(), new { MaterialGroupName = "玻璃瓶类" });
				var materialGroup_blp = apiResult_Groups.response?.FirstOrDefault();
				if (materialGroup_blp != null)
				{
					//物料组关联
					MessageModel<List<MaterialGroupMappingEntity>> apiResult_GroupMappings = await HttpHelper.PostAsync<List<MaterialGroupMappingEntity>>("DFM", "api/MaterialGroupMapping/GetList", _user.GetToken(), new { });
					var materialIds_blp = apiResult_GroupMappings.response?.FindAll(x => x.MaterialGroupId == materialGroup_blp.ID).Select(x => x.MaterialId);
					if (materialIds_blp?.Count() > 0)
					{
						result = poConsumeMaterials.FindAll(x => materialIds_blp.Contains(x.MaterialId));
					}
				}
			}
			return result;
		}

		/// <summary>
		/// 获取设备Function属性值
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> GetFunctionPropertyValue(string equipmentId, string functionCode, string propertyCode)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = false
			};
			var item2 = (await _funpdal.FindList(x => x.EquipmentId == equipmentId && x.FunctionCode == functionCode && x.PropertyCode == propertyCode)).FirstOrDefault();
			result.response = item2?.PropertyValue ?? item2?.DefaultValue;
			////获取Function
			//MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipmentId, _user.GetToken(), new { });
			//var equipmentAllData = apiResult_equipmentAllData.response;
			//if (equipmentAllData == null)
			//{
			//	result.msg = "equipmentAllData为空";
			//	return result;
			//}
			//var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == functionCode);
			//var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
			//result.response = item2?.ActualValue ?? item2?.DefaultValue;
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 获取单只平均重量
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <param name="PoExecutionId"></param>
		/// <param name="groupName"></param>
		/// <returns></returns>
		public async Task<MessageModel<decimal>> GetAverageWeightLogsheet(string equipmentId, string Id, string groupName)
		{
			var result = new MessageModel<decimal>
			{
				msg = "获取失败！",
				success = false
			};
			var bWeightRecords = await _bwrdal.FindList(x => x.BatchId == Id && x.AverageWeight > 0);
			if (bWeightRecords == null || bWeightRecords.Count == 0)
			{
				result.msg = "未找到BWeightRecord";
				return result;
			}
			var avg = bWeightRecords.Average(x => x.AverageWeight);
			result.success = true;
			result.msg = "获取成功！";
			//result.response = avg.AverageWeight;
			result.response = avg / 1000;
			return result;
			var parameterGroup = (await _parameterGroupdal.FindList(x => x.Code == groupName)).FirstOrDefault();
			if (parameterGroup == null)
			{
				result.msg = $"未找到此表单{groupName}！";
				return result;
			}
			var logsheet = (await _logsheetdal.FindList(x => x.ParameterGroupId == parameterGroup.ID && x.PoExecutionId == Id && x.EquipmentId == equipmentId && x.Status == 2, x => x.CreateDate, false)).FirstOrDefault();
			if (logsheet == null)
			{
				result.msg = $"未找到单只平均重量记录表！";
				return result;
			}
			var logsheetDetail = (await _logsheetDetaildal.FindList(x => x.LogsheetId == logsheet.ID/* && x.ParameterName == ""*/, x => x.CreateDate, false)).FirstOrDefault();
			if (logsheetDetail == null)
			{
				result.msg = $"未找到单只平均重量！";
				return result;
			}
			bool flag = decimal.TryParse(logsheetDetail.Value, out decimal v);
			if (!flag)
			{
				result.msg = $"数据转换异常！";
				return result;
			}
			result.response = v;
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 每次启动工单时获取下最新值更新到数据字典
		/// </summary>
		/// <param name="reportType"></param>
		/// <param name="equipmentCode"></param>
		/// <returns></returns>
		public async Task<MessageModel<string>> UpdateAutoReportDataItemData(string equipmentId, string reportType = "Consume")
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false
			};
			if (reportType != "Consume" && reportType != "Produce")
			{
				result.msg = "参数reportType配置错误，请配置为Consume或Produce！";
				return result;
			}
			var equipment = await _dal15.FindEntity(equipmentId);
			if (equipment == null)
			{
				result.msg = $"未找到equipmentId:{equipmentId}！";
				return result;
			}
			var dataItem = await _dal33.FindEntity(x => x.ItemCode == $"Auto{reportType}Time");
			if (dataItem == null)
			{
				result.msg = $"未找到Auto{reportType}Time,请检查数据字典配置！";
				return result;
			}
			var propertyResult = await GetFunctionPropertyValue(equipment.ID, reportType, $"{reportType}TagAddress");
			string tag = propertyResult?.response;
			if (string.IsNullOrEmpty(tag))
			{
				result.msg = $"未找到{reportType}TagAddress,请检查配置！";
				return result;
			}
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
			var dataItemDetail = dataItemDetailEntities?.Find(x => x.ItemName == equipment.EquipmentCode);
			var tags = tag.Split("|").ToList();
			var tags_qty = new List<decimal>();
			for (int i = 0; i < tags.Count; i++)
			{
				try
				{
					decimal currentValue = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, tags[i]))?.Value);
					tags_qty.Add(currentValue);
				}
				catch (Exception ex)
				{
					result.msg = $"计算tag：{tag}数据时出现异常：{ex.Message}";
					return result;
				}
			}
			if (dataItemDetail != null)
			{
				dataItemDetail.ItemValue = string.Join('|', tags_qty.Select(x => x));
				dataItemDetail.Modify(dataItemDetail.ID, "OPC");
				updateDataItemDetails.Add(dataItemDetail);
			}
			else
			{
				int maxSort = dataItemDetailEntities?.Select(x => x.SortCode)?.Max() ?? 0;
				SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
				{
					ItemId = dataItem.ID,
					ItemCode = dataItem.ItemCode,
					ItemName = equipment.EquipmentCode,
					ItemValue = string.Join('|', tags_qty.Select(x => x)),
					SortCode = ++maxSort,
					Enable = 1,
					Deleted = 0
				};
				dataItemDetailEntity.CreateCustomGuid("OPC");
				addDataItemDetails.Add(dataItemDetailEntity);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateDataItemDetails?.Count > 0)
				{
					await _dal34.Update(updateDataItemDetails);
				}
				if (addDataItemDetails?.Count > 0)
				{
					await _dal34.Add(addDataItemDetails);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<List<MMITrigerModel>> GetEquActionPropertyListByMMITriger(string equipmentId, string actionCode = "Consumption")
		{
			var result = new List<MMITrigerModel>();
			//获取EquipmentAction
			MessageModel<List<EquipmentActionModel>> apiResult_equipmentActionModels = await HttpHelper.PostAsync<List<EquipmentActionModel>>("DFM", "api/EquipmentAction/GetEquipmentAction", _user.GetToken(), new { EquipmentId = equipmentId });
			var equipmentActionModels = apiResult_equipmentActionModels.response;
			if (equipmentActionModels == null || equipmentActionModels.Count == 0)
			{
				//result.msg = "equipmentActionModels为空";
				return result;
			}
			var actions = equipmentActionModels.FindAll(x => x.ActionCode == actionCode);
			//获取Equipments
			MessageModel<List<DFM.Model.Models.EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentEntity>>("DFM", "api/Equipment/GetList", _user.GetToken(), new { });
			var equipments = apiResult_Equipments.response;
			if (equipments == null || equipments.Count == 0)
			{
				//result.msg = "equipments为空";
				return result;
			}
			foreach (var action in actions)
			{
				//获取EquipmentActionProperty
				MessageModel<List<EquipmentActionPropertyModel>> apiResult_equipmentActionProperties = await HttpHelper.PostAsync<List<EquipmentActionPropertyModel>>("DFM", "api/ActionPropertyValue/GetEquipmentActionProperty", _user.GetToken(), new { EquipmentId = equipmentId, EquipmentActionId = action.EquipmentActionId });
				var equipmentActionProperties = apiResult_equipmentActionProperties.response;
				if (equipmentActionProperties == null || equipmentActionProperties.Count == 0)
				{
					continue;
					//result.msg = "equipmentActionProperties为空";
					//return result;
				}
				var MMI_Triger = equipmentActionProperties.Find(x => x.PropertyCode == "MMI Triger");
				var v = !string.IsNullOrEmpty(MMI_Triger?.ActualValue) ? MMI_Triger?.ActualValue : MMI_Triger?.DefaultValue;
				if (v != "True")
				{
					continue;
				}
				var materialCode = equipmentActionProperties.Find(x => x.PropertyCode == "Material Code");
				var associatedNode = equipmentActionProperties.Find(x => x.PropertyCode == "Associated Node");
				if (associatedNode != null)
				{
					MMITrigerModel mMITrigerModel = new()
					{
						MaterialCode = !string.IsNullOrEmpty(materialCode?.ActualValue) ? materialCode?.ActualValue : materialCode?.DefaultValue,
						EquipmentCode = !string.IsNullOrEmpty(associatedNode?.ActualValue) ? associatedNode?.ActualValue : associatedNode?.DefaultValue,
					};
					mMITrigerModel.EquipmentId = equipments.Find(x => x.EquipmentCode == mMITrigerModel.EquipmentCode)?.ID;
					result.Add(mMITrigerModel);
				}
			}
			return result;
		}

		/// <summary>
		/// 获取产线下所有的设备
		/// </summary>
		/// <param name="allNodes"></param>
		/// <param name="pID"></param>
		/// <param name="level"></param>
		/// <returns></returns>
		public static List<EquipmentEntity> Getequipment(List<EquipmentEntity> allNodes, string pID, string level)
		{
			var resps = new List<EquipmentEntity>();
			List<EquipmentEntity> tempList = allNodes.Where(c => string.IsNullOrWhiteSpace(pID) ? string.IsNullOrWhiteSpace(c.ParentId) : c.ParentId == pID).ToList();
			for (int i = 0; i < tempList.Count; i++)
			{
				if (tempList[i].Level == level)
				{
					resps.Add(tempList[i]);
				}
				var list = Getequipment(allNodes, tempList[i].ID, level);
				if (list?.Count > 0)
				{
					resps.AddRange(list);
				}
			}
			return resps;
		}

		/// <summary>
		/// 获取产线下的设备
		/// </summary>
		/// <param name="lineCode"></param>
		/// <returns></returns>
		public async Task<List<EquipmentEntity>> GetequipmentByLine(string lineCode)
		{
			//获取Equipments
			MessageModel<List<DFM.Model.Models.EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentEntity>>("DFM", "api/Equipment/GetListByProductLineCode?key=" + lineCode, _user.GetToken(), new { });
			var equipments = apiResult_Equipments.response;
			return equipments ?? new List<EquipmentEntity>();
		}

		public async Task<MessageModel<string>> ApiByType(string type)
		{
			var result = new MessageModel<string>
			{
				msg = "操作失败！",
				success = false,
			};
			switch (type)
			{
				case "SAP_MATERIALGET":
					return await SyncMaterialFromSap();
				case "SAP_MATERIALUOMGET":
					return await SyncUnitConvertFromSap();
				default:
					break;
			}
			return result;
		}

		public class TimePeriodSplitter
		{
			public static List<Tuple<DateTime, DateTime>> SplitTimePeriodsAcrossDays(DateTime start, DateTime end)
			{
				var timePeriods = new List<Tuple<DateTime, DateTime>>();

				// 确保时间段不是反的
				if (start > end)
				{
					DateTime temp = start;
					start = end;
					end = temp;
				}

				// 获取时间段的总天数
				TimeSpan duration = end - start;
				int days = duration.Days + 1;

				// 迭代每一天，并将时间段拆分为不同的段
				for (int i = 0; i < days; i++)
				{
					DateTime dayStart = start.Date.AddDays(i);
					DateTime dayEnd = dayStart.AddDays(1).AddTicks(-1); // 当天的最后一刻

					// 如果当前天在时间段内，则添加到结果中
					if (dayStart < end)
					{
						DateTime segmentStart = dayStart > start ? dayStart : start;
						DateTime segmentEnd = dayEnd < end ? dayEnd : end;
						timePeriods.Add(Tuple.Create(segmentStart, segmentEnd));
					}
				}

				return timePeriods;
			}
		}

		#endregion

		#region 自动报工

		public async Task<MessageModel<AutoReportMessageModel>> AutoReport(string reportType, string equipmentCode, string tag)
		{
			var result = new MessageModel<AutoReportMessageModel>
			{
				msg = "操作失败！",
				success = false,
				response = new AutoReportMessageModel() { AutoReportMessages = new List<string>() }
			};
			if (reportType != "Consume" && reportType != "Produce")
			{
				result.msg = "参数reportType配置错误，请配置为Consume或Produce！";
				return result;
			}
			var equipment = (await _dal15.FindList(x => x.ID == equipmentCode || x.EquipmentCode == equipmentCode)).FirstOrDefault();
			if (equipment == null)
			{
				result.msg = "未找到Equipment,请检查配置！";
				return result;
			}
			equipmentCode = equipment.EquipmentCode;
			if (string.IsNullOrEmpty(tag))
			{
				var propertyResult = await GetFunctionPropertyValue(equipment.ID, reportType, $"{reportType}TagAddress");
				tag = propertyResult?.response;
			}
			if (string.IsNullOrEmpty(tag))
			{
				result.msg = $"未找到{reportType}TagAddress,请检查配置！";
				return result;
			}
			var dataItem = await _dal33.FindEntity(x => x.ItemCode == $"Auto{reportType}Time");
			if (dataItem == null)
			{
				result.msg = $"未找到Auto{reportType}Time,请检查数据字典配置！";
				return result;
			}
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
			var dataItemDetail = dataItemDetailEntities?.Find(x => x.ItemName == equipmentCode);
			var tags = tag.Split("|").ToList();
			var tags_qty = new List<dynamic>();
			var oldValues = dataItemDetail?.ItemValue?.Split("|")?.ToList() ?? new List<string>();
			for (int i = 0; i < tags.Count; i++)
			{
				try
				{
					decimal quantity = 0;
					decimal oldValue = 0;
					decimal currentValue = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, tags[i]))?.Value);
					if (oldValues.Count > i)
					{
						decimal.TryParse(oldValues[i], out oldValue);
					}
					else
					{
						oldValues.Add(currentValue.ToString());
					}
					if (oldValue <= currentValue)
					{
						quantity = currentValue - oldValue;
					}
					else
					{
						decimal InfluxdbMaxQuantity = Convert.ToDecimal((await _IInfluxDbServices.GetMaxInfluxData(dataItemDetail.ModifyDate.AddMinutes(-5), DateTime.Now, null, tag))?.Value);
						var q = InfluxdbMaxQuantity - oldValue;
						if (q < 0)
						{
							q = 0;
						}
						quantity = q + currentValue;
					}
					tags_qty.Add(new { tag = tags[i], oldValue = oldValue, currentValue = currentValue, quantity = quantity });
				}
				catch (Exception ex)
				{
					result.msg = $"计算tag：{tag}数据时出现异常：{ex.Message}";
					return result;
				}
			}
			if (dataItemDetail != null)
			{
				dataItemDetail.ItemValue = string.Join('|', tags_qty.Select(x => x.currentValue));
				dataItemDetail.Modify(dataItemDetail.ID, "OPC");
				updateDataItemDetails.Add(dataItemDetail);
			}
			else
			{
				int maxSort = dataItemDetailEntities?.Select(x => x.SortCode)?.Max() ?? 0;
				SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
				{
					ItemId = dataItem.ID,
					ItemCode = dataItem.ItemCode,
					ItemName = equipmentCode,
					ItemValue = string.Join('|', tags_qty.Select(x => x.currentValue)),
					SortCode = ++maxSort,
					Enable = 1,
					Deleted = 0
				};
				dataItemDetailEntity.CreateCustomGuid("OPC");
				addDataItemDetails.Add(dataItemDetailEntity);
			}
			result.response.Tag = string.Join('|', tags_qty.Select(x => x.tag));
			result.response.OldQuantity = string.Join('|', tags_qty.Select(x => x.oldValue));
			result.response.CurrentQuantity = string.Join('|', tags_qty.Select(x => x.currentValue));
			result.response.Quantity = tags_qty.Sum(x => (decimal)x.quantity);
			_unitOfWork.BeginTran();
			try
			{
				if (updateDataItemDetails?.Count > 0)
				{
					await _dal34.Update(updateDataItemDetails);
				}
				if (addDataItemDetails?.Count > 0)
				{
					await _dal34.Add(addDataItemDetails);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			if (result.response.Quantity <= 0)
			{
				result.msg = "本次执行对比上次增加数量为0";
				return result;
			}
			var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
			if (runOrder == null)
			{
				result.msg = $"当前设备{equipmentCode}未找到运行工单！";
				return result;
			}
			var productionOrder = await _dal5.FindEntity(x => x.ID == runOrder.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = $"未找到productionOrder！";
				return result;
			}
			SappackorderEntity sapPackOrder = null;
			if (productionOrder.SapOrderType != "ZXH2")
			{
				sapPackOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			}
			var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement！";
				return result;
			}
			var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment！";
				return result;
			}

			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
			var autoReportMessages = new List<AutoReportMessage>();
			List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
			List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
			List<string> deleteIds = new List<string>();
			List<PoProducedActualEntity> poProducedActualEntities = new List<PoProducedActualEntity>();
			List<MaterialSubLotEntity> materialSubLotEntities = new List<MaterialSubLotEntity>();
			switch (reportType)
			{
				case "Consume":
					{
						var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
						var equipmentCodes = list.Select(x =>
						{
							return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
						});
						if (equipmentCodes == null)
						{
							equipmentCodes = new List<string>();
						}
						var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
						var equipmentIds = equipments.Select(x => x.ID).ToList();
						var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId);

						//获取Function
						MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
						var equipmentAllData = apiResult_equipmentAllData.response;
						if (equipmentAllData == null)
						{
							result.msg = "equipmentAllData为空,请检查MES配置";
							return result;
						}
						var equFunction = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Consume");
						var equipmentFunctionProperty = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MasterStorageTank");
						var equipmentFunctionProperty2 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AverageWeightLogsheet");
						var equipmentFunctionProperty3 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AutoConsumeMaterialTypes");
						//var equipmentFunctionProperty4 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "ConsumeMaterialGroup");
						var equipmentFunctionProperty5 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "IsUnitConvert");
						var equipmentFunctionProperty6 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "IsConsumeSauce");
						var v1 = equipmentFunctionProperty?.ActualValue ?? equipmentFunctionProperty?.DefaultValue ?? "";
						var v2 = equipmentFunctionProperty2?.ActualValue ?? equipmentFunctionProperty2?.DefaultValue ?? "";
						var v3 = equipmentFunctionProperty3?.ActualValue ?? equipmentFunctionProperty3?.DefaultValue ?? "";
						//var v4 = equipmentFunctionProperty4?.ActualValue ?? equipmentFunctionProperty4?.DefaultValue ?? "";
						var isUnitConvert = equipmentFunctionProperty5?.ActualValue ?? equipmentFunctionProperty5?.DefaultValue ?? "";
						var isConsumeSauce = equipmentFunctionProperty6?.ActualValue ?? equipmentFunctionProperty6?.DefaultValue ?? "";
						var groupCodes = v3?.Split(';', StringSplitOptions.RemoveEmptyEntries)?.ToList();
						if (!groupCodes.Any())
						{
							result.msg = "AutoConsumeMaterialTypes为空,请检查MES配置";
							return result;
						}
						var consumeMatklZhs = new List<string>();
						if (productionOrder.SapOrderType != "ZXH2")
						{
							var dataItem_groupIds = (await _dal33.FindList(x => groupCodes.Contains(x.ItemCode))).Select(x => x.ID);
							consumeMatklZhs = (await _dal34.FindList(x => dataItem_groupIds.Contains(x.ItemId))).Select(x => x.ItemValue).ToList();
						}
						var dataItem_gdxhj1 = await _dal33.FindEntity(x => x.ItemCode == "GDXHJ1");
						var dataItemDetails_gdxhj1 = new List<string>();
						var unitIds_gdxhj1 = new List<string>();
						if (dataItem_gdxhj1 != null)
						{
							dataItemDetails_gdxhj1 = (await _dal34.FindList(x => x.ItemId == dataItem_gdxhj1.ID)).Select(x => x.ItemValue/*.ToUpper()*/).ToList();
							if (dataItemDetails_gdxhj1?.Count > 0)
							{
								unitIds_gdxhj1 = (await _unitDal.FindList(x => x.Deleted != 1 && x.Enable == 1 && dataItemDetails_gdxhj1.Contains(x.Name))).Select(x => x.ID).ToList();
							}
						}
						foreach (var item in poConsumeRequirements)
						{
							var material = await _dal2.FindEntity(x => x.ID == item.MaterialId);
							var autoReportMessage = new AutoReportMessage()
							{
								MaterialCode = material?.Code ?? item.MaterialId,
								MaterialName = material?.NAME ?? item.MaterialId,
								Quantity = 0,
								Success = true,
								Msg = "OK",
							};
							autoReportMessages.Add(autoReportMessage);
							if (material == null)
							{
								autoReportMessage.Success = false;
								autoReportMessage.Msg = $"未找到物料";
								continue;
							}
							{
								if (productionOrder.SapOrderType != "ZXH2" && material.Type != "ZSFG" && !consumeMatklZhs.Contains(material.MatklZh))
								{
									autoReportMessage.Success = false;
									autoReportMessage.Msg = $"未找到物料";
									continue;
								}
								decimal quantity2 = 0;
								//物料类型为酱料时要通过平均重量计算出消耗量
								if (material.Type == "ZSFG")
								{
									//判断当前设备是否消耗酱料
									if (isConsumeSauce != "True")
									{
										autoReportMessage.Success = false;
										autoReportMessage.Msg = $"后台任务配置为不消耗酱料";
										//autoReportMessages.Remove(autoReportMessage);
										continue;
									}
									//要把v2作为表单名去调用logsheet接口获取每瓶平均重量
									var q = await GetAverageWeightLogsheet(equipment.ID, runOrder.BatchId, v2);
									//q.response = 0.26m;
									if (!q.success)
									{
										var dataItemDetail2 = await _dal34.FindEntity(x => x.ItemId == dataItem.ID && x.ItemName == equipmentCode);
										if (dataItemDetail2 != null)
										{
											dataItemDetail2.ItemValue = result.response.OldQuantity;
											//dataItemDetail2.Modify(dataItemDetail2.ID, "OPC");
											await _dal34.Update(dataItemDetail2);
										}
										result.msg = q.msg;
										return result;
									}
									quantity2 = result.response.Quantity * Convert.ToDecimal(q.response);
								}
								else
								{
									decimal r1 = 1;
									//判断是灌装工单且当前设备消耗需要做单位转换
									if (productionOrder.SapOrderType != "ZXH2" && isUnitConvert == "True")
									{
										r1 = sapPackOrder.MngPu;
									}
									var rate = item.Quantity.Value / (productionOrder.PlanQty * r1);
									//if (productionOrder.SapOrderType != "ZXH2" && sapPackOrder != null)
									//{
									//	rate = item.Quantity.Value / sapPackOrder.MngPuo;
									//}
									quantity2 = Math.Round(result.response.Quantity * rate, 3);
								}
								var locations = equipmentCodes;
								var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
								//是酱料且没找到库存则产出再消耗
								if (material.Type == "ZSFG" && (inventorylistings == null || inventorylistings.Count == 0))
								{
									if (string.IsNullOrEmpty(v1))
									{
										autoReportMessage.Success = false;
										autoReportMessage.Msg = "属性MasterStorageTank为空";
									}
									else
									{
										var cgequipment = await _dal15.FindEntity(x => x.EquipmentCode == v1);
										if (cgequipment == null)
										{
											autoReportMessage.Success = false;
											autoReportMessage.Msg = $"未找到储罐：{v1}";
										}
										else
										{
											var cgRunOrder = (await _dal14.FindList(x => x.RunEquipmentId == cgequipment.ID && x.Status == "1")).OrderByDescending(x => x.CreateDate).FirstOrDefault();
											if (cgRunOrder == null)
											{
												autoReportMessage.Success = false;
												autoReportMessage.Msg = $"储罐：{v1}未找到运行工单";
											}
											else
											{
												var cgProductionOrder = await _dal5.FindEntity(x => x.ID == cgRunOrder.ProductionOrderId);
												if (cgProductionOrder == null)
												{
													autoReportMessage.Success = false;
													autoReportMessage.Msg = $"未找到工单";
												}
												else
												{
													var materialVersion = await _dal35.FindEntity(cgProductionOrder.MaterialVersionId);
													if (materialVersion == null)
													{
														autoReportMessage.Success = false;
														autoReportMessage.Msg = $"未找materialVersion";
													}
													else
													{
														var poProducedRequirements = await _dal27.FindList(x => x.ProductionOrderId == cgRunOrder.ProductionOrderId);
														if (poProducedRequirements == null || poProducedRequirements.Count == 0)
														{
															autoReportMessage.Success = false;
															autoReportMessage.Msg = $"未找poProducedRequirements";
														}
														else
														{
															var poProducedRequirement = poProducedRequirements.Find(x => x.MaterialId == material.ID);
															if (poProducedRequirement == null)
															{
																autoReportMessage.Success = false;
																autoReportMessage.Msg = $"储罐工单的产品物料号和当前灌装机要消耗的酱料物料号不一致";
															}
															else
															{
																var sublotId = "";
																//生成追溯码
																//if (sapStorageType.SuManaged == 1)
																{
																	var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
																	var ssccString = ssccStrings.response;
																	var materialsub = new MaterialSubLotEntity();
																	materialsub.SubLotId = ssccString;
																	materialsub.Type = "0";
																	materialsub.ExternalStatus = "3";
																	materialsub.CreateCustomGuid("OPC");
																	materialSubLotEntities.Add(materialsub);
																	sublotId = materialsub.ID.ToString();
																}

																//写入produce_actual表
																var poProducedActualEntity = new PoProducedActualEntity();
																poProducedActualEntity.ProductExecutionId = cgRunOrder.ID;
																poProducedActualEntity.ProductionOrderId = cgRunOrder.ProductionOrderId;
																poProducedActualEntity.PoProducedRequirementId = poProducedRequirement.ID;
																poProducedActualEntity.LotId = cgRunOrder.LotId;
																poProducedActualEntity.LotStatus = 2;
																poProducedActualEntity.SubLotId = sublotId;
																poProducedActualEntity.SubLotStatus = 3;
																poProducedActualEntity.SourceType = 1;
																poProducedActualEntity.EquipmentId = cgequipment.ID;
																poProducedActualEntity.DesinationEquipmentId = cgequipment.ID;
																poProducedActualEntity.UnitId = poProducedRequirement.UnitId;
																poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
																poProducedActualEntity.Quantity = quantity2;
																poProducedActualEntity.CreateCustomGuid("OPC");
																poProducedActualEntities.Add(poProducedActualEntity);

																//写入consumed_actual表
																var poConsumeActualEntity = new PoConsumeActualEntity
																{
																	ProductionOrderId = runOrder.ProductionOrderId,
																	PoConsumeRequirementId = item.ID,
																	ProductExecutionId = runOrder.ID,
																	EquipmentId = runOrder.RunEquipmentId,
																	SourceEquipmentId = poProducedActualEntity.DesinationEquipmentId,
																	Quantity = quantity2,
																	UnitId = item.UnitId,
																	LotId = cgRunOrder.LotId,
																	SubLotId = sublotId,
																	SubLotStatus = 3,
																	StorageBin = "MFG3",
																	//StorageLocation = "",
																	//ContainerId = inventoryModel.ContainerId,
																	TeamId = "",
																	ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
																	ReasonCode = "",
																	Comment = "",
																	Deleted = 0,
																	SendExternal = item.Quantity == 0 ? 1 : 0
																};
																autoReportMessage.Quantity += quantity2;
																poConsumeActualEntity.CreateCustomGuid("OPC");
																poConsumeActualEntities.Add(poConsumeActualEntity);
															}
														}
													}
												}
											}
										}
									}
									continue;
								}
								//假设有库存，判断库存数量是不是充足
								var inventoryQuantity = inventorylistings?.Sum(x => x.Quantity) ?? 0;
								if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
								{
									quantity2 = Math.Floor(quantity2);
								}
								if (inventoryQuantity < quantity2)
								{
									autoReportMessage.Success = false;
									autoReportMessage.Msg = "库存数量不足";
								}
								else
								{
									var sublotIds = inventorylistings.Select(x => x.SlotId).Distinct().ToList() ?? new List<string>();
									var materialTransfers = await _materialTransferDal.FindList(x => equipmentIds.Contains(x.NewEquipmentId) && sublotIds.Contains(x.NewSublotId) && (x.Comment == "WCS上料-转移" || x.Comment == "上料-转移" || x.Comment == "WCS上料-收货"), x => x.CreateDate);
									// 根据materialTransfers中的CreateDate对inventorylistings进行排序
									var sortedInventorylistings = inventorylistings.OrderBy(item =>
									{
										var order = materialTransfers.FirstOrDefault(o => o.NewSublotId == item.SlotId);
										return order != null ? order.CreateDate : item.CreateDate;
									});
									SerilogServer.LogDebug($"【CheckInventory】" +
														   $"{Environment.NewLine}InventoryListings：{FAJsonConvert.ToJson(inventorylistings)}" +
														   $"{Environment.NewLine}MaterialTransfers：{FAJsonConvert.ToJson(materialTransfers)}" +
														   $"{Environment.NewLine}SortedInventoryListings：{FAJsonConvert.ToJson(sortedInventorylistings)}", "AutoReport");
									foreach (var inventoryModel in sortedInventorylistings)
									{
										if (quantity2 <= 0)
										{
											break;
										}
										decimal consumeQuantity = quantity2;
										if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
										{
											consumeQuantity = Math.Floor(consumeQuantity);
										}
										if (quantity2 >= inventoryModel.Quantity)
										{
											deleteIds.Add(inventoryModel.InventoryId);
											consumeQuantity = inventoryModel.Quantity.Value;
										}
										else
										{
											var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
											if (materialInventory != null)
											{
												materialInventory.Quantity -= Math.Round(Convert.ToDecimal(consumeQuantity), 4);  // consumeQuantity;
												materialInventory.Modify(materialInventory.ID, "OPC");
												materialInventoryEntities.Add(materialInventory);
											}
										}
										quantity2 -= consumeQuantity;
										//写入consumed_actual表
										var poConsumeActualEntity = new PoConsumeActualEntity
										{
											ProductionOrderId = runOrder.ProductionOrderId,
											PoConsumeRequirementId = item.ID,
											ProductExecutionId = runOrder.ID,
											EquipmentId = runOrder.RunEquipmentId,
											SourceEquipmentId = inventoryModel.EquipmentId,
											Quantity = consumeQuantity,
											UnitId = item.UnitId,
											LotId = inventoryModel.LotId,
											SubLotId = inventoryModel.SlotId,
											SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
											StorageBin = item.StorageBin,
											StorageLocation = inventoryModel.LocationFcode,
											//ContainerId = inventoryModel.ContainerId,
											TeamId = "",
											ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
											ReasonCode = "",
											Comment = "",
											Deleted = 0,
											SendExternal = item.Quantity == 0 ? 1 : 0
										};
										autoReportMessage.Quantity += consumeQuantity;
										poConsumeActualEntity.CreateCustomGuid("OPC");
										poConsumeActualEntities.Add(poConsumeActualEntity);
									}
								}
							}
						}
						_unitOfWork.BeginTran();
						try
						{
							if (materialSubLotEntities?.Count > 0)
							{
								await _dal16.Add(materialSubLotEntities);
							}
							if (poProducedActualEntities?.Count > 0)
							{
								await _dal17.Add(poProducedActualEntities);
							}
							if (poConsumeActualEntities?.Count > 0)
							{
								await _dal28.Add(poConsumeActualEntities);
							}
							if (materialInventoryEntities?.Count > 0)
							{
								await _dal19.Update(materialInventoryEntities);
							}
							if (deleteIds?.Count > 0)
							{
								await _dal19.DeleteByIds(deleteIds.ToArray());
							}
							_unitOfWork.CommitTran();
						}
						catch (Exception ex)
						{
							result.msg = ex.Message;
							_unitOfWork.RollbackTran();
							return result;
						}
						break;
					}
				case "Produce":
					{
						var location = await GetProduceLocation(equipment.ID);
						//var location = await GetProduceLocation2(equipment.ID);
						if (location == null)
						{
							result.msg = "未找到location";
							return result;
						}
						var poProducedRequirements = await _dal27.FindList(x => x.ID == runOrder.PoProducedRequirementId);
						if (poProducedRequirements?.Count > 0)
						{
							var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
							var equipmentStorages = api_equipmentStorages.response;
							if (equipmentStorages == null || equipmentStorages.Count == 0)
							{
								result.msg = "未找到EquipmentStorages";
								return result;
							}
							var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == location.ID);
							if (equipmentStorage == null)
							{
								result.msg = "未找到EquipmentStorage";
								return result;
							}
							var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
							var equipmentRequirement = api_equipmentRequirement.response;
							if (equipmentRequirement == null)
							{
								result.msg = "未找到equipmentRequirement";
								return result;
							}
							var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
							var sapStorageTypes = api_sapStorageTypes.response;
							if (sapStorageTypes == null || sapStorageTypes.Count == 0)
							{
								result.msg = "未找到sapStorageTypes";
								return result;
							}
							var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
							if (sapStorageType == null)
							{
								result.msg = "未找到sapStorageType";
								return result;
							}
							foreach (var item in poProducedRequirements)
							{
								var rate = item.Quantity.Value / productionOrder.PlanQty;
								var quantity2 = Math.Round(result.response.Quantity * rate, 3);
								var material = await _dal2.FindEntity(item.MaterialId);
								var autoReportMessage = new AutoReportMessage()
								{
									MaterialCode = material?.Code ?? item.MaterialId,
									MaterialName = material?.NAME ?? item.MaterialId,
									Quantity = quantity2,
									Success = true,
									Msg = "OK",
								};
								autoReportMessages.Add(autoReportMessage);
								var sublotId = "";
								//生成追溯码
								if (sapStorageType.SuManaged == 1)
								{
									var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
									var ssccString = ssccStrings.response;
									var materialsub = new MaterialSubLotEntity();
									materialsub.SubLotId = ssccString;
									materialsub.Type = "0";
									materialsub.ExternalStatus = "3";
									materialsub.CreateCustomGuid("OPC");
									materialSubLotEntities.Add(materialsub);
									sublotId = materialsub.ID.ToString();
								}
								var poProducedActualEntity = new PoProducedActualEntity();
								poProducedActualEntity.ProductExecutionId = runOrder.ID;
								poProducedActualEntity.ProductionOrderId = runOrder.ProductionOrderId;
								poProducedActualEntity.PoProducedRequirementId = item.ID;
								poProducedActualEntity.LotId = runOrder.LotId;
								poProducedActualEntity.LotStatus = 2;
								poProducedActualEntity.SubLotId = sublotId;
								poProducedActualEntity.SubLotStatus = 3;
								poProducedActualEntity.SourceType = 1;
								poProducedActualEntity.EquipmentId = equipment.ID;
								poProducedActualEntity.DesinationEquipmentId = location.ID;
								poProducedActualEntity.UnitId = item.UnitId;
								poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
								poProducedActualEntity.Quantity = quantity2;
								poProducedActualEntity.CreateCustomGuid("OPC");
								poProducedActualEntities.Add(poProducedActualEntity);
								if (equipmentRequirement?.ManageInventory == "1")
								{
									var materialInventoryEntity = new MaterialInventoryEntity();
									materialInventoryEntity.LotId = runOrder.LotId;
									materialInventoryEntity.SublotId = sublotId;
									materialInventoryEntity.Quantity = Math.Round(Convert.ToDecimal(quantity2), 4);  //quantity2;
									materialInventoryEntity.QuantityUomId = item.UnitId;
									materialInventoryEntity.StorageLocation = location.EquipmentCode;
									materialInventoryEntity.EquipmentId = location.ID;
									materialInventoryEntity.IsPrechecked = "FALSE";
									materialInventoryEntity.ContainerId = "";
									//materialInventoryEntity.ProductionRequestId = runOrder.ProductionOrderId;
									materialInventoryEntity.SortOrder = 0;
									materialInventoryEntity.CreateCustomGuid("OPC");
									materialInventoryEntities.Add(materialInventoryEntity);
								}
							}
							_unitOfWork.BeginTran();
							try
							{
								if (materialSubLotEntities?.Count > 0)
								{
									await _dal16.Add(materialSubLotEntities);
								}
								if (poProducedActualEntities?.Count > 0)
								{
									await _dal17.Add(poProducedActualEntities);
								}
								if (materialInventoryEntities?.Count > 0)
								{
									await _dal19.Add(materialInventoryEntities);
								}
								_unitOfWork.CommitTran();
							}
							catch (Exception ex)
							{
								result.msg = ex.Message;
								_unitOfWork.RollbackTran();
								return result;
							}
						}
						break;
					}
				default:
					result.msg = "reportType错误，请检查任务配置参数！";
					return result;
					break;
			}
			var r = autoReportMessages.Select(x => $"{x.MaterialCode};{x.MaterialName};{x.Quantity};{x.Success};{x.Msg}").ToList();
			result.response.AutoReportMessages = r;
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<AutoReportMessageModel>> AutoReport2(string reportType, string equipmentCode, string tag)
		{
			var result = new MessageModel<AutoReportMessageModel>
			{
				msg = "操作失败！",
				success = false,
				response = new AutoReportMessageModel() { AutoReportMessages = new List<string>() }
			};
			if (reportType != "Consume" && reportType != "Produce")
			{
				result.msg = "参数reportType配置错误，请配置为Consume或Produce！";
				return result;
			}
			var equipment = await _dal15.FindEntity(x => x.EquipmentCode == equipmentCode);
			if (equipment == null)
			{
				result.msg = "未找到Equipment,请检查配置！";
				return result;
			}
			var dataItem = await _dal33.FindEntity(x => x.ItemCode == $"Auto{reportType}Time");
			if (dataItem == null)
			{
				result.msg = $"未找到Auto{reportType}Time,请检查数据字典配置！";
				return result;
			}
			if (string.IsNullOrEmpty(tag))
			{
				var propertyResult = await GetFunctionPropertyValue(equipment.ID, reportType, $"{reportType}TagAddress");
				tag = propertyResult?.response;
			}
			if (string.IsNullOrEmpty(tag))
			{
				result.msg = $"未找到{reportType}TagAddress,请检查配置！";
				return result;
			}
			decimal quantity = 0;
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
			var dataItemDetail = dataItemDetailEntities?.Find(x => x.ItemName == equipmentCode);
			decimal oldValue = 0;
			//decimal Influxdbquantity = oldValue + 60;
			decimal Influxdbquantity = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, tag))?.Value);
			if (dataItemDetail != null)
			{
				oldValue = Convert.ToDecimal(dataItemDetail.ItemValue);
				{
					if (oldValue <= Influxdbquantity)
					{
						quantity = Influxdbquantity - oldValue;
					}
					else
					{
						decimal InfluxdbMaxQuantity = Convert.ToDecimal((await _IInfluxDbServices.GetMaxInfluxData(dataItemDetail.ModifyDate.AddMinutes(-5), DateTime.Now, null, tag))?.Value);
						var q = InfluxdbMaxQuantity - oldValue;
						if (q < 0)
						{
							q = 0;
						}
						quantity = q + Influxdbquantity;
					}
					dataItemDetail.ItemValue = Influxdbquantity.ToString();
					dataItemDetail.Modify(dataItemDetail.ID, "OPC");
					updateDataItemDetails.Add(dataItemDetail);
				}
			}
			else
			{
				int maxSort = dataItemDetailEntities?.Select(x => x.SortCode)?.Max() ?? 0;
				SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
				{
					ItemId = dataItem.ID,
					ItemCode = dataItem.ItemCode,
					ItemName = equipmentCode,
					ItemValue = Influxdbquantity.ToString(),
					SortCode = ++maxSort,
					Enable = 1,
					Deleted = 0
				};
				dataItemDetailEntity.CreateCustomGuid("OPC");
				quantity = Influxdbquantity;
				addDataItemDetails.Add(dataItemDetailEntity);
			}
			result.response.OldQuantity = oldValue.ToString();
			result.response.CurrentQuantity = Influxdbquantity.ToString();
			result.response.Quantity = quantity;

			_unitOfWork.BeginTran();
			try
			{
				if (updateDataItemDetails?.Count > 0)
				{
					await _dal34.Update(updateDataItemDetails);
				}
				if (addDataItemDetails?.Count > 0)
				{
					await _dal34.Add(addDataItemDetails);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			_unitOfWork.CommitTran();
			if (quantity == 0)
			{
				result.msg = "本次执行对比上次增加数量为0";
				return result;
			}
			var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
			if (runOrder == null)
			{
				result.msg = $"当前设备{equipmentCode}未找到运行工单！";
				return result;
			}
			var productionOrder = await _dal5.FindEntity(x => x.ID == runOrder.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = $"未找到productionOrder！";
				return result;
			}
			SappackorderEntity sapPackOrder = null;
			if (productionOrder.SapOrderType != "ZXH2")
			{
				sapPackOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			}
			var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement！";
				return result;
			}
			var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment！";
				return result;
			}

			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
			var autoReportMessages = new List<AutoReportMessage>();
			List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
			List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
			List<string> deleteIds = new List<string>();
			List<PoProducedActualEntity> poProducedActualEntities = new List<PoProducedActualEntity>();
			List<MaterialSubLotEntity> materialSubLotEntities = new List<MaterialSubLotEntity>();
			switch (reportType)
			{
				case "Consume":
					{
						var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
						var equipmentCodes = list.Select(x =>
						{
							return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
						});
						if (equipmentCodes == null)
						{
							equipmentCodes = new List<string>();
						}
						var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
						var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId);

						//获取Function
						MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
						var equipmentAllData = apiResult_equipmentAllData.response;
						if (equipmentAllData == null)
						{
							result.msg = "equipmentAllData为空,请检查MES配置";
							return result;
						}
						var equFunction = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Consume");
						var equipmentFunctionProperty = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MasterStorageTank");
						var equipmentFunctionProperty2 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AverageWeightLogsheet");
						var equipmentFunctionProperty3 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AutoConsumeMaterialTypes");
						//var equipmentFunctionProperty4 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "ConsumeMaterialGroup");
						var v1 = equipmentFunctionProperty?.ActualValue ?? equipmentFunctionProperty?.DefaultValue ?? "";
						var v2 = equipmentFunctionProperty2?.ActualValue ?? equipmentFunctionProperty2?.DefaultValue ?? "";
						var v3 = equipmentFunctionProperty3?.ActualValue ?? equipmentFunctionProperty3?.DefaultValue ?? "";
						//var v4 = equipmentFunctionProperty4?.ActualValue ?? equipmentFunctionProperty4?.DefaultValue ?? "";
						var groupCodes = v3?.Split(';', StringSplitOptions.RemoveEmptyEntries)?.ToList();
						if (!groupCodes.Any())
						{
							result.msg = "AutoConsumeMaterialTypes为空,请检查MES配置";
							return result;
						}
						var dataItem_gdxhj1 = await _dal33.FindEntity(x => x.ItemCode == "GDXHJ1");
						var dataItemDetails_gdxhj1 = new List<string>();
						var unitIds_gdxhj1 = new List<string>();
						if (dataItem_gdxhj1 != null)
						{
							dataItemDetails_gdxhj1 = (await _dal34.FindList(x => x.ItemId == dataItem_gdxhj1.ID)).Select(x => x.ItemValue/*.ToUpper()*/).ToList();
							if (dataItemDetails_gdxhj1?.Count > 0)
							{
								unitIds_gdxhj1 = (await _unitDal.FindList(x => x.Deleted != 1 && x.Enable == 1 && dataItemDetails_gdxhj1.Contains(x.Name))).Select(x => x.ID).ToList();
							}
						}
						foreach (var item in poConsumeRequirements)
						{
							var material = await _dal2.FindEntity(x => x.ID == item.MaterialId);
							var autoReportMessage = new AutoReportMessage()
							{
								MaterialCode = material?.Code ?? item.MaterialId,
								MaterialName = material?.NAME ?? item.MaterialId,
								Quantity = 0,
								Success = true,
								Msg = "OK",
							};
							if (material == null)
							{
								autoReportMessage.Success = false;
								autoReportMessage.Msg = $"未找到物料";
								continue;
							}
							{
								decimal quantity2 = 0;
								//物料类型为酱料时 要通过平均重量计算出消耗量
								if (material.Type == "ZSFG")
								{
									//要把v2作为表单名去调用logsheet接口获取每瓶平均重量
									var q = await GetAverageWeightLogsheet(equipment.ID, runOrder.BatchId, v2);
									if (!q.success)
									{
										var dataItemDetail2 = await _dal34.FindEntity(x => x.ItemId == dataItem.ID && x.ItemName == equipmentCode);
										if (dataItemDetail2 != null)
										{
											dataItemDetail2.ItemValue = oldValue.ToString();
											//dataItemDetail2.Modify(dataItemDetail2.ID, "OPC");
											await _dal34.Update(dataItemDetail2);
										}
										result.msg = q.msg;
										return result;
									}
									quantity2 = quantity * Convert.ToDecimal(q.response);
								}
								else
								{
									var rate = item.Quantity.Value / productionOrder.PlanQty;
									if (productionOrder.SapOrderType != "ZXH2" && sapPackOrder != null)
									{
										rate = item.Quantity.Value / sapPackOrder.MngPuo;
									}
									quantity2 = Math.Round(quantity * rate, 3);
								}
								var locations = equipmentCodes;
								var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
								//是酱料且没找到库存则产出再消耗
								if (material.Type == "ZSFG" && (inventorylistings == null || inventorylistings.Count == 0))
								{
									if (string.IsNullOrEmpty(v1))
									{
										autoReportMessage.Success = false;
										autoReportMessage.Msg = "属性MasterStorageTank为空";
									}
									else
									{
										var cgequipment = await _dal15.FindEntity(x => x.EquipmentCode == v1);
										if (cgequipment == null)
										{
											autoReportMessage.Success = false;
											autoReportMessage.Msg = $"未找到储罐：{v1}";
										}
										else
										{
											var cgRunOrder = (await _dal14.FindList(x => x.RunEquipmentId == cgequipment.ID && x.Status == "1")).OrderByDescending(x => x.CreateDate).FirstOrDefault();
											if (cgRunOrder == null)
											{
												autoReportMessage.Success = false;
												autoReportMessage.Msg = $"储罐：{v1}未找到运行工单";
											}
											else
											{
												var cgProductionOrder = await _dal5.FindEntity(x => x.ID == cgRunOrder.ProductionOrderId);
												if (cgProductionOrder == null)
												{
													autoReportMessage.Success = false;
													autoReportMessage.Msg = $"未找到工单";
												}
												else
												{
													var materialVersion = await _dal35.FindEntity(cgProductionOrder.MaterialVersionId);
													if (materialVersion == null)
													{
														autoReportMessage.Success = false;
														autoReportMessage.Msg = $"未找materialVersion";
													}
													else
													{
														var poProducedRequirements = await _dal27.FindList(x => x.ProductionOrderId == cgRunOrder.ProductionOrderId);
														if (poProducedRequirements == null || poProducedRequirements.Count == 0)
														{
															autoReportMessage.Success = false;
															autoReportMessage.Msg = $"未找poProducedRequirements";
														}
														else
														{
															var poProducedRequirement = poProducedRequirements.Find(x => x.MaterialId == material.ID);
															if (poProducedRequirement == null)
															{
																autoReportMessage.Success = false;
																autoReportMessage.Msg = $"储罐工单的产品物料号和当前灌装机要消耗的酱料物料号不一致";
															}
															else
															{
																var sublotId = "";
																//生成追溯码
																{
																	var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
																	var ssccString = ssccStrings.response;
																	var materialsub = new MaterialSubLotEntity();
																	materialsub.SubLotId = ssccString;
																	materialsub.Type = "0";
																	materialsub.ExternalStatus = "3";
																	materialsub.CreateCustomGuid("OPC");
																	materialSubLotEntities.Add(materialsub);
																	sublotId = materialsub.ID.ToString();
																}

																//写入produce_actual表
																var poProducedActualEntity = new PoProducedActualEntity();
																poProducedActualEntity.ProductExecutionId = cgRunOrder.ID;
																poProducedActualEntity.ProductionOrderId = cgRunOrder.ProductionOrderId;
																poProducedActualEntity.PoProducedRequirementId = poProducedRequirement.ID;
																poProducedActualEntity.LotId = cgRunOrder.LotId;
																poProducedActualEntity.LotStatus = 2;
																poProducedActualEntity.SubLotId = sublotId;
																poProducedActualEntity.SubLotStatus = 3;
																poProducedActualEntity.SourceType = 1;
																poProducedActualEntity.EquipmentId = cgequipment.ID;
																poProducedActualEntity.DesinationEquipmentId = cgequipment.ID;
																poProducedActualEntity.UnitId = poProducedRequirement.UnitId;
																poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
																poProducedActualEntity.Quantity = quantity2;
																poProducedActualEntity.CreateCustomGuid("OPC");
																poProducedActualEntities.Add(poProducedActualEntity);

																//写入consumed_actual表
																var poConsumeActualEntity = new PoConsumeActualEntity
																{
																	ProductionOrderId = runOrder.ProductionOrderId,
																	PoConsumeRequirementId = item.ID,
																	ProductExecutionId = runOrder.ID,
																	EquipmentId = runOrder.RunEquipmentId,
																	SourceEquipmentId = poProducedActualEntity.DesinationEquipmentId,
																	Quantity = quantity2,
																	UnitId = item.UnitId,
																	LotId = runOrder.LotId,
																	SubLotId = sublotId,
																	SubLotStatus = 3,
																	StorageBin = "MFG3",
																	//StorageLocation = "",
																	//ContainerId = inventoryModel.ContainerId,
																	TeamId = "",
																	ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
																	ReasonCode = "",
																	Comment = "",
																	Deleted = 0,
																	SendExternal = item.Quantity == 0 ? 1 : 0
																};
																autoReportMessage.Quantity += quantity2;
																poConsumeActualEntity.CreateCustomGuid("OPC");
																poConsumeActualEntities.Add(poConsumeActualEntity);
															}
														}
													}
												}
											}
										}
									}
									continue;
								}
								//假设有库存，判断库存数量是不是充足
								var inventoryQuantity = inventorylistings?.Sum(x => x.Quantity) ?? 0;
								if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
								{
									quantity2 = Math.Floor(quantity2);
								}
								if (inventoryQuantity < quantity2)
								{
									autoReportMessage.Success = false;
									autoReportMessage.Msg = "库存数量不足";
								}
								else
								{
									var sublotIds = inventorylistings.Select(x => x.SlotId).Distinct().ToList() ?? new List<string>();
									var materialTransfers = await _materialTransferDal.FindList(x => sublotIds.Contains(x.NewSublotId) && (x.Comment == "WCS上料-转移" || x.Comment == "上料-转移" || x.Comment == "WCS上料-收货"), x => x.CreateDate);
									// 根据materialTransfers中的CreateDate对inventorylistings进行排序
									var sortedInventorylistings = inventorylistings.OrderBy(item =>
									{
										var order = materialTransfers.FirstOrDefault(o => o.NewSublotId == item.SlotId);
										return order != null ? order.CreateDate : item.CreateDate;
									});
									SerilogServer.LogDebug($"【CheckInventory】" +
										$"{Environment.NewLine}InventoryListings：{FAJsonConvert.ToJson(inventorylistings)}" +
										$"{Environment.NewLine}MaterialTransfers：{FAJsonConvert.ToJson(materialTransfers)}" +
										$"{Environment.NewLine}SortedInventoryListings：{FAJsonConvert.ToJson(sortedInventorylistings)}", "AutoReport");
									foreach (var inventoryModel in sortedInventorylistings)
									{
										if (quantity2 <= 0)
										{
											break;
										}
										decimal consumeQuantity = quantity2;
										if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
										{
											consumeQuantity = Math.Floor(consumeQuantity);
										}
										if (quantity2 >= inventoryModel.Quantity)
										{
											deleteIds.Add(inventoryModel.InventoryId);
											consumeQuantity = inventoryModel.Quantity.Value;
										}
										else
										{
											var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
											if (materialInventory != null)
											{
												materialInventory.Quantity -= Math.Round(Convert.ToDecimal(consumeQuantity), 4); //consumeQuantity;
												materialInventory.Modify(materialInventory.ID, "OPC");
												materialInventoryEntities.Add(materialInventory);
											}
										}
										quantity2 -= consumeQuantity;
										//写入consumed_actual表
										var poConsumeActualEntity = new PoConsumeActualEntity
										{
											ProductionOrderId = runOrder.ProductionOrderId,
											PoConsumeRequirementId = item.ID,
											ProductExecutionId = runOrder.ID,
											EquipmentId = runOrder.RunEquipmentId,
											SourceEquipmentId = inventoryModel.EquipmentId,
											Quantity = consumeQuantity,
											UnitId = item.UnitId,
											LotId = inventoryModel.LotId,
											SubLotId = inventoryModel.SlotId,
											SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
											StorageBin = inventoryModel.LocationS,
											StorageLocation = inventoryModel.LocationFcode,
											//ContainerId = inventoryModel.ContainerId,
											TeamId = "",
											ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
											ReasonCode = "",
											Comment = "",
											Deleted = 0,
											SendExternal = item.Quantity == 0 ? 1 : 0
										};
										autoReportMessage.Quantity += consumeQuantity;
										poConsumeActualEntity.CreateCustomGuid("OPC");
										poConsumeActualEntities.Add(poConsumeActualEntity);
									}
								}
							}
							autoReportMessages.Add(autoReportMessage);
						}
						_unitOfWork.BeginTran();
						try
						{
							if (materialSubLotEntities?.Count > 0)
							{
								await _dal16.Add(materialSubLotEntities);
							}
							if (poProducedActualEntities?.Count > 0)
							{
								await _dal17.Add(poProducedActualEntities);
							}
							if (poConsumeActualEntities?.Count > 0)
							{
								await _dal28.Add(poConsumeActualEntities);
							}
							if (materialInventoryEntities?.Count > 0)
							{
								await _dal19.Update(materialInventoryEntities);
							}
							if (deleteIds?.Count > 0)
							{
								await _dal19.DeleteByIds(deleteIds.ToArray());
							}
							_unitOfWork.CommitTran();
						}
						catch (Exception ex)
						{
							result.msg = ex.Message;
							_unitOfWork.RollbackTran();
							return result;
						}
						break;
					}
				case "Produce":
					{
						var location = await GetProduceLocation(equipment.ID);
						//var location = await GetProduceLocation2(equipment.ID);
						if (location == null)
						{
							result.msg = "未找到location";
							return result;
						}
						var poProducedRequirements = await _dal27.FindList(x => x.ID == runOrder.PoProducedRequirementId);
						if (poProducedRequirements?.Count > 0)
						{
							var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
							var equipmentStorages = api_equipmentStorages.response;
							if (equipmentStorages == null || equipmentStorages.Count == 0)
							{
								result.msg = "未找到EquipmentStorages";
								return result;
							}
							var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == location.ID);
							if (equipmentStorage == null)
							{
								result.msg = "未找到EquipmentStorage";
								return result;
							}
							var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
							var equipmentRequirement = api_equipmentRequirement.response;
							if (equipmentRequirement == null)
							{
								result.msg = "未找到equipmentRequirement";
								return result;
							}
							var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
							var sapStorageTypes = api_sapStorageTypes.response;
							if (sapStorageTypes == null || sapStorageTypes.Count == 0)
							{
								result.msg = "未找到sapStorageTypes";
								return result;
							}
							var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
							if (sapStorageType == null)
							{
								result.msg = "未找到sapStorageType";
								return result;
							}
							foreach (var item in poProducedRequirements)
							{
								var rate = item.Quantity.Value / productionOrder.PlanQty;
								var quantity2 = Math.Round(quantity * rate, 3);
								var material = await _dal2.FindEntity(item.MaterialId);
								var autoReportMessage = new AutoReportMessage()
								{
									MaterialCode = material?.Code ?? item.MaterialId,
									MaterialName = material?.NAME ?? item.MaterialId,
									Quantity = quantity2,
									Success = true,
									Msg = "OK",
								};
								var sublotId = "";
								//生成追溯码
								if (sapStorageType.SuManaged == 1)
								{
									var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
									var ssccString = ssccStrings.response;
									var materialsub = new MaterialSubLotEntity();
									materialsub.SubLotId = ssccString;
									materialsub.Type = "0";
									materialsub.ExternalStatus = "3";
									materialsub.CreateCustomGuid("OPC");
									materialSubLotEntities.Add(materialsub);
									sublotId = materialsub.ID.ToString();
								}
								var poProducedActualEntity = new PoProducedActualEntity();
								poProducedActualEntity.ProductExecutionId = runOrder.ID;
								poProducedActualEntity.ProductionOrderId = runOrder.ProductionOrderId;
								poProducedActualEntity.PoProducedRequirementId = item.ID;
								poProducedActualEntity.LotId = runOrder.LotId;
								poProducedActualEntity.LotStatus = 2;
								poProducedActualEntity.SubLotId = sublotId;
								poProducedActualEntity.SubLotStatus = 3;
								poProducedActualEntity.SourceType = 1;
								poProducedActualEntity.EquipmentId = equipment.ID;
								poProducedActualEntity.DesinationEquipmentId = location.ID;
								poProducedActualEntity.UnitId = item.UnitId;
								poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
								poProducedActualEntity.Quantity = quantity2;
								poProducedActualEntity.CreateCustomGuid("OPC");
								poProducedActualEntities.Add(poProducedActualEntity);
								if (equipmentRequirement?.ManageInventory == "1")
								{
									var materialInventoryEntity = new MaterialInventoryEntity();
									materialInventoryEntity.LotId = runOrder.LotId;
									materialInventoryEntity.SublotId = sublotId;
									materialInventoryEntity.Quantity = Math.Round(Convert.ToDecimal(quantity2), 4);// quantity2;
									materialInventoryEntity.QuantityUomId = item.UnitId;
									materialInventoryEntity.StorageLocation = location.EquipmentCode;
									materialInventoryEntity.EquipmentId = location.ID;
									materialInventoryEntity.IsPrechecked = "FALSE";
									materialInventoryEntity.ContainerId = "";
									//materialInventoryEntity.ProductionRequestId = runOrder.ProductionOrderId;
									materialInventoryEntity.SortOrder = 0;
									materialInventoryEntity.CreateCustomGuid("OPC");
									materialInventoryEntities.Add(materialInventoryEntity);
								}
								autoReportMessages.Add(autoReportMessage);
							}
							_unitOfWork.BeginTran();
							try
							{
								if (materialSubLotEntities?.Count > 0)
								{
									await _dal16.Add(materialSubLotEntities);
								}
								if (poProducedActualEntities?.Count > 0)
								{
									await _dal17.Add(poProducedActualEntities);
								}
								if (materialInventoryEntities?.Count > 0)
								{
									await _dal19.Add(materialInventoryEntities);
								}
								_unitOfWork.CommitTran();
							}
							catch (Exception ex)
							{
								result.msg = ex.Message;
								_unitOfWork.RollbackTran();
								return result;
							}
						}
						break;
					}
				default:
					result.msg = "reportType错误，请检查任务配置参数！";
					return result;
					break;
			}
			var r = autoReportMessages.Select(x => $"{x.MaterialCode};{x.MaterialName};{x.Quantity};{x.Success};{x.Msg}").ToList();
			result.response.AutoReportMessages = r;
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<AutoReportMessageModel>> AutoReport_v2(string reportType, string equipmentCode, string tag)
		{
			//equipmentCode = "Label/Palletizer-L2";
			//tag = "Channel_1.Device_1.Group1.TAG2";

			var result = new MessageModel<AutoReportMessageModel>
			{
				msg = "操作失败！",
				success = false,
				response = new AutoReportMessageModel() { AutoReportMessages = new List<string>() }
			};
			if (reportType != "Consume" && reportType != "Produce")
			{
				result.msg = "参数reportType配置错误，请配置为Consume或Produce！";
				return result;
			}
			var equipment = await _dal15.FindEntity(x => x.EquipmentCode == equipmentCode);
			if (equipment == null)
			{
				result.msg = "未找到Equipment,请检查配置！";
				return result;
			}
			var dataItem = await _dal33.FindEntity(x => x.ItemCode == $"Auto{reportType}Time");
			if (dataItem == null)
			{
				result.msg = $"未找到Auto{reportType}Time,请检查数据字典配置！";
				return result;
			}
			if (string.IsNullOrEmpty(tag))
			{
				var propertyResult = await GetFunctionPropertyValue(equipment.ID, reportType, $"{reportType}TagAddress");
				tag = propertyResult?.response;
			}
			if (string.IsNullOrEmpty(tag))
			{
				result.msg = $"未找到{reportType}TagAddress,请检查配置！";
				return result;
			}
			decimal quantity = 0;
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
			var dataItemDetail = dataItemDetailEntities?.Find(x => x.ItemName == equipmentCode);
			decimal oldValue = 0;
			//decimal Influxdbquantity = oldValue + 60;
			decimal Influxdbquantity = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, tag))?.Value);
			if (dataItemDetail != null)
			{
				oldValue = Convert.ToDecimal(dataItemDetail.ItemValue);
				{
					if (oldValue <= Influxdbquantity)
					{
						quantity = Influxdbquantity - oldValue;
					}
					else
					{
						decimal InfluxdbMaxQuantity = Convert.ToDecimal((await _IInfluxDbServices.GetMaxInfluxData(dataItemDetail.ModifyDate.AddMinutes(-5), DateTime.Now, null, tag))?.Value);
						var q = InfluxdbMaxQuantity - oldValue;
						if (q < 0)
						{
							q = 0;
						}
						quantity = q + Influxdbquantity;
						//quantity = InfluxdbMaxQuantity - oldValue + Influxdbquantity;
					}
					dataItemDetail.ItemValue = Influxdbquantity.ToString();
					dataItemDetail.Modify(dataItemDetail.ID, "OPC");
					updateDataItemDetails.Add(dataItemDetail);
				}
			}
			else
			{
				int maxSort = dataItemDetailEntities?.Select(x => x.SortCode)?.Max() ?? 0;
				SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
				{
					ItemId = dataItem.ID,
					ItemCode = dataItem.ItemCode,
					ItemName = equipmentCode,
					ItemValue = Influxdbquantity.ToString(),
					SortCode = ++maxSort,
					Enable = 1,
					Deleted = 0
				};
				dataItemDetailEntity.CreateCustomGuid("OPC");
				quantity = Influxdbquantity;
				addDataItemDetails.Add(dataItemDetailEntity);
			}
			result.response.OldQuantity = oldValue.ToString();
			result.response.CurrentQuantity = Influxdbquantity.ToString();
			result.response.Quantity = quantity;

			_unitOfWork.BeginTran();
			try
			{
				if (updateDataItemDetails?.Count > 0)
				{
					await _dal34.Update(updateDataItemDetails);
				}
				if (addDataItemDetails?.Count > 0)
				{
					await _dal34.Add(addDataItemDetails);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.msg = ex.Message;
				return result;
			}
			//var upResult = await UpdateAutoReportQuantity(reportType, equipment.ID, equipmentCode, tag);
			//if (!upResult.success)
			//{
			//	result.msg = upResult.msg;
			//	return result;
			//}
			//var quantity = upResult.response;
			if (quantity == 0)
			{
				result.msg = "本次执行对比上次增加数量为0";
				return result;
			}
			var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
			if (runOrder == null)
			{
				result.msg = $"当前设备{equipmentCode}未找到运行工单！";
				return result;
			}
			var productionOrder = await _dal5.FindEntity(x => x.ID == runOrder.ProductionOrderId);
			if (productionOrder == null)
			{
				result.msg = $"未找到productionOrder！";
				return result;
			}
			SappackorderEntity sapPackOrder = null;
			if (productionOrder.SapOrderType != "ZXH2")
			{
				sapPackOrder = await _dal9.FindEntity(x => x.Aufnr == productionOrder.ProductionOrderNo);
			}
			var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
			if (poSegmentRequirement == null)
			{
				result.msg = "未找到poSegmentRequirement！";
				return result;
			}
			var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
			if (sapSegment == null)
			{
				result.msg = "未找到sapSegment！";
				return result;
			}

			var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
			var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
			var autoReportMessages = new List<AutoReportMessage>();
			List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
			List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
			List<string> deleteIds = new List<string>();
			List<PoProducedActualEntity> poProducedActualEntities = new List<PoProducedActualEntity>();
			List<MaterialSubLotEntity> materialSubLotEntities = new List<MaterialSubLotEntity>();
			switch (reportType)
			{
				case "Consume":
					{
						var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
						var equipmentCodes = list.Select(x =>
						{
							return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
						});
						if (equipmentCodes == null)
						{
							equipmentCodes = new List<string>();
						}
						var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
						var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId);

						//获取Function
						MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
						var equipmentAllData = apiResult_equipmentAllData.response;
						if (equipmentAllData == null)
						{
							result.msg = "equipmentAllData为空,请检查MES配置";
							return result;
						}
						var equFunction = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Consume");
						var equipmentFunctionProperty = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MasterStorageTank");
						var equipmentFunctionProperty2 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AverageWeightLogsheet");
						var equipmentFunctionProperty3 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "AutoConsumeMaterialTypes");
						//var equipmentFunctionProperty4 = equFunction?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "ConsumeMaterialGroup");
						var v1 = equipmentFunctionProperty?.ActualValue ?? equipmentFunctionProperty?.DefaultValue ?? "";
						var v2 = equipmentFunctionProperty2?.ActualValue ?? equipmentFunctionProperty2?.DefaultValue ?? "";
						var v3 = equipmentFunctionProperty3?.ActualValue ?? equipmentFunctionProperty3?.DefaultValue ?? "";
						//var v4 = equipmentFunctionProperty4?.ActualValue ?? equipmentFunctionProperty4?.DefaultValue ?? "";
						var groupCodes = v3?.Split(';', StringSplitOptions.RemoveEmptyEntries)?.ToList();
						if (!groupCodes.Any())
						{
							result.msg = "AutoConsumeMaterialTypes为空,请检查MES配置";
							return result;
						}
						var dataItem_gdxhj1 = await _dal33.FindEntity(x => x.ItemCode == "GDXHJ1");
						var dataItemDetails_gdxhj1 = new List<string>();
						var unitIds_gdxhj1 = new List<string>();
						if (dataItem_gdxhj1 != null)
						{
							dataItemDetails_gdxhj1 = (await _dal34.FindList(x => x.ItemId == dataItem_gdxhj1.ID)).Select(x => x.ItemValue/*.ToUpper()*/).ToList();
							if (dataItemDetails_gdxhj1?.Count > 0)
							{
								unitIds_gdxhj1 = (await _unitDal.FindList(x => x.Deleted != 1 && x.Enable == 1 && dataItemDetails_gdxhj1.Contains(x.Name))).Select(x => x.ID).ToList();
							}
						}
						foreach (var item in poConsumeRequirements)
						{

							var material = await _dal2.FindEntity(x => x.ID == item.MaterialId);
							var autoReportMessage = new AutoReportMessage()
							{
								MaterialCode = material?.Code ?? item.MaterialId,
								MaterialName = material?.NAME ?? item.MaterialId,
								Quantity = 0,
								Success = true,
								Msg = "OK",
							};
							if (material == null)
							{
								autoReportMessage.Success = false;
								autoReportMessage.Msg = $"未找到物料";
								continue;
							}

							//物料类型为酱料时 要先生成储罐的产出记录 再生成罐装机的消耗记录
							if (material.Type == "ZSFG")
							{
								if (string.IsNullOrEmpty(v1))
								{
									autoReportMessage.Success = false;
									autoReportMessage.Msg = "属性MasterStorageTank为空";
								}
								else
								{
									var cgequipment = await _dal15.FindEntity(x => x.EquipmentCode == v1);
									if (cgequipment == null)
									{
										autoReportMessage.Success = false;
										autoReportMessage.Msg = $"未找到储罐：{v1}";
									}
									else
									{
										var cgRunOrder = (await _dal14.FindList(x => x.RunEquipmentId == cgequipment.ID && x.Status == "1")).OrderByDescending(x => x.CreateDate).FirstOrDefault();
										if (cgRunOrder == null)
										{
											autoReportMessage.Success = false;
											autoReportMessage.Msg = $"储罐：{v1}未找到运行工单";
										}
										else
										{
											var cgProductionOrder = await _dal5.FindEntity(x => x.ID == cgRunOrder.ProductionOrderId);
											if (cgProductionOrder == null)
											{
												autoReportMessage.Success = false;
												autoReportMessage.Msg = $"未找到工单";
											}
											else
											{
												var materialVersion = await _dal35.FindEntity(cgProductionOrder.MaterialVersionId);
												if (materialVersion == null)
												{
													autoReportMessage.Success = false;
													autoReportMessage.Msg = $"未找materialVersion";
												}
												else
												{
													var poProducedRequirements = await _dal27.FindList(x => x.ProductionOrderId == cgRunOrder.ProductionOrderId);
													if (poProducedRequirements == null || poProducedRequirements.Count == 0)
													{
														autoReportMessage.Success = false;
														autoReportMessage.Msg = $"未找poProducedRequirements";
													}
													else
													{
														//}
														//var productionMaterial = await _dal2.FindEntity(materialVersion.MaterialId);
														//if (productionMaterial == null)
														//{
														//	autoReportMessage.Success = false;
														//	autoReportMessage.Msg = $"未找productionMaterial";
														//}
														//else
														//{
														//if (productionMaterial.Code != material.Code)
														var poProducedRequirement = poProducedRequirements.Find(x => x.MaterialId == material.ID);
														if (poProducedRequirement == null)
														{
															autoReportMessage.Success = false;
															autoReportMessage.Msg = $"储罐工单的产品物料号和当前灌装机要消耗的酱料物料号不一致";
														}
														else
														{
															///要把v2作为表单名去调用logsheet接口获取每瓶平均重量
															var q = await GetAverageWeightLogsheet(equipment.ID, runOrder.BatchId, v2);
															//q.response = 0.26m;
															if (!q.success)
															{
																var dataItemDetail2 = await _dal34.FindEntity(x => x.ItemId == dataItem.ID && x.ItemName == equipmentCode);
																if (dataItemDetail2 != null)
																{
																	dataItemDetail2.ItemValue = oldValue.ToString();
																	//dataItemDetail2.Modify(dataItemDetail2.ID, "OPC");
																	await _dal34.Update(dataItemDetail2);
																}
																result.msg = q.msg;
																return result;
															}
															var cgconsumeQ = quantity * Convert.ToDecimal(q.response);
															if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
															{
																cgconsumeQ = Math.Floor(cgconsumeQ);
															}
															//var location = await GetProduceLocation2(cgequipment.ID);
															//if (location == null)
															//{
															//	autoReportMessage.Success = false;
															//	autoReportMessage.Msg = $"灌装机：{v1}未找到location";
															//}
															//var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
															//var equipmentStorages = api_equipmentStorages.response;
															//if (equipmentStorages == null || equipmentStorages.Count == 0)
															//{
															//	result.msg = "未找到EquipmentStorages";
															//	return result;
															//}
															//var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == cgequipment.ID);
															//if (equipmentStorage == null)
															//{
															//	result.msg = "未找到EquipmentStorage";
															//	return result;
															//}
															//var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
															//var equipmentRequirement = api_equipmentRequirement.response;
															//if (equipmentRequirement == null)
															//{
															//	result.msg = "未找到equipmentRequirement";
															//	return result;
															//}
															//var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
															//var sapStorageTypes = api_sapStorageTypes.response;
															//if (sapStorageTypes == null || sapStorageTypes.Count == 0)
															//{
															//	result.msg = "未找到sapStorageTypes";
															//	return result;
															//}
															//var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
															//if (sapStorageType == null)
															//{
															//	result.msg = "未找到sapStorageType";
															//	return result;
															//}
															var sublotId = "";
															////生成追溯码
															//if (sapStorageType.SuManaged == 1)
															{
																var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
																var ssccString = ssccStrings.response;
																var materialsub = new MaterialSubLotEntity();
																materialsub.SubLotId = ssccString;
																materialsub.Type = "0";
																materialsub.ExternalStatus = "3";
																materialsub.CreateCustomGuid("OPC");
																materialSubLotEntities.Add(materialsub);
																sublotId = materialsub.ID.ToString();
															}

															//写入produce_actual表
															var poProducedActualEntity = new PoProducedActualEntity();
															poProducedActualEntity.ProductExecutionId = cgRunOrder.ID;
															poProducedActualEntity.ProductionOrderId = cgRunOrder.ProductionOrderId;
															poProducedActualEntity.PoProducedRequirementId = poProducedRequirement.ID;
															poProducedActualEntity.LotId = cgRunOrder.LotId;
															poProducedActualEntity.LotStatus = 2;
															poProducedActualEntity.SubLotId = sublotId;
															poProducedActualEntity.SubLotStatus = 3;
															poProducedActualEntity.SourceType = 1;
															poProducedActualEntity.EquipmentId = cgequipment.ID;
															poProducedActualEntity.DesinationEquipmentId = cgequipment.ID;
															poProducedActualEntity.UnitId = poProducedRequirement.UnitId;
															poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
															poProducedActualEntity.Quantity = cgconsumeQ;
															poProducedActualEntity.CreateCustomGuid("OPC");
															poProducedActualEntities.Add(poProducedActualEntity);

															//写入consumed_actual表
															var poConsumeActualEntity = new PoConsumeActualEntity
															{
																ProductionOrderId = runOrder.ProductionOrderId,
																PoConsumeRequirementId = item.ID,
																ProductExecutionId = runOrder.ID,
																EquipmentId = runOrder.RunEquipmentId,
																SourceEquipmentId = poProducedActualEntity.DesinationEquipmentId,
																Quantity = cgconsumeQ,
																UnitId = item.UnitId,
																LotId = runOrder.LotId,
																SubLotId = sublotId,
																SubLotStatus = 3,
																StorageBin = "MFG3",
																//StorageLocation = "",
																//ContainerId = inventoryModel.ContainerId,
																TeamId = "",
																ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
																ReasonCode = "",
																Comment = "",
																Deleted = 0,
																SendExternal = item.Quantity == 0 ? 1 : 0
															};
															autoReportMessage.Quantity += cgconsumeQ;
															poConsumeActualEntity.CreateCustomGuid("OPC");
															poConsumeActualEntities.Add(poConsumeActualEntity);
														}
													}
												}
											}
										}
									}
								}
							}
							else
							{
								//var sapSegmentMaterialStep = sapSegmentMaterialSteps?.Find(x => x.MaterialId == item.MaterialId);
								//if (sapSegmentMaterialStep != null)
								//{
								//	if (sapSegmentMaterialStep.ParentQuantity > 0)
								//	{
								var rate = item.Quantity.Value / productionOrder.PlanQty;
								if (productionOrder.SapOrderType != "ZXH2" && sapPackOrder != null)
								{
									rate = item.Quantity.Value / sapPackOrder.MngPuo;
								}
								//var rate = sapSegmentMaterialStep.Quantity / sapSegmentMaterialStep.ParentQuantity;
								var quantity2 = Math.Round(quantity * rate, 3);
								var locations = equipmentCodes;
								//var materialGroups = await GetMaterialGroups(item.MaterialId);
								////判断物料所在的物料组是否是当前设备属性中配置的组，不是则跳至下一个物料
								//if (!materialGroups.Exists(x => groupCodes.Contains(x.MaterialGroupName)))
								//{
								//	autoReportMessage.Success = false;
								//	autoReportMessage.Msg = $"物料组：{v3}未找到该物料";
								//	continue;
								//}
								//var locations_FIFO = new List<string>();
								//var locations = new List<string>();
								//foreach (var item1 in equipments)
								//{
								//	var equipmentStorage = await _dal31.FindEntity(x => x.EquipmentId == item1.ID);
								//	if (equipmentStorage == null)
								//	{
								//		continue;
								//	}
								//	var equipmentRequirement = await _dal32.FindEntity(equipmentStorage.EquipmentRequirementId);
								//	if (equipmentRequirement == null)
								//	{
								//		continue;
								//	}
								//	if (materialGroups?.Exists(x => x.MaterialGroupName == equipmentRequirement.Code) == true)
								//	{
								//		if (equipmentStorage.OutgoingStorageMode == "FIFO")
								//		{
								//			locations_FIFO.Add(item1.EquipmentCode);
								//		}
								//		else
								//		{
								//			locations.Add(item1.EquipmentCode);
								//		}
								//	}
								//}
								//var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations_FIFO.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
								var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
								var inventoryQuantity = inventorylistings?.Sum(x => x.Quantity) ?? 0;
								if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
								{
									quantity2 = Math.Floor(quantity2);
								}
								if (inventoryQuantity < quantity2)
								{
									autoReportMessage.Success = false;
									autoReportMessage.Msg = "库存数量不足";
								}
								else
								{
									var sublotIds = inventorylistings.Select(x => x.SlotId).Distinct().ToList() ?? new List<string>();
									var materialTransfers = await _materialTransferDal.FindList(x => sublotIds.Contains(x.NewSublotId) && (x.Comment == "WCS上料-转移" || x.Comment == "上料-转移" || x.Comment == "WCS上料-收货"), x => x.CreateDate);
									// 根据materialTransfers中的CreateDate对inventorylistings进行排序
									var sortedInventorylistings = inventorylistings.OrderBy(item =>
									{
										var order = materialTransfers.FirstOrDefault(o => o.NewSublotId == item.SlotId);
										return order != null ? order.CreateDate : item.CreateDate;
									});
									SerilogServer.LogDebug($"【CheckInventory】" +
														   $"{Environment.NewLine}InventoryListings：{FAJsonConvert.ToJson(inventorylistings)}" +
														   $"{Environment.NewLine}MaterialTransfers：{FAJsonConvert.ToJson(materialTransfers)}" +
														   $"{Environment.NewLine}SortedInventoryListings：{FAJsonConvert.ToJson(sortedInventorylistings)}", "AutoReport");
									foreach (var inventoryModel in sortedInventorylistings)
									{
										if (quantity2 <= 0)
										{
											break;
										}
										decimal consumeQuantity = quantity2;
										if (item.UnitId != null && unitIds_gdxhj1.Contains(item.UnitId))
										{
											consumeQuantity = Math.Floor(consumeQuantity);
										}
										if (quantity2 >= inventoryModel.Quantity)
										{
											deleteIds.Add(inventoryModel.InventoryId);
											consumeQuantity = inventoryModel.Quantity.Value;
										}
										else
										{
											var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
											if (materialInventory != null)
											{
												materialInventory.Quantity -= Math.Round(Convert.ToDecimal(consumeQuantity), 4); //consumeQuantity;
												materialInventory.Modify(materialInventory.ID, "OPC");
												materialInventoryEntities.Add(materialInventory);
											}
										}
										quantity2 -= consumeQuantity;
										//写入consumed_actual表
										var poConsumeActualEntity = new PoConsumeActualEntity
										{
											ProductionOrderId = runOrder.ProductionOrderId,
											PoConsumeRequirementId = item.ID,
											ProductExecutionId = runOrder.ID,
											EquipmentId = runOrder.RunEquipmentId,
											SourceEquipmentId = inventoryModel.EquipmentId,
											Quantity = consumeQuantity,
											UnitId = item.UnitId,
											LotId = inventoryModel.LotId,
											SubLotId = inventoryModel.SlotId,
											SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
											StorageBin = inventoryModel.LocationS,
											StorageLocation = inventoryModel.LocationFcode,
											//ContainerId = inventoryModel.ContainerId,
											TeamId = "",
											ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
											ReasonCode = "",
											Comment = "",
											Deleted = 0,
											SendExternal = item.Quantity == 0 ? 1 : 0
										};
										autoReportMessage.Quantity += consumeQuantity;
										poConsumeActualEntity.CreateCustomGuid("OPC");
										poConsumeActualEntities.Add(poConsumeActualEntity);
									}
								}
								{
									//if (quantity2 > 0)
									//{
									//	foreach (var location in locations)
									//	{
									//		if (quantity2 <= 0)
									//		{
									//			break;
									//		}
									//		var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && x.LocationFcode == location, x => x.UpdateTimeStamp);
									//		foreach (var inventoryModel in inventorylistings)
									//		{
									//			if (quantity2 <= 0)
									//			{
									//				break;
									//			}
									//			decimal consumeQuantity = quantity2;
									//			if (quantity2 >= inventoryModel.Quantity)
									//			{
									//				deleteIds.Add(inventoryModel.InventoryId);
									//				consumeQuantity = inventoryModel.Quantity;
									//			}
									//			else
									//			{
									//				var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
									//				if (materialInventory != null)
									//				{
									//					materialInventory.Quantity -= consumeQuantity;
									//					materialInventory.Modify(materialInventory.ID, _user.Name);
									//					materialInventoryEntities.Add(materialInventory);
									//				}
									//			}
									//			quantity2 -= consumeQuantity;
									//			//写入consumed_actual表
									//			var poConsumeActualEntity = new PoConsumeActualEntity
									//			{
									//				ProductionOrderId = runOrder.ProductionOrderId,
									//				PoConsumeRequirementId = item.ID,
									//				ProductExecutionId = runOrder.ID,
									//              EquipmentId = runOrder.RunEquipmentId,
									//           	SourceEquipmentId = inventoryModel.EquipmentId ,
									//				Quantity = consumeQuantity,
									//				UnitId = item.UnitId,
									//				LotId = inventoryModel.LotId,
									//				SubLotId = inventoryModel.SubLotId,
									//				SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
									//				StorageBin = inventoryModel.LocationS,
									//				StorageLocation = location,
									//				//ContainerId = inventoryModel.ContainerId,
									//				TeamId = "",
									//				ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
									//				ReasonCode = "",
									//				Comment = "",
									//				Deleted = 0
									//			};
									//			autoReportMessage.Quantity += consumeQuantity;
									//			poConsumeActualEntity.CreateCustomGuid("OPC");
									//			poConsumeActualEntities.Add(poConsumeActualEntity);
									//		}
									//	}
									//}
									//	}
									//}
								}
							}
							autoReportMessages.Add(autoReportMessage);
						}
						_unitOfWork.BeginTran();
						try
						{
							if (materialSubLotEntities?.Count > 0)
							{
								await _dal16.Add(materialSubLotEntities);
							}
							if (poProducedActualEntities?.Count > 0)
							{
								await _dal17.Add(poProducedActualEntities);
							}
							if (poConsumeActualEntities?.Count > 0)
							{
								await _dal28.Add(poConsumeActualEntities);
							}
							if (materialInventoryEntities?.Count > 0)
							{
								await _dal19.Update(materialInventoryEntities);
							}
							if (deleteIds?.Count > 0)
							{
								await _dal19.DeleteByIds(deleteIds.ToArray());
							}
							_unitOfWork.CommitTran();
						}
						catch (Exception ex)
						{
							result.msg = ex.Message;
							_unitOfWork.RollbackTran();
							return result;
						}
						break;
					}
				case "Produce":
					{
						var location = await GetProduceLocation(equipment.ID);
						//var location = await GetProduceLocation2(equipment.ID);
						if (location == null)
						{
							result.msg = "未找到location";
							return result;
						}
						var poProducedRequirements = await _dal27.FindList(x => x.ID == runOrder.PoProducedRequirementId);
						if (poProducedRequirements?.Count > 0)
						{
							var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
							var equipmentStorages = api_equipmentStorages.response;
							if (equipmentStorages == null || equipmentStorages.Count == 0)
							{
								result.msg = "未找到EquipmentStorages";
								return result;
							}
							var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == location.ID);
							if (equipmentStorage == null)
							{
								result.msg = "未找到EquipmentStorage";
								return result;
							}
							var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
							var equipmentRequirement = api_equipmentRequirement.response;
							if (equipmentRequirement == null)
							{
								result.msg = "未找到equipmentRequirement";
								return result;
							}
							var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
							var sapStorageTypes = api_sapStorageTypes.response;
							if (sapStorageTypes == null || sapStorageTypes.Count == 0)
							{
								result.msg = "未找到sapStorageTypes";
								return result;
							}
							var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
							if (sapStorageType == null)
							{
								result.msg = "未找到sapStorageType";
								return result;
							}
							foreach (var item in poProducedRequirements)
							{
								var rate = item.Quantity.Value / productionOrder.PlanQty;
								var quantity2 = Math.Round(quantity * rate, 3);
								var material = await _dal2.FindEntity(item.MaterialId);
								var autoReportMessage = new AutoReportMessage()
								{
									MaterialCode = material?.Code ?? item.MaterialId,
									MaterialName = material?.NAME ?? item.MaterialId,
									Quantity = quantity2,
									Success = true,
									Msg = "OK",
								};
								var sublotId = "";
								//生成追溯码
								if (sapStorageType.SuManaged == 1)
								{
									var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
									var ssccString = ssccStrings.response;
									var materialsub = new MaterialSubLotEntity();
									materialsub.SubLotId = ssccString;
									materialsub.Type = "0";
									materialsub.ExternalStatus = "3";
									materialsub.CreateCustomGuid("OPC");
									materialSubLotEntities.Add(materialsub);
									sublotId = materialsub.ID.ToString();
								}
								var poProducedActualEntity = new PoProducedActualEntity();
								poProducedActualEntity.ProductExecutionId = runOrder.ID;
								poProducedActualEntity.ProductionOrderId = runOrder.ProductionOrderId;
								poProducedActualEntity.PoProducedRequirementId = item.ID;
								poProducedActualEntity.LotId = runOrder.LotId;
								poProducedActualEntity.LotStatus = 2;
								poProducedActualEntity.SubLotId = sublotId;
								poProducedActualEntity.SubLotStatus = 3;
								poProducedActualEntity.SourceType = 1;
								poProducedActualEntity.EquipmentId = equipment.ID;
								poProducedActualEntity.DesinationEquipmentId = location.ID;
								poProducedActualEntity.UnitId = item.UnitId;
								poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
								poProducedActualEntity.Quantity = quantity2;
								poProducedActualEntity.CreateCustomGuid("OPC");
								poProducedActualEntities.Add(poProducedActualEntity);
								if (equipmentRequirement?.ManageInventory == "1")
								{
									var materialInventoryEntity = new MaterialInventoryEntity();
									materialInventoryEntity.LotId = runOrder.LotId;
									materialInventoryEntity.SublotId = sublotId;
									materialInventoryEntity.Quantity = Math.Round(Convert.ToDecimal(quantity2), 4);// quantity2;
									materialInventoryEntity.QuantityUomId = item.UnitId;
									materialInventoryEntity.StorageLocation = location.EquipmentCode;
									materialInventoryEntity.EquipmentId = location.ID;
									materialInventoryEntity.IsPrechecked = "FALSE";
									materialInventoryEntity.ContainerId = "";
									//materialInventoryEntity.ProductionRequestId = runOrder.ProductionOrderId;
									materialInventoryEntity.SortOrder = 0;
									materialInventoryEntity.CreateCustomGuid("OPC");
									materialInventoryEntities.Add(materialInventoryEntity);
								}
								autoReportMessages.Add(autoReportMessage);
							}
							_unitOfWork.BeginTran();
							try
							{
								if (materialSubLotEntities?.Count > 0)
								{
									await _dal16.Add(materialSubLotEntities);
								}
								if (poProducedActualEntities?.Count > 0)
								{
									await _dal17.Add(poProducedActualEntities);
								}
								if (materialInventoryEntities?.Count > 0)
								{
									await _dal19.Add(materialInventoryEntities);
								}
								_unitOfWork.CommitTran();
							}
							catch (Exception ex)
							{
								result.msg = ex.Message;
								_unitOfWork.RollbackTran();
								return result;
							}
						}
						break;
					}
				default:
					result.msg = "reportType错误，请检查任务配置参数！";
					return result;
					break;
			}
			var r = autoReportMessages.Select(x => $"{x.MaterialCode};{x.MaterialName};{x.Quantity};{x.Success};{x.Msg}").ToList();
			result.response.AutoReportMessages = r;
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<decimal>> UpdateAutoReportQuantity(string reportType, string equipmentId, string equipmentCode, string tag)
		{
			var result = new MessageModel<decimal>
			{
				response = 0,
				msg = "操作失败！",
				success = false,
			};
			var propertyResult = await GetFunctionPropertyValue(equipmentId, reportType, $"{reportType}TagAddress");
			tag = !string.IsNullOrEmpty(tag) ? tag : propertyResult?.response;
			if (string.IsNullOrEmpty(tag))
			{
				return result;
			}
			decimal quantity = 0;
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
			//从influxdb取数
			try
			{
				var dataItem = await _dal33.FindEntity(x => x.ItemCode == $"Auto{reportType}Time");
				if (dataItem != null)
				{
					decimal Influxdbquantity = Convert.ToDecimal((await _IInfluxDbServices.GetLastInfluxData(DateTime.MinValue, DateTime.Now.AddDays(1), null, tag))?.Value);
					var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
					var dataItemDetail = dataItemDetailEntities.Find(x => x.ItemName == equipmentCode);
					if (dataItemDetail != null)
					{
						var oldValue = Convert.ToDecimal(dataItemDetail.ItemValue);
						if (oldValue > Influxdbquantity)
						{
							oldValue = 0;
						}
						quantity = Influxdbquantity - oldValue;
						dataItemDetail.ItemValue = Influxdbquantity.ToString();
						dataItemDetail.Modify(dataItemDetail.ID, "OPC");
						updateDataItemDetails.Add(dataItemDetail);
					}
					else
					{
						int maxSort = dataItemDetailEntities.Select(x => x.SortCode)?.Max() ?? 0;
						SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
						{
							ItemId = dataItem.ID,
							ItemCode = dataItem.ItemCode,
							ItemName = equipmentCode,
							ItemValue = Influxdbquantity.ToString(),
							SortCode = ++maxSort,
							Enable = 1,
							Deleted = 0
						};
						dataItemDetailEntity.CreateCustomGuid("OPC");
						quantity = Influxdbquantity;
						addDataItemDetails.Add(dataItemDetailEntity);
					}
				}
				result.response = quantity;
				_unitOfWork.BeginTran();
				try
				{
					if (updateDataItemDetails?.Count > 0)
					{
						await _dal34.Update(updateDataItemDetails);
					}
					if (addDataItemDetails?.Count > 0)
					{
						await _dal34.Add(addDataItemDetails);
					}
				}
				catch (Exception ex)
				{
					_unitOfWork.RollbackTran();
					result.msg = ex.Message;
					return result;
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}

		//public async Task<MessageModel<List<AutoReportMessage>>> AutoReport_V1(string reportType, string equipmentCode, string tag)
		//{
		//	var result = new MessageModel<List<AutoReportMessage>>
		//	{
		//		msg = "操作失败！",
		//		success = false,
		//		response = new List<AutoReportMessage>()
		//	};
		//	var equipment = await _dal15.FindEntity(x => x.EquipmentCode == equipmentCode);
		//	if (equipment == null)
		//	{
		//		result.msg = "未找到Equipment,请检查配置是否正确！";
		//		return result;
		//	}
		//	var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
		//	if (runOrder == null)
		//	{
		//		result.msg = $"当前设备{equipmentCode}未找到运行工单！";
		//		return result;
		//	}
		//	var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
		//	if (poSegmentRequirement == null)
		//	{
		//		result.msg = "未找到poSegmentRequirement！";
		//		return result;
		//	}
		//	var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
		//	if (sapSegment == null)
		//	{
		//		result.msg = "未找到sapSegment！";
		//		return result;
		//	}
		//	MessageModel<List<SapBomPhaseInjectionEntity>> apiResult_sapBomPhaseInjections = await HttpHelper.PostAsync<List<SapBomPhaseInjectionEntity>>("DFM", "api/SapBomPhaseInjection/GetList", _user.GetToken(), new { });
		//	if (apiResult_sapBomPhaseInjections == null || apiResult_sapBomPhaseInjections.success != true)
		//	{
		//		result.msg = apiResult_sapBomPhaseInjections.msg;
		//		return result;
		//	}
		//	var materialIds = apiResult_sapBomPhaseInjections.response?.Select(x => x.ProductMaterialId).ToList();
		//	var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
		//	var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
		//	decimal quantity = 0;
		//	decimal Influxdbquantity = 0;
		//	//从influxdb取数
		//	try
		//	{
		//		DateTime start = DateTime.MinValue;
		//		DateTime end = DateTime.Now;
		//		//start = end.AddDays(1);
		//		var exp = Expressionable.Create<TagValue>()
		//		.And(it => it.Tag == tag)
		//		.And(it => it.Time >= start && it.Time < end)
		//		.ToExpression();
		//		var lastNewData = _influxDbHelper.QueryBy<TagValue, DateTime>(exp, m => m.Time, 0, 1, out int count);//倒序取最新的一条
		//		Influxdbquantity = Convert.ToDecimal(lastNewData.FirstOrDefault().Value);
		//	}
		//	catch (Exception ex)
		//	{
		//		result.msg = ex.Message;
		//		return result;
		//	}
		//	List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
		//	List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
		//	List<string> deleteIds = new List<string>();
		//	List<PoProducedActualEntity> poProducedActualEntities = new List<PoProducedActualEntity>();
		//	List<MaterialSubLotEntity> materialSubLotEntities = new List<MaterialSubLotEntity>();
		//	List<SEFA.DFM.Model.Models.DataItemDetailEntity> addDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
		//	List<SEFA.DFM.Model.Models.DataItemDetailEntity> updateDataItemDetails = new List<SEFA.DFM.Model.Models.DataItemDetailEntity>();
		//	switch (reporttType)
		//	{
		//		case "Consume":
		//			{
		//				var dataItem = await _dal33.FindEntity(x => x.ItemCode == "AutoConsumeTime");
		//				if (dataItem == null)
		//				{
		//					result.msg = "未找到AutoConsumeTime！";
		//					return result;
		//				}
		//				var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
		//				if (dataItemDetailEntities == null)
		//				{
		//					result.msg = "未找到dataItemDetailEntities！";
		//					return result;
		//				}
		//				int maxSort = dataItemDetailEntities.Select(x => x.SortCode)?.Max() ?? 0;
		//				var dataItemDetail = dataItemDetailEntities.Find(x => x.ItemName == equipmentCode);
		//				if (dataItemDetail != null)
		//				{
		//					dataItemDetail.ItemValue = Influxdbquantity.ToString();
		//					dataItemDetail.Modify(dataItemDetail.ID, "OPC");
		//					updateDataItemDetails.Add(dataItemDetail);
		//					quantity = Influxdbquantity - Convert.ToDecimal(dataItemDetail.ItemValue);
		//				}
		//				else
		//				{
		//					SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
		//					{
		//						ItemId = dataItem.ID,
		//						ItemCode = dataItem.ItemCode,
		//						ItemName = equipmentCode,
		//						ItemValue = Influxdbquantity.ToString(),
		//						SortCode = ++maxSort,
		//						Enable = 1,
		//						Deleted = 0
		//					};
		//					dataItemDetailEntity.CreateCustomGuid("OPC");
		//					quantity = Influxdbquantity;
		//					addDataItemDetails.Add(dataItemDetailEntity);
		//				}
		//				var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
		//				var equipmentCodes = list.Select(x =>
		//				{
		//					return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
		//				});
		//				if (equipmentCodes == null)
		//				{
		//					equipmentCodes = new List<string>();
		//				}
		//				var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
		//				materialIds = materialIds == null ? new List<string>() : materialIds;
		//				var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId && !materialIds.Contains(x.MaterialId));
		//				var sapSegmentMaterials = await _dal24.FindList(x => x.SapSegmentId == poSegmentRequirement.SegmentId);
		//				var sapSegmentMaterialIds = sapSegmentMaterials?.Select(x => x.ID);
		//				if (sapSegmentMaterialIds == null)
		//				{
		//					result.msg = "未找到sapSegmentMaterialIds！";
		//					return result;
		//				}
		//				var sapSegmentMaterialSteps = await _dal25.FindList(x => sapSegmentMaterialIds.Contains(x.SapSegmentMaterialId));
		//				foreach (var item in poConsumeRequirements)
		//				{
		//					var sapSegmentMaterialStep = sapSegmentMaterialSteps?.Find(x => x.MaterialId == item.MaterialId);
		//					if (sapSegmentMaterialStep != null)
		//					{
		//						if (sapSegmentMaterialStep.ParentQuantity > 0)
		//						{
		//							var rate = sapSegmentMaterialStep.Quantity / sapSegmentMaterialStep.ParentQuantity;
		//							var quantity2 = Math.Round(quantity * rate, 3);
		//							var materialGroups = await GetMaterialGroups(item.MaterialId);
		//							var locations_FIFO = new List<string>();
		//							var locations = new List<string>();
		//							foreach (var item1 in equipments)
		//							{
		//								var equipmentStorage = await _dal31.FindEntity(x => x.EquipmentId == item1.ID);
		//								if (equipmentStorage == null)
		//								{
		//									continue;
		//								}
		//								var equipmentRequirement = await _dal32.FindEntity(equipmentStorage.EquipmentRequirementId);
		//								if (equipmentRequirement == null)
		//								{
		//									continue;
		//								}
		//								if (materialGroups?.Exists(x => x.MaterialGroupName == equipmentRequirement.Code) == true)
		//								{
		//									if (equipmentStorage.OutgoingStorageMode == "FIFO")
		//									{
		//										locations_FIFO.Add(item1.EquipmentCode);
		//									}
		//									else
		//									{
		//										locations.Add(item1.EquipmentCode);
		//									}
		//								}
		//							}
		//							foreach (var location in locations_FIFO)
		//							{
		//								if (quantity2 <= 0)
		//								{
		//									break;
		//								}
		//								var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId) || x.BatchId == runOrder.BatchId) && x.LocationFcode == location, x => x.UpdateTimeStamp);
		//								foreach (var inventoryModel in inventorylistings)
		//								{
		//									if (quantity2 <= 0)
		//									{
		//										break;
		//									}
		//									decimal consumeQuantity = quantity2;
		//									if (quantity2 >= inventoryModel.Quantity)
		//									{
		//										deleteIds.Add(inventoryModel.InventoryId);
		//										consumeQuantity = inventoryModel.Quantity;
		//									}
		//									else
		//									{
		//										var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
		//										if (materialInventory != null)
		//										{
		//											materialInventory.Quantity -= consumeQuantity;
		//											materialInventory.Modify(materialInventory.ID, "OPC");
		//											materialInventoryEntities.Add(materialInventory);
		//										}
		//									}
		//									quantity2 -= consumeQuantity;
		//									//写入consumed_actual表
		//									var poConsumeActualEntity = new PoConsumeActualEntity
		//									{
		//										ProductionOrderId = runOrder.ProductionOrderId,
		//										PoConsumeRequirementId = item.ID,
		//										ProductExecutionId = runOrder.ID,
		//										EquipmentId = runOrder.RunEquipmentId,
		//										SourceEquipmentId = runOrder.RunEquipmentId,
		//										Quantity = consumeQuantity,
		//										UnitId = item.UnitId,
		//										LotId = inventoryModel.LotId,
		//										SubLotId = inventoryModel.SubLotId,
		//										SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
		//										StorageBin = inventoryModel.LocationS,
		//										StorageLocation = location,
		//										//ContainerId = inventoryModel.ContainerId,
		//										TeamId = "",
		//										ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
		//										ReasonCode = "",
		//										Comment = "",
		//										Deleted = 0
		//									};
		//									poConsumeActualEntity.CreateCustomGuid("OPC");
		//									poConsumeActualEntities.Add(poConsumeActualEntity);
		//								}
		//							}
		//							if (quantity2 > 0)
		//							{
		//								foreach (var location in locations)
		//								{
		//									if (quantity2 <= 0)
		//									{
		//										break;
		//									}
		//									var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId) || x.BatchId == runOrder.BatchId) && x.LocationFcode == location, x => x.UpdateTimeStamp);
		//									foreach (var inventoryModel in inventorylistings)
		//									{
		//										if (quantity2 <= 0)
		//										{
		//											break;
		//										}
		//										decimal consumeQuantity = quantity2;
		//										if (quantity2 >= inventoryModel.Quantity)
		//										{
		//											deleteIds.Add(inventoryModel.InventoryId);
		//											consumeQuantity = inventoryModel.Quantity;
		//										}
		//										else
		//										{
		//											var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
		//											if (materialInventory != null)
		//											{
		//												materialInventory.Quantity -= consumeQuantity;
		//												materialInventory.Modify(materialInventory.ID, _user.Name);
		//												materialInventoryEntities.Add(materialInventory);
		//											}
		//										}
		//										quantity2 -= consumeQuantity;
		//										//写入consumed_actual表
		//										var poConsumeActualEntity = new PoConsumeActualEntity
		//										{
		//											ProductionOrderId = runOrder.ProductionOrderId,
		//											PoConsumeRequirementId = item.ID,
		//											ProductExecutionId = runOrder.ID,
		//											EquipmentId = runOrder.RunEquipmentId,
		//											SourceEquipmentId = runOrder.RunEquipmentId,
		//											Quantity = consumeQuantity,
		//											UnitId = item.UnitId,
		//											LotId = inventoryModel.LotId,
		//											SubLotId = inventoryModel.SubLotId,
		//											SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
		//											StorageBin = inventoryModel.LocationS,
		//											StorageLocation = location,
		//											//ContainerId = inventoryModel.ContainerId,
		//											TeamId = "",
		//											ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
		//											ReasonCode = "",
		//											Comment = "",
		//											Deleted = 0
		//										};
		//										poConsumeActualEntity.CreateCustomGuid("OPC");
		//										poConsumeActualEntities.Add(poConsumeActualEntity);
		//									}
		//								}
		//							}
		//						}
		//					}
		//					result.response.Add(autoReportMessage);
		//				}
		//				_unitOfWork.BeginTran();
		//				try
		//				{
		//					if (poConsumeActualEntities?.Count > 0)
		//					{
		//						await _dal28.Add(poConsumeActualEntities);
		//					}
		//					if (materialInventoryEntities?.Count > 0)
		//					{
		//						await _dal19.Update(materialInventoryEntities);
		//					}
		//					if (deleteIds?.Count > 0)
		//					{
		//						await _dal19.DeleteByIds(deleteIds.ToArray());
		//					}
		//					if (updateDataItemDetails?.Count > 0)
		//					{
		//						await _dal34.Update(updateDataItemDetails);
		//					}
		//					if (addDataItemDetails?.Count > 0)
		//					{
		//						await _dal34.Add(addDataItemDetails);
		//					}
		//				}
		//				catch (Exception ex)
		//				{
		//					result.msg = ex.Message;
		//					_unitOfWork.RollbackTran();
		//					return result;
		//				}
		//				_unitOfWork.CommitTran();
		//				break;
		//			}
		//		case "Produce":
		//			{
		//				var dataItem = await _dal33.FindEntity(x => x.ItemCode == "AutoProduceTime");
		//				if (dataItem == null)
		//				{
		//					result.msg = "未找到AutoProduceTime！";
		//					return result;
		//				}
		//				var dataItemDetailEntities = await _dal34.FindList(x => x.ItemId == dataItem.ID);
		//				if (dataItemDetailEntities == null)
		//				{
		//					result.msg = "未找到dataItemDetailEntities！";
		//					return result;
		//				}
		//				int maxSort = dataItemDetailEntities.Select(x => x.SortCode)?.Max() ?? 0;
		//				var dataItemDetail = dataItemDetailEntities.Find(x => x.ItemName == equipmentCode);
		//				if (dataItemDetail != null)
		//				{
		//					dataItemDetail.ItemValue = Influxdbquantity.ToString();
		//					dataItemDetail.Modify(dataItemDetail.ID, "OPC");
		//					updateDataItemDetails.Add(dataItemDetail);
		//					quantity = Influxdbquantity - Convert.ToDecimal(dataItemDetail.ItemValue);
		//				}
		//				else
		//				{
		//					SEFA.DFM.Model.Models.DataItemDetailEntity dataItemDetailEntity = new DFM.Model.Models.DataItemDetailEntity()
		//					{
		//						ItemId = dataItem.ID,
		//						ItemCode = dataItem.ItemCode,
		//						ItemName = equipmentCode,
		//						ItemValue = Influxdbquantity.ToString(),
		//						SortCode = ++maxSort,
		//						Enable = 1,
		//						Deleted = 0
		//					};
		//					dataItemDetailEntity.CreateCustomGuid("OPC");
		//					quantity = Influxdbquantity;
		//					addDataItemDetails.Add(dataItemDetailEntity);
		//				}
		//				var produceNodeDefault = await _dal29.FindEntity(x => x.EquipmentId == equipmentId && x.ActionCode == "Production" && x.PropertyCode == "Is Default" && (x.PropertyValue == "1" || (string.IsNullOrEmpty(x.PropertyValue) && x.DefaultValue == "1")));
		//				var produceNodeDefault = await _dal29.FindEntity(x => x.EquipmentId == equipment.ID && x.ActionCode == "Production" && x.PropertyCode == "Is Default");
		//				if (produceNodeDefault == null)
		//				{
		//					result.msg = "未找到Production Is Default";
		//					return result;
		//				}
		//				var produceNode = await _dal29.FindEntity(x => x.EquipmentId == equipment.ID && x.ActionCode == "Production" && x.PropertyCode == "Associated Node" && x.EquipmnetActionId == produceNodeDefault.EquipmnetActionId);
		//				if (produceNode == null)
		//				{
		//					result.msg = "未找到Production Associated Node";
		//					return result;
		//				}
		//				var produceNode_equipmentCode = !string.IsNullOrEmpty(produceNode.PropertyValue) ? produceNode.PropertyValue : produceNode.DefaultValue;
		//				var produceNode_equipment = await _dal15.FindEntity(x => produceNode_equipmentCode == x.EquipmentCode);
		//				if (produceNode_equipment == null)
		//				{
		//					result.msg = "未找到Production Equipment";
		//					return result;
		//				}
		//				var locationId = produceNode_equipment.ID;
		//				materialIds = materialIds == null ? new List<string>() : materialIds;
		//				var poProducedRequirements = await _dal27.FindList(x => x.ID == runOrder.PoProducedRequirementId && !materialIds.Contains(x.MaterialId));
		//				if (poProducedRequirements?.Count > 0)
		//				{
		//					var api_equipmentStorages = await HttpHelper.PostAsync<List<DFM.Model.Models.EquipmentStorageEntity>>("DFM", "api/EquipmentStorage/GetList", _user.GetToken(), new { });
		//					var equipmentStorages = api_equipmentStorages.response;
		//					if (equipmentStorages == null || equipmentStorages.Count == 0)
		//					{
		//						result.msg = "未找到EquipmentStorages";
		//						return result;
		//					}
		//					var equipmentStorage = equipmentStorages.Find(x => x.EquipmentId == locationId);
		//					if (equipmentStorage == null)
		//					{
		//						result.msg = "未找到EquipmentStorage";
		//						return result;
		//					}
		//					var api_equipmentRequirement = await HttpHelper.GetApiAsync<EquipmentRequirementEntity>("DFM", "api/EquipmentRequirement/GetEntity/" + equipmentStorage.EquipmentRequirementId, _user.GetToken(), null);
		//					var equipmentRequirement = api_equipmentRequirement.response;
		//					if (equipmentRequirement == null)
		//					{
		//						result.msg = "未找到equipmentRequirement";
		//						return result;
		//					}
		//					var api_sapStorageTypes = await HttpHelper.PostAsync<List<DFM.Model.Models.SapStorageTypeEntity>>("DFM", "api/SapStorageType/GetList", _user.GetToken(), new { StroageType = equipmentRequirement.Code });
		//					var sapStorageTypes = api_sapStorageTypes.response;
		//					if (sapStorageTypes == null || sapStorageTypes.Count == 0)
		//					{
		//						result.msg = "未找到sapStorageTypes";
		//						return result;
		//					}
		//					var sapStorageType = sapStorageTypes.Find(x => x.StroageType == equipmentRequirement.Code);
		//					if (sapStorageType == null)
		//					{
		//						result.msg = "未找到sapStorageType";
		//						return result;
		//					}
		//					foreach (var item in poProducedRequirements)
		//					{
		//						var sublotId = "";
		//						//生成追溯码
		//						if (sapStorageType.SuManaged == 1)
		//						{
		//							var ssccStrings = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", _user.GetToken(), new { });
		//							var ssccString = ssccStrings.response;
		//							var materialsub = new MaterialSubLotEntity();
		//							materialsub.SubLotId = ssccString;
		//							materialsub.Type = "0";
		//							materialsub.ExternalStatus = "3";
		//							materialsub.CreateCustomGuid(_user.Name);
		//							materialSubLotEntities.Add(materialsub);
		//							sublotId = materialsub.ID.ToString();
		//						}
		//						var poProducedActualEntity = new PoProducedActualEntity();
		//						poProducedActualEntity.ProductExecutionId = runOrder.ID;
		//						poProducedActualEntity.ProductionOrderId = runOrder.ProductionOrderId;
		//						poProducedActualEntity.PoProducedRequirementId = item.ID;
		//						poProducedActualEntity.LotId = runOrder.LotId;
		//						poProducedActualEntity.LotStatus = 2;
		//						poProducedActualEntity.SubLotId = sublotId;
		//						poProducedActualEntity.SubLotStatus = 3;
		//						poProducedActualEntity.SourceType = 0;
		//						poProducedActualEntity.EquipmentId = equipment.ID;
		//						poProducedActualEntity.DesinationEquipmentId = locationId;
		//						poProducedActualEntity.UnitId = item.UnitId;
		//						poProducedActualEntity.ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000";
		//						poProducedActualEntity.Quantity = quantity;
		//						poProducedActualEntity.CreateCustomGuid("OPC");
		//						poProducedActualEntities.Add(poProducedActualEntity);
		//						if (equipmentRequirement?.ManageInventory == "1")
		//						{
		//							var materialInventoryEntity = new MaterialInventoryEntity();
		//							materialInventoryEntity.LotId = runOrder.LotId;
		//							materialInventoryEntity.SublotId = sublotId;
		//							materialInventoryEntity.Quantity = quantity;
		//							materialInventoryEntity.QuantityUomId = item.UnitId;
		//							materialInventoryEntity.StorageLocation = "";
		//							materialInventoryEntity.EquipmentId = locationId;
		//							materialInventoryEntity.IsPrechecked = "FALSE";
		//							materialInventoryEntity.ContainerId = "";
		//							materialInventoryEntity.ProductionRequestId = runOrder.ProductionOrderId;
		//							materialInventoryEntity.SortOrder = 0;
		//							materialInventoryEntity.CreateCustomGuid(_user.Name);
		//							materialInventoryEntities.Add(materialInventoryEntity);
		//						}
		//					}
		//					_unitOfWork.BeginTran();
		//					try
		//					{
		//						if (materialSubLotEntities?.Count > 0)
		//						{
		//							await _dal16.Add(materialSubLotEntities);
		//						}
		//						if (poProducedActualEntities?.Count > 0)
		//						{
		//							await _dal17.Add(poProducedActualEntities);
		//						}
		//						if (materialInventoryEntities?.Count > 0)
		//						{
		//							await _dal19.Add(materialInventoryEntities);
		//						}
		//						if (updateDataItemDetails?.Count > 0)
		//						{
		//							await _dal34.Update(updateDataItemDetails);
		//						}
		//						if (addDataItemDetails?.Count > 0)
		//						{
		//							await _dal34.Add(addDataItemDetails);
		//						}
		//					}
		//					catch (Exception ex)
		//					{
		//						result.msg = ex.Message;
		//						_unitOfWork.RollbackTran();
		//						return result;
		//					}
		//					_unitOfWork.CommitTran();
		//				}
		//				break;
		//			}
		//		default:
		//			result.msg = "reporttType错误，请检查任务配置参数！";
		//			return result;
		//			break;
		//	}
		//	result.msg = "操作成功！";
		//	result.success = true;
		//	return result;
		//}

		#endregion

		#region MMI接口

		#region MES调用

		/// <summary>
		/// 工单启动_MES ✔
		/// </summary>
		/// <param name="batchId"></param>
		/// <param name="status">0:工单关闭，1：工单运行中；2：工单暂停；3：取消</param>
		/// <returns></returns>
		public async Task<MessageModel<MMI_POStart_Res>> POStart(string execId, string batchId, string equipmentId, int status, bool isKx = false)
		{
			string interFaceName = "工单启动";
			var result = new MessageModel<MMI_POStart_Res>
			{
				msg = "操作失败！",
				success = false,
			};
			var batch = await _dal6.FindEntity(batchId);
			if (batch == null)
			{
				SerilogServer.LogDebug($"【工单启动】未找到batch", "MMIInterfaceLog");
				result.msg = "未找到batch";
				return result;
			}
			var equipment = await _dal15.FindEntity(equipmentId);
			if (equipment == null)
			{
				SerilogServer.LogDebug($"【工单启动】未找到equipment", "MMIInterfaceLog");
				result.msg = "未找到equipment";
				return result;
			}
			var v = (await GetFunctionPropertyValue(equipment.ID, "POManagement", "SendMMI")).response;
			if (v != "1")
			{
				result.msg = "无需下发MMI";
				result.success = true;
				return result;
			}
			var productionOrder = await _dal5.FindEntity(batch.ProductionOrderId);
			if (productionOrder == null)
			{
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},未找到productionOrder", "MMIInterfaceLog");
				result.msg = "未找到productionOrder";
				return result;
			}
			var batchs = await _dal6.FindList(x => x.ProductionOrderId == batch.ProductionOrderId);
			if (status == 1 && batch.Number != "1")
			{
				result.msg = "【工单启动】batch.Number不为第一个batch则不用下发MMI";
				result.success = true;
				return result;
			}
			var maxBatchNumber = batchs.OrderByDescending(x => int.Parse(x.Number)).FirstOrDefault()?.Number;
			if (status == 0 && batch.Number != maxBatchNumber)
			{
				result.msg = "【工单关闭】batch.Number不为最后一个batch则不用下发MMI";
				result.success = true;
				return result;
			}
			var materialVersion = await _dal35.FindEntity(productionOrder.MaterialVersionId);
			if (materialVersion == null)
			{
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},未找到materialVersion", "MMIInterfaceLog");
				result.msg = "未找到materialVersion";
				return result;
			}
			var material = await _dal2.FindEntity(materialVersion.MaterialId);
			if (material == null)
			{
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},未找到material", "MMIInterfaceLog");
				result.msg = "未找到material";
				return result;
			}
			var isSoysauce = (await _materPropertydal.FindEntity(x => x.MaterialId == materialVersion.MaterialId && x.PropertyCode == "IsSoysauce"))?.PropertyValue ?? "";
			int.TryParse(isSoysauce, out int i_isSoysauce);
			//batch.ProductionOrderId = "123";

			MessageModel<PoRecipeDataModel> apiResult_poRecipeDataModel = await HttpHelper.PostAsync<PoRecipeDataModel>("DFM", "api/RecipeCommon/GetPoRecipeDataNew", _user.GetToken(), new { ProductionOrderId = batch.ProductionOrderId });
			if (apiResult_poRecipeDataModel.response == null || apiResult_poRecipeDataModel.success != true)
			{
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},PoRecipeDataModel为空", "MMIInterfaceLog");
				result.msg = apiResult_poRecipeDataModel.msg;
				return result;
			}
			var data = apiResult_poRecipeDataModel.response;
			var batchComsumeMaterial = (await _dal30.FindList(x => x.ID == batchId && x.IsWater == "1")).FirstOrDefault();
			try
			{
				//煮缸/储缸（1：煮缸；2：储缸）
				var isCookTank = (await GetFunctionPropertyValue(equipment.ID, "POManagement", "IsCookTank")).response;
				int.TryParse(isCookTank, out int i_isCookTank);
				MMI_POStart request = new MMI_POStart()
				{
					ProductionNo = productionOrder.ProductionOrderNo,
					ProductId = material.Code,
					ProductName = material.NAME,
					ProductVersion = materialVersion.MaterialVersionNumber,
					EquipmentId = (await GetFunctionPropertyValue(equipment.ID, "POManagement", "MMIEquipmentCode")).response,
					EquipmentType = i_isCookTank,
					ProcutionQty = productionOrder.PlanQty,
					TotalBatch = batchs.Count,
					BatchIndex = Convert.ToInt32(batch.Number),
					BatchQty = batch.TargetQuantity,
					BatchNumber = batch.BatchCode,
					BatchStatus = status,
					RecipeID = 0,
					IsSoysauce = i_isSoysauce,
					RecipeTable = new JArray(),
					ControlParameters = new List<ControlRecipe>(),
					Water = batchComsumeMaterial?.Quantity ?? 0
				};
				int sort = 0;
				var steps = new List<string>();
				if (isKx)
				{
					steps = (data.RecipeTableList?.FindAll(x => x.ParameterGroupName != "Step0").OrderBy(x => x.ParameterGroupSortOrder)?.Select(x => x.ParameterGroupName)?.Distinct())?.ToList();
				}
				else
				{
					steps = (data.RecipeTableList?.FindAll(x => x.EquipmentId == equipmentId).OrderBy(x => x.ParameterGroupSortOrder)?.Select(x => x.ParameterGroupName)?.Distinct())?.ToList();
				}
				steps = steps ?? new List<string>();
				foreach (var step in steps)
				{
					int.TryParse(step.Replace("Step", ""), out sort);
					var stepParmeters = new List<RecipeParameterModel>();
					if (isKx)
					{
						stepParmeters = data.RecipeTableList?.FindAll(x => x.ParameterGroupName == step)?.DistinctBy(x => x.ID).ToList();
					}
					else
					{
						stepParmeters = data.RecipeTableList?.FindAll(x => x.EquipmentId == equipmentId && x.ParameterGroupName == step)?.DistinctBy(x => x.ID).ToList();
					}
					var jobj = new JObject
					{
						//{ "StepNo", ++sort }
						{ "StepNo", sort }
					};
					foreach (var patmeter in stepParmeters)
					{
						patmeter.ParameterName = patmeter.ParameterName == "时间" ? "Duration" : patmeter.ParameterName;
						patmeter.ParameterName = patmeter.ParameterName == "温度" ? "Temperatrue" : patmeter.ParameterName;
						patmeter.ParameterName = patmeter.ParameterName == "是否投料" ? "FlagFeed" : patmeter.ParameterName;
						patmeter.ParameterName = patmeter.ParameterName == "是否QA" ? "FlagQA" : patmeter.ParameterName;
						if (patmeter.ParameterName.StartsWith("Flag"))
						{
							jobj.Add(patmeter.ParameterName, (int)(patmeter.Target ?? 0m));
						}
						else
						{
							jobj.Add(patmeter.ParameterName, patmeter.Target ?? 0m);
						}
					}
					request.RecipeTable.Add(jobj);
				}
				foreach (var item in data.ControlRecipeList)
				{
					var controlRecipe = request.ControlParameters.Find(x => x.GrouopName == item.ParameterGroupName && x.EquipmentName == item.EquipmentName);
					if (controlRecipe == null)
					{
						controlRecipe = new ControlRecipe()
						{
							GrouopName = item.ParameterGroupName,
							EquipmentName = item.EquipmentName,
							Parameters = new List<Parameter>(),
						};
						request.ControlParameters.Add(controlRecipe);
					}
					controlRecipe.Parameters.Add(
						new Parameter()
						{
							Name = item.ParameterName,
							Value = item.Target.ToString(),
							Uom = item.Uom
						});
				}
				var baseUrl = ServiceList.GetUrls.FirstOrDefault((ServerUrl t) => t.ServerName == "MMI")!.Url;
				var str = FAJsonConvert.ToJson(new List<MMI_POStart> { request });
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},数据获取完成，准备调用接口", "MMIInterfaceLog");
				var response = await HttpMethods.HttpPost(baseUrl + "api/MMI/API3/MES_2_MMI/InOrderStartOrStop", str);
				string detail = $"【工单启动】{productionOrder.ProductionOrderNo}，接口调用完成{Environment.NewLine}请求内容：{str}{Environment.NewLine}返回结果：{response}";
				SerilogServer.LogDebug(detail, "MMIInterfaceLog");
				result.response = FAJsonConvert.FormJson<MMI_POStart_Res>(response);
				if (result.response?.ReturnCode != "1")
				{
					await _interfaceLogServices.AddInterFaceLog(execId, productionOrder.ProductionOrderNo, batch.Number, "InOrderStartOrStop", interFaceName, result.response?.ReturnMessage, detail);
					return result;
				}
				await _interfaceLogServices.AddInterFaceLog(execId, productionOrder.ProductionOrderNo, batch.Number, "InOrderStartOrStop", interFaceName, "发送成功", detail);
			}
			catch (Exception ex)
			{
				SerilogServer.LogDebug($"【工单启动】{productionOrder.ProductionOrderNo},出现异常{ex.Message}", "MMIInterfaceLog");
				result.msg = ex.Message;
				return result;
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// 投料完成_MES ✔
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <param name="tippingStatus"></param>
		/// <param name="productionOrderId"></param>
		/// <param name="number"></param>
		/// <returns></returns>
		public async Task<MessageModel<MMI_Res>> FeedingCompleted(string execId, string equipmentId, int tippingStatus, string productionOrderId, string number)
		{
			string interFaceName = "投料完成";
			var result = new MessageModel<MMI_Res>
			{
				msg = "操作失败！",
				success = false,
			};
			try
			{
				var equipment = await _dal15.FindEntity(x => x.EquipmentCode == equipmentId);
				if (equipment == null)
				{
					result.msg = $"MES系统物理模型未找到：{equipmentId}";
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				var v = (await GetFunctionPropertyValue(equipment.ID, "POManagement", "SendMMI")).response;
				if (v != "1")
				{
					result.msg = "无需下发MMI";
					result.success = true;
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				//获取Function
				MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
				var equipmentAllData = apiResult_equipmentAllData.response;
				if (equipmentAllData == null)
				{
					result.msg = "equipmentAllData为空,请检查MES配置";
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Tipping");
				var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MMITrigger");
				var v1 = item2?.ActualValue ?? item2?.DefaultValue;
				if (v1 == "True")
				{
					var propertyEntity = (await _dal12.FindList(x => x.FunctionId == item.FunctionId && x.PropertyCode == "ButtonEnable", "MODIFYDATE DESC"))?.FirstOrDefault();
					if (propertyEntity == null)
					{
						result.msg = "未找到属性ButtonEnable";
						SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
						return result;
					}
					var functionPropertyValue = await _dal13.FindEntity(x => x.PropertyId == propertyEntity.ID && x.EquipmentId == equipment.ID);
					_unitOfWork.BeginTran();
					try
					{
						if (functionPropertyValue == null)
						{
							functionPropertyValue = new FunctionPropertyValueEntity()
							{
								EquipmentId = equipment.ID,
								EquipmentFunctionId = item.EquipmentFunctionId,
								FunctionId = item.FunctionId,
								PropertyId = propertyEntity.ID,
								Enable = 1,
								Deleted = 0,
							};
							functionPropertyValue.PropertyValue = "False";
							functionPropertyValue.CreateCustomGuid("MMI");
							await _dal13.Add(functionPropertyValue);
						}
						else
						{
							functionPropertyValue.PropertyValue = "False";
							functionPropertyValue.Modify(functionPropertyValue.ID, "MMI");
							await _dal13.Update(functionPropertyValue);
						}
						_unitOfWork.CommitTran();
					}
					catch (Exception ex)
					{
						_unitOfWork.RollbackTran();
						result.msg = ex.Message;
						SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
						return result;
					}
				}
				var order = await _dal5.FindEntity(productionOrderId);
				if (order == null)
				{
					result.msg = $"未找到ProductionOrder";
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				var materialVersion = await _dal35.FindEntity(order.MaterialVersionId);
				if (materialVersion == null)
				{
					result.msg = $"未找到materialVersion";
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				var material = await _dal2.FindEntity(materialVersion.MaterialId);
				if (material == null)
				{
					result.msg = $"未找到material";
					SerilogServer.LogDebug($"【投料完成】{result.msg}", "MMIInterfaceLog");
					return result;
				}
				MMI_FeedingCompleted request = new MMI_FeedingCompleted()
				{
					EquipmentId = (await GetFunctionPropertyValue(equipment.ID, "POManagement", "MMIEquipmentCode")).response,
					TippingStatus = tippingStatus,
					ProductionNo = order.ProductionOrderNo,
					ProductId = material.Code,
					ProductName = material.NAME,
					ProductVersion = materialVersion.MaterialVersionNumber,
					BatchIndex = number
				};
				var baseUrl = ServiceList.GetUrls.FirstOrDefault((ServerUrl t) => t.ServerName == "MMI")!.Url;
				var str = FAJsonConvert.ToJson(new List<MMI_FeedingCompleted>() { request });
				SerilogServer.LogDebug($"【投料完成】准备调用接口", "MMIInterfaceLog");
				var response = await HttpMethods.HttpPost(baseUrl + "api/MMI/API1/MES_2_MMI/InFeedingEnd", str);
				string detail = $"【投料完成】接口调用完成{Environment.NewLine}请求内容：{str}{Environment.NewLine}返回结果：{response}";
				SerilogServer.LogDebug(detail, "MMIInterfaceLog");
				result.response = FAJsonConvert.FormJson<MMI_Res>(response);
				if (result.response.ReturnCode == "-1")
				{
					await _interfaceLogServices.AddInterFaceLog(execId, order.ProductionOrderNo, number, "InFeedingEnd", interFaceName, result.response?.ReturnMessage, detail);
					return result;
				}
				await _interfaceLogServices.AddInterFaceLog(execId, order.ProductionOrderNo, number, "InFeedingEnd", interFaceName, "发送成功", detail);
			}
			catch (Exception ex)
			{
				SerilogServer.LogDebug($"【投料完成】err:{ex.StackTrace}", "MMIInterfaceLog");
			}
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		/// <summary>
		/// QA状态同步_MES ✔
		/// </summary>
		/// <param name="equipmentId"></param>
		/// <param name="qAStatus">QA状态 1：QA处理中；2：QA放行：3：QA结果异常，物料锁定</param>
		/// <param name="productionOrderId"></param>
		/// <param name="number"></param>
		/// <returns></returns>
		public async Task<MessageModel<MMI_Res>> QAStatusSync(string execId, string equipmentId, int qAStatus, string productionOrderId, string number)
		{
			string interFaceName = "QA状态同步";
			var result = new MessageModel<MMI_Res>
			{
				msg = "操作失败！",
				success = false,
			};
			var v = (await GetFunctionPropertyValue(equipmentId, "POManagement", "SendMMI")).response;
			if (v != "1")
			{
				result.msg = "无需下发MMI";
				result.success = true;
				return result;
			}
			var order = await _dal5.FindEntity(productionOrderId);
			if (order == null)
			{
				result.msg = $"未找到ProductionOrder";
				SerilogServer.LogDebug($"【QA状态同步】{result.msg}", "MMIInterfaceLog");
				return result;
			}
			var materialVersion = await _dal35.FindEntity(order.MaterialVersionId);
			if (materialVersion == null)
			{
				result.msg = $"未找到materialVersion";
				SerilogServer.LogDebug($"【QA状态同步】{result.msg}", "MMIInterfaceLog");
				return result;
			}
			var material = await _dal2.FindEntity(materialVersion.MaterialId);
			if (material == null)
			{
				result.msg = $"未找到material";
				SerilogServer.LogDebug($"【QA状态同步】{result.msg}", "MMIInterfaceLog");
				return result;
			}
			MMI_QAStatusSync request = new MMI_QAStatusSync()
			{
				EquipmentId = (await GetFunctionPropertyValue(equipmentId, "POManagement", "MMIEquipmentCode")).response,
				QAStatus = qAStatus,
				ProductionNo = order.ProductionOrderNo,
				ProductId = material.Code,
				ProductName = material.NAME,
				ProductVersion = materialVersion.MaterialVersionNumber,
				BatchIndex = number
			};
			var baseUrl = ServiceList.GetUrls.FirstOrDefault((ServerUrl t) => t.ServerName == "MMI")!.Url;
			var str = FAJsonConvert.ToJson(new List<MMI_QAStatusSync>() { request });
			SerilogServer.LogDebug($"【QA状态同步】准备调用接口", "MMIInterfaceLog");
			var response = await HttpMethods.HttpPost(baseUrl + "api/MMI/API2/MES_2_MMI/InQAEnd", str);
			string detail = $"【QA状态同步】接口调用完成{Environment.NewLine}请求内容：{str}{Environment.NewLine}返回结果：{response}";
			SerilogServer.LogDebug(detail, "MMIInterfaceLog");
			result.response = FAJsonConvert.FormJson<MMI_Res>(response);
			if (result.response.ReturnCode == "-1")
			{
				await _interfaceLogServices.AddInterFaceLog(execId, order.ProductionOrderNo, number, "InQAEnd", interFaceName, result.response?.ReturnMessage, detail);
				return result;
			}
			await _interfaceLogServices.AddInterFaceLog(execId, order.ProductionOrderNo, number, "InQAEnd", interFaceName, "发送成功", detail);
			result.msg = "操作成功！";
			result.success = true;
			return result;
		}

		#endregion

		#region MES被调用

		/// <summary>
		/// 物料消耗反馈_Proleit
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> ConsumeFeedback(List<MMI_ConsumeFeedback> reqModels)
		{
			SerilogServer.LogDebug($"【物料消耗反馈】接口被调用", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			var autoReportMessages = new List<AutoReportMessage>();
			string err = "";
			foreach (var reqModel in reqModels)
			{
				List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
				List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
				List<string> deleteIds = new List<string>();
				var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
				var equipment = equipmentResult.response;
				//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
				if (equipment == null)
				{
					err += equipmentResult.msg;
					//err += $"未找到Equipment{reqModel.EquipmentId}";
					continue;
				}
				var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
				if (runOrder == null)
				{
					err += $"设备{reqModel.EquipmentId}未找到运行工单！";
					continue;
				}
				var productionOrder = await _dal5.FindEntity(x => x.ID == runOrder.ProductionOrderId);
				if (productionOrder == null)
				{
					err += $"设备{reqModel.EquipmentId}未找到运行工单！";
					continue;
				}
				var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
				if (poSegmentRequirement == null)
				{
					err += $"设备{reqModel.EquipmentId}工单{productionOrder.ProductionOrderNo}未找到poSegmentRequirement！";
					continue;
				}
				var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
				if (sapSegment == null)
				{
					err += $"设备{reqModel.EquipmentId}工单{productionOrder.ProductionOrderNo}未找到sapSegment！";
					continue;
				}

				var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
				var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
				var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
				var equipmentCodes = list.Select(x =>
				{
					return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
				});
				if (equipmentCodes == null)
				{
					equipmentCodes = new List<string>();
				}
				var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
				var material = (await _dal2.FindList(x => x.Code == reqModel.MaterialCode /*|| x.NAME == reqModel.MaterialCode*/)).FirstOrDefault();
				var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId && x.MaterialId == material.ID);
				if (poConsumeRequirements == null || poConsumeRequirements.Count == 0)
				{
					err += $"工单{productionOrder.ProductionOrderNo}未找到物料{reqModel.MaterialCode}的消耗需求！";
					continue;
				}
				var item = poConsumeRequirements.FirstOrDefault();
				var autoReportMessage = new AutoReportMessage()
				{
					MaterialCode = material?.Code ?? item.MaterialId,
					MaterialName = material?.NAME ?? item.MaterialId,
					Quantity = 0,
					Success = true,
					Msg = "OK",
				};
				var quantity2 = reqModel.Qty;
				var materialGroups = await GetMaterialGroups(item.MaterialId);
				var locations_FIFO = new List<string>();
				var locations = new List<string>();
				foreach (var item1 in equipments)
				{
					var equipmentStorage = await _dal31.FindEntity(x => x.EquipmentId == item1.ID);
					if (equipmentStorage == null)
					{
						continue;
					}
					var equipmentRequirement = await _dal32.FindEntity(equipmentStorage.EquipmentRequirementId);
					if (equipmentRequirement == null)
					{
						continue;
					}
					if (materialGroups?.Exists(x => x.MaterialGroupName == equipmentRequirement.Code) == true)
					{
						if (equipmentStorage.OutgoingStorageMode == "FIFO")
						{
							locations_FIFO.Add(item1.EquipmentCode);
						}
						else
						{
							locations.Add(item1.EquipmentCode);
						}
					}
				}
				var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations_FIFO.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
				var inventoryQuantity = inventorylistings?.Sum(x => x.Quantity) ?? 0;
				if (inventoryQuantity < quantity2)
				{
					autoReportMessage.Success = false;
					autoReportMessage.Msg = "库存数量不足";
				}
				else
				{
					foreach (var inventoryModel in inventorylistings)
					{
						if (quantity2 <= 0)
						{
							break;
						}
						decimal consumeQuantity = quantity2;
						if (quantity2 >= inventoryModel.Quantity)
						{
							deleteIds.Add(inventoryModel.InventoryId);
							consumeQuantity = inventoryModel.Quantity.Value;
						}
						else
						{
							var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
							if (materialInventory != null)
							{
								materialInventory.Quantity -= Math.Round(Convert.ToDecimal(consumeQuantity), 4); //consumeQuantity;
								materialInventory.Modify(materialInventory.ID, "MMI");
								materialInventoryEntities.Add(materialInventory);
							}
						}
						quantity2 -= consumeQuantity;
						//写入consumed_actual表
						var poConsumeActualEntity = new PoConsumeActualEntity
						{
							ProductionOrderId = runOrder.ProductionOrderId,
							PoConsumeRequirementId = item.ID,
							ProductExecutionId = runOrder.ID,
							EquipmentId = runOrder.RunEquipmentId,
							SourceEquipmentId = runOrder.RunEquipmentId,
							Quantity = consumeQuantity,
							UnitId = item.UnitId,
							LotId = inventoryModel.LotId,
							SubLotId = inventoryModel.SubLotId,
							SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
							StorageBin = inventoryModel.LocationS,
							StorageLocation = inventoryModel.LocationFcode,
							//ContainerId = inventoryModel.ContainerId,
							TeamId = "",
							ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
							ReasonCode = "",
							Comment = "",
							Deleted = 0,
							SendExternal = item.Quantity == 0 ? 1 : 0
						};
						autoReportMessage.Quantity += consumeQuantity;
						poConsumeActualEntity.CreateCustomGuid("MMI");
						poConsumeActualEntities.Add(poConsumeActualEntity);
					}
				}
				autoReportMessages.Add(autoReportMessage);
				_unitOfWork.BeginTran();
				try
				{
					if (poConsumeActualEntities?.Count > 0)
					{
						await _dal28.Add(poConsumeActualEntities);
					}
					if (materialInventoryEntities?.Count > 0)
					{
						await _dal19.Update(materialInventoryEntities);
					}
					if (deleteIds?.Count > 0)
					{
						await _dal19.DeleteByIds(deleteIds.ToArray());
					}

					_unitOfWork.CommitTran();

				}
				catch (Exception ex)
				{
					err += ex.Message;
					//result.ReturnMessage = ex.Message;
					_unitOfWork.RollbackTran();
					//return result;
				}
			}
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！" + err;//+ FAJsonConvert.ToJson(autoReportMessages);
			SerilogServer.LogDebug($"【物料消耗反馈】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		///// <summary>
		///// 物料消耗反馈_Proleit
		///// </summary>
		///// <param name="reqModel"></param>
		///// <returns></returns>
		//public async Task<MMI_Res> ConsumeFeedback2(List<MMI_ConsumeFeedback> reqModels)
		//{
		//	var result = new MMI_Res
		//	{
		//		ReturnCode = "-1",
		//		ReturnMessage = "操作失败！",
		//		ReturnMessageID = "",
		//	};
		//	var autoReportMessages = new List<AutoReportMessage>();
		//	List<PoConsumeActualEntity> poConsumeActualEntities = new List<PoConsumeActualEntity>();
		//	List<MaterialInventoryEntity> materialInventoryEntities = new List<MaterialInventoryEntity>();
		//	List<string> deleteIds = new List<string>();
		//	//var groups = reqModels.GroupBy(x => x.EquipmentId).Where(g => g.Count() > 1);
		//	string err = "";
		//	List<string> equipmentIds = new List<string>();
		//	// 从原始列表中删除重复项
		//	//foreach (var group in groups)
		//	//{
		//	//	equipmentIds.Add(group.Key);
		//	//	foreach (var item in group)
		//	//	{
		//	//		reqModels.Remove(item);
		//	//	}
		//	//}
		//	foreach (var reqModel in reqModels)
		//	{
		//		var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
		//		var equipment = equipmentResult.response;
		//		//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
		//		if (equipment == null)
		//		{
		//			err += equipmentResult.msg;
		//			//err += $"未找到Equipment{reqModel.EquipmentId}";
		//			continue;
		//		}
		//		var runOrder = await _dal14.FindEntity(x => x.RunEquipmentId == equipment.ID && x.Status == "1" && x.EndTime == null);
		//		if (runOrder == null)
		//		{
		//			err += $"设备{reqModel.EquipmentId}未找到运行工单！";
		//			continue;
		//		}
		//		var productionOrder = await _dal5.FindEntity(x => x.ID == runOrder.ProductionOrderId);
		//		if (productionOrder == null)
		//		{
		//			err += $"设备{reqModel.EquipmentId}未找到运行工单！";
		//			continue;
		//		}
		//		var poSegmentRequirement = await _dal7.FindEntity(runOrder.PoSegmentRequirementId);
		//		if (poSegmentRequirement == null)
		//		{
		//			err += $"设备{reqModel.EquipmentId}工单{productionOrder.ProductionOrderNo}未找到poSegmentRequirement！";
		//			continue;
		//		}
		//		var sapSegment = await _dal8.FindEntity(poSegmentRequirement.SegmentId);
		//		if (sapSegment == null)
		//		{
		//			err += $"设备{reqModel.EquipmentId}工单{productionOrder.ProductionOrderNo}未找到sapSegment！";
		//			continue;
		//		}

		//		var api_shifts = await HttpHelper.PostAsync<List<ShiftView>>("DFM", "api/Calendar/GetListByModelId", _user.GetToken(), new { MODELID = equipment.ID, finddate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
		//		var shift = api_shifts.response?.Find(x => Convert.ToDateTime(x.StartTime) <= runOrder.StartTime && Convert.ToDateTime(x.EndTime) >= runOrder.StartTime);
		//		var list = await _dal29.FindList(x => x.EquipmentId == equipment.ID && x.ActionCode == "Consumption" && x.PropertyCode == "Associated Node");
		//		var equipmentCodes = list.Select(x =>
		//		{
		//			return !string.IsNullOrEmpty(x.PropertyValue) ? x.PropertyValue : x.DefaultValue;
		//		});
		//		if (equipmentCodes == null)
		//		{
		//			equipmentCodes = new List<string>();
		//		}
		//		var equipments = await _dal15.FindList(x => equipmentCodes.Contains(x.EquipmentCode));
		//		var material = (await _dal2.FindList(x => x.Code == reqModel.MaterialCode /*|| x.NAME == reqModel.MaterialCode*/)).FirstOrDefault();
		//		var poConsumeRequirements = await _dal26.FindList(x => x.PoSegmentRequirementId == runOrder.PoSegmentRequirementId && x.MaterialId == material.ID);
		//		if (poConsumeRequirements == null || poConsumeRequirements.Count == 0)
		//		{
		//			err += $"工单{productionOrder.ProductionOrderNo}未找到物料{reqModel.MaterialCode}的消耗需求！";
		//			continue;
		//		}
		//		var item = poConsumeRequirements.FirstOrDefault();
		//		var autoReportMessage = new AutoReportMessage()
		//		{
		//			MaterialCode = material?.Code ?? item.MaterialId,
		//			MaterialName = material?.NAME ?? item.MaterialId,
		//			Quantity = 0,
		//			Success = true,
		//			Msg = "OK",
		//		};
		//		var quantity2 = reqModel.Qty;
		//		var materialGroups = await GetMaterialGroups(item.MaterialId);
		//		var locations_FIFO = new List<string>();
		//		var locations = new List<string>();
		//		foreach (var item1 in equipments)
		//		{
		//			var equipmentStorage = await _dal31.FindEntity(x => x.EquipmentId == item1.ID);
		//			if (equipmentStorage == null)
		//			{
		//				continue;
		//			}
		//			var equipmentRequirement = await _dal32.FindEntity(equipmentStorage.EquipmentRequirementId);
		//			if (equipmentRequirement == null)
		//			{
		//				continue;
		//			}
		//			if (materialGroups?.Exists(x => x.MaterialGroupName == equipmentRequirement.Code) == true)
		//			{
		//				if (equipmentStorage.OutgoingStorageMode == "FIFO")
		//				{
		//					locations_FIFO.Add(item1.EquipmentCode);
		//				}
		//				else
		//				{
		//					locations.Add(item1.EquipmentCode);
		//				}
		//			}
		//		}
		//		var inventorylistings = await _dal21.FindList(x => x.MaterialId == item.MaterialId && (string.IsNullOrEmpty(x.BatchId2) || x.BatchId2 == runOrder.BatchId) && locations_FIFO.Contains(x.LocationFcode), x => x.UpdateTimeStamp);
		//		var inventoryQuantity = inventorylistings?.Sum(x => x.Quantity) ?? 0;
		//		if (inventoryQuantity < quantity2)
		//		{
		//			autoReportMessage.Success = false;
		//			autoReportMessage.Msg = "库存数量不足";
		//		}
		//		else
		//		{
		//			foreach (var inventoryModel in inventorylistings)
		//			{
		//				if (quantity2 <= 0)
		//				{
		//					break;
		//				}
		//				decimal consumeQuantity = quantity2;
		//				if (quantity2 >= inventoryModel.Quantity)
		//				{
		//					deleteIds.Add(inventoryModel.InventoryId);
		//					consumeQuantity = inventoryModel.Quantity.Value;
		//				}
		//				else
		//				{
		//					var materialInventory = await _dal19.FindEntity(inventoryModel.InventoryId);
		//					if (materialInventory != null)
		//					{
		//						materialInventory.Quantity -= consumeQuantity;
		//						materialInventory.Modify(materialInventory.ID, "MMI");
		//						materialInventoryEntities.Add(materialInventory);
		//					}
		//				}
		//				quantity2 -= consumeQuantity;
		//				//写入consumed_actual表
		//				var poConsumeActualEntity = new PoConsumeActualEntity
		//				{
		//					ProductionOrderId = runOrder.ProductionOrderId,
		//					PoConsumeRequirementId = item.ID,
		//					ProductExecutionId = runOrder.ID,
		//					EquipmentId = runOrder.RunEquipmentId,
		//					SourceEquipmentId = runOrder.RunEquipmentId,
		//					Quantity = consumeQuantity,
		//					UnitId = item.UnitId,
		//					LotId = inventoryModel.LotId,
		//					SubLotId = inventoryModel.SubLotId,
		//					SubLotStatus = Convert.ToInt32(inventoryModel.StatusS),
		//					StorageBin = inventoryModel.LocationS,
		//					StorageLocation = inventoryModel.LocationFcode,
		//					//ContainerId = inventoryModel.ContainerId,
		//					TeamId = "",
		//					ShiftId = shift?.ShiftId ?? "02308292-2383-5176-163e-0370f6000000",
		//					ReasonCode = "",
		//					Comment = "",
		//					Deleted = 0
		//				};
		//				autoReportMessage.Quantity += consumeQuantity;
		//				poConsumeActualEntity.CreateCustomGuid("MMI");
		//				poConsumeActualEntities.Add(poConsumeActualEntity);
		//			}
		//		}
		//		autoReportMessages.Add(autoReportMessage);
		//	}
		//	_unitOfWork.BeginTran();
		//	try
		//	{
		//		if (poConsumeActualEntities?.Count > 0)
		//		{
		//			await _dal28.Add(poConsumeActualEntities);
		//		}
		//		if (materialInventoryEntities?.Count > 0)
		//		{
		//			await _dal19.Update(materialInventoryEntities);
		//		}
		//		if (deleteIds?.Count > 0)
		//		{
		//			await _dal19.DeleteByIds(deleteIds.ToArray());
		//		}
		//	}
		//	catch (Exception ex)
		//	{
		//		result.ReturnMessage = ex.Message;
		//		_unitOfWork.RollbackTran();
		//		return result;
		//	}
		//	_unitOfWork.CommitTran();
		//	result.ReturnCode = "1";
		//	result.ReturnMessage = "操作成功！" + err;//+ FAJsonConvert.ToJson(autoReportMessages);
		//	if (equipmentIds.Count > 0)
		//	{
		//		result.ReturnMessage += "重复设备：" + string.Join(",", equipmentIds);
		//	}
		//	return result;
		//}

		/// <summary>
		/// 生产执行信息反馈_Proleit ✔
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> ProductionFeedback(List<MMI_ProductionFeedback> reqModels)
		{
			SerilogServer.LogDebug($"【生产执行信息反馈】接口被调用", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			var addList = new List<ParaExecutionLogEntity>();
			var ids = new List<string>();
			foreach (var reqModel in reqModels)
			{
				if (string.IsNullOrEmpty(reqModel.ProductionNo))
				{
					continue;
				}
				ParaExecutionLogEntity paraExecutionLogEntity = new();
				_mapper.Map(reqModel, paraExecutionLogEntity);
				var productionOrder = await _dal5.FindEntity(x => x.ProductionOrderNo == reqModel.ProductionNo);
				if (productionOrder != null)
				{
					paraExecutionLogEntity.ProductionNo = productionOrder.ID;
					if (!ids.Contains(productionOrder.ID))
					{
						ids.Add(productionOrder.ID);
					}
				}
				var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
				var equipment = equipmentResult.response;
				//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
				if (equipment != null)
				{
					paraExecutionLogEntity.EquipmentId = equipment.ID;
				}
				paraExecutionLogEntity.CreateCustomGuid("MMI");
				addList.Add(paraExecutionLogEntity);
			}
			_unitOfWork.BeginTran();
			try
			{
				if (addList.Count > 0)
				{
					await _paraExecutionLog.Add(addList);
				}
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.ReturnMessage = ex.Message;
				SerilogServer.LogDebug($"【生产执行信息反馈】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
				return result;
			}
			_unitOfWork.CommitTran();
			await CalculationStopTime(ids);
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！";
			SerilogServer.LogDebug($"【生产执行信息反馈】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 计算停机时间
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		//public async Task<bool> CalculationStopTime(List<string> ids)
		//{
		//	if (!ids.Any())
		//	{
		//		return false;
		//	}
		//	var paraExecutionLogs = await _paraExecutionLog.FindList(x => ids.Contains(x.ProductionNo));
		//	if (paraExecutionLogs.Any())
		//	{
		//		var groups = paraExecutionLogs.GroupBy(x => x.ProductionNo);
		//		if (groups.Any())
		//		{
		//			foreach (var group in groups)
		//			{
		//				var paraExecutionLogs1 = group.Where(x => x.Paraname == "煮料过程停机时间").OrderBy(x => x.StepStartTime).ToList();
		//				var duration = paraExecutionLogs1.Sum(x => Convert.ToDecimal(x.Values));
		//				long timestampSec = long.Parse(paraExecutionLogs1.FirstOrDefault().TotalStartTime);
		//				DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(timestampSec);
		//				DateTime startTime = dateTimeOffset.LocalDateTime;
		//				await _cookConfirmationServices.AddTime(group.Key, 2, duration, startTime);
		//			}
		//		}
		//	}
		//	return true;
		//}

		public async Task<bool> CalculationStopTime(List<string> ids)
		{
			if (!ids.Any())
			{
				return false;
			}

			try
			{
				var paraExecutionLogs = await _paraExecutionLog.FindList(x => ids.Contains(x.ProductionNo));
				if (!paraExecutionLogs.Any())
				{
					return false;
				}

				var groups = paraExecutionLogs.GroupBy(x => x.ProductionNo);
				foreach (var group in groups)
				{
					var paraExecutionLogs1 = group
						.Where(x => x.Paraname == "煮料过程停机时间")
						.OrderBy(x => x.StepStartTime)
						.ToList();

					var start = group
					.Where(x => x.StepName == "煮料")
					.OrderBy(x => x.StepStartTime)
					.FirstOrDefault();

					var duration = paraExecutionLogs1.Sum(x => Convert.ToDecimal(x.Values));
					long timestampSec = long.Parse(start?.StepStartTime ?? "0");
					DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(timestampSec);
					DateTime startTime = dateTimeOffset.LocalDateTime;

					await _cookConfirmationServices.AddTime(group.Key, 2, duration, startTime);
				}
			}
			catch (Exception ex)
			{
				// 记录异常日志
				Console.WriteLine($"Error in CalculationStopTime: {ex.Message}");
				return false;
			}

			return true;
		}

		/// <summary>
		/// 请求投料_Proleit ✔
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> RequestFeeding(List<MMI_RequestFeeding> reqModels)
		{
			string interFaceName = "请求投料";
			SerilogServer.LogDebug($"【请求投料】接口被调用,return：{FAJsonConvert.ToJson(reqModels)}", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			var now = DateTime.Now;
			string err = "";
			var addList = new List<FunctionPropertyValueEntity>();
			var updateList = new List<FunctionPropertyValueEntity>();
			var poProducedExecutions = new List<PoProducedExecutionEntity>();
			var poProducedExecutions2 = new List<PoProducedExecutionEntity>();
			var toAndonList = new List<dynamic>();
			var logs = new List<InterfaceLogEntity>();
			foreach (var reqModel in reqModels)
			{
				try
				{
					InterfaceLogEntity log = new InterfaceLogEntity();
					log.OrderNo = reqModel.ProductionNo;
					log.BatchNo = reqModel.BatchIndex;
					log.Name = "RequestFeeding";
					log.Description = interFaceName;
					log.Detail = $"【请求投料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModel)}";
					log.CreateCustomGuid("MMI");
					logs.Add(log);
					var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
					var equipment = equipmentResult.response;
					//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
					if (equipment == null)
					{
						err += equipmentResult.msg;
						log.Content = equipmentResult.msg;
						log.Detail += $"{Environment.NewLine}返回结果：{equipmentResult.msg}";
						continue;
					}
					var runOrder = await _dal36.FindEntity(x => x.ProcessOrder == reqModel.ProductionNo && x.RunEquipmentId == equipment.ID
					 && x.Number == reqModel.BatchIndex && x.Status == "1");
					if (runOrder == null)
					{
						if (reqModel.BatchIndex == "1")
						{
							err += $"设备{equipment.EquipmentCode}未找到运行的工单";
							log.Content = $"设备{equipment.EquipmentCode}未找到运行的工单";
							log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}未找到运行的工单";
							continue;
						}
						else
						{
							var processOrder = (await _dal37.FindList(x => x.EquipmentId == equipment.ID && x.ProcessOrder == reqModel.ProductionNo && (x.Status == 2 || x.Status == 5 || x.Status == 6))).FirstOrDefault();
							if (processOrder == null)
							{
								err += $"设备:{equipment.EquipmentCode}不可运行该工单";
								log.Content = $"设备{equipment.EquipmentCode}不可运行该工单";
								log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}不可运行该工单";
								continue;
							}
							var batch = await _dal6.FindEntity(x => x.ProductionOrderId == processOrder.ProductionOrderId && x.PoSegmentRequirementId == processOrder.ID && x.Number == reqModel.BatchIndex);
							if (batch == null)
							{
								err += $"未找到batch";
								log.Content = $"未找到batch";
								log.Detail += $"{Environment.NewLine}返回结果：未找到batch";
								continue;
							}
							var poSegmentRequirement = await _dal7.FindEntity(processOrder.ID);
							if (poSegmentRequirement == null)
							{
								err += $"未找到poSegmentRequirement";
								log.Content = $"未找到poSegmentRequirement";
								log.Detail += $"{Environment.NewLine}返回结果：未找到poSegmentRequirement";
								continue;
							}
							MessageModel<DFM.Model.Models.SapSegmentEntity> apiResult_sapSegment = await HttpHelper.GetApiAsync<DFM.Model.Models.SapSegmentEntity>("DFM", "api/SapSegment/GetEntity/" + poSegmentRequirement.SegmentId, _user.GetToken(), null);
							var sapSegment = apiResult_sapSegment.response;
							if (sapSegment == null)
							{
								err += $"未找到sapSegment";
								log.Content = $"未找到sapSegment";
								log.Detail += $"{Environment.NewLine}返回结果：未找到sapSegment";
								continue;
							}
							MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + batch.MaterialId, _user.GetToken(), null);
							var material = apiResult_material.response;
							if (material == null)
							{
								err += $"未找到material";
								log.Content = $"未找到material";
								log.Detail += $"{Environment.NewLine}返回结果：未找到material";
								continue;
							}
							runOrder = (await _dal36.FindList(x => x.ProcessOrder == reqModel.ProductionNo && x.RunEquipmentId == equipment.ID && x.Status == "1" /*&& x.Number == (int.Parse(reqModel.BatchIndex) - 1).ToString()*/ && x.EndTime == null, x => x.CreateDate))?.FirstOrDefault();
							if (runOrder != null)
							{
								var runOrderEx = await _dal14.FindEntity(runOrder.ID);
								if (runOrderEx == null)
								{
									err += $"设备{equipment.EquipmentCode}未找到工单";
									log.Content = $"设备{equipment.EquipmentCode}未找到工单";
									log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}未找到工单";
									continue;
								}
								PoProducedExecutionEntity model = new PoProducedExecutionEntity();
								model.CreateCustomGuid("MMI");
								model.SapEquipmentId = sapSegment.SapEquipmentId;
								model.LineId = batch.LineId;
								model.ProductionOrderId = processOrder.ProductionOrderId;
								model.PoSegmentRequirementId = processOrder.ID;
								model.PoProducedRequirementId = batch.PoProducedRequirementId;
								model.BatchId = batch.ID;
								model.RunEquipmentId = equipment.ID;
								model.MaterialId = batch.MaterialId;
								model.MaterialVersionId = batch.MaterialVersionId;
								model.MaterialCode = material.Code;
								model.MaterialDescription = material.Description;
								model.TargetQuantity = batch.TargetQuantity;
								model.UnitId = batch.UnitId;
								model.Status = "1";//RUNNING
								model.Deleted = 0;
								model.StartTime = DateTime.Now;
								model.LotId = runOrderEx.LotId;
								log.ExecutionId = model.ID;
								poProducedExecutions.Add(model);
								runOrderEx.EndTime = model.StartTime.Value.AddSeconds(-1);
								runOrderEx.Status = "3";
								runOrderEx.Modify(runOrderEx.ID, "MMIInterface");
								poProducedExecutions2.Add(runOrderEx);
							}
							else
							{
								err += $"未找到第一批工单的运行记录";
								log.Content = $"未找到第一批工单的运行记录";
								log.Detail += $"{Environment.NewLine}返回结果：未找到第一批工单的运行记录";
								continue;
							}
							toAndonList.Add(new { BatchId = batch.ID, EquipmentId = equipment.ID });
						}
					}
					else if (reqModel.BatchIndex == "1")
					{
						log.ExecutionId = runOrder.ID;
						var runOrderEx = await _dal14.FindEntity(runOrder.ID);
						if (runOrderEx != null)
						{
							var batch = await _dal6.FindEntity(runOrderEx.BatchId);
							if (batch != null && batch.PrepStatus != "8" && batch.PrepStatus != "9")
							{
								toAndonList.Add(new { BatchId = batch.ID, EquipmentId = equipment.ID });
							}
						}
					}
					//获取Function
					MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + equipment.ID, _user.GetToken(), new { });
					var equipmentAllData = apiResult_equipmentAllData.response;
					if (equipmentAllData == null)
					{
						err += $"设备{equipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
						log.Content = $"设备{equipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
						log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
						continue;
					}
					var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Tipping");
					var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MMITrigger");
					var v1 = item2?.ActualValue ?? item2?.DefaultValue;
					if (v1 == "True")
					{
						var propertyEntity = await _dal12.FindEntity(x => x.PropertyCode == "ButtonEnable");
						if (propertyEntity == null)
						{
							err += $"设备{equipment.EquipmentCode}未找到属性ButtonEnable,请检查MES配置";
							log.Content = $"设备{equipment.EquipmentCode}未找到属性ButtonEnable,请检查MES配置";
							log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}未找到属性ButtonEnable,请检查MES配置";
							continue;
						}
						var functionPropertyValue = await _dal13.FindEntity(x => x.PropertyId == propertyEntity.ID && x.EquipmentId == equipment.ID);
						if (functionPropertyValue == null)
						{
							functionPropertyValue = new FunctionPropertyValueEntity()
							{
								EquipmentId = equipment.ID,
								EquipmentFunctionId = item.EquipmentFunctionId,
								FunctionId = item.FunctionId,
								PropertyId = propertyEntity.ID,
								Enable = 1,
								Deleted = 0,
							};
							functionPropertyValue.PropertyValue = "True";
							functionPropertyValue.CreateCustomGuid("Interface");
							addList.Add(functionPropertyValue);
						}
						else
						{
							functionPropertyValue.PropertyValue = "True";
							functionPropertyValue.Modify(functionPropertyValue.ID, "Interface");
							updateList.Add(functionPropertyValue);
						}
						log.Content = $"设备{equipment.EquipmentCode}允许投料";
						log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}允许投料";
					}
				}
				catch (Exception ex)
				{
					SerilogServer.LogDebug($"【请求投料】出现异常：{ex.StackTrace.ToString()}", "MMIInterfaceLog");
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (addList.Count > 0)
				{
					await _dal13.Add(addList);
				}
				if (updateList.Count > 0)
				{
					await _dal13.Update(updateList);
				}
				if (poProducedExecutions2?.Count > 0)
				{
					await _dal14.Update(poProducedExecutions2);
				}
				if (poProducedExecutions?.Count > 0)
				{
					await _dal14.Add(poProducedExecutions);
				}
				if (logs?.Count > 0)
				{
					await _interfaceLogServices.Add(logs);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.ReturnMessage = ex.Message;
				SerilogServer.LogDebug($"【请求投料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
				return result;
			}
			foreach (var item in toAndonList)
			{
				try
				{
					_andonServices.SendBatchIdToAndon(item.BatchId, item.EquipmentId);
				}
				catch (Exception ex)
				{


				}
			}
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！" + err;
			SerilogServer.LogDebug($"【请求投料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 煮料完成出料_Proleit ✔
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> CookingCompleted(List<MMI_CookingCompleted> reqModels)
		{
			SerilogServer.LogDebug($"【煮料完成出料】接口被调用", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			var confIds = new List<string>();
			var startList = new List<dynamic>();
			var poProducedExecutions = new List<PoProducedExecutionEntity>();
			var poProducedExecutions2 = new List<PoProducedExecutionEntity>();
			var productionOrders = new List<ProductionOrderEntity>();
			var materialLots = new List<MaterialLotEntity>();
			List<PoEquipmentEntity> poEs = new List<PoEquipmentEntity>();
			string err = "";
			foreach (var reqModel in reqModels)
			{
				var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
				var equipment = equipmentResult.response;
				//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
				if (equipment == null)
				{
					err += $"未找到Equipment{reqModel.EquipmentId}";
					continue;
				}
				var runOrder = await _dal36.FindEntity(x => x.ProcessOrder == reqModel.ProductionNO && x.RunEquipmentId == equipment.ID && x.Number == reqModel.BatchIndex && x.Status == "1");
				if (runOrder == null)
				{
					err += $"设备{reqModel.EquipmentId}未找到运行的工单";
					continue;
				}
				var runOrderEx = await _dal14.FindEntity(runOrder.ID);
				if (runOrderEx == null)
				{
					err += $"设备{reqModel.EquipmentId}未找到工单";
					continue;
				}
				if (reqModel.TransferStatus == 1)
				{
					var equipmentResult2 = await GetEquipmentFromMMIEquipmentCode(reqModel.DestinationEquipmentId);
					var destinationEquipment = equipmentResult2.response;
					//var destinationEquipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.DestinationEquipmentId);
					if (destinationEquipment == null)
					{
						err += $"未找到DestinationEquipmentId:{reqModel.DestinationEquipmentId}";
						continue;
					}
					var runOrder2 = await _dal36.FindEntity(x => /*x.ProcessOrder == reqModel.ProductionNO &&*/ x.RunEquipmentId == destinationEquipment.ID /*&& x.Number == reqModel.BatchIndex*/ && x.Status == "1" && x.EndTime == null);
					if (runOrder2 != null)
					{
						var runOrderEx1 = await _dal14.FindEntity(runOrder2.ID);
						if (runOrderEx1 != null)
						{
							runOrderEx1.EndTime = DateTime.Now;
							runOrderEx1.Status = "3";
							runOrderEx1.Modify(runOrderEx1.ID, "MMI");
							poProducedExecutions.Add(runOrderEx1);
							confIds.Add(runOrderEx1.ProductionOrderId);
						}
					}
					//var processOrder = (await _dal37.FindList(x => x.EquipmentId == destinationEquipment.ID && x.ProductionOrderId == runOrder.ProductionOrderId && (x.Status == 2 || x.Status == 5 || x.Status == 6))).FirstOrDefault();
					//if (processOrder == null)
					//{
					//	err += $"目的设备:{reqModel.DestinationEquipmentId}不可运行该工单";
					//	continue;
					//}
					var batch = await _dal6.FindEntity(x => x.ProductionOrderId == runOrderEx.ProductionOrderId && x.PoSegmentRequirementId == runOrderEx.PoSegmentRequirementId && x.Number == reqModel.BatchIndex);
					if (batch == null)
					{
						err += $"未找到batch";
						continue;
					}
					var poSegmentRequirement = await _dal7.FindEntity(runOrderEx.PoSegmentRequirementId);
					if (poSegmentRequirement == null)
					{
						err += $"未找到poSegmentRequirement";
						continue;
					}
					MessageModel<DFM.Model.Models.SapSegmentEntity> apiResult_sapSegment = await HttpHelper.GetApiAsync<DFM.Model.Models.SapSegmentEntity>("DFM", "api/SapSegment/GetEntity/" + poSegmentRequirement.SegmentId, _user.GetToken(), null);
					var sapSegment = apiResult_sapSegment.response;
					if (sapSegment == null)
					{
						err += $"未找到sapSegment";
						continue;
					}
					MessageModel<DFM.Model.Models.MaterialEntity> apiResult_material = await HttpHelper.GetApiAsync<DFM.Model.Models.MaterialEntity>("DFM", "api/Material/GetEntity/" + batch.MaterialId, _user.GetToken(), null);
					var material = apiResult_material.response;
					if (material == null)
					{
						err += $"未找到material";
						continue;
					}
					//batch.BatchCode = (await GetBatchCode(new GetBatchCodeModel() { productionId = runOrder.ProductionOrderId, productionDate = DateTime.Now, equipmentCode = destinationEquipment.EquipmentCode })).response ?? "";
					//batch.Modify(batch.ID, _user.Name);

					//var value = await _bProductionOrderListViewServices.GetMaterialPropertyValue(batch.MaterialId, "物料有效期属性编码");

					MaterialLotEntity materialLot = await _dal38.FindEntity(runOrderEx.LotId);

					//MaterialLotEntity materialLot = new();
					//materialLot.LotId = batch.BatchCode;
					//materialLot.MaterialId = batch.MaterialId;
					//materialLot.ProductionDateLocal = DateTime.Now;
					//materialLot.ExpirationDate = materialLot.ProductionDateLocal.Value.AddDays(processOrder.ExpirationDay);
					//materialLot.Type = "1";
					//materialLot.ExternalStatus = "2";//默认为解锁
					//materialLot.DispositionId = "1";
					//materialLot.CreateCustomGuid("MMI");
					//materialLots.Add(materialLot);
					PoProducedExecutionEntity model = new PoProducedExecutionEntity();
					model.CreateCustomGuid("MMI");
					model.SapEquipmentId = sapSegment.SapEquipmentId;
					model.LineId = batch.LineId;
					model.ProductionOrderId = runOrderEx.ProductionOrderId;
					model.PoSegmentRequirementId = runOrderEx.PoSegmentRequirementId;
					model.PoProducedRequirementId = batch.PoProducedRequirementId;
					model.BatchId = batch.ID;
					model.RunEquipmentId = destinationEquipment.ID;
					model.MaterialId = batch.MaterialId;
					model.MaterialVersionId = batch.MaterialVersionId;
					model.MaterialCode = material.Code;
					model.MaterialDescription = material.Description;
					model.TargetQuantity = batch.TargetQuantity;
					model.UnitId = batch.UnitId;
					model.Status = "1";//RUNNING
					model.Deleted = 0;
					model.StartTime = DateTime.Now;
					model.LotId = runOrderEx.LotId;
					poProducedExecutions2.Add(model);
					startList.Add(new { BatchId = model.BatchId, model.ID, EquipmentId = model.RunEquipmentId });
					var productionOrder = await _dal5.FindEntity(runOrderEx.ProductionOrderId);
					productionOrder.PoStatus = "6";
					if (productionOrder.StartTime == null)
					{
						productionOrder.StartTime = model.StartTime;
					}
					productionOrder.Modify(productionOrder.ID, "MMI");
					productionOrders.Add(productionOrder);

					var poeqs = await _dalpoeq.FindList(x => x.OrderId == productionOrder.ID);
					var poeq = poeqs.FindAll(x => x.SegmentId == poSegmentRequirement.SegmentId && x.EquipmentId == destinationEquipment.ID)?.FirstOrDefault();
					if (poeq == null)
					{
						poeq = new PoEquipmentEntity()
						{
							OrderId = productionOrder.ID,
							SegmentId = poSegmentRequirement.SegmentId,
							EquipmentId = destinationEquipment.ID
						};
						poeq.CreateCustomGuid("MMI");
						poEs.Add(poeq);

						var EquipmentIdList = poeqs.Select(x => x.EquipmentId).ToList();
						EquipmentIdList.Add(poeq.EquipmentId);
						EquipmentIdList = EquipmentIdList.Distinct().ToList();
						MessageModel<string> apiResult = await HttpHelper.PostAsync<string>("DFM", "api/RecipeCommon/BindPoRecipe", _user.GetToken(), new { ProductionOrderId = productionOrder.ID, MaterialVersionId = productionOrder.MaterialVersionId, ProductionTime = productionOrder.PlanStartTime, EquipmentIdList = EquipmentIdList });
						SerilogServer.LogDebug($"【煮料完成出料】[{productionOrder.ProductionOrderNo}]绑定配方数据完成", "MMIInterfaceLog");
					}
				}
				else
				{
					runOrderEx.EndTime = DateTime.Now;
					runOrderEx.Status = "3";
					runOrderEx.Modify(runOrderEx.ID, "MMI");
					poProducedExecutions.Add(runOrderEx);
					confIds.Add(runOrderEx.ID);
				}
			}

			_unitOfWork.BeginTran();
			try
			{
				if (poProducedExecutions?.Count > 0)
				{
					await _dal14.Update(poProducedExecutions);
				}

				if (poProducedExecutions2?.Count > 0)
				{
					await _dal14.Add(poProducedExecutions2);
				}

				//if (materialLots?.Count > 0)
				//{
				//	await _dal38.Add(materialLots);
				//}

				if (productionOrders?.Count > 0)
				{
					await _dal5.Update(productionOrders);
				}

				if (poEs.Count > 0)
				{
					await _dalpoeq.Add(poEs);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception e)
			{
				_unitOfWork.RollbackTran();
				result.ReturnMessage = e.Message;
				SerilogServer.LogDebug($"【煮料完成出料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
				return result;
			}
			foreach (var id in confIds)
			{
				await _performanceServices.SaveConfirmation(id);
			}
			foreach (var item in startList)
			{
				try
				{
					await POStart(item.ID, item.BatchId, item.EquipmentId, 1);
					//发送给ANDON系统
					await _andonServices.CheckNextOrderMaterialPrep(item.ID);
					await UpdateAutoReportDataItemData(item.EquipmentId);
				}
				catch (Exception ex)
				{
					SerilogServer.LogDebug($"【煮料完成出料】POStart工单信息发送MMI时出现异常：{ex.Message}", "MMIInterfaceLog");
				}
			}
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！" + err;
			SerilogServer.LogDebug($"【煮料完成出料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 储存缸到灌装机出料_Proleit ✔
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> StorageToFilling(List<MMI_StorageToFilling> reqModels)
		{
			string interFaceName = "储存缸到灌装机出料";
			SerilogServer.LogDebug($"【储存缸到灌装机出料】接口被调用", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			var confIds = new List<string>();
			var autoIds = new List<string>();
			var poProducedExecutions = new List<PoProducedExecutionEntity>();
			var functionPropertyValues = new List<FunctionPropertyValueEntity>();
			var functionPropertyValues2 = new List<FunctionPropertyValueEntity>();
			var logs = new List<InterfaceLogEntity>();
			string err = "";
			foreach (var reqModel in reqModels)
			{
				InterfaceLogEntity log = new InterfaceLogEntity();
				log.OrderNo = reqModel.ProductionNO;
				log.BatchNo = reqModel.BatchIndex;
				log.Name = "StorageToFilling";
				log.Description = interFaceName;
				log.Detail = $"【储存缸到灌装机出料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModel)}";
				log.CreateCustomGuid("MMI");
				logs.Add(log);
				var equipmentResult = await GetEquipmentFromMMIEquipmentCode(reqModel.EquipmentId);
				var equipment = equipmentResult.response;
				//var equipment = await _dal15.FindEntity(x => x.EquipmentCode == reqModel.EquipmentId);
				if (equipment == null)
				{
					err += equipmentResult.msg;
					log.Content = equipmentResult.msg;
					log.Detail += $"{Environment.NewLine}返回结果：{equipmentResult.msg}";
					continue;
				}
				var dataItem = await _dal33.FindEntity(x => x.ItemCode == "StorageFillerMapping");
				if (dataItem == null)
				{
					err += $"未找到StorageFillerMapping,请检查数据字典配置！";
					log.Content = $"未找到StorageFillerMapping,请检查数据字典配置！";
					log.Detail += $"{Environment.NewLine}返回结果：未找到StorageFillerMapping,请检查数据字典配置！";
					continue;
				}
				var dataItemDetail = await _dal34.FindEntity(x => x.ItemId == dataItem.ID && x.ItemValue == reqModel.DestinationEquipmentId);
				if (dataItemDetail == null)
				{
					err += $"未找到MMI设备{reqModel.DestinationEquipmentId},请检查数据字典配置！";
					log.Content = $"未找到MMI设备{reqModel.DestinationEquipmentId},请检查数据字典配置！";
					log.Detail += $"{Environment.NewLine}返回结果：未找到MMI设备{reqModel.DestinationEquipmentId},请检查数据字典配置！";
					continue;
				}
				var destinationEquipment = await _dal15.FindEntity(x => x.EquipmentCode == dataItemDetail.ItemName);
				if (destinationEquipment == null)
				{
					err += $"未找到MMI设备编码{reqModel.DestinationEquipmentId}，请检查物理模型属性配置！";
					log.Content = $"未找到MMI设备编码{reqModel.DestinationEquipmentId}，请检查物理模型属性配置！";
					log.Detail += $"{Environment.NewLine}返回结果：未找到MMI设备编码{reqModel.DestinationEquipmentId}，请检查物理模型属性配置！";
					continue;
				}
				var runOrder = await _dal36.FindEntity(x => x.ProcessOrder == reqModel.ProductionNO && x.RunEquipmentId == equipment.ID && x.Number == reqModel.BatchIndex && x.Status == "1");
				if (runOrder == null)
				{
					err += $"设备{equipment.EquipmentCode}未找到运行的工单";
					log.Content = $"设备{equipment.EquipmentCode}未找到运行的工单";
					log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}未找到运行的工单";
					continue;
				}
				log.ExecutionId = runOrder.ID;
				var runOrderEx = await _dal14.FindEntity(runOrder.ID);
				if (runOrderEx == null)
				{
					err += $"设备{equipment.EquipmentCode}未找到运行的工单";
					log.Content = $"设备{equipment.EquipmentCode}未找到运行的工单";
					log.Detail += $"{Environment.NewLine}返回结果：设备{equipment.EquipmentCode}未找到运行的工单";
					continue;
				}
				//获取Function
				MessageModel<EquipmentAllDataModel> apiResult_equipmentAllData = await HttpHelper.PostAsync<EquipmentAllDataModel>("DFM", "api/Equipment/GetEquipmentAllData?EquipmentId=" + destinationEquipment.ID, _user.GetToken(), new { });
				var equipmentAllData = apiResult_equipmentAllData.response;
				if (equipmentAllData == null)
				{
					err += $"设备{destinationEquipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
					log.Content = $"设备{destinationEquipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
					log.Detail += $"{Environment.NewLine}返回结果：设备{destinationEquipment.EquipmentCode}equipmentAllData为空,请检查MES配置";
					continue;
				}
				var item = equipmentAllData.EquipmentFunctionPropertyList?.Find(x => x.FunctionCode == "Consume");
				var item2 = item?.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == "MasterStorageTank");
				var v1 = item2?.ActualValue ?? item2?.DefaultValue ?? "";
				var propertyEntity = await _dal12.FindEntity(x => x.FunctionId == item.FunctionId && x.PropertyCode == "MasterStorageTank");
				if (propertyEntity == null)
				{
					err += $"设备{destinationEquipment.EquipmentCode}未找到属性MasterStorageTank,请检查MES配置";
					log.Content = $"设备{destinationEquipment.EquipmentCode}找到属性MasterStorageTank,请检查MES配置";
					log.Detail += $"{Environment.NewLine}返回结果：设备{destinationEquipment.EquipmentCode}找到属性MasterStorageTank,请检查MES配置";
					continue;
				}
				var functionPropertyValue = await _dal13.FindEntity(x => x.PropertyId == propertyEntity.ID && x.EquipmentId == destinationEquipment.ID);
				//出料开始则取储罐设备编码写入属性MasterStorageTank，反之则清空值
				string v = reqModel.TransferStatus == 1 ? equipment.EquipmentCode : "";
				if (v1 != v)
				{
					if (functionPropertyValue == null)
					{
						functionPropertyValue = new FunctionPropertyValueEntity()
						{
							EquipmentId = destinationEquipment.ID,
							EquipmentFunctionId = item.EquipmentFunctionId,
							FunctionId = item.FunctionId,
							PropertyId = propertyEntity.ID,
							Enable = 1,
							Deleted = 0,
						};
						functionPropertyValue.PropertyValue = v;
						functionPropertyValue.CreateCustomGuid("MMI");
						functionPropertyValues.Add(functionPropertyValue);
					}
					else
					{
						functionPropertyValue.PropertyValue = v;
						functionPropertyValue.Modify(functionPropertyValue.ID, "MMI");
						functionPropertyValues2.Add(functionPropertyValue);
					}
				}
				if (reqModel.TransferStatus == 2 || reqModel.TransferStatus == 3)
				{
					autoIds.Add(destinationEquipment.EquipmentCode);
				}
				if (reqModel.TransferStatus == 3)
				{
					runOrderEx.EndTime = DateTime.Now;
					runOrderEx.Status = "3";
					runOrderEx.Modify(runOrderEx.ID, "MMI");
					poProducedExecutions.Add(runOrderEx);
					confIds.Add(runOrderEx.ID);
				}
				log.Content = $"储存缸{equipment.EquipmentCode}到灌装机{destinationEquipment.EquipmentCode}出料完成";
				log.Detail += $"{Environment.NewLine}返回结果：储存缸{equipment.EquipmentCode}到灌装机{destinationEquipment.EquipmentCode}出料完成";
			}
			_unitOfWork.BeginTran();
			try
			{
				if (functionPropertyValues?.Count > 0)
				{
					await _dal13.Add(functionPropertyValues);
				}
				if (functionPropertyValues2?.Count > 0)
				{
					await _dal13.Update(functionPropertyValues2);
				}
				if (poProducedExecutions?.Count > 0)
				{
					await _dal14.Update(poProducedExecutions);
				}
				if (logs.Count > 0)
				{
					await _interfaceLogServices.Add(logs);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception e)
			{
				_unitOfWork.RollbackTran();
				result.ReturnMessage = e.Message;
				SerilogServer.LogDebug($"【储存缸到灌装机出料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
				return result;
			}
			foreach (var id in confIds)
			{
				await _performanceServices.SaveConfirmation(id);
			}
			try
			{
				//foreach (var item in autoIds)
				//{
				//	///触发灌装机的消耗
				//	await AutoReport("Consume", item, null);
				//}
			}
			catch (Exception ex)
			{
				SerilogServer.LogDebug($"【储存缸到灌装机出料】触发灌装机自动消耗时出现异常{ex.Message}", "MMIInterfaceLog");
			}
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！" + err;
			SerilogServer.LogDebug($"【储存缸到灌装机出料】处理完成{Environment.NewLine}请求内容：{FAJsonConvert.ToJson(reqModels)}{Environment.NewLine}返回结果：{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 接收CIP记录_Proleit 
		/// </summary>
		/// <param name="reqModel"></param>
		/// <returns></returns>
		public async Task<MMI_Res> ReceiveCIPRecords(List<MMI_CIP> reqModels)
		{
			SerilogServer.LogDebug($"【接收CIP记录】request:{FAJsonConvert.ToJson(reqModels)}", "MMIInterfaceLog");
			var result = new MMI_Res
			{
				ReturnCode = "-1",
				ReturnMessage = "操作失败！",
				ReturnMessageID = "",
			};
			string err = "";
			var pvs = await _funpdal.FindList(x => x.FunctionCode == "POManagement" && x.PropertyCode == "MMIEquipmentCode");
			var equipments = await _dal15.Query();
			if (reqModels == null)
			{
				return result;
			}
			var groups = reqModels.GroupBy(x => new { x.Equipment, x.CIPOrderNo, x.CIPClass, x.TotalStartTime, x.TotalEndTime });
			var cipDatas = await _cipInfoDal.Query();
			List<CipinfoEntity> cipinfos = new List<CipinfoEntity>();
			List<CipinfoDetailEntity> cipinfoDetails = new List<CipinfoDetailEntity>();
			var toandonList = new List<dynamic>();
			foreach (var group in groups)
			{
				var (equipmentIds, equipmentCodes, orderNos) = await GetEquipmentsFromMMIEquipmentCodes(pvs, equipments, group.Key.Equipment);
				var cipData = cipDatas.Find(x => x.EquipmentCode == equipmentCodes && x.CipOrderCode == group.Key.CIPOrderNo && x.CipType == group.Key.CIPClass && x.CipStart == group.Key.TotalStartTime && x.CipEnd == group.Key.TotalEndTime);
				if (cipData == null)
				{
					cipData = new CipinfoEntity();
					cipData.EquipmentCode = equipmentCodes;
					cipData.OrderCode = orderNos;
					cipData.CipOrderCode = group.Key.CIPOrderNo;
					cipData.CipType = group.Key.CIPClass;
					cipData.CipStart = group.Key.TotalStartTime;
					cipData.CipEnd = group.Key.TotalEndTime;
					cipData.CipStation = "";
					cipData.CreateCustomGuid("MMIInterface");
					cipinfos.Add(cipData);
					foreach (var item in equipmentIds)
					{
						toandonList.Add(new { EquipmentId = item, CipEnd = cipData.CipEnd });
					}
				}
				var g = group.OrderBy(x => x.StepStartTime);
				foreach (var item in g)
				{
					//根据开始时间和CIPStepKey找过去的CIPStep，找到时间连续的
					var cipinfoDetail1 = cipinfoDetails.FindLast(x => x.CipInfoId == cipData.ID && x.CipStep == item.CIPStepKey && x.StepEnd == item.StepStartTime);
					if (cipinfoDetail1 != null)
					{
						cipinfoDetail1.StepEnd = item.StepEndTime;
					}
					else
					{
						cipinfoDetail1 = cipinfoDetails.FindLast(x => x.CipInfoId == cipData.ID && x.CipStep == item.CIPStepKey && x.StepStart == item.StepEndTime);
						if (cipinfoDetail1 != null)
						{
							cipinfoDetail1.StepStart = item.StepStartTime;
						}
						else
						{
							var cipinfoDetail = new CipinfoDetailEntity();
							cipinfoDetail.CipInfoId = cipData.ID;
							cipinfoDetail.CipStep = item.CIPStepKey;
							cipinfoDetail.StepStart = item.StepStartTime;
							cipinfoDetail.StepEnd = item.StepEndTime;
							//从influxdb取数
							cipinfoDetail.StepTemperature = null;
							cipinfoDetail.StepConcentration = null;
							cipinfoDetail.StepTraffic = null;
							//cipinfoDetail.Remark = "";
							cipinfoDetail.CreateCustomGuid("MMIInterface");
							cipinfoDetails.Add(cipinfoDetail);
						}
					}
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (cipinfos.Count > 1000)
				{
					await _cipInfoDal.AddBigData(cipinfos);
				}
				else if (cipinfos.Count > 0)
				{
					await _cipInfoDal.Add(cipinfos);
				}
				if (cipinfoDetails.Count > 1000)
				{
					await _cipInfoDetailDal.AddBigData(cipinfoDetails);
				}
				else if (cipinfoDetails.Count > 0)
				{
					await _cipInfoDetailDal.Add(cipinfoDetails);
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				err = ex.Message;
				result.ReturnMessage += err;
				SerilogServer.LogDebug($"【接收CIP记录】return:{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
				return result;
			}
			try
			{
				foreach (var item in toandonList)
				{
					var r = await _andonServices.SendCipEquipmentIdToAndon(item.EquipmentId, item.CipEnd);
				}
			}
			catch (Exception ex)
			{
				SerilogServer.LogDebug($"【接收CIP记录】发送Andon时出现异常：{ex.StackTrace}", "MMIInterfaceLog");
			}
			result.ReturnCode = "1";
			result.ReturnMessage = "操作成功！" + err;
			SerilogServer.LogDebug($"【接收CIP记录】return:{FAJsonConvert.ToJson(result)}", "MMIInterfaceLog");
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="mMIEquipmentCode"></param>
		/// <returns></returns>
		public async Task<MessageModel<EquipmentEntity>> GetEquipmentFromMMIEquipmentCode(string mMIEquipmentCode)
		{
			return await GetEquipmentFromProperty("POManagement", "MMIEquipmentCode", mMIEquipmentCode);
		}

		public async Task<(List<string>, string, string)> GetEquipmentsFromMMIEquipmentCodes(List<SEFA.PPM.Model.Models.PTM.FunctionPropertyVEntity> pvs, List<EquipmentEntity> equipments, string mMIEquipmentCodes)
		{
			string result = mMIEquipmentCodes;
			string orderNos = "";
			List<string> equipmentIds = new List<string>();
			var codes = mMIEquipmentCodes?.Split(",")?.ToList();
			if (codes.Any())
			{
				result = "";
				var runOrders = await _dal14.Query();
				var orders = await _dal5.Query();
				//var pvs = await _funpdal.FindList(x => x.FunctionCode == "POManagement" && x.PropertyCode == "MMIEquipmentCode");
				foreach (var item in codes)
				{
					try
					{
						var equipmentF = pvs?.Find(x => x.PropertyValue == item);
						if (equipmentF != null)
						{
							var equipment = equipments.Find(x => x.ID == equipmentF.EquipmentId);
							equipmentIds.Add(equipment.ID);
							result += "," + equipment?.EquipmentCode ?? item;
							if (equipment != null)
							{
								var equipment_runorders = runOrders.FindAll(x => x.RunEquipmentId == equipmentF.EquipmentId).OrderBy(x => x.StartTime);
								var last = equipment_runorders.LastOrDefault();
								if (last != null)
								{
									var last2 = equipment_runorders.Where(x => x.StartTime <= last.StartTime && x.ID != last.ID && x.ProductionOrderId != last.ProductionOrderId)?.LastOrDefault();
									if (last2 != null)
									{
										var order = orders.Find(x => x.ID == last2.ProductionOrderId);
										if (order != null)
										{
											orderNos += "," + order.ProductionOrderNo;
										}
										else
										{
											orderNos += "," + "Not found order";
										}
									}
									else
									{
										orderNos += "," + "Not found previous order";
									}
								}
								else
								{
									orderNos += "," + "Not found last run order";
								}
							}
							else
							{
								result += "," + item;
								orderNos += "," + "Not found equipment";
							}
						}
						else
						{
							result += "," + item;
							orderNos += "," + "Not found MMIEquipmentCode";
						}
					}
					catch (Exception ex)
					{
						orderNos += "," + "err";
						SerilogServer.LogDebug($"【接收CIP记录】GetEquipmentsFromMMIEquipmentCodes err:{ex.StackTrace}", "MMIInterfaceLog");
					}
				}
				char[] charsToTrim = { ',' };
				result = result.Trim(charsToTrim);
				orderNos = orderNos.Trim(charsToTrim);
			}
			return (equipmentIds, result, orderNos);
		}

		/// <summary>
		/// 根据设备属性值找到对应的设备
		/// </summary>
		/// <param name="functionCode"></param>
		/// <param name="propertyCode"></param>
		/// <param name="propertyValue"></param>
		/// <returns></returns>
		public async Task<MessageModel<EquipmentEntity>> GetEquipmentFromProperty(string functionCode, string propertyCode, string propertyValue)
		{
			var result = new MessageModel<EquipmentEntity>
			{
				msg = "获取失败！",
				success = false,
			};
			var functionEntity = (await _dalfunction.FindList(x => x.FunctionCode == functionCode)).FirstOrDefault();
			if (functionEntity == null)
			{
				result.msg = $"未找到Function{functionCode}";
				return result;
			}
			var functionProperty = (await _dal12.FindList(x => x.FunctionId == functionEntity.ID && x.PropertyCode == propertyCode)).FirstOrDefault();
			if (functionProperty == null)
			{
				result.msg = $"未找到属性{propertyCode}";
				return result;
			}
			var functionPropertyValues = await _dal13.FindList(x => x.FunctionId == functionEntity.ID && x.PropertyId == functionProperty.ID && x.PropertyValue == propertyValue);
			if (functionPropertyValues?.Count > 1)
			{
				SerilogServer.LogDebug($"找到属性{propertyCode}值为：{propertyValue}的设备数量有{functionPropertyValues?.Count}个，请检查配置", "FindEquipmentFunctionPropertyValue");
			}
			var functionPropertyValue = functionPropertyValues.FirstOrDefault();
			if (functionPropertyValue == null)
			{
				SerilogServer.LogDebug($"未找到属性{propertyCode}值为：{propertyValue}的设备", "FindEquipmentFunctionPropertyValue");
				result.msg = $"未找到属性{propertyCode}值为：{propertyValue}的设备";
				return result;
			}
			var equipment = await _dal15.FindEntity(functionPropertyValue.EquipmentId);
			if (equipment == null)
			{
				result.msg = $"未找到设备";
				return result;
			}
			result.response = equipment;
			result.success = true;
			result.msg = "获取成功！";
			return result;
		}

		public async Task<MessageModel<string>> GetBatchCode(GetBatchCodeModel reqModel)
		{
			var result = new MessageModel<string>
			{
				msg = "获取失败！",
				success = true
			};
			if (string.IsNullOrEmpty(reqModel.productionId))
			{
				result.msg = "productionId不能为空";
				return result;
			}
			try
			{
				//获取上层列表
				MessageModel<List<EquipmentEntity>> apiResult_Equipments = await HttpHelper.PostAsync<List<EquipmentEntity>>("DFM", "api/Equipment/GetProductLine", _user.GetToken(), new { equipmentCode = reqModel.equipmentCode, level = "Line" });
				var equipments = apiResult_Equipments.response;
				if (equipments == null || equipments.Count == 0)
				{
					result.msg = "未找到Line";
					return result;
				}
				var line = equipments.FirstOrDefault();
				if (line == null)
				{
					result.msg = "未找到Line";
					return result;
				}
				var allCode = (await GetFunctionPropertyValue(line.ID, "Common", "CounterfeitLineCode")).response;
				if (allCode == null)
				{
					result.msg = "未找到LineCode";
					return result;
				}
				if (allCode.Length < 2)
				{
					result.msg = "LineCode位数不足两位";
					return result;
				}
				string plant = allCode[0].ToString();//Y
				string lineCode = allCode[1].ToString();//A
				try
				{
					var productionOrder = await _dal5.FindEntity(reqModel.productionId);
					if (productionOrder == null)
					{
						result.msg = "未找到productionOrder";
						return result;
					}
					///煮制工单
					if (productionOrder.SapOrderType == "ZXH2")
					{
						reqModel.productionDate = productionOrder.PlanStartTime.Value;
					}
					var bgsResponse = await _esbHelper.PostXMLString("BGS00001", InterfaceHelper.GetBgsRequestXML(plant, lineCode, reqModel.productionDate.ToString("yyyyMMdd")));
					if (bgsResponse.successed != true)
					{
						result.msg = bgsResponse.msg;
						return result;
					}
					var batchResponse = InterfaceHelper.ParseFromBgsResponseXml<BGS.GetBatchResponse>(bgsResponse.Response);
					if (!batchResponse.GetBatchResult.IsSuccess)
					{
						result.msg = batchResponse.GetBatchResult.Message;
						return result;
					}
					result.response = batchResponse.GetBatchResult.Batch;
				}
				catch (Exception ex)
				{
					result.msg = ex.Message;
					result.response = string.Empty;
					return result;
				}
				//result.response = "";
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				result.response = string.Empty;
				return result;
			}
			result.msg = "获取成功！";
			result.success = true;
			return result;
		}

		public async Task<MessageModel<ParameterGroupEntity>> GetParameterGroup(string productionOrderId, string equipmentId)
		{
			var result = new MessageModel<ParameterGroupEntity>
			{
				msg = "操作失败！",
				success = false,
				response = new ParameterGroupEntity()
			};
			var code = (await GetFunctionPropertyValue(equipmentId, "POManagement", "CookInspectionLogsheet")).response;
			if (string.IsNullOrWhiteSpace(code))
			{
				result.msg = $"设备{equipmentId}属性CookInspectionLogsheet为空";
				return result;
			}
			string sql = @"SELECT
								g.* 
							FROM
								( SELECT PRODUCTION_ORDER_ID, RECIPE_SECTION_VERSION_ID, EQUIPMENT_ID FROM DFM_M_RECIPE_EXEUTION GROUP BY PRODUCTION_ORDER_ID, RECIPE_SECTION_VERSION_ID, EQUIPMENT_ID ) r
								JOIN DFM_M_PARAMETER_GROUP g ON g.RECIPE_SECTION_VERSION_ID = r.RECIPE_SECTION_VERSION_ID 
							WHERE
								r.PRODUCTION_ORDER_ID = @productionOrderId 
								AND EQUIPMENT_ID = @equipmentId
								AND g.CODE = @Code";
			try
			{
				result.response = (await _parameterGroupdal.QuerySql(sql,
					new[] {
						new SugarParameter ("@productionOrderId", productionOrderId),
						new SugarParameter ("@equipmentId", equipmentId),
						new SugarParameter ("@Code", code)
					}))?.FirstOrDefault();
			}
			catch (Exception ex)
			{
				result.msg = ex.Message;
				return result;
			}
			result.success = true;
			result.msg = "操作成功！";
			return result;
		}


		#endregion

		#endregion

		#region HRS考勤系统接口

		/// <summary>
		/// 从HRS考勤系统获取Token
		/// </summary>
		/// <returns></returns>
		public async Task<string> GetTokenFromHRS()
		{
			string key = "HRSToken";
			string token = "";
			if (await _redisBasketRepository.Exist(key))
			{
				/*获取key值*/
				token = await _redisBasketRepository.GetValue(key);
			}
			else
			{
				string userName = "Interface";
				string password = "492CF8CFC09043B550B631B78FC22B4A";
				try
				{
					userName = Appsettings.app("HRSAuth", "UserName");
					password = Appsettings.app("HRSAuth", "Password");
				}
				catch (Exception ex)
				{

				}
				var tokenResult = await _esbHelper.PostJson<StartSession, dynamic>("HRS_StartSession", new { acc = userName, pwd = password });
				if (tokenResult != null && tokenResult.successed == true && tokenResult.Response != null && tokenResult.Response.MsgId == 0)
				{
					token = tokenResult.Response.Result;
					/*设置值，有效期t秒*/
					await _redisBasketRepository.Set(key, token, new TimeSpan(0, 0, 0, 600));
				}
			}
			return token;
		}

		/// <summary>
		/// 从HRS考勤系统获取数据
		/// </summary>
		/// <param name="reqModel">请求参数</param>
		/// <returns></returns>
		public async Task<MessageModel<HRSResModel>> GetDataFromHRS(Paras reqModel)
		{
			var result = new MessageModel<HRSResModel>
			{
				msg = "操作失败！",
				success = false,
			};
			HRSReqModel hRSReq = new HRSReqModel()
			{
				funcId = "207",
				dataFormat = "json",
				paras = reqModel,
			};
			hRSReq.accessToken = await GetTokenFromHRS();
			if (string.IsNullOrEmpty(hRSReq.accessToken))
			{
				result.msg = "获取Token异常！";
				return result;
			}
			LKKESBRequest lKKESBRequest = new LKKESBRequest
			{
				tranNo = Guid.NewGuid().ToString(),
				messageId = "HRS_GetData",
				postData = hRSReq.ToJson(),
			};
			var hrsResult = await _esbHelper.PostJson<HRSResModel>(lKKESBRequest);
			result.response = hrsResult?.Response;
			result.msgDev = hrsResult?.Response?.Msg;
			if (hrsResult?.successed == true && hrsResult?.Response?.MsgId == 0)
			{
				var addWorkingHours = new List<WorkingHourEntity>();
				var updateWorkingHours = new List<WorkingHourEntity>();
				var workingHours = await _whdal.Query();
				if (hrsResult?.Response?.Result?.attendData?.Row != null)
				{
					foreach (var item in hrsResult?.Response?.Result?.attendData?.Row)
					{
						var workingHour = workingHours.Find(x => x.EmployeeId == item.BADGE && x.Date == item.TERM.ToString("yyyy-MM-dd"));
						if (workingHour != null)
						{
							workingHour.Modify(workingHour.ID, _user.Name);
							updateWorkingHours.Add(workingHour);
						}
						else
						{
							workingHour = new WorkingHourEntity();
							workingHour.EmployeeId = item.BADGE;
							workingHour.Date = item.TERM.ToString("yyyy-MM-dd");
							workingHour.CreateCustomGuid(_user.Name);
							addWorkingHours.Add(workingHour);
						}
						workingHour.ActualWorkingHour = item.AMOUNT467515 == null ? 0 : item.AMOUNT467515.Value;
						workingHour.PlantName = item.DPTITLE3;
						workingHour.GroupName = item.DPTITLE4;
					}
					_unitOfWork.BeginTran();
					try
					{
						if (updateWorkingHours.Count > 1000)
						{
							await _whdal.StorageBigData(updateWorkingHours);
						}
						else if (updateWorkingHours.Count > 0)
						{
							await _whdal.Update(updateWorkingHours);
						}

						if (addWorkingHours.Count > 1000)
						{
							await _whdal.AddBigData(addWorkingHours);
						}
						else if (addWorkingHours.Count > 0)
						{
							await _whdal.Add(addWorkingHours);
						}
						_unitOfWork.CommitTran();
					}
					catch (Exception ex)
					{
						_unitOfWork.RollbackTran();
						result.msgDev = ex.Message;
						return result;
					}
				}
				result.msg = "操作成功！";
				result.success = true;
			}
			return result;
		}

		#endregion
	}
}