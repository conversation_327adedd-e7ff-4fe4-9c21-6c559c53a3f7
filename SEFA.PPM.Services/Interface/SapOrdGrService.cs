using AutoMapper;
using ORDGI2;
using ORDGR2;
using SEFA.Base.Common;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;
using SEFA.PPM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Dm.parser.LVal;
using static SEFA.PTM.Services.ConsumeViewServices;

namespace SEFA.PPM.Services
{

	public class SapOrdGrService : BaseServices<PoProducedActualEntity>, ILkkEsbService
	{
		private readonly IBaseRepository<PoProducedActualEntity> _dal;
		private readonly IUnitOfWork _unitOfWork;
		private readonly string _serviceName = "ESB_ORDGR";

		public SapOrdGrService(IBaseRepository<PoProducedActualEntity> dal,
			IUnitOfWork unitOfWork
			)
		{
			this._dal = dal;
			base.BaseDal = dal;
			_unitOfWork = unitOfWork;
			var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
			_serviceName = string.IsNullOrEmpty(serviceName) ? "ESB_ORDGR" : serviceName;
		}

		/// <summary>
		/// 接口名称，和ESB的MessageID一致
		/// </summary>
		public string ServiceName
		{
			get => _serviceName;
		}

		/// <summary>
		/// 执行接口
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public async Task<DataResultModel> Execute(DataRequestModel request)
		{
			SerilogServer.LogDebug($"Request:{FAJsonConvert.ToJson(request)}", "SapOrdGrServiceLog");
			var result = new DataResultModel(request)
			{
				ids = new List<string>()
			};
			var updateList = new List<PoProducedActualEntity>();
			var poProducedActuals = await _dal.FindList(x => x.SendExternal == 4 && x.TranNo == request.tranNo);
			if (poProducedActuals == null || poProducedActuals.Count == 0)
			{
				return result;
			}
			ZRFC_PP_MES_ORDGR2Response reponse = null;
			try
			{
				reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDGR2Response>(request.data.ToString());
			}
			catch (Exception ex)
			{
				reponse = null;
			}
			if (string.IsNullOrEmpty(request.data.ToString()) || reponse == null || reponse.IT_RET == null || reponse.IT_RET.Length == 0)
			{
				foreach (var item in poProducedActuals)
				{
					item.SendExternal = 2;
					item.Msg = request.msg;
					item.Modify(item.ID, request.messageId);
					updateList.Add(item);
				}
			}
			else
			{
				var ids = poProducedActuals.Where(x => !string.IsNullOrEmpty(x.Pid)).Select(x => x.Pid);
				var poProducedActuals2 = await _dal.FindList(x => ids.Contains(x.ID));
				if (reponse.IT_GR.Length > 0)
				{
					//为空则为产出，不为空则为反冲
					bool flag = string.IsNullOrEmpty(reponse.IT_GR[0].MBLNR_C);
					if (reponse.IT_RET.Length > 0)
					{
						var orderNos_F = reponse.IT_RET.ToList().Where(x => x.TYPE != "S").Select(x => x.AUFNR)?.Distinct()?.ToList() ?? new List<string>();
						var productionOrders = await _dal.Db.Queryable<ProductionOrderEntity>().Where(x => orderNos_F.Contains(x.ProductionOrderNo)).ToListAsync();
						foreach (var item in reponse.IT_RET)
						{
							//成功或者反冲走此逻辑
							if (item.TYPE == "S" || !flag)
							{
								var srnum = int.Parse(item.SRNUM);
								PoProducedActualEntity poProducedActual = null;
								//产出
								if (flag)
								{
									poProducedActual = poProducedActuals.Find(x => x.Index == srnum);
								}
								//反冲
								else
								{
									//通过凭证号、行号找到产出
									var poProducedActual_i = poProducedActuals2.Find(x => x.Mblnr == item.MBLNR_C && x.Zeile == item.SRNUM);
									if (poProducedActual_i != null)
									{
										//通过产出id和行号找到反冲
										poProducedActual = poProducedActuals.FirstOrDefault(x => x.Pid == poProducedActual_i.ID && x.Index == srnum);
									}
								}
								if (poProducedActual != null)
								{
									poProducedActual.Type = item.TYPE;
									poProducedActual.Mjahr = item.MJAHR;
									poProducedActual.Mblnr = item.MBLNR;
									poProducedActual.Zeile = item.ZEILE;
									poProducedActual.Msg = item.MSG;
									poProducedActual.SendExternal = item.TYPE == "S" ? 1 : 2;
									poProducedActual.Modify(poProducedActual.ID, request.messageId);
									updateList.Add(poProducedActual);
								}
							}
							//产出失败走此逻辑
							else
							{
								var order = productionOrders.FirstOrDefault(x => x.ProductionOrderNo == item.AUFNR);
								if (order != null)
								{
									var poProducedActuals_F = poProducedActuals.Where(x => x.ProductionOrderId == order.ID).ToList();
									if (poProducedActuals_F.Count > 0)
									{
										foreach (var poProducedActual_i in poProducedActuals_F)
										{
											poProducedActual_i.Type = item.TYPE;
											poProducedActual_i.Mjahr = item.MJAHR;
											poProducedActual_i.Mblnr = item.MBLNR;
											poProducedActual_i.Zeile = item.ZEILE;
											poProducedActual_i.Msg = item.MSG;
											poProducedActual_i.SendExternal = 2;
											poProducedActual_i.Modify(poProducedActual_i.ID, request.messageId);
											updateList.Add(poProducedActual_i);
										}
									}
								}
							}
						}
						//foreach (var item in reponse.IT_RET)
						//{
						//	if (item.TYPE == "S")
						//	{
						//		var srnum = int.Parse(item.SRNUM);
						//		var poConsumeActual = poProducedActuals.Find(x => x.Index == srnum);
						//		if (!flag)
						//		{
						//			//通过凭证号、行号找到产出
						//			var poConsumeActuals_i = poProducedActuals2.Find(x => x.Mblnr == item.MBLNR_C && x.Zeile == item.SRNUM);
						//			if (poConsumeActuals_i != null)
						//			{
						//				//通过产出id和行号找到反冲
						//				poConsumeActual = poProducedActuals.FirstOrDefault(x => x.Pid == poConsumeActuals_i.ID && x.Index == srnum);
						//			}
						//		}
						//		if (poConsumeActual != null)
						//		{
						//			poConsumeActual.Type = item.TYPE;
						//			poConsumeActual.Mjahr = item.MJAHR;
						//			poConsumeActual.Mblnr = item.MBLNR;
						//			poConsumeActual.Zeile = item.ZEILE;
						//			poConsumeActual.Msg = item.MSG;
						//			poConsumeActual.SendExternal = 1;
						//			poConsumeActual.Modify(poConsumeActual.ID, request.messageId);
						//			updateList.Add(poConsumeActual);
						//		}
						//	}
						//	else
						//	{
						//		var order = productionOrders.FirstOrDefault(x => x.ProductionOrderNo == item.AUFNR);
						//		if (order != null)
						//		{
						//			var poConsumeActuals_F = poProducedActuals.Where(x => x.ProductionOrderId == order.ID).ToList();
						//			if (poConsumeActuals_F.Count > 0)
						//			{
						//				foreach (var poConsumeActual in poConsumeActuals_F)
						//				{
						//					poConsumeActual.Type = item.TYPE;
						//					poConsumeActual.Mjahr = item.MJAHR;
						//					poConsumeActual.Mblnr = item.MBLNR;
						//					poConsumeActual.Zeile = item.ZEILE;
						//					poConsumeActual.Msg = item.MSG;
						//					poConsumeActual.SendExternal = 2;
						//					poConsumeActual.Modify(poConsumeActual.ID, request.messageId);
						//					updateList.Add(poConsumeActual);
						//				}
						//			}
						//		}
						//	}
						//}
					}
				}
			}
			_unitOfWork.BeginTran();
			try
			{
				if (updateList.Count > 0)
				{
					if (updateList.Count > 1000)
					{
						await _dal.StorageBigData(updateList);
					}
					else
					{
						await _dal.Update(updateList);
					}
				}
				_unitOfWork.CommitTran();
			}
			catch (Exception ex)
			{
				_unitOfWork.RollbackTran();
				result.Fail(ex.Message + ex.StackTrace);
			}
			return result;

		}

		///// <summary>
		///// 执行接口
		///// </summary>
		///// <param name="request"></param>
		///// <returns></returns>
		//public async Task<DataResultModel> Execute(DataRequestModel request)
		//{
		//	SerilogServer.LogDebug($"Request:{FAJsonConvert.ToJson(request)}", "SapOrdGrServiceLog");
		//	var result = new DataResultModel(request)
		//	{
		//		ids = new List<string>()
		//	};
		//	var updateList = new List<PoProducedActualEntity>();
		//	var poProducedActuals = await _dal.FindList(x => x.SendExternal == 4 && x.TranNo == request.tranNo);
		//	if (poProducedActuals == null || poProducedActuals.Count == 0)
		//	{
		//		return result;
		//	}
		//	var ids = poProducedActuals.Where(x => !string.IsNullOrEmpty(x.Pid)).Select(x => x.Pid);
		//	var poProducedActuals2 = await _dal.FindList(x => ids.Contains(x.ID));
		//	var reponse = InterfaceHelper.ParseFromSapResponseXml<ZRFC_PP_MES_ORDGR2Response>(request.data.ToString());
		//	if (reponse.IT_RET.Length > 1)
		//	{
		//		for (int i = 0; i < reponse.IT_RET.Length; i++)
		//		{
		//			var poProducedActual = poProducedActuals.Find(x => x.Zeile == reponse.IT_RET[i].SRNUM);
		//			if (!string.IsNullOrEmpty(reponse.IT_GR[i].MBLNR_C))
		//			{
		//				//通过凭证号、行号找到产出
		//				var poProducedActuals_i = poProducedActuals2.Find(x => x.Mblnr == reponse.IT_RET[i].MBLNR_C && x.Zeile == reponse.IT_RET[i].SRNUM);
		//				if (poProducedActuals_i != null)
		//				{
		//					//通过产出id和行号找到反冲
		//					poProducedActual = poProducedActuals.FirstOrDefault(x => x.Pid == poProducedActuals_i.ID && x.Zeile == reponse.IT_RET[i].SRNUM);
		//				}
		//			}
		//			if (poProducedActual != null)
		//			{
		//				poProducedActual.Type = reponse.IT_RET[i].TYPE;
		//				poProducedActual.Mjahr = reponse.IT_RET[i].MJAHR;
		//				poProducedActual.Mblnr = reponse.IT_RET[i].MBLNR;
		//				//poProducedActual.Zeile = reponse.IT_RET[i].ZEILE;
		//				poProducedActual.Msg = reponse.IT_RET[i].MSG;
		//				poProducedActual.SendExternal = reponse.IT_RET[i].TYPE == "S" ? 1 : 2;
		//				poProducedActual.Modify(poProducedActual.ID, request.messageId);
		//				updateList.Add(poProducedActual);
		//			}
		//		}
		//	}
		//	else
		//	{
		//		foreach (var poProducedActual in poProducedActuals)
		//		{
		//			poProducedActual.Type = reponse.IT_RET[0].TYPE;
		//			poProducedActual.Mjahr = reponse.IT_RET[0].MJAHR;
		//			poProducedActual.Mblnr = reponse.IT_RET[0].MBLNR;
		//			//poProducedActual.Zeile = reponse.IT_RET[0].ZEILE;
		//			poProducedActual.Msg = reponse.IT_RET[0].MSG;
		//			poProducedActual.SendExternal = reponse.IT_RET[0].TYPE == "S" ? 1 : 2;
		//			poProducedActual.Modify(poProducedActual.ID, request.messageId);
		//			updateList.Add(poProducedActual);
		//		}
		//	}
		//	_unitOfWork.BeginTran();
		//	try
		//	{
		//		if (updateList.Count > 0)
		//		{
		//			if (updateList.Count > 1000)
		//			{
		//				await _dal.StorageBigData(updateList);
		//			}
		//			else
		//			{
		//				await _dal.Update(updateList);
		//			}
		//		}
		//		_unitOfWork.CommitTran();
		//	}
		//	catch (Exception ex)
		//	{
		//		_unitOfWork.RollbackTran();
		//		result.Fail(ex.Message + ex.StackTrace);
		//	}
		//	return result;

		//}
	}
}
