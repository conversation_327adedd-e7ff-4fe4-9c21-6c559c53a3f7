using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Bson;
using SEFA.Base;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.IServices.Interface;
using SEFA.PPM.IServices.Interface.WMS;
using SEFA.PPM.Model.Models.Interface.WMS;

namespace SEFA.PPM.Services.Interface;

public class WmsServices : IWmsServices
{
    private static readonly string CallMaterialUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "CallMaterialUrl" }).ObjToString();

    private static readonly string SendDistributionMaterialUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "SendDistributionMaterialUrl" }).ObjToString();

    private static readonly string PalletReleaseUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "PalletReleaseUrl" }).ObjToString();

    private static readonly string PalletReturnRequestUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "PalletReturnRequestUrl" }).ObjToString();

    private static readonly string BarCodePrintUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "BarCodePrintUrl" }).ObjToString();

    private static readonly string EmptyIbcReturnUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "EmptyIBCReturnUrl" }).ObjToString();

    private static readonly string IbcMaterialInBoundUrl =
        Appsettings.app(new string[] { "AppSettings:WMS_Interface_Url", "IBCMaterialInBoundUrl" }).ObjToString();

    private static readonly string LineWarehouseMaterialOutBoundUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "LineWarehouseMaterialOutBoundUrl" }).ObjToString();

    private static readonly string InventoryQueryUrl =
        Appsettings.app(new string[] { "WMS_Interface_Url", "InventoryQueryUrl" }).ObjToString();

    // 注入日志服务
    private readonly IWMSInterfaceLogServices _logServices;
    private readonly IUser _user;

    public WmsServices(IWMSInterfaceLogServices logServices,
        IUser user)
    {
        _logServices = logServices;
        _user = user;
    }

    #region WMS叫料

    /// <summary>
    /// 异步调用叫料接口
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<MessageModel<String>> CallMaterialAsync(CallMaterialSheetEntity request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "CallMaterial",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<string>("WMS", CallMaterialUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "CallMaterial");
            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "叫料接口返回为空！";
                SerilogServer.LogDebug(result.msg, "CallMaterial");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "叫料接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "CallMaterial");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }

            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "叫料接口调用异常：" + ex.Message;
            SerilogServer.LogDebug(result.msg, "CallMaterial");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region WMS托盘解绑

    /// <summary>
    /// 异步调用托盘解绑接口
    /// </summary>
    /// <param name="request">托盘解绑请求参数</param>
    /// <returns>解绑结果</returns>
    public async Task<MessageModel<String>> PalletReleaseAsync(PalletReleaseRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "托盘解绑成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "PalletRelease",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", PalletReleaseUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "PalletRelease");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "托盘解绑接口返回为空！";
                result.response = "托盘解绑失败";
                SerilogServer.LogDebug(result.msg, "PalletRelease");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "托盘解绑接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "PalletRelease");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "托盘解绑接口调用异常：" + ex.Message;
            result.response = "托盘解绑失败";
            SerilogServer.LogDebug(result.msg, "PalletRelease");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region WMS打印标签

    /// <summary>
    /// 异步调用打印标签接口
    /// </summary>
    /// <param name="request">打印标签请求参数</param>
    /// <returns>打印结果</returns>
    public async Task<MessageModel<String>> BarCodePrintAsync(BarCodePrintRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "打印标签成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "BarCodePrint",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", BarCodePrintUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "BarCodePrint");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "打印标签接口返回为空！";
                result.response = "打印标签失败";
                SerilogServer.LogDebug(result.msg, "BarCodePrint");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "打印标签接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "BarCodePrint");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "打印标签接口调用异常：" + ex.Message;
            result.response = "打印标签失败";
            SerilogServer.LogDebug(result.msg, "BarCodePrint");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region WMS组托退库申请

    /// <summary>
    /// 异步调用组托退库申请接口
    /// </summary>
    /// <param name="request">组托退库申请请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> PalletReturnRequestAsync(PalletReturnRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "组托退库申请成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "PalletReturnRequest",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", PalletReturnRequestUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "PalletReturnRequest");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "组托退库申请接口返回为空！";
                result.response = "组托退库申请失败";
                SerilogServer.LogDebug(result.msg, "PalletReturnRequest");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "组托退库申请接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "PalletReturnRequest");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "组托退库申请接口调用异常：" + ex.Message;
            result.response = "组托退库申请失败";
            SerilogServer.LogDebug(result.msg, "PalletReturnRequest");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region IBC桶空桶退库申请

    /// <summary>
    /// 异步调用IBC桶空桶退库申请接口
    /// </summary>
    /// <param name="request">退库请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> EmptyIBCReturnAsync(EmptyIBCReturnRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "IBC桶空桶退库申请成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "EmptyIBCReturn",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", EmptyIbcReturnUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "EmptyIBCReturn");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "IBC桶空桶退库申请接口返回为空！";
                result.response = "IBC桶空桶退库申请失败";
                SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "IBC桶空桶退库申请接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "IBC桶空桶退库申请接口调用异常：" + ex.Message;
            result.response = "IBC桶空桶退库申请失败";
            SerilogServer.LogDebug(result.msg, "EmptyIBCReturn");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region IBC桶配置成品入库申请

    /// <summary>
    /// 异步调用IBC桶配置成品入库申请接口
    /// </summary>
    /// <param name="request">入库请求参数</param>
    /// <returns>申请结果</returns>
    public async Task<MessageModel<String>> IBCMaterialInBoundAsync(IBCMaterialInBoundRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "IBC桶配置成品入库申请成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "IBCMaterialInBound",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", IbcMaterialInBoundUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "IBCMaterialInBound");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "IBC桶配置成品入库申请接口返回为空！";
                result.response = "IBC桶配置成品入库申请失败";
                SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "IBC桶配置成品入库申请接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "IBC桶配置成品入库申请接口调用异常：" + ex.Message;
            result.response = "IBC桶配置成品入库申请失败";
            SerilogServer.LogDebug(result.msg, "IBCMaterialInBound");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region 线边物料出库

    /// <summary>
    /// 异步调用线边物料出库接口
    /// </summary>
    /// <param name="request">出库请求参数</param>
    /// <returns>出库结果</returns>
    public async Task<MessageModel<String>> LineWarehouseMaterialOutBoundAsync(
        LineWarehouseMaterialOutBoundRequest request)
    {
        MessageModel<String> result = new MessageModel<String>();
        result.success = true;
        result.msg = "成功";
        result.response = "线边物料出库成功";

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "LineWarehouseMaterialOutBound",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult = await HttpHelper.PostAsync<String>("WMS", LineWarehouseMaterialOutBoundUrl, null, request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "LineWarehouseMaterialOutBound");

            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "线边物料出库接口返回为空！";
                result.response = "线边物料出库失败";
                SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "线边物料出库接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = apiResult.success;
            result.msg = apiResult.msg;
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");

            // 记录成功日志
            logEntity.IsSuccess = apiResult.success;
            logEntity.ResponseData = apiResult.ToJson();
            if (!apiResult.success)
            {
                logEntity.ErrorMessage = apiResult.msg;
            }
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "线边物料出库接口调用异常：" + ex.Message;
            result.response = "线边物料出库失败";
            SerilogServer.LogDebug(result.msg, "LineWarehouseMaterialOutBound");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion

    #region 库存查询

    /// <summary>
    /// 异步调用库存查询接口
    /// </summary>
    /// <param name="request">库存查询请求参数</param>
    /// <returns>查询结果</returns>
    public async Task<MessageModel<List<DistributionMaterialDetailEntity>>> InventoryQueryAsync(
        InventoryQueryRequest request)
    {
        MessageModel<List<DistributionMaterialDetailEntity>> result =
            new MessageModel<List<DistributionMaterialDetailEntity>>();
        result.success = true;
        result.msg = "成功";
        result.response = new List<DistributionMaterialDetailEntity>();

        // 创建日志实体
        var logEntity = new WmsInterfaceLogEntity
        {
            InterfaceName = "InventoryQuery",
            RequestData = request?.ToJson(),
            Direction = 0, // 我们调用WMS
            CreateDate = DateTime.Now,
            CreateUserId = "SYSTEM"
        };

        try
        {
            // 调用HTTP辅助类发送POST请求到指定接口
            var apiResult =
                await HttpHelper.PostAsync<List<DistributionMaterialDetailEntity>>("WMS", InventoryQueryUrl, null,
                    request);
            // 记录返回内容的调试日志
            SerilogServer.LogDebug($"返回内容：" + apiResult.ToJson(), "InventoryQuery");
            // 检查返回结果是否为空
            if (apiResult == null)
            {
                // 如果返回为空，设置结果为失败并记录错误消息
                result.success = false;
                result.msg = "库存查询接口返回为空！";
                SerilogServer.LogDebug(result.msg, "InventoryQuery");

                // 记录失败日志
                logEntity.IsSuccess = false;
                logEntity.ErrorMessage = result.msg;
                logEntity.ResponseData = "库存查询接口返回为空";
                logEntity.CreateCustomGuid(_user.Name);
                await _logServices.AddAsync(logEntity);

                return result;
            }

            // 将接口返回的结果赋值给本地结果对象
            result.success = true;
            result.msg = "查询成功";
            // 记录接口返回消息的调试日志
            SerilogServer.LogDebug(result.msg, "InventoryQuery");

            // 记录成功日志
            logEntity.IsSuccess = true;
            logEntity.ResponseData = apiResult.ToJson();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            // 设置响应数据
            result = apiResult;
        }
        catch (Exception ex)
        {
            // 捕获异常，设置结果为失败并记录异常堆栈信息
            result.success = false;
            result.msg = "库存查询接口调用异常：" + ex.Message;
            SerilogServer.LogDebug(result.msg, "InventoryQuery");

            // 记录异常日志
            logEntity.IsSuccess = false;
            logEntity.ErrorMessage = result.msg;
            logEntity.ResponseData = ex.ToString();
            logEntity.CreateCustomGuid(_user.Name);
            await _logServices.AddAsync(logEntity);

            return result;
        }

        return result;
    }

    #endregion
}