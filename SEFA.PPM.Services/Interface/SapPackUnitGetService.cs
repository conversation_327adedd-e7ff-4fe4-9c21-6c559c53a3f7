using AutoMapper;
using SEFA.Base.Common;
using SEFA.Base.ESB;
using SEFA.Base.IRepository.Base;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Services.BASE;

namespace SEFA.PPM.Services.Interface
{
    public class SapPackUnitGetService : BaseServices<PackunitEntity>, ILkkEsbService
    {
        private readonly IBaseRepository<PackunitEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly string _serviceName = "SAP_PACKINGUNITGET";

        public SapPackUnitGetService(IBaseRepository<PackunitEntity> dal, IMapper mapper, IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            var serviceName = Appsettings.app("LKKESBConfig", this.GetType().Name);
            _serviceName = string.IsNullOrEmpty(serviceName) ? "SAP_PACKINGUNITGET" : serviceName;
        }

        /// <summary>
        /// 接口名称，和ESB的MessageID一致
        /// </summary>
        public string ServiceName
        {
            get => _serviceName;
        }

        /// <summary>
        /// 执行接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<DataResultModel> Execute(DataRequestModel request)
        {
            var result = new DataResultModel(request);
            try
            {
                //MaterialEntity许愿替换成实体类
                var sourceDataList = JsonConvert.DeserializeObject<List<PP_SAP_PackingUnit>>(request.data.ToString());
                _unitOfWork.BeginTran();
                try
                {
                    foreach (var data in sourceDataList)
                    {
                        string container = data.TRATY;
                        PackunitEntity obj = await _dal.FindEntity(p => p.Packingunit == container);
                        if (obj == null)
                        {
                            obj = new PackunitEntity()
                            {
                                Packingunit = data.TRATY,
                                Description = data.VTEXT
                            };
                            obj.CreateCustomGuid("SAP_PACKINGUNITGET");
                            await _dal.Add(obj);
                        }
                        else
                        {
                            obj.Description = data.VTEXT;
                            obj.Modify(obj.ID, "SAP_PACKINGUNITGET");
                            await _dal.Update(obj);
                        }
                    }
                    _unitOfWork.CommitTran();
                }
                catch (Exception ex)
                {
                    result.msg = "操作失败";
                    _unitOfWork.RollbackTran();
                    return result.Fail(ex.Message + ex.StackTrace);
                }
                result.msg = "Success";
                return result;
            }
            catch (Exception ex)
            {
                return result.Fail(ex.Message + ex.StackTrace);
            }
        }
    }
}
