using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// HistoryViewRepository
	/// </summary>
    public class HistoryViewRepository : BaseRepository<HistoryViewEntity>, IHistoryViewRepository
    {
        public HistoryViewRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}