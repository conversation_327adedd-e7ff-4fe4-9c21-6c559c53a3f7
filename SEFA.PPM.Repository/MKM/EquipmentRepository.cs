using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// EquipmentRepository
	/// </summary>
    public class EquipmentRepository : BaseRepository<EquipmentEntity>, IEquipmentRepository
    {
        public EquipmentRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}