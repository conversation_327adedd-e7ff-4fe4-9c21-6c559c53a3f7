using SEFA.MKM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.MKM.Model.Models;
using SEFA.Base.Repository.Base;
using SEFA.PPM.Model.Models.MKM;

namespace SEFA.MKM.Repository
{
	/// <summary>
	/// MaterialRepository
	/// </summary>
    public class MaterialRepository : BaseRepository<MaterialEntity>, IMaterialRepository
    {
        public MaterialRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}